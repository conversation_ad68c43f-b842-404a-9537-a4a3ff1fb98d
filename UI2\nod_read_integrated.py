#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NOD文件解析器 - 整合版本
调用test_nod2.py的解析类和test_nod4.py的功能
将解析的数据保存到txt文件中
"""

import os
import sys
from datetime import datetime

# 导入test_nod2.py中的类
from nod_Spectrum_read import NodFileReader, Spectrum

# 导入test_nod4.py中的函数
from nod_raman_read import parse_nod_file


def save_spectrum_to_txt_detailed(spectrum: Spectrum, output_path: str):
    """
    将Spectrum对象保存为详细的txt文件
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write("=" * 60 + "\n")
            f.write("NOD文件解析结果 - 详细报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"解析方法: test_nod2.py NodFileReader类\n")
            f.write("\n")
            
            # 使用get_formatted_metadata获取完整的元数据信息
            reader = NodFileReader()
            formatted_metadata = reader.get_formatted_metadata(spectrum)
            
            # 写入完整的NOD文件信息
            f.write("📋 完整NOD文件信息:\n")
            f.write("-" * 50 + "\n")
            f.write(f"📝 样品名称: {formatted_metadata['样品名称']}\n")
            f.write(f"👤 操作员: {formatted_metadata['操作员']}\n")
            f.write(f"📅 创建时间: {formatted_metadata['创建时间']}\n")
            f.write(f"📄 文件版本: {formatted_metadata['文件版本']}\n")
            f.write(f"🔢 像素数: {formatted_metadata['像素数']}\n")
            f.write(f"⏱️ 积分时间: {formatted_metadata['积分时间']}\n")
            f.write(f"💡 激光功率: {formatted_metadata['激光功率']}\n")
            f.write(f"🔄 扫描次数: {formatted_metadata['扫描次数']}\n")
            f.write(f"🎯 采集模式: {formatted_metadata['采集模式']}\n")
            f.write(f"📏 采样间隔: {formatted_metadata['采样间隔']}\n")
            f.write(f"🔧 设备型号: {formatted_metadata['设备型号']}\n")
            f.write(f"🏷️ 设备序列号: {formatted_metadata['设备序列号']}\n")
            f.write("\n")
            
            # 写入光谱数据信息
            if spectrum.spectrum_data:
                f.write("【光谱数据信息】\n")
                f.write("-" * 30 + "\n")
                
                if spectrum.spectrum_data.raman_shift is not None:
                    f.write(f"拉曼位移数据点数: {len(spectrum.spectrum_data.raman_shift)}\n")
                    f.write(f"拉曼位移范围: {spectrum.spectrum_data.raman_shift[0]:.2f} - {spectrum.spectrum_data.raman_shift[-1]:.2f}\n")
                
                if spectrum.spectrum_data.intensity is not None:
                    f.write(f"强度数据点数: {len(spectrum.spectrum_data.intensity)}\n")
                    f.write(f"强度范围: {spectrum.spectrum_data.intensity.min():.2f} - {spectrum.spectrum_data.intensity.max():.2f}\n")
                
                if spectrum.spectrum_data.raw is not None:
                    f.write(f"原始数据点数: {len(spectrum.spectrum_data.raw)}\n")
                
                if spectrum.spectrum_data.dark is not None:
                    f.write(f"暗电流数据点数: {len(spectrum.spectrum_data.dark)}\n")
                
                f.write(f"是否已处理: {spectrum.spectrum_data.is_processed}\n")
                f.write("\n")
            
            # 写入光谱数据（前10个和后10个数据点）
            if (spectrum.spectrum_data and 
                spectrum.spectrum_data.raman_shift is not None and 
                spectrum.spectrum_data.intensity is not None):
                
                f.write("【光谱数据样本】\n")
                f.write("-" * 30 + "\n")
                f.write("格式: 拉曼位移(cm^-1) | 强度\n")
                f.write("\n")
                
                # 前10个数据点
                f.write("前10个数据点:\n")
                for i in range(min(10, len(spectrum.spectrum_data.raman_shift))):
                    raman = spectrum.spectrum_data.raman_shift[i]
                    intensity = spectrum.spectrum_data.intensity[i]
                    f.write(f"{raman:8.2f} | {intensity:10.2f}\n")
                
                # 如果有更多数据点，显示中间省略号
                if len(spectrum.spectrum_data.raman_shift) > 20:
                    f.write("...\n")
                
                # 后10个数据点
                if len(spectrum.spectrum_data.raman_shift) > 10:
                    f.write("后10个数据点:\n")
                    start_idx = max(10, len(spectrum.spectrum_data.raman_shift) - 10)
                    for i in range(start_idx, len(spectrum.spectrum_data.raman_shift)):
                        raman = spectrum.spectrum_data.raman_shift[i]
                        intensity = spectrum.spectrum_data.intensity[i]
                        f.write(f"{raman:8.2f} | {intensity:10.2f}\n")
                
                f.write("\n")
        
        print(f"详细报告已保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"保存详细报告时出错: {e}")
        return None


def save_simple_parse_to_txt(metadata, raman_data, output_path: str):
    """
    将test_nod4.py解析的结果保存为简单的txt文件
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write("=" * 60 + "\n")
            f.write("NOD文件解析结果 - 简单报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"解析方法: test_nod4.py parse_nod_file函数\n")
            f.write("\n")
            
            # 写入元数据
            f.write("【文件元数据】\n")
            f.write("-" * 30 + "\n")
            for key, value in metadata.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")
            
            # 写入数据统计
            f.write("【数据统计】\n")
            f.write("-" * 30 + "\n")
            f.write(f"总数据点数: {len(raman_data)}\n")
            
            if raman_data:
                raman_shifts = [point[0] for point in raman_data]
                intensities = [point[1] for point in raman_data]
                
                f.write(f"拉曼位移范围: {min(raman_shifts):.2f} - {max(raman_shifts):.2f}\n")
                f.write(f"强度范围: {min(intensities):.2f} - {max(intensities):.2f}\n")
                f.write(f"平均强度: {sum(intensities)/len(intensities):.2f}\n")
            f.write("\n")
            
            # 写入光谱数据（前10个和后10个数据点）
            f.write("【光谱数据样本】\n")
            f.write("-" * 30 + "\n")
            f.write("格式: 拉曼位移(cm^-1) | 强度\n")
            f.write("\n")
            
            # 前10个数据点
            f.write("前10个数据点:\n")
            for i in range(min(10, len(raman_data))):
                raman, intensity = raman_data[i]
                f.write(f"{raman:8.2f} | {intensity:10.2f}\n")
            
            # 如果有更多数据点，显示中间省略号
            if len(raman_data) > 20:
                f.write("...\n")
            
            # 后10个数据点
            if len(raman_data) > 10:
                f.write("后10个数据点:\n")
                start_idx = max(10, len(raman_data) - 10)
                for i in range(start_idx, len(raman_data)):
                    raman, intensity = raman_data[i]
                    f.write(f"{raman:8.2f} | {intensity:10.2f}\n")
            
            f.write("\n")
        
        print(f"简单报告已保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"保存简单报告时出错: {e}")
        return None


def save_complete_data_to_txt(spectrum: Spectrum, raman_data, output_path: str):
    """
    将完整的光谱数据保存为txt文件
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write("=" * 60 + "\n")
            f.write("NOD文件完整数据\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"解析方法: test_nod2.py + test_nod4.py 整合\n")
            f.write("\n")
            
            # 使用get_formatted_metadata获取完整的元数据信息
            reader = NodFileReader()
            formatted_metadata = reader.get_formatted_metadata(spectrum)
            
            # 写入完整的NOD文件信息
            f.write("📋 完整NOD文件信息:\n")
            f.write("-" * 50 + "\n")
            f.write(f"📝 样品名称: {formatted_metadata['样品名称']}\n")
            f.write(f"👤 操作员: {formatted_metadata['操作员']}\n")
            f.write(f"📅 创建时间: {formatted_metadata['创建时间']}\n")
            f.write(f"📄 文件版本: {formatted_metadata['文件版本']}\n")
            f.write(f"🔢 像素数: {formatted_metadata['像素数']}\n")
            f.write(f"⏱️ 积分时间: {formatted_metadata['积分时间']}\n")
            f.write(f"💡 激光功率: {formatted_metadata['激光功率']}\n")
            f.write(f"🔄 扫描次数: {formatted_metadata['扫描次数']}\n")
            f.write(f"🎯 采集模式: {formatted_metadata['采集模式']}\n")
            f.write(f"📏 采样间隔: {formatted_metadata['采样间隔']}\n")
            f.write(f"🔧 设备型号: {formatted_metadata['设备型号']}\n")
            f.write(f"🏷️ 设备序列号: {formatted_metadata['设备序列号']}\n")
            f.write("\n")
            
            # 写入完整的光谱数据
            f.write("【完整光谱数据】\n")
            f.write("-" * 30 + "\n")
            f.write("拉曼位移(cm^-1) | 强度\n")
            f.write("-" * 30 + "\n")
            
            # 使用test_nod4.py解析的数据（通常更完整）
            for raman, intensity in raman_data:
                f.write(f"{raman:8.2f} | {intensity:10.2f}\n")
            
            f.write("\n")
            
            # 如果有test_nod2.py的额外数据，也写入
            if (spectrum.spectrum_data and 
                spectrum.spectrum_data.raman_shift is not None and 
                spectrum.spectrum_data.intensity is not None):
                
                f.write("【test_nod2.py解析的额外数据】\n")
                f.write("-" * 30 + "\n")
                f.write("拉曼位移(cm^-1) | 强度\n")
                f.write("-" * 30 + "\n")
                
                for i in range(len(spectrum.spectrum_data.raman_shift)):
                    raman = spectrum.spectrum_data.raman_shift[i]
                    intensity = spectrum.spectrum_data.intensity[i]
                    f.write(f"{raman:8.2f} | {intensity:10.2f}\n")
        
        print(f"完整数据已保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"保存完整数据时出错: {e}")
        return None


def main():
    """主函数"""
    # 设置输入文件路径
    nod_file_path = r"nod\A01-莫西沙星标准品1mgmL+SN-P1-_2856_20250701-1.nod"
    
    # 检查文件是否存在
    if not os.path.exists(nod_file_path):
        print(f"错误: 文件不存在 - {nod_file_path}")
        return
    
    # 创建输出目录
    output_dir = "exported_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print("=" * 60)
    print("NOD文件解析器 - 整合版本")
    print("=" * 60)
    print(f"输入文件: {nod_file_path}")
    print(f"输出目录: {output_dir}")
    print()
    
    # 方法1: 使用test_nod2.py的NodFileReader类
    print("【方法1】使用test_nod2.py的NodFileReader类解析...")
    try:
        reader = NodFileReader()
        spectrum = reader.read_file(nod_file_path)
        
        # if spectrum:
        #     print("✓ test_nod2.py解析成功")
            
        #     # 保存详细报告
        #     base_name = os.path.splitext(os.path.basename(nod_file_path))[0]
        #     detailed_txt_path = os.path.join(output_dir, f"{base_name}_detailed_report.txt")
        #     save_spectrum_to_txt_detailed(spectrum, detailed_txt_path)
        # else:
        #     print("✗ test_nod2.py解析失败")
        #     spectrum = None
            
    except Exception as e:
        print(f"✗ test_nod2.py解析出错: {e}")
        spectrum = None
    
    #print()
    
    # 方法2: 使用test_nod4.py的parse_nod_file函数
    # print("【方法2】使用test_nod4.py的parse_nod_file函数解析...")
    try:
        metadata, raman_data = parse_nod_file(nod_file_path)
        
    #     if metadata.get("error"):
    #         print(f"✗ test_nod4.py解析失败: {metadata['error']}")
    #         metadata, raman_data = {}, []
    #     else:
    #         print("✓ test_nod4.py解析成功")
    #         print(f"  元数据项数: {len(metadata)}")
    #         print(f"  数据点数: {len(raman_data)}")
            
    #         # 保存简单报告
    #         base_name = os.path.splitext(os.path.basename(nod_file_path))[0]
    #         simple_txt_path = os.path.join(output_dir, f"{base_name}_simple_report.txt")
    #         save_simple_parse_to_txt(metadata, raman_data, simple_txt_path)
            
    except Exception as e:
        print(f"✗ test_nod4.py解析出错: {e}")
        metadata, raman_data = {}, []
    
    print()
    
    # 方法3: 整合两种方法的结果
    print("【方法3】整合两种方法的结果...")
    if spectrum and raman_data:
        try:
            # 保存完整数据
            base_name = os.path.splitext(os.path.basename(nod_file_path))[0]
            complete_txt_path = os.path.join(output_dir, f"{base_name}_complete_data.txt")
            save_complete_data_to_txt(spectrum, raman_data, complete_txt_path)
            print("✓ 整合数据保存成功")
        except Exception as e:
            print(f"✗ 整合数据保存失败: {e}")
    else:
        print("✗ 无法整合数据（两种方法都未成功解析）")
    
    print()
    print("=" * 60)
    print("解析完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
