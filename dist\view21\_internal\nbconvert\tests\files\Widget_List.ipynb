{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Widget List"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import ipywidgets as widgets"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Numeric widgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are many widgets distributed with ipywidgets that are designed to display numeric values.  Widgets exist for displaying integers and floats, both bounded and unbounded.  The integer widgets share a similar naming scheme to their floating point counterparts.  By replacing `Float` with `Int` in the widget name, you can find the Integer equivalent."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### IntSlider"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "900a279374724477aa598b64f8b23103", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=7, continuous_update=False, description='Test:', max=10)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.IntSlider(\n", "    value=7,\n", "    min=0,\n", "    max=10,\n", "    step=1,\n", "    description='Test:',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='horizontal',\n", "    readout=True,\n", "    readout_format='d'\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### FloatSlider"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "41aa649b6f92435789ad05d6694d3979", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatSlider(value=7.5, continuous_update=False, description='Test:', max=10.0, readout_format='.1f')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FloatSlider(\n", "    value=7.5,\n", "    min=0,\n", "    max=10.0,\n", "    step=0.1,\n", "    description='Test:',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='horizontal',\n", "    readout=True,\n", "    readout_format='.1f',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sliders can also be **displayed vertically**."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8090e9058047406c9192f1f87605f75e", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatSlider(value=7.5, continuous_update=False, description='Test:', max=10.0, orientation='vertical', readout…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FloatSlider(\n", "    value=7.5,\n", "    min=0,\n", "    max=10.0,\n", "    step=0.1,\n", "    description='Test:',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='vertical',\n", "    readout=True,\n", "    readout_format='.1f',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FloatLogSlider"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `FloatLogSlider` has a log scale, which makes it easy to have a slider that covers a wide range of positive magnitudes. The `min` and `max` refer to the minimum and maximum exponents of the `base`, and the `value` refers to the actual value of the slider."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e7307a79e0b940fe93b22363a7acbae1", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatLogSlider(value=10.0, description='Log Slider', max=10.0, min=-10.0, step=0.2)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FloatLogSlider(\n", "    value=10,\n", "    base=10,\n", "    min=-10, # max exponent of base\n", "    max=10, # min exponent of base\n", "    step=0.2, # exponent step\n", "    description='Log Slider'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### IntRangeSlider"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c6fd73ecfd494cffb6e40003dbbaa2c2", "version_major": 2, "version_minor": 0}, "text/plain": ["IntRangeSlider(value=(5, 7), continuous_update=False, description='Test:', max=10)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.IntRangeSlider(\n", "    value=[5, 7],\n", "    min=0,\n", "    max=10,\n", "    step=1,\n", "    description='Test:',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='horizontal',\n", "    readout=True,\n", "    readout_format='d',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FloatRangeSlider"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "96217540c98840768a45b109f8909b27", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatRangeSlider(value=(5.0, 7.5), continuous_update=False, description='Test:', max=10.0, readout_format='.1f…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FloatRangeSlider(\n", "    value=[5, 7.5],\n", "    min=0,\n", "    max=10.0,\n", "    step=0.1,\n", "    description='Test:',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='horizontal',\n", "    readout=True,\n", "    readout_format='.1f',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### IntProgress"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "847242b521014f4898f9037b01b78b98", "version_major": 2, "version_minor": 0}, "text/plain": ["IntProgress(value=7, description='Loading:', max=10)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.IntProgress(\n", "    value=7,\n", "    min=0,\n", "    max=10,\n", "    step=1,\n", "    description='Loading:',\n", "    bar_style='', # 'success', 'info', 'warning', 'danger' or ''\n", "    orientation='horizontal'\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### FloatProgress"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03dd979c243747bda108752309931db9", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatProgress(value=7.5, bar_style='info', description='Loading:', max=10.0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FloatProgress(\n", "    value=7.5,\n", "    min=0,\n", "    max=10.0,\n", "    step=0.1,\n", "    description='Loading:',\n", "    bar_style='info',\n", "    orientation='horizontal'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The numerical text boxes that impose some limit on the data (range, integer-only) impose that restriction when the user presses enter.\n", "\n", "### BoundedIntText"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c692115e21264cbea36e9a71738dfead", "version_major": 2, "version_minor": 0}, "text/plain": ["BoundedIntText(value=7, description='Text:', max=10)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.BoundedIntText(\n", "    value=7,\n", "    min=0,\n", "    max=10,\n", "    step=1,\n", "    description='Text:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### BoundedFloatText"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dcf141916aef43c09cc1178957387339", "version_major": 2, "version_minor": 0}, "text/plain": ["BoundedFloatText(value=7.5, description='Text:', max=10.0, step=0.1)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.BoundedFloatText(\n", "    value=7.5,\n", "    min=0,\n", "    max=10.0,\n", "    step=0.1,\n", "    description='Text:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### IntText"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "609fc788fb2041ddbdbea60833f7badc", "version_major": 2, "version_minor": 0}, "text/plain": ["IntText(value=7, description='Any:')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.IntText(\n", "    value=7,\n", "    description='Any:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### FloatText"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5669796f20942638b17f63d89f5a666", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatText(value=7.5, description='Any:')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FloatText(\n", "    value=7.5,\n", "    description='Any:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Boolean widgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are three widgets that are designed to display a boolean value."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ToggleButton"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98768dcd13004995a26343792098f450", "version_major": 2, "version_minor": 0}, "text/plain": ["ToggleButton(value=False, description='Click me', icon='check', tooltip='Description')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.ToggleButton(\n", "    value=False,\n", "    description='Click me',\n", "    disabled=False,\n", "    button_style='', # 'success', 'info', 'warning', 'danger' or ''\n", "    tooltip='Description',\n", "    icon='check'\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Checkbox"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "035fe1c814fd4531acdced08bd020b9d", "version_major": 2, "version_minor": 0}, "text/plain": ["Checkbox(value=False, description='Check me')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Checkbox(\n", "    value=False,\n", "    description='Check me',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Valid\n", "\n", "The valid widget provides a read-only indicator."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "231497b8422a46a6953ec8d68367c3df", "version_major": 2, "version_minor": 0}, "text/plain": ["Valid(value=False, description='Valid!')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Valid(\n", "    value=False,\n", "    description='Valid!',\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Selection widgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are several widgets that can be used to display single selection lists, and two that can be used to select multiple values.  All inherit from the same base class.  You can specify the **enumeration of selectable options by passing a list** (options are either (label, value) pairs, or simply values for which the labels are derived by calling `str`)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Dropdown"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5930139c8f4b45ad812a21f336a511ae", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Number:', index=1, options=('1', '2', '3'), value='2')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Dropdown(\n", "    options=['1', '2', '3'],\n", "    value='2',\n", "    description='Number:',\n", "    disabled=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following is also valid, displaying the words `'One', 'Two', 'Three'` as the dropdown choices but returning the values `1, 2, 3`."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9f8ad824c2a34110a10af40c8e08daf9", "version_major": 2, "version_minor": 0}, "text/plain": ["Dropdown(description='Number:', index=1, options=(('One', 1), ('Two', 2), ('Three', 3)), value=2)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Dropdown(\n", "    options=[('One', 1), ('Two', 2), ('Three', 3)],\n", "    value=2,\n", "    description='Number:',\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### RadioButtons"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "823c6dd9efad4992aeb019b94f63d15b", "version_major": 2, "version_minor": 0}, "text/plain": ["RadioButtons(description='Pizza topping:', options=('pepperoni', 'pineapple', 'anchovies'), value='pepperoni')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.RadioButtons(\n", "    options=['pepperoni', 'pineapple', 'anchovies'],\n", "#     value='pineapple',\n", "    description='Pizza topping:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Select"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6df3a3fe02bb4149a2bec37be4a3666e", "version_major": 2, "version_minor": 0}, "text/plain": ["Select(description='OS:', index=2, options=('Linux', 'Windows', 'OSX'), value='OSX')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Select(\n", "    options=['Linux', 'Windows', 'OSX'],\n", "    value='OSX',\n", "    # rows=10,\n", "    description='OS:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SelectionSlider"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "acff2c29635946478ac8cee9d4d4ecf3", "version_major": 2, "version_minor": 0}, "text/plain": ["SelectionSlider(continuous_update=False, description='I like my eggs ...', index=1, options=('scrambled', 'sun…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.SelectionSlider(\n", "    options=['scrambled', 'sunny side up', 'poached', 'over easy'],\n", "    value='sunny side up',\n", "    description='I like my eggs ...',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='horizontal',\n", "    readout=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SelectionRangeSlider\n", "\n", "The value, index, and label keys are 2-tuples of the min and max values selected. The options must be nonempty."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "71842466a7c3448881c37c5f88460b15", "version_major": 2, "version_minor": 0}, "text/plain": ["SelectionRangeSlider(description='Months (2015)', index=(0, 11), options=(('Jan', datetime.date(2015, 1, 1)), …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import datetime\n", "dates = [datetime.date(2015,i,1) for i in range(1,13)]\n", "options = [(i.strftime('%b'), i) for i in dates]\n", "widgets.SelectionRangeSlider(\n", "    options=options,\n", "    index=(0,11),\n", "    description='Months (2015)',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### ToggleButtons"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bcfce0d42db14c708259607736602179", "version_major": 2, "version_minor": 0}, "text/plain": ["ToggleButtons(description='Speed:', options=('Slow', 'Regular', 'Fast'), tooltips=('Description of slow', 'Des…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.ToggleButtons(\n", "    options=['Slow', 'Regular', 'Fast'],\n", "    description='Speed:',\n", "    disabled=False,\n", "    button_style='', # 'success', 'info', 'warning', 'danger' or ''\n", "    tooltips=['Description of slow', 'Description of regular', 'Description of fast'],\n", "#     icons=['check'] * 3\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SelectMultiple\n", "Multiple values can be selected with <kbd>shift</kbd> and/or <kbd>ctrl</kbd> (or <kbd>command</kbd>) pressed and mouse clicks or arrow keys."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a8661e762bf14bf3a28d9a5255337543", "version_major": 2, "version_minor": 0}, "text/plain": ["SelectMultiple(description='Fruits', index=(1,), options=('Apples', 'Oranges', 'Pears'), value=('Oranges',))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.SelectMultiple(\n", "    options=['Apples', 'Oranges', 'Pears'],\n", "    value=['Oranges'],\n", "    #rows=10,\n", "    description='Fruits',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## String widgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are several widgets that can be used to display a string value.  The `Text` and `Textarea` widgets accept input.  The `HTML` and `HTMLMath` widgets display a string as HTML (`HTMLMath` also renders math). The `Label` widget can be used to construct a custom control label."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Text"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ebd0457751e441e4a18c0b19c5b3ae44", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='Hello World', description='String:', placeholder='Type something')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Text(\n", "    value='Hello World',\n", "    placeholder='Type something',\n", "    description='String:',\n", "    disabled=False   \n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Textarea"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7193b362ed0f4200a01d8c71095fe25d", "version_major": 2, "version_minor": 0}, "text/plain": ["Textarea(value='Hello World', description='String:', placeholder='Type something')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Textarea(\n", "    value='Hello World',\n", "    placeholder='Type something',\n", "    description='String:',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Label\n", "\n", "The `Label` widget is useful if you need to build a custom description next to a control using similar styling to the built-in control descriptions."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4fd3732a16a144caa2b18720ef1f08d9", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(Label(value='The $m$ in $E=mc^2$:'), FloatSlider(value=0.0)))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.HBox([widgets.Label(value=\"The $m$ in $E=mc^2$:\"), widgets.FloatSlider()])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### HTML"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8717ac2d097441e9bd4978443f6da52f", "version_major": 2, "version_minor": 0}, "text/plain": ["HTML(value='Hello <b>World</b>', description='Some HTML', placeholder='Some HTML')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.HTML(\n", "    value=\"Hello <b>World</b>\",\n", "    placeholder='Some HTML',\n", "    description='Some HTML',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### HTML Math"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "52a07264d52e4b0fa37bc6b341555e12", "version_major": 2, "version_minor": 0}, "text/plain": ["HTMLMath(value='Some math and <i>HTML</i>: \\\\(x^2\\\\) and $$\\\\frac{x+1}{x-1}$$', description='Some HTML', place…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.HTMLMath(\n", "    value=r\"Some math and <i>HTML</i>: \\(x^2\\) and $$\\frac{x+1}{x-1}$$\",\n", "    placeholder='Some HTML',\n", "    description='Some HTML',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cade29a9b6f34fadbf36883d78ade0cb", "version_major": 2, "version_minor": 0}, "text/plain": ["Image(value=b'\\x89PNG\\r\\n\\x1a\\n\\x00\\x00\\x00\\rIHDR\\x00\\x00\\x00d\\x00\\x00\\x00G\\x08\\x02\\x00\\x00\\x00~\\xb8u\\x9b\\x00\\…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["file = open(\"testimage.png\", \"rb\")\n", "image = file.read()\n", "widgets.Image(\n", "    value=image,\n", "    format='png',\n", "    width=300,\n", "    height=400,\n", ")"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "929f2abd63624e94ac29a8a7820ec245", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(description='Click me', icon='check', style=ButtonStyle(), tooltip='Click me')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Button(\n", "    description='Click me',\n", "    disabled=False,\n", "    button_style='', # 'success', 'info', 'warning', 'danger' or ''\n", "    tooltip='Click me',\n", "    icon='check'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output\n", "\n", "The `Output` widget can capture and display stdout, stderr and [rich output generated by IPython](http://ipython.readthedocs.io/en/stable/api/generated/IPython.display.html#module-IPython.display). For detailed documentation, see the [output widget examples](https://ipywidgets.readthedocs.io/en/latest/examples/Output Widget.html)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Play (Animation) widget"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `Play` widget is useful to perform animations by iterating on a sequence of integers with a certain speed. The value of the slider below is linked to the player."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3f71a71a7af341a2a69f423d4cbfa008", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(Play(value=50, description='Press play'), IntSlider(value=0)))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["play = widgets.Play(\n", "#     interval=10,\n", "    value=50,\n", "    min=0,\n", "    max=100,\n", "    step=1,\n", "    description=\"Press play\",\n", "    disabled=False\n", ")\n", "slider = widgets.IntSlider()\n", "widgets.jslink((play, 'value'), (slider, 'value'))\n", "widgets.HBox([play, slider])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Date picker\n", "\n", "The date picker widget works in Chrome, Firefox and IE Edge, but does not currently work in Safari because it does not support the HTML date input field."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ce0d60101a5a4762ac52b60159817270", "version_major": 2, "version_minor": 0}, "text/plain": ["DatePicker(value=None, description='Pick a Date')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.DatePicker(\n", "    description='Pick a Date',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Color picker"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7cfdb695c61941959b405c0bd1dd0e28", "version_major": 2, "version_minor": 0}, "text/plain": ["ColorPicker(value='blue', description='Pick a color')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.ColorPicker(\n", "    concise=False,\n", "    description='Pick a color',\n", "    value='blue',\n", "    disabled=False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Upload\n", "\n", "The `FileUpload` allows to upload any type of file(s) as bytes."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "390a8d90df58477facd911357ef828fa", "version_major": 2, "version_minor": 0}, "text/plain": ["FileUpload(value={}, description='Upload')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.FileUpload(\n", "    accept='',  # Accepted file extension e.g. '.txt', '.pdf', 'image/*', 'image/*,.pdf'\n", "    multiple=False  # True to accept multiple files upload else False\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Controller\n", "\n", "The `Controller` allows a game controller to be used as an input device."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dd431edda13e42b384c3fdf0da876c8e", "version_major": 2, "version_minor": 0}, "text/plain": ["Controller()"]}, "metadata": {}, "output_type": "display_data"}], "source": ["widgets.Controller(\n", "    index=0,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Container/Layout widgets\n", "\n", "These widgets are used to hold other widgets, called children. Each has a `children` property that may be set either when the widget is created or later."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Box"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e6414e51a6b84b77bbca07174042f407", "version_major": 2, "version_minor": 0}, "text/plain": ["Box(children=(Label(value='0'), Label(value='1'), Label(value='2'), Label(value='3')))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["items = [widgets.Label(str(i)) for i in range(4)]\n", "widgets.Box(items)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### HBox"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cc87bd8da3484961bb490f6dd9a916f4", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(Label(value='0'), Label(value='1'), Label(value='2'), Label(value='3')))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["items = [widgets.Label(str(i)) for i in range(4)]\n", "widgets.HBox(items)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### VBox"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03bb2698030e48c590b7917360bb3725", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(VBox(children=(Label(value='0'), Label(value='1'))), VBox(children=(Label(value='2'), Label(val…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["items = [widgets.Label(str(i)) for i in range(4)]\n", "left_box = widgets.VBox([items[0], items[1]])\n", "right_box = widgets.VBox([items[2], items[3]])\n", "widgets.HBox([left_box, right_box])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Accordion"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ff697b7cefa34a4bba21a8021995bae2", "version_major": 2, "version_minor": 0}, "text/plain": ["Accordion(children=(IntSlider(value=0), Text(value='')), _titles={'0': 'Slider', '1': 'Text'})"]}, "metadata": {}, "output_type": "display_data"}], "source": ["accordion = widgets.Accordion(children=[widgets.IntSlider(), widgets.Text()])\n", "accordion.set_title(0, 'Slider')\n", "accordion.set_title(1, 'Text')\n", "accordion"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tabs\n", "\n", "In this example the children are set after the tab is created. Titles for the tabs are set in the same way they are for `Accordion`."]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "592cd261e1e14f70b59e056dda9f9f20", "version_major": 2, "version_minor": 0}, "text/plain": ["Tab(children=(Text(value='', description='P0'), Text(value='', description='P1'), Text(value='', description='…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tab_contents = ['P0', 'P1', 'P2', 'P3', 'P4']\n", "children = [widgets.Text(description=name) for name in tab_contents]\n", "tab = widgets.Tab()\n", "tab.children = children\n", "for i in range(len(children)):\n", "    tab.set_title(i, str(i))\n", "tab"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Accordion and Tab use `selected_index`, not value\n", "\n", "Unlike the rest of the widgets discussed earlier, the container widgets `Accordion` and `Tab` update their `selected_index` attribute when the user changes which accordion or tab is selected. That means that you can both see what the user is doing *and* programmatically set what the user sees by setting the value of `selected_index`.\n", "\n", "Setting `selected_index = None` closes all of the accordions or deselects all tabs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the cells below try displaying or setting the `selected_index` of the `tab` and/or `accordion`."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["tab.selected_index = 3"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["accordion.selected_index = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Nesting tabs and accordions\n", "\n", "Tabs and accordions can be nested as deeply as you want. If you have a few minutes, try nesting a few accordions or putting an accordion inside a tab or a tab inside an accordion. \n", "\n", "The example below makes a couple of tabs with an accordion children in one of them"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "20fa8782d12a4ff6b6a0e2475be79593", "version_major": 2, "version_minor": 0}, "text/plain": ["Tab(children=(Accordion(children=(IntSlider(value=0), Text(value='')), selected_index=None, _titles={'0': 'Sli…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tab_nest = widgets.Tab()\n", "tab_nest.children = [accordion, accordion]\n", "tab_nest.set_title(0, 'An accordion')\n", "tab_nest.set_title(1, 'Copy of the accordion')\n", "tab_nest"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"00e918754ce3473987c4ac8b68d2aa64": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "035fe1c814fd4531acdced08bd020b9d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "CheckboxModel", "state": {"description": "Check me", "disabled": false, "layout": "IPY_MODEL_f1d4f927cfe04ac5b8784495a911569a", "style": "IPY_MODEL_68cade49753a451ebff709ea1bce25b2", "value": false}}, "038eb3f451d74fa485a78b19d7ff6c98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "03bb2698030e48c590b7917360bb3725": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_19136da8d2ff44f4952e094ee80b2b7a", "IPY_MODEL_a326834ae4984777bd48e84b0783e086"], "layout": "IPY_MODEL_586c578b082049069483c78be61401b8"}}, "03dd979c243747bda108752309931db9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"bar_style": "info", "description": "Loading:", "layout": "IPY_MODEL_d74712fbafbb462aacfa22022d962aaa", "max": 10, "style": "IPY_MODEL_e85adb3440784b0fa3fb45747a792635", "value": 7.5}}, "04a6c32fdfaf4e5186b95d48d912409b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "04dcbf013c294e69a7f6f09eb643caa4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "0863b101fbd741d1a487843907da3b70": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "09bb3f943ad441a4b775f30e64098d16": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "0bd3f78432cd4db9acab71fe81d79918": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "0c19bce9461b4019a752a077a071d693": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "10ca989ef6eb4c459266c44999d0066b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "134a3c21e8084b3b8fdce65629d4fcfc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "154a6e22eb05444ab5cc3664454cd7ca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "19136da8d2ff44f4952e094ee80b2b7a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "VBoxModel", "state": {"children": ["IPY_MODEL_dfb871f032ce46398953670eede31aa8", "IPY_MODEL_795e4e83becb46cb9e2ce6568b3e52ab"], "layout": "IPY_MODEL_ef64bc2bcae54900a7b6c58618dfd4ae"}}, "1d08477048c641978807b98886b5b7fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "1dcd36317f414f6d979a0a80c0658a20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"description": "P4", "layout": "IPY_MODEL_d23acd51730f4c51b71f610e7958e7a8", "style": "IPY_MODEL_0863b101fbd741d1a487843907da3b70"}}, "1e325582b42744cea09cbef4861791d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "20fa8782d12a4ff6b6a0e2475be79593": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TabModel", "state": {"_titles": {"0": "An accordion", "1": "<PERSON><PERSON> of the accordion"}, "children": ["IPY_MODEL_ff697b7cefa34a4bba21a8021995bae2", "IPY_MODEL_ff697b7cefa34a4bba21a8021995bae2"], "layout": "IPY_MODEL_d6afc68827cc4354bbff5a857e754f5c"}}, "231497b8422a46a6953ec8d68367c3df": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ValidModel", "state": {"description": "Valid!", "layout": "IPY_MODEL_b018e1cfbaaf4feda8e277c5b27cc4ad", "style": "IPY_MODEL_350651b22bda442e89b2c31c0d1af181"}}, "23e838c1a76d4f0aad12bdf2b8225a12": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "260c10d89a5943988afeefed0bbfa0df": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "2a0afd8e637c4f809c1da6e1449f56bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "2eb456965b8e44399ea612590a08e244": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "2f06cb6df4ef433eb0da36bc7f592af5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_eca4345e6d0d4a1abcafacce461803d7", "style": "IPY_MODEL_cb7bcde2dd664b21b3e5af5b1c27056e", "value": "2"}}, "323e892406cd4c428e9e0d3a50e934bc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "350651b22bda442e89b2c31c0d1af181": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "3588b17584d44b6f8bc0c2f6bb4d1995": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "38dedb3a22f7455dbc369f897ffd9c11": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_598af5eb6dc4473cbfbc8ecd10ad9538", "style": "IPY_MODEL_ba3d23bdea80492c9fe170cc7d835660", "value": "2"}}, "38e92957ff814f648355507fba1efdc2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "390a8d90df58477facd911357ef828fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FileUploadModel", "state": {"description_tooltip": null, "layout": "IPY_MODEL_23e838c1a76d4f0aad12bdf2b8225a12", "style": "IPY_MODEL_c067bbcce1984f77a3f84947cfad1a46"}}, "396218b29fae4e49aab649f23ba9271d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_09bb3f943ad441a4b775f30e64098d16", "style": "IPY_MODEL_8c203e0890c84b1e9548f48d9c341760", "value": "1"}}, "3afb971b70db448f8533ccbcd79add82": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"layout": "IPY_MODEL_e756063607f74ab9b545082e7b57b1f7", "style": "IPY_MODEL_7b7607f095fc4840842fee5b9eea53e5"}}, "3b5dc9fd4be74759a0fdbc33624292a7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "3f4d3d533ac1485d9261d15466ceb172": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "3f71a71a7af341a2a69f423d4cbfa008": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_48b16c2cb388479c95098c6f7ab13ed0", "IPY_MODEL_df4231d6e4284db2b84ffc2e28997aa3"], "layout": "IPY_MODEL_00e918754ce3473987c4ac8b68d2aa64"}}, "41078ec3634f4a8db9d2e97ee24cd40b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_3b5dc9fd4be74759a0fdbc33624292a7", "style": "IPY_MODEL_5b5bee1cf34d4c2c9745f6c1eb265dd9", "value": "3"}}, "41aa649b6f92435789ad05d6694d3979": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatSliderModel", "state": {"continuous_update": false, "description": "Test:", "layout": "IPY_MODEL_f970119cba234e2e89dbe8f00a276b2b", "max": 10, "readout_format": ".1f", "step": 0.1, "style": "IPY_MODEL_1d08477048c641978807b98886b5b7fe", "value": 7.5}}, "451615e6cc664f1ca018e19c1ef76dc2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "48b16c2cb388479c95098c6f7ab13ed0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "PlayModel", "state": {"description": "Press play", "layout": "IPY_MODEL_5dbb57473ce442be95ed0a07b5e89f56", "style": "IPY_MODEL_de13d0355c6d4a2b9316672bc6934801", "value": 50}}, "494158494996463db0a7d963c5271a27": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "49d605fd694c46d6a0ac936d6becbec0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "49f0fdfe199c4ac4b776b8d8ca75c039": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "4b932535cf844e6ab601ab75f888c09f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "4dae83cdf2c14744a15b7afe4afbdf18": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatSliderModel", "state": {"layout": "IPY_MODEL_b88e771825244b5f90859aad40ae4f24", "step": 0.1, "style": "IPY_MODEL_88f65ff1addb428290726c11935e6ad3"}}, "4fd3732a16a144caa2b18720ef1f08d9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_a1e428857ed543d0bcd12419a140b9a6", "IPY_MODEL_4dae83cdf2c14744a15b7afe4afbdf18"], "layout": "IPY_MODEL_2eb456965b8e44399ea612590a08e244"}}, "52a07264d52e4b0fa37bc6b341555e12": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLMathModel", "state": {"description": "Some HTML", "layout": "IPY_MODEL_6b2683b0184146fe9bad3e56c2339ab3", "placeholder": "Some HTML", "style": "IPY_MODEL_2a0afd8e637c4f809c1da6e1449f56bf", "value": "Some math and <i>HTML</i>: \\(x^2\\) and $$\\frac{x+1}{x-1}$$"}}, "53e9cff815544fd2b7cf21f623ea3d5b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "54ef622958f44411a0e3dd9cc1b8512d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "580272bd24304a27901acca042999cb4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "586c578b082049069483c78be61401b8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "592cd261e1e14f70b59e056dda9f9f20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TabModel", "state": {"_titles": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4"}, "children": ["IPY_MODEL_5bc370f1b4cd4c15af7e9c2b99013fd8", "IPY_MODEL_9902d30b0c7e466082e946aad58713a7", "IPY_MODEL_c9aab996feee474396da46870ded182e", "IPY_MODEL_fbd6a8d0fcfe47ca9d613460ce04c7ac", "IPY_MODEL_1dcd36317f414f6d979a0a80c0658a20"], "layout": "IPY_MODEL_98f35b3048fb4ad4b97830813b8c56a9", "selected_index": 3}}, "5930139c8f4b45ad812a21f336a511ae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DropdownModel", "state": {"_options_labels": ["1", "2", "3"], "description": "Number:", "index": 1, "layout": "IPY_MODEL_8abc66a1d21a4143aba20affe2791893", "style": "IPY_MODEL_b640344b4ce9483f8c1eb1accec5b31b"}}, "598af5eb6dc4473cbfbc8ecd10ad9538": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "5b5bee1cf34d4c2c9745f6c1eb265dd9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "5b71ec56890c41ea92ab28931f29b356": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "5bc370f1b4cd4c15af7e9c2b99013fd8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"description": "P0", "layout": "IPY_MODEL_c83be8eed1b24f529cd630ba7849bff9", "style": "IPY_MODEL_e5c506e01fb949ebbbd882ecaa6cca11"}}, "5d6dfecbdce94630a5b976743886caae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_9a904164751944739c0e8354a2d31f2c", "style": "IPY_MODEL_638d66bac51d431283079fc13cf54471", "value": "0"}}, "5dbb57473ce442be95ed0a07b5e89f56": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "5eb2135b7b90441ca147dcdb423e11b3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "5eb2f6edffbd420a8c6e64671c9ffcf5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "609fc788fb2041ddbdbea60833f7badc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "IntTextModel", "state": {"description": "Any:", "layout": "IPY_MODEL_7d54d02236f04723b37ad56a860ab45a", "step": 1, "style": "IPY_MODEL_df99f53bcb5c4406807811a16321a6b2", "value": 7}}, "62dc35d98b124fbb860cc71433f1374d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "638d66bac51d431283079fc13cf54471": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "66108e24c03d4e0a8496d0f7b88b9c5e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "674c413e5b6d4535b65ff2b11a831c35": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "68cade49753a451ebff709ea1bce25b2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "69607ad701a740729934cc824ae60b82": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "6a25c30366e84c8c82f5aab0eaefa780": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "6b2683b0184146fe9bad3e56c2339ab3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "6df3a3fe02bb4149a2bec37be4a3666e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SelectModel", "state": {"_options_labels": ["Linux", "Windows", "OSX"], "description": "OS:", "index": 2, "layout": "IPY_MODEL_6a25c30366e84c8c82f5aab0eaefa780", "style": "IPY_MODEL_1e325582b42744cea09cbef4861791d1"}}, "6f10ef0d2d2242e7b04af8e0bd343724": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "71842466a7c3448881c37c5f88460b15": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SelectionRangeSliderModel", "state": {"_model_name": "SelectionRangeSliderModel", "_options_labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "_view_name": "SelectionRangeSliderView", "description": "Months (2015)", "index": [0, 11], "layout": "IPY_MODEL_580272bd24304a27901acca042999cb4", "style": "IPY_MODEL_9124f145f2cf4c7e8bee6214f80b4785"}}, "7193b362ed0f4200a01d8c71095fe25d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextareaModel", "state": {"description": "String:", "layout": "IPY_MODEL_62dc35d98b124fbb860cc71433f1374d", "placeholder": "Type something", "style": "IPY_MODEL_038eb3f451d74fa485a78b19d7ff6c98", "value": "Hello World"}}, "73b095f2a6f444a8b4e46ae881920624": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "787d731d70ad4a2b86d2ef88a1344ee8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_49f0fdfe199c4ac4b776b8d8ca75c039", "style": "IPY_MODEL_a9cdef23ee394ed0adcc647df833b53e", "value": "3"}}, "795e4e83becb46cb9e2ce6568b3e52ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_73b095f2a6f444a8b4e46ae881920624", "style": "IPY_MODEL_dde741b0a4064eaf8c22dc856f005050", "value": "1"}}, "79db6a6169a8408ebb5bb548ef4b244e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "7b2c8a53b2934f03b131ab2a4e94803d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "7b7607f095fc4840842fee5b9eea53e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "7cfdb695c61941959b405c0bd1dd0e28": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ColorPickerModel", "state": {"description": "Pick a color", "disabled": false, "layout": "IPY_MODEL_e6ff9a77a66d4d479e62aadcb457a502", "style": "IPY_MODEL_69607ad701a740729934cc824ae60b82", "value": "blue"}}, "7d54d02236f04723b37ad56a860ab45a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "7dcdfac1330d4037b37c55d940d351b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "7e05e4f80924449fa7ab690f4aa09d95": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "8090e9058047406c9192f1f87605f75e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatSliderModel", "state": {"continuous_update": false, "description": "Test:", "layout": "IPY_MODEL_91584febed5b40928a2c4a25ef2bc05b", "max": 10, "orientation": "vertical", "readout_format": ".1f", "step": 0.1, "style": "IPY_MODEL_54ef622958f44411a0e3dd9cc1b8512d", "value": 7.5}}, "8158b0907516446fa7b61b36f864af58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "819aa731a0ee464c94c65d8da1924c2d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "823c6dd9efad4992aeb019b94f63d15b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "RadioButtonsModel", "state": {"_options_labels": ["pepperoni", "pineapple", "anchovies"], "description": "Pizza topping:", "index": 0, "layout": "IPY_MODEL_f0c52cce43074ccc9df9b73dba0f403d", "style": "IPY_MODEL_aacf8ac27d0440e2bc2f5e1da35ec6a7"}}, "82542873f571491380be494d9b9721f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "83a3e44999d94577bf97972fa6b8fadb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "847242b521014f4898f9037b01b78b98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "IntProgressModel", "state": {"description": "Loading:", "layout": "IPY_MODEL_ca77f11b03e342789d260d79acfcddc7", "max": 10, "style": "IPY_MODEL_c5211ce5f43d4bde800dee7cecfb526a", "value": 7}}, "86125fedf2574c9b9c3316531abf8660": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "8717ac2d097441e9bd4978443f6da52f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"description": "Some HTML", "layout": "IPY_MODEL_38e92957ff814f648355507fba1efdc2", "placeholder": "Some HTML", "style": "IPY_MODEL_8158b0907516446fa7b61b36f864af58", "value": "Hello <b>World</b>"}}, "88f65ff1addb428290726c11935e6ad3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "8abc66a1d21a4143aba20affe2791893": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "8c203e0890c84b1e9548f48d9c341760": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "8d8c7442599d4b66a1bdc4d27cfa0b07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "900a279374724477aa598b64f8b23103": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "IntSliderModel", "state": {"continuous_update": false, "description": "Test:", "layout": "IPY_MODEL_c7bddd70abea43aabf018069af51ca53", "max": 10, "style": "IPY_MODEL_7e05e4f80924449fa7ab690f4aa09d95", "value": 7}}, "9124f145f2cf4c7e8bee6214f80b4785": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "91584febed5b40928a2c4a25ef2bc05b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "929f2abd63624e94ac29a8a7820ec245": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ButtonModel", "state": {"description": "Click me", "icon": "check", "layout": "IPY_MODEL_83a3e44999d94577bf97972fa6b8fadb", "style": "IPY_MODEL_9dcec744fdd544f08dcf299940f6abc6", "tooltip": "Click me"}}, "96217540c98840768a45b109f8909b27": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatRangeSliderModel", "state": {"_model_name": "FloatRangeSliderModel", "_view_name": "FloatRangeSliderView", "continuous_update": false, "description": "Test:", "layout": "IPY_MODEL_e6d54404c45445269ebe04c4c148597c", "max": 10, "readout_format": ".1f", "step": 0.1, "style": "IPY_MODEL_b34559b51e5a4f3bbb46771023b8b818", "value": [5, 7.5]}}, "9645a3abd2394befa4880849ba14c439": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "98768dcd13004995a26343792098f450": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ToggleButtonModel", "state": {"description": "Click me", "icon": "check", "layout": "IPY_MODEL_c127f121f54c4edc93483af8793ec782", "style": "IPY_MODEL_5eb2135b7b90441ca147dcdb423e11b3", "tooltip": "Description"}}, "98f35b3048fb4ad4b97830813b8c56a9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "9902d30b0c7e466082e946aad58713a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"description": "P1", "layout": "IPY_MODEL_efda474ceb1944b3abf51813c9dd6806", "style": "IPY_MODEL_0bd3f78432cd4db9acab71fe81d79918"}}, "99105fe0a12842178af5bf34886763a8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "9a904164751944739c0e8354a2d31f2c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "9dcec744fdd544f08dcf299940f6abc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ButtonStyleModel", "state": {}}, "9f8ad824c2a34110a10af40c8e08daf9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DropdownModel", "state": {"_options_labels": ["One", "Two", "Three"], "description": "Number:", "index": 1, "layout": "IPY_MODEL_cc60cf34ded446ff87a7156be8f8680e", "style": "IPY_MODEL_82542873f571491380be494d9b9721f7"}}, "a1e428857ed543d0bcd12419a140b9a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_10ca989ef6eb4c459266c44999d0066b", "style": "IPY_MODEL_3f4d3d533ac1485d9261d15466ceb172", "value": "The $m$ in $E=mc^2$:"}}, "a326834ae4984777bd48e84b0783e086": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "VBoxModel", "state": {"children": ["IPY_MODEL_38dedb3a22f7455dbc369f897ffd9c11", "IPY_MODEL_41078ec3634f4a8db9d2e97ee24cd40b"], "layout": "IPY_MODEL_04a6c32fdfaf4e5186b95d48d912409b"}}, "a4bba5e46daf4b09bde561ead851e8ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "a5669796f20942638b17f63d89f5a666": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatTextModel", "state": {"description": "Any:", "layout": "IPY_MODEL_e1945b3d92f748e7b2267d7e6707029f", "step": null, "style": "IPY_MODEL_86125fedf2574c9b9c3316531abf8660", "value": 7.5}}, "a8661e762bf14bf3a28d9a5255337543": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SelectMultipleModel", "state": {"_options_labels": ["Apples", "Oranges", "<PERSON><PERSON>s"], "description": "Fruits", "index": [1], "layout": "IPY_MODEL_451615e6cc664f1ca018e19c1ef76dc2", "rows": 5, "style": "IPY_MODEL_7dcdfac1330d4037b37c55d940d351b0"}}, "a8977af75efc4b5ea256e6135aa39099": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "a9cdef23ee394ed0adcc647df833b53e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "aa68c9873a734057b3ae3bb3150e5b1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "aacf8ac27d0440e2bc2f5e1da35ec6a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "acff2c29635946478ac8cee9d4d4ecf3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SelectionSliderModel", "state": {"_options_labels": ["scrambled", "sunny side up", "poached", "over easy"], "continuous_update": false, "description": "I like my eggs ...", "index": 1, "layout": "IPY_MODEL_79db6a6169a8408ebb5bb548ef4b244e", "style": "IPY_MODEL_3588b17584d44b6f8bc0c2f6bb4d1995"}}, "b018e1cfbaaf4feda8e277c5b27cc4ad": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "b34559b51e5a4f3bbb46771023b8b818": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "b3a24607a26d4a6db3c25ba9811e0329": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_e4b8cc72664b4af9957e48ac2caa71b7", "style": "IPY_MODEL_8d8c7442599d4b66a1bdc4d27cfa0b07", "value": "3"}}, "b44fdad619a340a8912851d84be35ba5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "b640344b4ce9483f8c1eb1accec5b31b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "b88e771825244b5f90859aad40ae4f24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "ba3d23bdea80492c9fe170cc7d835660": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "bcfce0d42db14c708259607736602179": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ToggleButtonsModel", "state": {"_options_labels": ["Slow", "Regular", "Fast"], "button_style": "", "description": "Speed:", "icons": [], "index": 0, "layout": "IPY_MODEL_7b2c8a53b2934f03b131ab2a4e94803d", "style": "IPY_MODEL_f3945c9d4cbf4c1ab3b5dbc6e0e15475", "tooltips": ["Description of slow", "Description of regular", "Description of fast"]}}, "c067bbcce1984f77a3f84947cfad1a46": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ButtonStyleModel", "state": {}}, "c127f121f54c4edc93483af8793ec782": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "c44c6deb729b4a4dbf951b9bbf9d0901": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "c5211ce5f43d4bde800dee7cecfb526a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "c692115e21264cbea36e9a71738dfead": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "BoundedIntTextModel", "state": {"description": "Text:", "layout": "IPY_MODEL_323e892406cd4c428e9e0d3a50e934bc", "max": 10, "style": "IPY_MODEL_04dcbf013c294e69a7f6f09eb643caa4", "value": 7}}, "c6936f7070ed48669eb5c416dc79ada8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "c6fd73ecfd494cffb6e40003dbbaa2c2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "IntRangeSliderModel", "state": {"_model_name": "IntRangeSliderModel", "_view_name": "IntRangeSliderView", "continuous_update": false, "description": "Test:", "layout": "IPY_MODEL_f2bad359e4ce420db4595c12674cce10", "max": 10, "style": "IPY_MODEL_819aa731a0ee464c94c65d8da1924c2d", "value": [5, 7]}}, "c7bddd70abea43aabf018069af51ca53": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "c83be8eed1b24f529cd630ba7849bff9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "c9aab996feee474396da46870ded182e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"description": "P2", "layout": "IPY_MODEL_4b932535cf844e6ab601ab75f888c09f", "style": "IPY_MODEL_134a3c21e8084b3b8fdce65629d4fcfc"}}, "ca77f11b03e342789d260d79acfcddc7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "cade29a9b6f34fadbf36883d78ade0cb": {"buffers": [{"data": "iVBORw0KGgoAAAANSUhEUgAAAGQAAABHCAIAAAB+uHWbAAAACXBIWXMAABJ0AAASdAHeZh94AAAHNUlEQVR4nO2cbVMaVxTH/6CggNIAxgxqGpLGzEQjmdHMdNKxnbaZaqbGMdYkjd8mHyWvMmOmbcjgQ9JYrXWMwVKCxEcI2ggyCqg8ycMucPuC1Ek1dtkFlkX9vXIO9+w5+/fevffs3l0RIQQnZIeYsUUymdzd3QWQSCQoiip8SsKFWSybzTYwMJBMJg0Gg8Fg4CEnwVLO2KKxsdFqtVIUlUqlRCIRAJvN5nK5Mn+zIplMEkIkEgmHROPxeGVlJZ+Om5upb75p0el0exZmsVZXVy9fvmy32/V6fcayvr7e0dFRVlbGNrzX66UoqqGhga0jAIvF0trayo8jReHpU7S0hNfWZtmJtafRh4jFYrGYeQjv49SpU+l0moMjgAsXLvDj+PffmJ7G7dugaZHV+p+fmMXKI9yGQwa1Wl1oR0Lw++8Qi9HfDwA0vb8Br2IJmUgEg4P44gt8+umhbU7EAoClJczP44cfIJX+X7PjLlY6jV9/hVqNvj7mxsdarO1tPH+OGzdQW5tV++Mr1uvXcLtx7x6yXwJxmYxLnWQSBgPKy9HdzUIpHMOetbGBsTF0deGTT1j7Hi+xXr5EJIL+frAv1YDjMwzjcfz8M06fRkcHR6WQjVivXr169OgRTdMDAwM2m41jnKKyugqjETdvorExp+Mwi3Xt2jW5XF5WVnblyhWr1QqAoiir1bq1tZVTZL4YG4PbjTt3oFCw8HK73Qd7BvM169mzZ4FAYHp6OhaL1dfXA5BKpXq9vrxc6Ne7SARGI778Ehxuc9TX11dXV8/Ozn5oZD7hW7duHTSKxWIO97P4ZHERCwu4cwec7p5BJBIdPMEjeIFPpzEygkgEfX0clToMoQ8ltuzsYGQE332H06fzf/AjJZbFgvV13L8PTncJmTkiw5Cm8fQpJBJ0dxdKKRyNnuXxYHISN29yqWBYUfJiTU0hGsXduwXsUHuUsFiJBAwGtLXh4kWeIpaqWCsrmJnB7dvI4RkIa0pPLEIwNgapFPfv8x26xMQKhzE0hPZ2LhVM7jCLZTabvV5vZ2enyWQSiUTXr1/nIa2PMj8Puz3/6/LsYZ5Crl69GovFCCGrq6srKysACCGJRCKVShU+vfek0xgeRiyG3l6elEomkwe3DDH3rOHhYY/HYzKZlEplxkLTtNPpbGhoUKlU+U/zAFtbeP4cHR2oqeEh2nt8Pp/L5dpnZBarp6dnn0UqlTY1NXHYGMKBv/7CxkYBK5jD0Gq1CoXC+t/NDsItd2gaT56gshJdXXwrdRgCnQ09HkxMoKsL/w59QSA4sQjBy5eIRt9vZREUwhIrFsPgIFpb8dlnxU7lYwhILKcTr1+jqwtyebFTOQShiPXiBeRy9PVxf6jHA8UXKxyG0Yivv0ZdXbFTYaLIYmUqmHv3IPjnakAR11mpFIaGEI+jt7c0lEKxelZRKpjcYRbr3bt3kUikubl5YmJCpVJ9dKc3K8xmbG4WoYLJHeZ81Wr18vIygFQqFY1GcwlGUTAYIJMJqIJhBXPKsVgsEonY7XaFQrG0tAQgkUhYLBa/388qktuNJ0/w7bdobuaYK5+4XK59VTQAECai0ajf79/d3Q2FQtFolBAyPDyceQsne/74g/z2G0mnWTkVmWAwODEx8aGF+Zolk8lkMhnnf1E8DoMBn3+O8+c5H0MoFHY2dDphNqO3FxUVBY3DE4USixCMjkIux48/FihCESiIWMEgRkbw1VclUMGwIv9izc3B6SzmM5jCkc/VTiqFwUFQFHp6jqBSyGPP8vnw4gU6O6HR5OuQgiM/Yv35J3y+kqxgWJHrydE0fvkFVVX4/vsjrhRy7FluNyYn0d2Nqqp85SNomDvD2tpa5iMYDocjHA5njIRgchLLy+jvZ6FUMpmkD755nB2xWIxnx4Mwi7W8vOx2uwOBwNu3b4eGhgBQFH76CQ0NuHGDXbDt7e2NjQ1uiS4sLPDseBDmYahUKlOpVCAQ2NtHr1QqaXpkYUHENo1EIpFOp7m9ALSzs8NNaM6ONE1funTpQ4uIMH24x+FwiMVihUIRCoW0Wm11dTWHwEeDsgcPHvx/C41Go1arq6qqNBrN3NyczWbT6XRTU1MrKyvns76T4PV6x8fHZTLZ4uKi3W4/d+5c9m+zPHz4UK/Xh0Kh0dFRsVic/XcaZmdn19fX6+rqHj9+LJFINFmvALe3t8fHx+PxeDgcnp6ePnPmTOaLFOxm+6WlpYqKis3NzbW1NY/Hk72jyWRqa2szm801NTXBYDASiWTvK5FICCHz8/NNTU0zMzPZO9bU1Ozs7AA4e/asw+HI3lGlUul0OkKIyWRqbm5+8+ZNxs5u6aDT6fx+v8fjqa2tZTWvtba2ZjYO+v1+xoG/j0Qi4XA45HK5zWZj9QQgGAxGo1GLxRIKhVi9luXz+YxGo1arbWlpsVqt7e3tGTvzNeuEPY76ojuvnIjFghOxWHAiFgtOxGLBP8cR6WT0GAbhAAAAAElFTkSuQmCC", "encoding": "base64", "path": ["value"]}], "model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ImageModel", "state": {"height": "400", "layout": "IPY_MODEL_d087879420fc41e3b806b06d069f1595", "width": "300"}}, "cb7bcde2dd664b21b3e5af5b1c27056e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "cc60cf34ded446ff87a7156be8f8680e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "cc87bd8da3484961bb490f6dd9a916f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_5d6dfecbdce94630a5b976743886caae", "IPY_MODEL_396218b29fae4e49aab649f23ba9271d", "IPY_MODEL_eb8a3a3920ab46d3a338b56d473dc067", "IPY_MODEL_787d731d70ad4a2b86d2ef88a1344ee8"], "layout": "IPY_MODEL_99105fe0a12842178af5bf34886763a8"}}, "ce0d60101a5a4762ac52b60159817270": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DatePickerModel", "state": {"description": "Pick a Date", "disabled": false, "layout": "IPY_MODEL_a8977af75efc4b5ea256e6135aa39099", "style": "IPY_MODEL_674c413e5b6d4535b65ff2b11a831c35"}}, "d087879420fc41e3b806b06d069f1595": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "d23acd51730f4c51b71f610e7958e7a8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "d248e931f171477786aceb10021afbe0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_aa68c9873a734057b3ae3bb3150e5b1d", "style": "IPY_MODEL_c6936f7070ed48669eb5c416dc79ada8", "value": "0"}}, "d34344e7870441d7b71d540d2575edcb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "SliderStyleModel", "state": {"description_width": ""}}, "d36d2a40226b4f20ba7f82743df3fd40": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_dbf67a9f4c6b414b8dcbe6f479ed332e", "style": "IPY_MODEL_66108e24c03d4e0a8496d0f7b88b9c5e", "value": "1"}}, "d6af3750810346b0bd395d2727c32418": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "d6afc68827cc4354bbff5a857e754f5c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "d74712fbafbb462aacfa22022d962aaa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "d8a5419b4d95458397d2bebb7be72320": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "IntSliderModel", "state": {"layout": "IPY_MODEL_d6af3750810346b0bd395d2727c32418", "style": "IPY_MODEL_9645a3abd2394befa4880849ba14c439"}}, "db1d02c5be164481a5814edd0408df7e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "dbf67a9f4c6b414b8dcbe6f479ed332e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "dcf141916aef43c09cc1178957387339": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "BoundedFloatTextModel", "state": {"description": "Text:", "layout": "IPY_MODEL_ffaf6b99f7fd4692b25883585f21ef2c", "max": 10, "style": "IPY_MODEL_b44fdad619a340a8912851d84be35ba5", "value": 7.5}}, "dd431edda13e42b384c3fdf0da876c8e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ControllerModel", "state": {"layout": "IPY_MODEL_53e9cff815544fd2b7cf21f623ea3d5b"}}, "dde741b0a4064eaf8c22dc856f005050": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "de13d0355c6d4a2b9316672bc6934801": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "df4231d6e4284db2b84ffc2e28997aa3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "IntSliderModel", "state": {"layout": "IPY_MODEL_260c10d89a5943988afeefed0bbfa0df", "style": "IPY_MODEL_d34344e7870441d7b71d540d2575edcb", "value": 50}}, "df99f53bcb5c4406807811a16321a6b2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "dfb871f032ce46398953670eede31aa8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_5b71ec56890c41ea92ab28931f29b356", "style": "IPY_MODEL_db1d02c5be164481a5814edd0408df7e", "value": "0"}}, "e1945b3d92f748e7b2267d7e6707029f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "e4b8cc72664b4af9957e48ac2caa71b7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "e5c506e01fb949ebbbd882ecaa6cca11": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "e6414e51a6b84b77bbca07174042f407": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "BoxModel", "state": {"children": ["IPY_MODEL_d248e931f171477786aceb10021afbe0", "IPY_MODEL_d36d2a40226b4f20ba7f82743df3fd40", "IPY_MODEL_2f06cb6df4ef433eb0da36bc7f592af5", "IPY_MODEL_b3a24607a26d4a6db3c25ba9811e0329"], "layout": "IPY_MODEL_c44c6deb729b4a4dbf951b9bbf9d0901"}}, "e6d54404c45445269ebe04c4c148597c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "e6ff9a77a66d4d479e62aadcb457a502": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "e7307a79e0b940fe93b22363a7acbae1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatLogSliderModel", "state": {"description": "Log Slider", "layout": "IPY_MODEL_5eb2f6edffbd420a8c6e64671c9ffcf5", "max": 10, "min": -10, "step": 0.2, "style": "IPY_MODEL_494158494996463db0a7d963c5271a27", "value": 10}}, "e756063607f74ab9b545082e7b57b1f7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "e85adb3440784b0fa3fb45747a792635": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "eb8a3a3920ab46d3a338b56d473dc067": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"layout": "IPY_MODEL_0c19bce9461b4019a752a077a071d693", "style": "IPY_MODEL_fd35b0fdb7734293a968f7d046ef1600", "value": "2"}}, "ebd0457751e441e4a18c0b19c5b3ae44": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"description": "String:", "layout": "IPY_MODEL_6f10ef0d2d2242e7b04af8e0bd343724", "placeholder": "Type something", "style": "IPY_MODEL_a4bba5e46daf4b09bde561ead851e8ef", "value": "Hello World"}}, "eca4345e6d0d4a1abcafacce461803d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "eddc968a6e184d599b89553aa7bc7ef9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LinkModel", "state": {"source": ["IPY_MODEL_48b16c2cb388479c95098c6f7ab13ed0", "value"], "target": ["IPY_MODEL_df4231d6e4284db2b84ffc2e28997aa3", "value"]}}, "ef64bc2bcae54900a7b6c58618dfd4ae": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "efda474ceb1944b3abf51813c9dd6806": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "f0c52cce43074ccc9df9b73dba0f403d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "f1d4f927cfe04ac5b8784495a911569a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "f2bad359e4ce420db4595c12674cce10": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "f349f3d268a64741a954b9ef9edb4c60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "f3945c9d4cbf4c1ab3b5dbc6e0e15475": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ToggleButtonsStyleModel", "state": {"button_width": "", "description_width": ""}}, "f970119cba234e2e89dbe8f00a276b2b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}, "fbd6a8d0fcfe47ca9d613460ce04c7ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "TextModel", "state": {"description": "P3", "layout": "IPY_MODEL_154a6e22eb05444ab5cc3664454cd7ca", "style": "IPY_MODEL_f349f3d268a64741a954b9ef9edb4c60"}}, "fd35b0fdb7734293a968f7d046ef1600": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"description_width": ""}}, "ff697b7cefa34a4bba21a8021995bae2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "AccordionModel", "state": {"_titles": {"0": "Slide<PERSON>", "1": "Text"}, "children": ["IPY_MODEL_d8a5419b4d95458397d2bebb7be72320", "IPY_MODEL_3afb971b70db448f8533ccbcd79add82"], "layout": "IPY_MODEL_49d605fd694c46d6a0ac936d6becbec0", "selected_index": null}}, "ffaf6b99f7fd4692b25883585f21ef2c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 2}