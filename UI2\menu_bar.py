#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 菜单栏模块，包含logo、文件、校准、设置、帮助等菜单
from PyQt6.QtWidgets import QMenuBar, QMenu, QLabel, QWidget, QHBoxLayout, QFileDialog, QMessageBox, QInputDialog, QProgressDialog, QApplication
from PyQt6.QtGui import QAction, QIcon, QPixmap
from PyQt6.QtCore import pyqtSignal, Qt
import datetime
import subprocess
import os
import json
from log_manager import log_user_action
from language_manager import get_text, set_language



class MenuBar(QMenuBar):
    # 定义语言切换信号
    language_changed_signal = pyqtSignal(str)

    title_changed = pyqtSignal(str)
    open_files_signal = pyqtSignal(list)  # 新增：打开文件信号
    save_files_signal = pyqtSignal()  # 新增：保存文件信号
    print_report_signal = pyqtSignal()  # 新增：打印报告信号
    # 测点规划信号已删除

    def __init__(self, parent=None):
        super().__init__(parent)
        # 添加公司logo（ico格式）
        logo_widget = QWidget(self)
        logo_layout = QHBoxLayout(logo_widget)
        logo_layout.setContentsMargins(4, 0, 8, 0)
        logo_layout.setSpacing(4)
        logo_label = QLabel()
        logo_label.setPixmap(QPixmap("./UI2/logo48.ico").scaledToHeight(20, Qt.TransformationMode.SmoothTransformation))
        logo_widget.setFixedWidth(40)  # 或者你需要的合理宽度


        logo_layout.addWidget(logo_label)
        logo_layout.addStretch()
        logo_widget.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        self.setCornerWidget(logo_widget, Qt.Corner.TopLeftCorner)
        # 菜单引用
        self.file_menu = None
        self.calibration_menu = None
        self.settings_menu = None
        self.help_menu = None
        self.language_menu = None
        
        # 动作引用
        self.open_action = None
        self.print_action = None
        self.work_dir_action = None
        self.about_action = None
        self.laser_action = None
        self.laser_off_action = None
        self.wavenumber_action = None
        self.chinese_action = None
        self.english_action = None
        self.manual_action = None
        self.instrument_info_action = None
        self.import_database_action = None  # 新增：导入数据库动作
        
        # 新增：工作目录属性
        self.work_dir = ""
        # 新增：最近文件属性
        self.recent_files = []
        self.recent_files_menu = None
        # 新增：登录信息
        self.login_username = "user"
        self.login_time = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
        
        self.load_recent_files()  # 新增：启动时加载最近文件
        self.setup_ui()
        
    def setup_ui(self):
        """初始化菜单栏UI组件"""
        # 文件菜单
        self.file_menu = self.addMenu(get_text("file"))
        self.open_action = QAction(get_text("open_spectrum_file"), self)
        self.open_action.setShortcut("Ctrl+O")
        self.file_menu.addAction(self.open_action)
        self.print_action = QAction(get_text("print_report"), self)
        self.print_action.setShortcut("Ctrl+P")
        self.file_menu.addAction(self.print_action)
        self.save_action = QAction(get_text("save_files"), self)
        self.save_action.setShortcut("Ctrl+i")
        self.file_menu.addAction(self.save_action)
        self.file_menu.addSeparator()
        self.work_dir_action = QAction(get_text("set_working_directory"), self)
        self.file_menu.addAction(self.work_dir_action)
        # 最近文件菜单
        self.recent_files_menu = QMenu(get_text("recent_files"), self)
        self.file_menu.addMenu(self.recent_files_menu)
        self.file_menu.addSeparator()
        self.about_action = QAction(get_text("about"), self)
        self.file_menu.addAction(self.about_action)
        
        # 校准菜单
        self.calibration_menu = self.addMenu(get_text("calibration"))
        self.laser_action = QAction(get_text("laser_on"), self)
        self.calibration_menu.addAction(self.laser_action)
        self.laser_off_action = QAction(get_text("laser_off"), self)
        self.calibration_menu.addAction(self.laser_off_action)
        self.auxiliary_test_action = QAction(get_text("auxiliary_test"), self)
        self.calibration_menu.addAction(self.auxiliary_test_action)
        self.wavenumber_action = QAction(get_text("wavenumber_calibration"), self)
        self.calibration_menu.addAction(self.wavenumber_action)
        #self.calibration_menu.addSeparator()  # 添加分隔线
        # self.lower_machine_action = QAction("下位机设置", self)
        # self.calibration_menu.addAction(self.lower_machine_action)  
        # self.stepper_action = QAction("步进机设置", self)
        # self.calibration_menu.addAction(self.stepper_action)
        
        # 设置菜单
        self.settings_menu = self.addMenu(get_text("settings"))
        self.language_menu = QMenu(get_text("language"), self)
        self.chinese_action = QAction(get_text("chinese"), self)
        self.english_action = QAction(get_text("english"), self)
        self.language_menu.addAction(self.chinese_action)
        self.language_menu.addAction(self.english_action)
        self.settings_menu.addMenu(self.language_menu)
        self.settings_menu.addSeparator()  # 添加分隔线
        
        # 设备监控配置菜单项
        self.device_monitor_action = QAction("设备监控配置", self)
        self.device_monitor_action.setToolTip("配置设备连接监控和自动重连设置")
        self.settings_menu.addAction(self.device_monitor_action)
        
        self.update_service_action = QAction(get_text("update_service"), self)
        self.settings_menu.addAction(self.update_service_action)
        self.software_management_action = QAction(get_text("software_management"), self)
        self.settings_menu.addAction(self.software_management_action)
        
        # 帮助菜单
        self.help_menu = self.addMenu(get_text("help"))
        self.manual_action = QAction(get_text("manual"), self)
        self.help_menu.addAction(self.manual_action)
        self.instrument_info_action = QAction(get_text("instrument_info"), self)
        self.help_menu.addAction(self.instrument_info_action)
        self.help_menu.addSeparator()  # 添加分隔线
        self.import_database_action = QAction(get_text("import_database"), self)
        self.help_menu.addAction(self.import_database_action)
        
        # 连接信号
        self.setup_connections()
        
    def setup_connections(self):
        """设置菜单动作的信号连接"""
        # 文件菜单动作
        self.open_action.triggered.connect(self.handle_open_file)
        self.save_action.triggered.connect(self.handle_save_files)
        self.print_action.triggered.connect(self.handle_print_report)
        self.work_dir_action.triggered.connect(self.handle_set_work_dir)
        self.about_action.triggered.connect(self.handle_about)
    
        # 校准菜单动作
        self.laser_action.triggered.connect(self.handle_laser)
        self.laser_off_action.triggered.connect(self.handle_laser_off)
        self.auxiliary_test_action.triggered.connect(self.handle_auxiliary_test)
        self.wavenumber_action.triggered.connect(self.handle_wavenumber_calibration)
        # self.lower_machine_action.triggered.connect(self.handle_lower_machine_calibration)
        # self.stepper_action.triggered.connect(self.handle_stepper_settings)
        # 语言菜单动作
        self.chinese_action.triggered.connect(lambda: self.handle_language_change("zh"))
        self.english_action.triggered.connect(lambda: self.handle_language_change("en"))
        # 帮助菜单动作
        self.manual_action.triggered.connect(self.handle_manual)
        self.instrument_info_action.triggered.connect(self.handle_instrument_info)
        self.import_database_action.triggered.connect(self.handle_import_database)
        # 设置菜单动作
        self.device_monitor_action.triggered.connect(self.handle_device_monitor_config)
        self.update_service_action.triggered.connect(self.handle_update_service)
        self.software_management_action.triggered.connect(self.handle_software_management)
        
        # # 添加密码管理功能到软件管理
        # self.password_management_action = QAction("密码管理", self)
        # self.settings_menu.addAction(self.password_management_action)
        # self.password_management_action.triggered.connect(self.handle_password_management)
        
        # 添加日志查看器入口（仅管理员可见）
        self.log_viewer_action = QAction(get_text("log_viewer"), self)
        self.settings_menu.addAction(self.log_viewer_action)
        self.log_viewer_action.triggered.connect(self.handle_log_viewer)
        self.log_viewer_action.setVisible(False)  # 默认隐藏，管理员登录后显示
    
    # 各菜单动作的处理方法
    def handle_open_file(self):
        log_user_action("菜单-打开文件", self._get_current_user(), {})
        """处理打开图谱文件"""
        # 使用当前工作目录作为初始路径
        initial_dir = self.work_dir if self.work_dir else ""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, 
            "选择光谱数据文件", 
            initial_dir, 
            "光谱文件 (*.csv *.txt *.nod);;CSV文件 (*.csv);;TXT文件 (*.txt);;NOD文件 (*.nod);;所有文件 (*)"
        )
        if file_paths:
            self.open_files_signal.emit(file_paths)
            self.update_recent_files(file_paths)
    
    def handle_save_files(self):
        log_user_action("菜单-保存文件", self._get_current_user(), {})
        """处理保存文件"""
        self.save_files_signal.emit()
    
    def handle_print_report(self):
        log_user_action("菜单-打印报告", self._get_current_user(), {})
        """处理打印报告"""
        self.print_report_signal.emit()
    
    def handle_set_work_dir(self):
        log_user_action("菜单-设置工作目录", self._get_current_user(), {})
        """处理设置工作目录"""
        dir_path = QFileDialog.getExistingDirectory(self, get_text("set_working_directory"), self.work_dir or "")
        if dir_path:
            self.work_dir = dir_path
            # 更新编辑器中的工作目录设置
            self.update_editor_work_directory(dir_path)
            print(f"工作目录已设置为: {dir_path}")
    
    def update_editor_work_directory(self, work_dir):
        """更新编辑器中的工作目录设置"""
        try:
            # 获取主窗口实例
            main_window = self.parent()
            print(f"[调试] 主窗口实例: {main_window}")
            print(f"[调试] 主窗口是否有editor属性: {hasattr(main_window, 'editor') if main_window else False}")
            
            if main_window and hasattr(main_window, 'editor'):
                editor = main_window.editor
                print(f"[调试] 获取到的编辑器实例: {editor}")
                # 设置工作目录
                editor.work_directory = work_dir
                print(f"[调试] 已设置编辑器工作目录: {work_dir}")
                print(f"[调试] 验证编辑器工作目录: {getattr(editor, 'work_directory', '未设置')}")
                
                # 确保菜单栏的 work_dir 也是最新的
                self.work_dir = work_dir
                print(f"[调试] 已更新菜单栏工作目录: {self.work_dir}")
            else:
                print(f"[调试] 无法获取编辑器实例")
        except Exception as e:
            print(f"更新编辑器工作目录失败: {e}")
            import traceback
            traceback.print_exc()
    
    def handle_about(self):
        """处理关于对话框"""
        info = (
            f"<b>{get_text('about')}</b><br>"
            f"{get_text('software_name')}<br>"
            f"{get_text('model_specification')}<br>"
            f"{get_text('release_version')}<br>"
            f"{get_text('full_version')}<br>"
            f"{get_text('release_date')}<br>"
            f"{get_text('security_level')}<br>"
            f"{get_text('login_username').format(self.login_username)}<br>"
            f"{get_text('last_login_time').format(self.login_time)}<br>"
        )
        QMessageBox.information(self, get_text('about'), info)
    
    def handle_laser(self):
        """处理激光控制"""
        try:
            # 首先检查通讯状态
            if not self._check_communication_status():
                return
            
            # 直接导入并创建激光器控制器
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            spectrumeter_dir = os.path.join(os.path.dirname(current_dir), 'spectrumeter')
            if spectrumeter_dir not in sys.path:
                sys.path.insert(0, spectrumeter_dir)
            
            from laser_controller_usb import LaserController
            
            # 创建激光器控制器并检查状态
            laser = LaserController()
            
            # 检查激光器状态
            status = laser.get_status_summary()
            if not status.get("serial_connected", False):
                QMessageBox.warning(self, "错误", "激光器串口未连接，请先点击侧边栏通讯按钮")
                return
            
            if not status.get("hardware_power_on", False):
                QMessageBox.warning(self, "错误", "激光器开关未打开，请检查硬件")
                return
            
            # 开启激光器
            laser.enable_laser()
            # 设置默认功率为50%
            laser.set_power_percent(50)
            QMessageBox.information(self, "成功", "激光器已开启，功率设置为50%")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"开启激光器失败: {str(e)}")
    
    def handle_laser_off(self):
        """处理关闭激光"""
        try:
            # 首先检查通讯状态
            if not self._check_communication_status():
                return
            
            # 直接导入并创建激光器控制器
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            spectrumeter_dir = os.path.join(os.path.dirname(current_dir), 'spectrumeter')
            if spectrumeter_dir not in sys.path:
                sys.path.insert(0, spectrumeter_dir)
            
            from laser_controller_usb import LaserController
            
            # 创建激光器控制器并关闭激光
            laser = LaserController()
            laser.disable_laser()
            QMessageBox.information(self, "成功", "激光器已关闭")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"关闭激光器失败: {str(e)}")
    
    def _check_communication_status(self):
        """检查通讯状态"""
        try:
            # 获取主窗口
            main_window = self.parent()
            if not main_window:
                QMessageBox.warning(self, "错误", "无法获取主窗口")
                return False
            
            # 检查主窗口是否有sidebar属性
            if not hasattr(main_window, 'sidebar'):
                # 尝试通过应用程序获取主窗口
                app = QApplication.instance()
                if app:
                    # 遍历所有窗口，找到主窗口
                    for widget in app.topLevelWidgets():
                        if hasattr(widget, 'sidebar'):
                            main_window = widget
                            break
                    else:
                        QMessageBox.warning(self, "错误", "无法找到主窗口或侧边栏")
                        return False
                else:
                    QMessageBox.warning(self, "错误", "无法获取应用程序实例")
                    return False
            
            # 获取侧边栏
            sidebar = main_window.sidebar
            if not sidebar:
                QMessageBox.warning(self, "错误", "无法获取侧边栏")
                return False
            
            # 检查激光器是否已连接
            if not hasattr(sidebar, 'laser_connected'):
                QMessageBox.warning(self, "错误", "侧边栏没有laser_connected属性")
                return False
                
            if not sidebar.laser_connected:
                QMessageBox.warning(self, "错误", "请先连接激光器（点击侧边栏通讯按钮）")
                return False
            
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"检查通讯状态失败: {str(e)}")
            return False
    
    def handle_auxiliary_test(self):
        """处理辅助测试"""
        # 第一步：显示初始提示
        reply = QMessageBox.question(
            self, 
            get_text('auxiliary_test'), 
            get_text('dark_current_completed'),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 循环检测过程
        while True:
            # 显示检测提示
            reply = QMessageBox.question(
                self, 
                get_text('auxiliary_test'), 
                get_text('adjust_element_lamp'),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                break
    
    def handle_wavenumber_calibration(self):
        """处理波数校准"""
        # 测点规划功能已删除
        pass
    
    
    def handle_language_change(self, language):
        """处理语言切换"""
        self.language_changed_signal.emit(language)
    
    def handle_manual(self):
        """处理操作手册显示"""
        # 指定PDF文件路径
        manual_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../manual.pdf'))
        if os.path.exists(manual_path):
            try:
                subprocess.Popen(['xdg-open', manual_path])
            except Exception as e:
                QMessageBox.warning(self, get_text('error'), get_text('cannot_open_manual').format(e))
        else:
            QMessageBox.warning(self, get_text('error'), get_text('manual_not_found').format(manual_path))
    
    def handle_instrument_info(self):
        """处理仪器信息显示"""
        QMessageBox.information(self, get_text('instrument_info'), "neoversion-4.0")
    
    def handle_update_service(self):
        log_user_action("菜单-更新服务", self._get_current_user(), {})
        """处理更新服务"""
        import zipfile
        import shutil
        import tempfile
        from pathlib import Path
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QPushButton, QLabel, QFileDialog, QDialogButtonBox

        # 创建自定义对话框
        dialog = QDialog(self)
        dialog.setWindowTitle(get_text("service_update"))
        layout = QVBoxLayout()
        
        # ZIP文件选择
        zip_label = QLabel(get_text("update_package_location"))
        zip_path_label = QLabel(get_text("not_selected"))
        zip_path_label.setStyleSheet("color: gray;")
        zip_button = QPushButton(get_text("select_update_package"))
        layout.addWidget(zip_label)
        layout.addWidget(zip_path_label)
        layout.addWidget(zip_button)
        
        # 目标目录选择
        target_label = QLabel(f"\n{get_text('installation_directory')}")
        target_path_label = QLabel(get_text("not_selected"))
        target_path_label.setStyleSheet("color: gray;")
        target_button = QPushButton(get_text("select_installation_directory"))
        layout.addWidget(target_label)
        layout.addWidget(target_path_label)
        layout.addWidget(target_button)
        
        # 添加说明文字
        hint_label = QLabel(f"\n{get_text('update_instructions')}")
        hint_label.setStyleSheet("color: gray; font-size: 10pt;")
        layout.addWidget(hint_label)
        
        # 添加一些空间
        spacer = QLabel("")
        layout.addWidget(spacer)
        
        # 确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        layout.addWidget(button_box)
        
        dialog.setLayout(layout)
        
        # 保存选择的路径
        selected_paths = {"zip": "", "target": ""}
        
        def choose_zip():
            path, _ = QFileDialog.getOpenFileName(
                dialog,
                get_text("select_service_update_package"),
                os.path.expanduser("~"),
                "ZIP文件 (*.zip);;所有文件 (*)"
            )
            if path:
                selected_paths["zip"] = path
                zip_path_label.setText(path)
                zip_path_label.setStyleSheet("color: black;")
        
        def choose_target():
            path = QFileDialog.getExistingDirectory(
                dialog,
                get_text("select_installation_directory_service"),
                os.path.expanduser("~")
            )
            if path:
                selected_paths["target"] = path
                target_path_label.setText(path)
                target_path_label.setStyleSheet("color: black;")
        
        # 连接信号
        zip_button.clicked.connect(choose_zip)
        target_button.clicked.connect(choose_target)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        # 显示对话框
        if dialog.exec() != QDialog.DialogCode.Accepted:
            return
            
        # 验证选择
        if not selected_paths["zip"] or not selected_paths["target"]:
            QMessageBox.warning(self, get_text("incomplete_input"), get_text("please_select_package_and_directory"))
            return
            
        # 设置路径
        zip_file_path = Path(selected_paths["zip"])
        target_service_dir = Path(selected_paths["target"]) / "Service"
        
        try:
            # 确认更新操作
            reply = QMessageBox.question(
                self, get_text("confirm_update"), 
                f"即将使用更新包更新服务\n\n"
                f"更新包：{zip_file_path.name}\n"
                f"目标目录：{target_service_dir}\n\n"
                "此操作将：\n"
                "1. 停止当前服务\n"
                "2. 备份现有文件\n"
                "3. 解压新文件\n"
                "4. 重启服务\n\n"
                "是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # 创建进度对话框
            progress = QProgressDialog("正在更新服务...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setAutoClose(False)
            progress.show()
            
            # 停止服务
            progress.setLabelText("正在停止服务...")
            progress.setValue(10)
            self._run_service_command(target_service_dir / "停止服务.bat")
            
            # 备份现有文件
            progress.setLabelText("正在备份现有文件...")
            progress.setValue(25)
            backup_dir = target_service_dir.parent / f"Service_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if target_service_dir.exists():
                shutil.copytree(target_service_dir, backup_dir)
            
            # 解压新文件
            progress.setLabelText("正在解压更新包...")
            progress.setValue(50)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 解压文件
                if zip_file_path.suffix.lower() == '.zip':
                    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                        zip_ref.extractall(temp_path)
                else:
                    QMessageBox.critical(self, "更新失败", f"不支持的文件格式: {zip_file_path.suffix}")
                    return
                
                # 复制文件到目标目录
                progress.setLabelText("正在复制新文件...")
                progress.setValue(75)
                
                # 确保目标目录存在
                target_service_dir.mkdir(parents=True, exist_ok=True)
                
                # 查找解压后的服务文件
                extracted_files = list(temp_path.rglob("*"))
                service_files = [f for f in extracted_files if f.is_file()]
                
                for file_path in service_files:
                    relative_path = file_path.relative_to(temp_path)
                    target_file = target_service_dir / relative_path
                    
                    # 创建目标目录
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 复制文件
                    shutil.copy2(file_path, target_file)
            
            # 重新安装并启动服务
            progress.setLabelText("正在重新安装服务...")
            progress.setValue(90)
            
            # 卸载旧服务
            self._run_service_command(target_service_dir / "卸载服务.bat")
            
            # 安装新服务
            self._run_service_command(target_service_dir / "安装服务.bat")
            
            # 启动服务
            progress.setLabelText("正在启动服务...")
            progress.setValue(95)
            self._run_service_command(target_service_dir / "启动服务.bat")
            
            # 完成
            progress.setValue(100)
            progress.close()
            
            QMessageBox.information(
                self, "更新成功", 
                f"服务更新完成！\n\n"
                f"更新包：{zip_file_path.name}\n"
                f"备份位置：{backup_dir}\n"
                f"服务目录：{target_service_dir}\n\n"
                "服务已重新启动。"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "更新失败", f"服务更新过程中发生错误：\n{str(e)}")
            print(f"服务更新失败: {e}")
    
    def _run_service_command(self, bat_file_path):
        """运行服务相关的批处理命令"""
        import subprocess
        from pathlib import Path
        
        bat_path = Path(bat_file_path)
        if not bat_path.exists():
            print(f"批处理文件不存在: {bat_path}")
            return False
        
        try:
            # 在Windows上运行批处理文件
            result = subprocess.run(
                [str(bat_path)], 
                cwd=bat_path.parent,
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"成功执行: {bat_path.name}")
                return True
            else:
                print(f"执行失败: {bat_path.name}, 错误: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"执行超时: {bat_path.name}")
            return False
        except Exception as e:
            print(f"执行异常: {bat_path.name}, 错误: {e}")
            return False
    
    def handle_software_management(self):
        log_user_action("菜单-软件管理", self._get_current_user(), {})
        """处理软件管理"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox, QDialogButtonBox, QGridLayout
        
        # 创建软件管理对话框
        dialog = QDialog(self)
        dialog.setWindowTitle(get_text("software_management"))
        dialog.setFixedSize(400, 300)
        
        # 创建主布局
        layout = QVBoxLayout()
        
        # 创建网格布局用于表单
        form_layout = QGridLayout()
        
        # 用户名设置
        username_label = QLabel(get_text("username"))
        username_edit = QLineEdit()
        username_edit.setText("admin")  # 默认用户名
        reset_password_btn = QPushButton(get_text("reset_password"))
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(username_edit, 0, 1)
        form_layout.addWidget(reset_password_btn, 0, 2)
        
        # 最大曲线数设置
        max_curves_label = QLabel(get_text("max_curves"))
        max_curves_spin = QSpinBox()
        max_curves_spin.setRange(1, 1000)
        max_curves_spin.setValue(100)  # 默认值100
        form_layout.addWidget(max_curves_label, 1, 0)
        form_layout.addWidget(max_curves_spin, 1, 1)
        
        # 默认横坐标设置
        x_axis_label = QLabel(get_text("x_axis"))
        x_axis_combo = QComboBox()
        x_axis_combo.addItems(["拉曼位移", "像素"])
        x_axis_combo.setCurrentText("拉曼位移")  # 默认选择
        form_layout.addWidget(x_axis_label, 2, 0)
        form_layout.addWidget(x_axis_combo, 2, 1)
        
        # 输出格式设置
        output_format_label = QLabel(get_text("output_format"))
        output_format_combo = QComboBox()
        output_format_combo.addItems(["nod", "txt", "csv"])
        output_format_combo.setCurrentText("nod")  # 默认选择
        form_layout.addWidget(output_format_label, 3, 0)
        form_layout.addWidget(output_format_combo, 3, 1)
        
        # 重置按钮
        reset_btn = QPushButton(get_text("reset_settings"))
        form_layout.addWidget(reset_btn, 4, 0, 1, 2)
        
        # 添加表单到主布局
        layout.addLayout(form_layout)
        
        # 添加一些空间
        layout.addStretch()
        
        # 底部按钮
        button_layout = QHBoxLayout()
        change_btn = QPushButton(get_text("change_settings"))
        cancel_btn = QPushButton(get_text("cancel"))
        button_layout.addStretch()
        button_layout.addWidget(change_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 加载当前设置
        self.load_current_settings_to_dialog(username_edit, max_curves_spin, x_axis_combo, output_format_combo)
        
        # 连接信号
        def handle_reset_password():
            try:
                from password_dialog import PasswordDialog
                dialog_pwd = PasswordDialog(dialog)  # 父窗口设为软件管理对话框
                dialog_pwd.password_changed.connect(self.on_password_changed)
                dialog_pwd.exec()
            except ImportError as e:
                QMessageBox.critical(dialog, get_text("error"), f"无法导入密码管理模块: {str(e)}")
        

        
        def handle_reset():
            # 重置所有设置到默认值
            username_edit.setText("admin")
            max_curves_spin.setValue(100)
            x_axis_combo.setCurrentText("拉曼位移")
            output_format_combo.setCurrentText("nod")
            QMessageBox.information(dialog, get_text("reset_settings"), get_text("settings_reset_successfully"))
        
        def handle_change():
            # 保存设置到文件
            settings = {
                "username": username_edit.text(),
                "max_curves": max_curves_spin.value(),
                "x_axis": x_axis_combo.currentText(),
                "output_format": output_format_combo.currentText()
            }
            
            try:
                self.save_software_settings(settings)
                # 更新编辑器中的设置
                self.update_editor_settings(settings)
                QMessageBox.information(dialog, "更改", f"设置已保存：\n{settings}")
                dialog.accept()
            except Exception as e:
                QMessageBox.critical(dialog, "错误", f"保存设置失败：{str(e)}")
        
        def handle_cancel():
            dialog.reject()
        
        # 连接按钮信号
        reset_password_btn.clicked.connect(handle_reset_password)
        reset_btn.clicked.connect(handle_reset)
        change_btn.clicked.connect(handle_change)
        cancel_btn.clicked.connect(handle_cancel)
        
        # 显示对话框
        dialog.exec()
    
    def load_current_settings_to_dialog(self, username_edit, max_curves_spin, x_axis_combo, output_format_combo):
        """加载当前设置到对话框"""
        try:
            settings = self.load_software_settings()
            if settings:
                username_edit.setText(settings.get("username", "admin"))
                max_curves_spin.setValue(settings.get("max_curves", 100))
                x_axis_combo.setCurrentText(settings.get("x_axis", "拉曼位移"))
                output_format_combo.setCurrentText(settings.get("output_format", "nod"))
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def save_software_settings(self, settings):
        """保存软件设置到文件"""
        import json
        import os
        
        settings_file = os.path.join(os.path.dirname(__file__), "software_settings.json")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)
        
        # 保存设置到文件
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        
        print(f"软件设置已保存到: {settings_file}")
    
    def load_software_settings(self):
        """从文件加载软件设置"""
        import json
        import os
        
        settings_file = os.path.join(os.path.dirname(__file__), "software_settings.json")
        
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                print(f"软件设置已从文件加载: {settings_file}")
                return settings
            except Exception as e:
                print(f"加载软件设置失败: {e}")
        
        # 返回默认设置
        return {
            "username": "admin",
            "max_curves": 100,
            "x_axis": "拉曼位移",
            "output_format": "nod"
        }
    
    def update_editor_settings(self, settings):
        """更新编辑器中的设置"""
        try:
            # 获取主窗口实例
            main_window = self.parent()
            if main_window and hasattr(main_window, 'editor'):
                editor = main_window.editor
                # 设置输出格式
                editor.output_format = settings.get("output_format", "nod")
                # 设置最大曲线数
                editor.max_curves = settings.get("max_curves", 100)
                # 设置默认横坐标
                editor.default_x_axis = settings.get("x_axis", "拉曼位移")
                print(f"编辑器设置已更新: {settings}")
        except Exception as e:
            print(f"更新编辑器设置失败: {e}")
    
    def handle_password_management(self):
        """处理密码管理"""
        try:
            from password_dialog import PasswordDialog
            dialog = PasswordDialog(self)
            dialog.password_changed.connect(self.on_password_changed)
            dialog.exec()
        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法导入密码管理模块: {str(e)}")
    
    def on_password_changed(self, username, new_password):
        """密码更改成功处理"""
        QMessageBox.information(self, "成功", f"用户 {username} 的密码已成功更改")
    
    def handle_log_viewer(self):
        """处理打开日志查看器"""
        try:
            from log_viewer import LogViewerWindow
            
            # 检查是否已经打开了日志查看器窗口
            if not hasattr(self, '_log_viewer_window') or self._log_viewer_window is None:
                self._log_viewer_window = LogViewerWindow()
                # 绑定关闭事件，窗口关闭时清空变量
                self._log_viewer_window.destroyed.connect(lambda: setattr(self, '_log_viewer_window', None))
            
            self._log_viewer_window.show()
            self._log_viewer_window.raise_()
            self._log_viewer_window.activateWindow()
            
        except ImportError as e:
            QMessageBox.critical(self, get_text("error"), f"无法导入日志查看器模块: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, get_text("error"), f"打开日志查看器时出错: {str(e)}")

    def handle_import_database(self):
        log_user_action("菜单-导入数据库", self._get_current_user(), {})
        """处理导入数据库"""
        # 使用当前工作目录作为初始路径
        initial_dir = self.work_dir if self.work_dir else ""
        
        # 提供两种导入方式：导入数据库文件或导入光谱文件到数据库
        from PyQt6.QtWidgets import QInputDialog
        
        choice, ok = QInputDialog.getItem(
            self, 
            get_text("select_import_method"), 
            get_text("please_select_import_method"),
            [get_text("import_spectrum_files_to_database"), get_text("import_existing_database_file")], 
            0, 
            False
        )
        
        if not ok:
            return
            
        if choice == get_text("import_spectrum_files_to_database"):
            # 选择光谱文件导入到数据库
            file_paths, _ = QFileDialog.getOpenFileNames(
                self, 
                get_text("select_spectrum_data_files"), 
                initial_dir, 
                "光谱文件 (*.csv *.txt *.nod);;CSV文件 (*.csv);;TXT文件 (*.txt);;NOD文件 (*.nod);;所有文件 (*)"
            )
            
            if file_paths:
                try:
                    from database_importer import DatabaseImporter
                    from PyQt6.QtWidgets import QProgressDialog
                    from PyQt6.QtCore import Qt
                    
                    importer = DatabaseImporter()
                    
                    # 使用QProgressDialog显示进度
                    progress_dialog = QProgressDialog(get_text("importing_files").format(len(file_paths)), get_text("cancel"), 0, len(file_paths), self)
                    progress_dialog.setWindowTitle(get_text("import_progress"))
                    progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                    progress_dialog.setAutoClose(True)
                    progress_dialog.setAutoReset(True)
                    progress_dialog.show()
                    
                    # 导入文件
                    results = {}
                    for i, file_path in enumerate(file_paths):
                        # 更新进度
                        progress_dialog.setValue(i)
                        progress_dialog.setLabelText(get_text("importing_file").format(os.path.basename(file_path)))
                        
                        # 检查是否取消
                        if progress_dialog.wasCanceled():
                            break
                        
                        # 导入单个文件
                        success = importer.import_spectrum_file(file_path)
                        results[file_path] = success
                    
                    # 完成进度
                    progress_dialog.setValue(len(file_paths))
                    
                    # 关闭数据库连接
                    importer.disconnect()
                    
                    # 统计结果
                    success_count = sum(1 for success in results.values() if success)
                    failed_files = [path for path, success in results.items() if not success]
                    
                    if failed_files:
                        error_msg = get_text("import_results").format(success_count, len(file_paths), "\n".join(failed_files[:5]))
                        if len(failed_files) > 5:
                            error_msg += get_text("and_more_files").format(len(failed_files) - 5)
                        QMessageBox.warning(self, get_text("import_complete"), error_msg)
                    else:
                        QMessageBox.information(self, get_text("import_successful"), get_text("successfully_imported_files").format(success_count))
                    
                    print(f"数据库导入完成: {success_count}/{len(file_paths)} 个文件成功")
                    
                except ImportError:
                    QMessageBox.critical(self, "导入失败", "无法导入数据库模块，请检查依赖")
                except Exception as e:
                    QMessageBox.critical(self, "导入失败", f"数据库导入失败：\n{str(e)}")
                    print(f"数据库导入失败: {e}")
        
        else:
            # 导入现有数据库文件
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "选择数据库文件", 
                initial_dir, 
                "数据库文件 (*.db *.sqlite *.sqlite3);;SQL文件 (*.sql);;所有文件 (*)"
            )
            
            if file_path:
                try:
                    # 这里可以实现数据库文件的导入逻辑
                    # 例如：复制数据库文件到本地、合并数据库等
                    QMessageBox.information(self, "导入成功", f"数据库文件已成功导入：\n{file_path}")
                    print(f"导入数据库文件: {file_path}")
                except Exception as e:
                    QMessageBox.critical(self, "导入失败", f"数据库导入失败：\n{str(e)}")
                    print(f"数据库导入失败: {e}")

    def handle_device_monitor_config(self):
        """处理设备监控配置"""
        log_user_action("菜单-设备监控配置", self._get_current_user(), {})
        try:
            from device_monitor_dialog import DeviceMonitorDialog
            dialog = DeviceMonitorDialog(self)
            dialog.exec()
        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法导入设备监控配置模块: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开设备监控配置失败: {str(e)}")

    def update_language(self, language):
        set_language(language)
        if language == 'zh':
            self.file_menu.setTitle(get_text('file'))
            self.open_action.setText(get_text('open_spectrum_file'))
            self.print_action.setText(get_text('print_report'))
            self.save_action.setText(get_text('save_files'))
            self.work_dir_action.setText(get_text('set_working_directory'))
            self.about_action.setText(get_text('about'))
            self.recent_files_menu.setTitle(get_text('recent_files'))
            self.calibration_menu.setTitle(get_text('calibration'))
            self.laser_action.setText(get_text('laser_on'))
            self.laser_off_action.setText(get_text('laser_off'))
            self.auxiliary_test_action.setText(get_text('auxiliary_test'))
            self.wavenumber_action.setText(get_text('wavenumber_calibration'))
            self.settings_menu.setTitle(get_text('settings'))
            self.language_menu.setTitle(get_text('language'))
            self.chinese_action.setText(get_text('chinese'))
            self.english_action.setText(get_text('english'))
            self.update_service_action.setText(get_text('update_service'))
            self.software_management_action.setText(get_text('software_management'))
            self.log_viewer_action.setText(get_text('log_viewer'))
            self.help_menu.setTitle(get_text('help'))
            self.manual_action.setText(get_text('manual'))
            self.instrument_info_action.setText(get_text('instrument_info'))
            self.import_database_action.setText(get_text('import_database'))
            self.device_monitor_action.setText("设备监控配置")
        else:
            self.file_menu.setTitle(get_text('file'))
            self.open_action.setText(get_text('open_spectrum_file'))
            self.print_action.setText(get_text('print_report'))
            self.save_action.setText(get_text('save_files'))
            self.work_dir_action.setText(get_text('set_working_directory'))
            self.about_action.setText(get_text('about'))
            self.recent_files_menu.setTitle(get_text('recent_files'))
            self.calibration_menu.setTitle(get_text('calibration'))
            self.laser_action.setText(get_text('laser_on'))
            self.laser_off_action.setText(get_text('laser_off'))
            self.auxiliary_test_action.setText(get_text('auxiliary_test'))
            self.wavenumber_action.setText(get_text('wavenumber_calibration'))
            self.settings_menu.setTitle(get_text('settings'))
            self.language_menu.setTitle(get_text('language'))
            self.chinese_action.setText(get_text('chinese'))
            self.english_action.setText(get_text('english'))
            self.update_service_action.setText(get_text('update_service'))
            self.software_management_action.setText(get_text('software_management'))
            self.log_viewer_action.setText(get_text('log_viewer'))
            self.help_menu.setTitle(get_text('help'))
            self.manual_action.setText(get_text('manual'))
            self.instrument_info_action.setText(get_text('instrument_info'))
            self.import_database_action.setText(get_text('import_database'))
            self.device_monitor_action.setText("设备监控配置")

    def get_recent_files_path(self):
        """获取最近文件json的路径"""
        return os.path.join(os.path.dirname(__file__), "recent_files.json")

    def load_recent_files(self):
        """加载最近文件列表"""
        try:
            with open(self.get_recent_files_path(), "r", encoding="utf-8") as f:
                self.recent_files = json.load(f)
        except Exception:
            self.recent_files = []

    def save_recent_files(self):
        """保存最近文件列表"""
        try:
            with open(self.get_recent_files_path(), "w", encoding="utf-8") as f:
                json.dump(self.recent_files, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存最近文件失败: {e}")

    def update_recent_files(self, file_paths):
        """更新最近文件列表和菜单"""
        # 支持单个或多个文件
        if isinstance(file_paths, str):
            file_paths = [file_paths]
        for file_path in file_paths:
            if file_path in self.recent_files:
                self.recent_files.remove(file_path)
            self.recent_files.insert(0, file_path)
        self.recent_files = self.recent_files[:10]
        # 更新菜单
        self.recent_files_menu.clear()
        for file_path in self.recent_files:
            action = QAction(file_path, self)
            action.triggered.connect(lambda checked, p=file_path: self.open_files_signal.emit([p]))
            self.recent_files_menu.addAction(action)
        if not self.recent_files:
            self.recent_files_menu.addAction("无最近文件")
        self.save_recent_files()  # 新增：保存最近文件

    # 辅助函数：获取当前用户名
    def _get_current_user(self):
        mainwin = self.parent() if hasattr(self, 'parent') else None
        if mainwin and hasattr(mainwin, 'user_manager') and hasattr(mainwin.user_manager, 'current_user'):
            return mainwin.user_manager.current_user
        return 'unknown'


