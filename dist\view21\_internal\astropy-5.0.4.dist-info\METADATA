Metadata-Version: 2.1
Name: astropy
Version: 5.0.4
Summary: Astronomy and astrophysics core library
Home-page: http://astropy.org
Author: The Astropy Developers
Author-email: <EMAIL>
License: BSD 3-Clause License
Project-URL: Documentation, https://docs.astropy.org
Project-URL: Source, https://github.com/astropy/astropy
Keywords: astronomy,astrophysics,cosmology,space,science,units,table,wcs,samp,coordinate,fits,modeling,models,fitting,ascii
Platform: UNKNOWN
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: C
Classifier: Programming Language :: Cython
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Scientific/Engineering :: Astronomy
Classifier: Topic :: Scientific/Engineering :: Physics
Requires-Python: >=3.8
License-File: LICENSE.rst
Requires-Dist: numpy (>=1.18)
Requires-Dist: pyerfa (>=2.0)
Requires-Dist: PyYAML (>=3.13)
Requires-Dist: packaging (>=19.0)
Provides-Extra: all
Requires-Dist: scipy (>=1.3) ; extra == 'all'
Requires-Dist: matplotlib (!=3.4.0,>=3.1) ; extra == 'all'
Requires-Dist: certifi ; extra == 'all'
Requires-Dist: dask[array] ; extra == 'all'
Requires-Dist: h5py ; extra == 'all'
Requires-Dist: pyarrow (>=5.0.0) ; extra == 'all'
Requires-Dist: beautifulsoup4 ; extra == 'all'
Requires-Dist: html5lib ; extra == 'all'
Requires-Dist: bleach ; extra == 'all'
Requires-Dist: pandas ; extra == 'all'
Requires-Dist: sortedcontainers ; extra == 'all'
Requires-Dist: pytz ; extra == 'all'
Requires-Dist: jplephem ; extra == 'all'
Requires-Dist: mpmath ; extra == 'all'
Requires-Dist: asdf (>=2.9.2) ; extra == 'all'
Requires-Dist: bottleneck ; extra == 'all'
Requires-Dist: ipython (>=4.2) ; extra == 'all'
Requires-Dist: pytest (>=7.0) ; extra == 'all'
Requires-Dist: typing-extensions (>=********) ; extra == 'all'
Provides-Extra: docs
Requires-Dist: sphinx (<4) ; extra == 'docs'
Requires-Dist: sphinx-astropy (>=1.6) ; extra == 'docs'
Requires-Dist: pytest (>=7.0) ; extra == 'docs'
Requires-Dist: scipy (>=1.3) ; extra == 'docs'
Requires-Dist: matplotlib (!=3.4.0,>=3.1) ; extra == 'docs'
Requires-Dist: sphinx-changelog (>=1.1.0) ; extra == 'docs'
Requires-Dist: Jinja2 (<3.1) ; extra == 'docs'
Provides-Extra: recommended
Requires-Dist: scipy (>=1.3) ; extra == 'recommended'
Requires-Dist: matplotlib (!=3.4.0,>=3.1) ; extra == 'recommended'
Provides-Extra: test
Requires-Dist: pytest (>=7.0) ; extra == 'test'
Requires-Dist: pytest-doctestplus (>=0.12) ; extra == 'test'
Requires-Dist: pytest-astropy-header (>=0.2.1) ; extra == 'test'
Requires-Dist: pytest-astropy (>=0.9) ; extra == 'test'
Requires-Dist: pytest-xdist ; extra == 'test'
Provides-Extra: test_all
Requires-Dist: pytest (>=7.0) ; extra == 'test_all'
Requires-Dist: pytest-doctestplus (>=0.12) ; extra == 'test_all'
Requires-Dist: pytest-astropy-header (>=0.2.1) ; extra == 'test_all'
Requires-Dist: pytest-astropy (>=0.9) ; extra == 'test_all'
Requires-Dist: pytest-xdist ; extra == 'test_all'
Requires-Dist: objgraph ; extra == 'test_all'
Requires-Dist: ipython (>=4.2) ; extra == 'test_all'
Requires-Dist: coverage ; extra == 'test_all'
Requires-Dist: skyfield (>=1.20) ; extra == 'test_all'
Requires-Dist: sgp4 (>=2.3) ; extra == 'test_all'

=======
Astropy
=======

|Actions Status| |CircleCI Status| |Azure Status| |Coverage Status| |PyPI Status| |Documentation Status| |Zenodo|

The Astropy Project (http://astropy.org/) is a community effort to develop a
single core package for Astronomy in Python and foster interoperability between
Python astronomy packages. This repository contains the core package which is
intended to contain much of the core functionality and some common tools needed
for performing astronomy and astrophysics with Python.

Releases are `registered on PyPI <https://pypi.org/project/astropy>`_,
and development is occurring at the
`project's GitHub page <http://github.com/astropy/astropy>`_.

For installation instructions, see the `online documentation <https://docs.astropy.org/>`_
or  `docs/install.rst <docs/install.rst>`_ in this source distribution.

Contributing Code, Documentation, or Feedback
---------------------------------------------

The Astropy Project is made both by and for its users, so we welcome and
encourage contributions of many kinds. Our goal is to keep this a positive,
inclusive, successful, and growing community by abiding with the
`Astropy Community Code of Conduct <http://www.astropy.org/about.html#codeofconduct>`_.

More detailed information on contributing to the project or submitting feedback
can be found on the `contributions <http://www.astropy.org/contribute.html>`_
page. A `summary of contribution guidelines <CONTRIBUTING.md>`_ can also be
used as a quick reference when you are ready to start writing or validating
code for submission.

Supporting the Project
----------------------

|NumFOCUS| |Donate|

The Astropy Project is sponsored by NumFOCUS, a 501(c)(3) nonprofit in the
United States. You can donate to the project by using the link above, and this
donation will support our mission to promote sustainable, high-level code base
for the astronomy community, open code development, educational materials, and
reproducible scientific research.

License
-------

Astropy is licensed under a 3-clause BSD style license - see the
`LICENSE.rst <LICENSE.rst>`_ file.

.. |Actions Status| image:: https://github.com/astropy/astropy/workflows/CI/badge.svg
    :target: https://github.com/astropy/astropy/actions
    :alt: Astropy's GitHub Actions CI Status

.. |CircleCI Status| image::  https://img.shields.io/circleci/build/github/astropy/astropy/main?logo=circleci&label=CircleCI
    :target: https://circleci.com/gh/astropy/astropy
    :alt: Astropy's CircleCI Status

.. |Azure Status| image:: https://dev.azure.com/astropy-project/astropy/_apis/build/status/astropy.astropy?repoName=astropy%2Fastropy&branchName=main
    :target: https://dev.azure.com/astropy-project/astropy
    :alt: Astropy's Azure Pipelines Status

.. |Coverage Status| image:: https://codecov.io/gh/astropy/astropy/branch/main/graph/badge.svg
    :target: https://codecov.io/gh/astropy/astropy
    :alt: Astropy's Coverage Status

.. |PyPI Status| image:: https://img.shields.io/pypi/v/astropy.svg
    :target: https://pypi.org/project/astropy
    :alt: Astropy's PyPI Status

.. |Zenodo| image:: https://zenodo.org/badge/DOI/10.5281/zenodo.4670728.svg
   :target: https://doi.org/10.5281/zenodo.4670728
   :alt: Zenodo DOI

.. |Documentation Status| image:: https://img.shields.io/readthedocs/astropy/latest.svg?logo=read%20the%20docs&logoColor=white&label=Docs&version=stable
    :target: https://docs.astropy.org/en/stable/?badge=stable
    :alt: Documentation Status

.. |NumFOCUS| image:: https://img.shields.io/badge/powered%20by-NumFOCUS-orange.svg?style=flat&colorA=E1523D&colorB=007D8A
    :target: http://numfocus.org
    :alt: Powered by NumFOCUS

.. |Donate| image:: https://img.shields.io/badge/Donate-to%20Astropy-brightgreen.svg
    :target: https://numfocus.salsalabs.org/donate-to-astropy/index.html


If you locally cloned this repo before 7 Apr 2021
-------------------------------------------------

The primary branch for this repo has been transitioned from ``master`` to
``main``.  If you have a local clone of this repository and want to keep your
local branch in sync with this repo, you'll need to do the following in your
local clone from your terminal::

   git fetch --all --prune
   # you can stop here if you don't use your local "master"/"main" branch
   git branch -m master main
   git branch -u origin/main main

If you are using a GUI to manage your repos you'll have to find the equivalent
commands as it's different for different programs. Alternatively, you can just
delete your local clone and re-clone!


