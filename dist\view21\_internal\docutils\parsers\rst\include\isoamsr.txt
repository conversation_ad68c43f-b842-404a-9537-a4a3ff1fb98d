.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <http://docutils.sourceforge.net>.

.. |apE|      unicode:: U+02A70 .. APPROXIMATELY EQUAL OR EQUAL TO
.. |ape|      unicode:: U+0224A .. ALMOST EQUAL OR EQUAL TO
.. |apid|     unicode:: U+0224B .. TRIPLE TILDE
.. |asymp|    unicode:: U+02248 .. ALMOST EQUAL TO
.. |Barv|     unicode:: U+02AE7 .. SHORT DOWN TACK WITH OVERBAR
.. |bcong|    unicode:: U+0224C .. ALL EQUAL TO
.. |bepsi|    unicode:: U+003F6 .. GREEK REVERSED LUNATE EPSILON SYMBOL
.. |bowtie|   unicode:: U+022C8 .. BOWTIE
.. |bsim|     unicode:: U+0223D .. REVERSED TILDE
.. |bsime|    unicode:: U+022CD .. REVERSED TILDE EQUALS
.. |bsolhsub| unicode:: U+0005C U+02282 .. REVERSE SOLIDUS, SUBSET OF
.. |bump|     unicode:: U+0224E .. GEOMETRICALLY EQUIVALENT TO
.. |bumpE|    unicode:: U+02AAE .. EQUALS SIGN WITH BUMPY ABOVE
.. |bumpe|    unicode:: U+0224F .. DIFFERENCE BETWEEN
.. |cire|     unicode:: U+02257 .. RING EQUAL TO
.. |Colon|    unicode:: U+02237 .. PROPORTION
.. |Colone|   unicode:: U+02A74 .. DOUBLE COLON EQUAL
.. |colone|   unicode:: U+02254 .. COLON EQUALS
.. |congdot|  unicode:: U+02A6D .. CONGRUENT WITH DOT ABOVE
.. |csub|     unicode:: U+02ACF .. CLOSED SUBSET
.. |csube|    unicode:: U+02AD1 .. CLOSED SUBSET OR EQUAL TO
.. |csup|     unicode:: U+02AD0 .. CLOSED SUPERSET
.. |csupe|    unicode:: U+02AD2 .. CLOSED SUPERSET OR EQUAL TO
.. |cuepr|    unicode:: U+022DE .. EQUAL TO OR PRECEDES
.. |cuesc|    unicode:: U+022DF .. EQUAL TO OR SUCCEEDS
.. |cupre|    unicode:: U+0227C .. PRECEDES OR EQUAL TO
.. |Dashv|    unicode:: U+02AE4 .. VERTICAL BAR DOUBLE LEFT TURNSTILE
.. |dashv|    unicode:: U+022A3 .. LEFT TACK
.. |easter|   unicode:: U+02A6E .. EQUALS WITH ASTERISK
.. |ecir|     unicode:: U+02256 .. RING IN EQUAL TO
.. |ecolon|   unicode:: U+02255 .. EQUALS COLON
.. |eDDot|    unicode:: U+02A77 .. EQUALS SIGN WITH TWO DOTS ABOVE AND TWO DOTS BELOW
.. |eDot|     unicode:: U+02251 .. GEOMETRICALLY EQUAL TO
.. |efDot|    unicode:: U+02252 .. APPROXIMATELY EQUAL TO OR THE IMAGE OF
.. |eg|       unicode:: U+02A9A .. DOUBLE-LINE EQUAL TO OR GREATER-THAN
.. |egs|      unicode:: U+02A96 .. SLANTED EQUAL TO OR GREATER-THAN
.. |egsdot|   unicode:: U+02A98 .. SLANTED EQUAL TO OR GREATER-THAN WITH DOT INSIDE
.. |el|       unicode:: U+02A99 .. DOUBLE-LINE EQUAL TO OR LESS-THAN
.. |els|      unicode:: U+02A95 .. SLANTED EQUAL TO OR LESS-THAN
.. |elsdot|   unicode:: U+02A97 .. SLANTED EQUAL TO OR LESS-THAN WITH DOT INSIDE
.. |equest|   unicode:: U+0225F .. QUESTIONED EQUAL TO
.. |equivDD|  unicode:: U+02A78 .. EQUIVALENT WITH FOUR DOTS ABOVE
.. |erDot|    unicode:: U+02253 .. IMAGE OF OR APPROXIMATELY EQUAL TO
.. |esdot|    unicode:: U+02250 .. APPROACHES THE LIMIT
.. |Esim|     unicode:: U+02A73 .. EQUALS SIGN ABOVE TILDE OPERATOR
.. |esim|     unicode:: U+02242 .. MINUS TILDE
.. |fork|     unicode:: U+022D4 .. PITCHFORK
.. |forkv|    unicode:: U+02AD9 .. ELEMENT OF OPENING DOWNWARDS
.. |frown|    unicode:: U+02322 .. FROWN
.. |gap|      unicode:: U+02A86 .. GREATER-THAN OR APPROXIMATE
.. |gE|       unicode:: U+02267 .. GREATER-THAN OVER EQUAL TO
.. |gEl|      unicode:: U+02A8C .. GREATER-THAN ABOVE DOUBLE-LINE EQUAL ABOVE LESS-THAN
.. |gel|      unicode:: U+022DB .. GREATER-THAN EQUAL TO OR LESS-THAN
.. |ges|      unicode:: U+02A7E .. GREATER-THAN OR SLANTED EQUAL TO
.. |gescc|    unicode:: U+02AA9 .. GREATER-THAN CLOSED BY CURVE ABOVE SLANTED EQUAL
.. |gesdot|   unicode:: U+02A80 .. GREATER-THAN OR SLANTED EQUAL TO WITH DOT INSIDE
.. |gesdoto|  unicode:: U+02A82 .. GREATER-THAN OR SLANTED EQUAL TO WITH DOT ABOVE
.. |gesdotol| unicode:: U+02A84 .. GREATER-THAN OR SLANTED EQUAL TO WITH DOT ABOVE LEFT
.. |gesl|     unicode:: U+022DB U+0FE00 .. GREATER-THAN slanted EQUAL TO OR LESS-THAN
.. |gesles|   unicode:: U+02A94 .. GREATER-THAN ABOVE SLANTED EQUAL ABOVE LESS-THAN ABOVE SLANTED EQUAL
.. |Gg|       unicode:: U+022D9 .. VERY MUCH GREATER-THAN
.. |gl|       unicode:: U+02277 .. GREATER-THAN OR LESS-THAN
.. |gla|      unicode:: U+02AA5 .. GREATER-THAN BESIDE LESS-THAN
.. |glE|      unicode:: U+02A92 .. GREATER-THAN ABOVE LESS-THAN ABOVE DOUBLE-LINE EQUAL
.. |glj|      unicode:: U+02AA4 .. GREATER-THAN OVERLAPPING LESS-THAN
.. |gsdot|    unicode:: U+022D7 .. GREATER-THAN WITH DOT
.. |gsim|     unicode:: U+02273 .. GREATER-THAN OR EQUIVALENT TO
.. |gsime|    unicode:: U+02A8E .. GREATER-THAN ABOVE SIMILAR OR EQUAL
.. |gsiml|    unicode:: U+02A90 .. GREATER-THAN ABOVE SIMILAR ABOVE LESS-THAN
.. |Gt|       unicode:: U+0226B .. MUCH GREATER-THAN
.. |gtcc|     unicode:: U+02AA7 .. GREATER-THAN CLOSED BY CURVE
.. |gtcir|    unicode:: U+02A7A .. GREATER-THAN WITH CIRCLE INSIDE
.. |gtdot|    unicode:: U+022D7 .. GREATER-THAN WITH DOT
.. |gtquest|  unicode:: U+02A7C .. GREATER-THAN WITH QUESTION MARK ABOVE
.. |gtrarr|   unicode:: U+02978 .. GREATER-THAN ABOVE RIGHTWARDS ARROW
.. |homtht|   unicode:: U+0223B .. HOMOTHETIC
.. |lap|      unicode:: U+02A85 .. LESS-THAN OR APPROXIMATE
.. |lat|      unicode:: U+02AAB .. LARGER THAN
.. |late|     unicode:: U+02AAD .. LARGER THAN OR EQUAL TO
.. |lates|    unicode:: U+02AAD U+0FE00 .. LARGER THAN OR slanted EQUAL
.. |ldot|     unicode:: U+022D6 .. LESS-THAN WITH DOT
.. |lE|       unicode:: U+02266 .. LESS-THAN OVER EQUAL TO
.. |lEg|      unicode:: U+02A8B .. LESS-THAN ABOVE DOUBLE-LINE EQUAL ABOVE GREATER-THAN
.. |leg|      unicode:: U+022DA .. LESS-THAN EQUAL TO OR GREATER-THAN
.. |les|      unicode:: U+02A7D .. LESS-THAN OR SLANTED EQUAL TO
.. |lescc|    unicode:: U+02AA8 .. LESS-THAN CLOSED BY CURVE ABOVE SLANTED EQUAL
.. |lesdot|   unicode:: U+02A7F .. LESS-THAN OR SLANTED EQUAL TO WITH DOT INSIDE
.. |lesdoto|  unicode:: U+02A81 .. LESS-THAN OR SLANTED EQUAL TO WITH DOT ABOVE
.. |lesdotor| unicode:: U+02A83 .. LESS-THAN OR SLANTED EQUAL TO WITH DOT ABOVE RIGHT
.. |lesg|     unicode:: U+022DA U+0FE00 .. LESS-THAN slanted EQUAL TO OR GREATER-THAN
.. |lesges|   unicode:: U+02A93 .. LESS-THAN ABOVE SLANTED EQUAL ABOVE GREATER-THAN ABOVE SLANTED EQUAL
.. |lg|       unicode:: U+02276 .. LESS-THAN OR GREATER-THAN
.. |lgE|      unicode:: U+02A91 .. LESS-THAN ABOVE GREATER-THAN ABOVE DOUBLE-LINE EQUAL
.. |Ll|       unicode:: U+022D8 .. VERY MUCH LESS-THAN
.. |lsim|     unicode:: U+02272 .. LESS-THAN OR EQUIVALENT TO
.. |lsime|    unicode:: U+02A8D .. LESS-THAN ABOVE SIMILAR OR EQUAL
.. |lsimg|    unicode:: U+02A8F .. LESS-THAN ABOVE SIMILAR ABOVE GREATER-THAN
.. |Lt|       unicode:: U+0226A .. MUCH LESS-THAN
.. |ltcc|     unicode:: U+02AA6 .. LESS-THAN CLOSED BY CURVE
.. |ltcir|    unicode:: U+02A79 .. LESS-THAN WITH CIRCLE INSIDE
.. |ltdot|    unicode:: U+022D6 .. LESS-THAN WITH DOT
.. |ltlarr|   unicode:: U+02976 .. LESS-THAN ABOVE LEFTWARDS ARROW
.. |ltquest|  unicode:: U+02A7B .. LESS-THAN WITH QUESTION MARK ABOVE
.. |ltrie|    unicode:: U+022B4 .. NORMAL SUBGROUP OF OR EQUAL TO
.. |mcomma|   unicode:: U+02A29 .. MINUS SIGN WITH COMMA ABOVE
.. |mDDot|    unicode:: U+0223A .. GEOMETRIC PROPORTION
.. |mid|      unicode:: U+02223 .. DIVIDES
.. |mlcp|     unicode:: U+02ADB .. TRANSVERSAL INTERSECTION
.. |models|   unicode:: U+022A7 .. MODELS
.. |mstpos|   unicode:: U+0223E .. INVERTED LAZY S
.. |Pr|       unicode:: U+02ABB .. DOUBLE PRECEDES
.. |pr|       unicode:: U+0227A .. PRECEDES
.. |prap|     unicode:: U+02AB7 .. PRECEDES ABOVE ALMOST EQUAL TO
.. |prcue|    unicode:: U+0227C .. PRECEDES OR EQUAL TO
.. |prE|      unicode:: U+02AB3 .. PRECEDES ABOVE EQUALS SIGN
.. |pre|      unicode:: U+02AAF .. PRECEDES ABOVE SINGLE-LINE EQUALS SIGN
.. |prsim|    unicode:: U+0227E .. PRECEDES OR EQUIVALENT TO
.. |prurel|   unicode:: U+022B0 .. PRECEDES UNDER RELATION
.. |ratio|    unicode:: U+02236 .. RATIO
.. |rtrie|    unicode:: U+022B5 .. CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
.. |rtriltri| unicode:: U+029CE .. RIGHT TRIANGLE ABOVE LEFT TRIANGLE
.. |samalg|   unicode:: U+02210 .. N-ARY COPRODUCT
.. |Sc|       unicode:: U+02ABC .. DOUBLE SUCCEEDS
.. |sc|       unicode:: U+0227B .. SUCCEEDS
.. |scap|     unicode:: U+02AB8 .. SUCCEEDS ABOVE ALMOST EQUAL TO
.. |sccue|    unicode:: U+0227D .. SUCCEEDS OR EQUAL TO
.. |scE|      unicode:: U+02AB4 .. SUCCEEDS ABOVE EQUALS SIGN
.. |sce|      unicode:: U+02AB0 .. SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN
.. |scsim|    unicode:: U+0227F .. SUCCEEDS OR EQUIVALENT TO
.. |sdote|    unicode:: U+02A66 .. EQUALS SIGN WITH DOT BELOW
.. |sfrown|   unicode:: U+02322 .. FROWN
.. |simg|     unicode:: U+02A9E .. SIMILAR OR GREATER-THAN
.. |simgE|    unicode:: U+02AA0 .. SIMILAR ABOVE GREATER-THAN ABOVE EQUALS SIGN
.. |siml|     unicode:: U+02A9D .. SIMILAR OR LESS-THAN
.. |simlE|    unicode:: U+02A9F .. SIMILAR ABOVE LESS-THAN ABOVE EQUALS SIGN
.. |smid|     unicode:: U+02223 .. DIVIDES
.. |smile|    unicode:: U+02323 .. SMILE
.. |smt|      unicode:: U+02AAA .. SMALLER THAN
.. |smte|     unicode:: U+02AAC .. SMALLER THAN OR EQUAL TO
.. |smtes|    unicode:: U+02AAC U+0FE00 .. SMALLER THAN OR slanted EQUAL
.. |spar|     unicode:: U+02225 .. PARALLEL TO
.. |sqsub|    unicode:: U+0228F .. SQUARE IMAGE OF
.. |sqsube|   unicode:: U+02291 .. SQUARE IMAGE OF OR EQUAL TO
.. |sqsup|    unicode:: U+02290 .. SQUARE ORIGINAL OF
.. |sqsupe|   unicode:: U+02292 .. SQUARE ORIGINAL OF OR EQUAL TO
.. |ssmile|   unicode:: U+02323 .. SMILE
.. |Sub|      unicode:: U+022D0 .. DOUBLE SUBSET
.. |subE|     unicode:: U+02AC5 .. SUBSET OF ABOVE EQUALS SIGN
.. |subedot|  unicode:: U+02AC3 .. SUBSET OF OR EQUAL TO WITH DOT ABOVE
.. |submult|  unicode:: U+02AC1 .. SUBSET WITH MULTIPLICATION SIGN BELOW
.. |subplus|  unicode:: U+02ABF .. SUBSET WITH PLUS SIGN BELOW
.. |subrarr|  unicode:: U+02979 .. SUBSET ABOVE RIGHTWARDS ARROW
.. |subsim|   unicode:: U+02AC7 .. SUBSET OF ABOVE TILDE OPERATOR
.. |subsub|   unicode:: U+02AD5 .. SUBSET ABOVE SUBSET
.. |subsup|   unicode:: U+02AD3 .. SUBSET ABOVE SUPERSET
.. |Sup|      unicode:: U+022D1 .. DOUBLE SUPERSET
.. |supdsub|  unicode:: U+02AD8 .. SUPERSET BESIDE AND JOINED BY DASH WITH SUBSET
.. |supE|     unicode:: U+02AC6 .. SUPERSET OF ABOVE EQUALS SIGN
.. |supedot|  unicode:: U+02AC4 .. SUPERSET OF OR EQUAL TO WITH DOT ABOVE
.. |suphsol|  unicode:: U+02283 U+0002F .. SUPERSET OF, SOLIDUS
.. |suphsub|  unicode:: U+02AD7 .. SUPERSET BESIDE SUBSET
.. |suplarr|  unicode:: U+0297B .. SUPERSET ABOVE LEFTWARDS ARROW
.. |supmult|  unicode:: U+02AC2 .. SUPERSET WITH MULTIPLICATION SIGN BELOW
.. |supplus|  unicode:: U+02AC0 .. SUPERSET WITH PLUS SIGN BELOW
.. |supsim|   unicode:: U+02AC8 .. SUPERSET OF ABOVE TILDE OPERATOR
.. |supsub|   unicode:: U+02AD4 .. SUPERSET ABOVE SUBSET
.. |supsup|   unicode:: U+02AD6 .. SUPERSET ABOVE SUPERSET
.. |thkap|    unicode:: U+02248 .. ALMOST EQUAL TO
.. |thksim|   unicode:: U+0223C .. TILDE OPERATOR
.. |topfork|  unicode:: U+02ADA .. PITCHFORK WITH TEE TOP
.. |trie|     unicode:: U+0225C .. DELTA EQUAL TO
.. |twixt|    unicode:: U+0226C .. BETWEEN
.. |Vbar|     unicode:: U+02AEB .. DOUBLE UP TACK
.. |vBar|     unicode:: U+02AE8 .. SHORT UP TACK WITH UNDERBAR
.. |vBarv|    unicode:: U+02AE9 .. SHORT UP TACK ABOVE SHORT DOWN TACK
.. |VDash|    unicode:: U+022AB .. DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
.. |Vdash|    unicode:: U+022A9 .. FORCES
.. |vDash|    unicode:: U+022A8 .. TRUE
.. |vdash|    unicode:: U+022A2 .. RIGHT TACK
.. |Vdashl|   unicode:: U+02AE6 .. LONG DASH FROM LEFT MEMBER OF DOUBLE VERTICAL
.. |veebar|   unicode:: U+022BB .. XOR
.. |vltri|    unicode:: U+022B2 .. NORMAL SUBGROUP OF
.. |vprop|    unicode:: U+0221D .. PROPORTIONAL TO
.. |vrtri|    unicode:: U+022B3 .. CONTAINS AS NORMAL SUBGROUP
.. |Vvdash|   unicode:: U+022AA .. TRIPLE VERTICAL BAR RIGHT TURNSTILE
