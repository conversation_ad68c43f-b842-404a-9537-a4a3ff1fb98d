If you use Astropy for work/research presented in a publication (whether
directly, or as a dependency to another package), we recommend and encourage
the following acknowledgment:

  This research made use of Astropy, a community-developed core Python package
  for Astronomy (Astropy Collaboration, 2018).

where (Astropy Collaboration, 2018) is a citation to this paper:

  https://ui.adsabs.harvard.edu/abs/2018AJ....156..123T

An earlier paper is also available describing the status of the package at
the time of v0.2. If you have used Astropy for a long time, you are
encouraged to acknowledge both papers:

  This research made use of Astropy, a community-developed core Python package
  for Astronomy (Astropy Collaboration, 2013, 2018).

where (Astropy Collaboration, 2013) is a citation to this paper:

  https://ui.adsabs.harvard.edu/abs/2013A%26A...558A..33A

We encourage you to also include citations to the papers in the main text
wherever appropriate.


Recommended BibTeX entries for the above citations are:

@ARTICLE{2018AJ....156..123T,
   author = {{The Astropy Collaboration} and {<PERSON><PERSON><PERSON><PERSON><PERSON>}, <PERSON><PERSON>~<PERSON><PERSON> and {<PERSON>p{\H o}cz}, <PERSON><PERSON>~<PERSON><PERSON> and
	{<PERSON>{\"u}nther}, <PERSON><PERSON>~<PERSON><PERSON> and {<PERSON>}, <PERSON><PERSON>~<PERSON><PERSON> and {<PERSON>}, S.~M. and
	{<PERSON><PERSON>l}, <PERSON>. and {<PERSON><PERSON>}, <PERSON>.~L. and {<PERSON>}, <PERSON>.~W. and {<PERSON>}, <PERSON>. and
	{<PERSON>ins<PERSON>}, A. and {<PERSON>}, <PERSON>.~T. and {<PERSON>}, L.~<PERSON>. and
	{P{\'e}rez-Su{\'a}rez}, D. and {de <PERSON>-<PERSON>rro}, <PERSON>. and {<PERSON> <PERSON>tributors}, (. and
	{<PERSON>d<PERSON>}, T.~L. and {Cruz}, K.~L. and {Robitaille}, T.~P. and
	{Tollerud}, E.~J. and {Coordination Committee}, (. and {Ardelean}, C. and
	{Babej}, T. and {Bach}, Y.~P. and {Bachetti}, M. and {Bakanov}, A.~V. and
	{Bamford}, S.~P. and {Barentsen}, G. and {Barmby}, P. and {Baumbach}, A. and
	{Berry}, K.~L. and {Biscani}, F. and {Boquien}, M. and {Bostroem}, K.~A. and
	{Bouma}, L.~G. and {Brammer}, G.~B. and {Bray}, E.~M. and {Breytenbach}, H. and
	{Buddelmeijer}, H. and {Burke}, D.~J. and {Calderone}, G. and
	{Cano Rodr{\'{\i}}guez}, J.~L. and {Cara}, M. and {Cardoso}, J.~V.~M. and
	{Cheedella}, S. and {Copin}, Y. and {Corrales}, L. and {Crichton}, D. and
	{D{\rsquo}Avella}, D. and {Deil}, C. and {Depagne}, {\'E}. and
	{Dietrich}, J.~P. and {Donath}, A. and {Droettboom}, M. and
	{Earl}, N. and {Erben}, T. and {Fabbro}, S. and {Ferreira}, L.~A. and
	{Finethy}, T. and {Fox}, R.~T. and {Garrison}, L.~H. and {Gibbons}, S.~L.~J. and
	{Goldstein}, D.~A. and {Gommers}, R. and {Greco}, J.~P. and
	{Greenfield}, P. and {Groener}, A.~M. and {Grollier}, F. and
	{Hagen}, A. and {Hirst}, P. and {Homeier}, D. and {Horton}, A.~J. and
	{Hosseinzadeh}, G. and {Hu}, L. and {Hunkeler}, J.~S. and {Ivezi{\'c}}, {\v Z}. and
	{Jain}, A. and {Jenness}, T. and {Kanarek}, G. and {Kendrew}, S. and
	{Kern}, N.~S. and {Kerzendorf}, W.~E. and {Khvalko}, A. and
	{King}, J. and {Kirkby}, D. and {Kulkarni}, A.~M. and {Kumar}, A. and
	{Lee}, A. and {Lenz}, D. and {Littlefair}, S.~P. and {Ma}, Z. and
	{Macleod}, D.~M. and {Mastropietro}, M. and {McCully}, C. and
	{Montagnac}, S. and {Morris}, B.~M. and {Mueller}, M. and {Mumford}, S.~J. and
	{Muna}, D. and {Murphy}, N.~A. and {Nelson}, S. and {Nguyen}, G.~H. and
	{Ninan}, J.~P. and {N{\"o}the}, M. and {Ogaz}, S. and {Oh}, S. and
	{Parejko}, J.~K. and {Parley}, N. and {Pascual}, S. and {Patil}, R. and
	{Patil}, A.~A. and {Plunkett}, A.~L. and {Prochaska}, J.~X. and
	{Rastogi}, T. and {Reddy Janga}, V. and {Sabater}, J. and {Sakurikar}, P. and
	{Seifert}, M. and {Sherbert}, L.~E. and {Sherwood-Taylor}, H. and
	{Shih}, A.~Y. and {Sick}, J. and {Silbiger}, M.~T. and {Singanamalla}, S. and
	{Singer}, L.~P. and {Sladen}, P.~H. and {Sooley}, K.~A. and
	{Sornarajah}, S. and {Streicher}, O. and {Teuben}, P. and {Thomas}, S.~W. and
	{Tremblay}, G.~R. and {Turner}, J.~E.~H. and {Terr{\'o}n}, V. and
	{van Kerkwijk}, M.~H. and {de la Vega}, A. and {Watkins}, L.~L. and
	{Weaver}, B.~A. and {Whitmore}, J.~B. and {Woillez}, J. and
	{Zabalza}, V. and {Contributors}, (.},
    title = "{The Astropy Project: Building an Open-science Project and Status of the v2.0 Core Package}",
  journal = {\aj},
archivePrefix = "arXiv",
   eprint = {1801.02634},
 primaryClass = "astro-ph.IM",
 keywords = {methods: data analysis, methods: miscellaneous, methods: statistical, reference systems },
     year = 2018,
    month = sep,
   volume = 156,
      eid = {123},
    pages = {123},
      doi = {10.3847/1538-3881/aabc4f},
   adsurl = {https://ui.adsabs.harvard.edu/abs/2018AJ....156..123T},
  adsnote = {Provided by the SAO/NASA Astrophysics Data System}
}

@ARTICLE{2013A&A...558A..33A,
   author = {{Astropy Collaboration} and {Robitaille}, T.~P. and {Tollerud}, E.~J. and
    {Greenfield}, P. and {Droettboom}, M. and {Bray}, E. and {Aldcroft}, T. and
    {Davis}, M. and {Ginsburg}, A. and {Price-Whelan}, A.~M. and
    {Kerzendorf}, W.~E. and {Conley}, A. and {Crighton}, N. and
    {Barbary}, K. and {Muna}, D. and {Ferguson}, H. and {Grollier}, F. and
    {Parikh}, M.~M. and {Nair}, P.~H. and {Unther}, H.~M. and {Deil}, C. and
    {Woillez}, J. and {Conseil}, S. and {Kramer}, R. and {Turner}, J.~E.~H. and
    {Singer}, L. and {Fox}, R. and {Weaver}, B.~A. and {Zabalza}, V. and
    {Edwards}, Z.~I. and {Azalee Bostroem}, K. and {Burke}, D.~J. and
    {Casey}, A.~R. and {Crawford}, S.~M. and {Dencheva}, N. and
    {Ely}, J. and {Jenness}, T. and {Labrie}, K. and {Lian Lim}, P. and
    {Pierfederici}, F. and {Pontzen}, A. and {Ptak}, A. and {Refsdal}, B. and
    {Servillat}, M. and {Streicher}, O.},
    title = "{Astropy: A community Python package for astronomy}",
  journal = {\aap},
 keywords = {methods: data analysis, methods: miscellaneous, virtual observatory tools},
     year = 2013,
    month = oct,
   volume = 558,
      eid = {A33},
    pages = {A33},
      doi = {10.1051/0004-6361/201322068},
   adsurl = {https://ui.adsabs.harvard.edu/abs/2013A%26A...558A..33A},
  adsnote = {Provided by the SAO/NASA Astrophysics Data System}
}
