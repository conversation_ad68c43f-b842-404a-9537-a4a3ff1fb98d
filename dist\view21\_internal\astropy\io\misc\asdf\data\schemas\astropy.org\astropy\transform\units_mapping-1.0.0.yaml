%YAML 1.1
---
$schema: "http://stsci.edu/schemas/yaml-schema/draft-01"
id: "http://astropy.org/schemas/astropy/transform/units_mapping-1.0.0"
tag: "tag:astropy.org:astropy/transform/units_mapping-1.0.0"

title: |
  Mapper that operates on the units of the input.

description: |
  This transform operates on the units of the input, first converting to
  the expected input units, then assigning replacement output units without
  further conversion.

examples:
  -
    - Assign units of seconds to dimensionless input.
    - |
      !<tag:astropy.org:astropy/transform/units_mapping-1.0.0>
        unit_inputs:
          - name: x
            unit: !unit/unit-1.0.0
        unit_outputs:
          - name: x
            unit: !unit/unit-1.0.0 s
  -
    - Convert input to meters, then assign dimensionless units.
    - |
      !<tag:astropy.org:astropy/transform/units_mapping-1.0.0>
        unit_inputs:
          - name: x
            unit: !unit/unit-1.0.0 m
        unit_outputs:
          - name: x
            unit: !unit/unit-1.0.0

  -
    - Convert input to meters, then drop units entirely.
    - |
      !<tag:astropy.org:astropy/transform/units_mapping-1.0.0>
        unit_inputs:
          - name: x
            unit: !unit/unit-1.0.0 m
        unit_outputs:
          - name: x

  -
    - Accept any units, then replace with meters.
    - |
      !<tag:astropy.org:astropy/transform/units_mapping-1.0.0>
        unit_inputs:
          - name: x
        unit_outputs:
          - name: x
            unit: !unit/unit-1.0.0 m

allOf:
  - $ref: "http://stsci.edu/schemas/asdf/transform/transform-1.2.0"
  - type: object
    properties:
      unit_inputs:
        description: |
          Array of input configurations.
        type: array
        items:
          $ref: "#/definitions/value_configuration"
      unit_outputs:
        description: |
          Array of output configurations.
        type: array
        items:
          $ref: "#/definitions/value_configuration"
    required: [unit_inputs, unit_outputs]

definitions:
  value_configuration:
    description: |
      Configuration of a single model value (input or output).
    type: object
    properties:
      name:
        description: |
          Value name.
        type: string
      unit:
        description: |
          Expected unit.
        $ref: "http://stsci.edu/schemas/asdf/unit/unit-1.0.0"
      equivalencies:
        description: |
          Equivalencies to apply when converting value to expected unit.
        $ref: "http://astropy.org/schemas/astropy/units/equivalency-1.0.0"
      allow_dimensionless:
        description: |
          Allow this value to receive dimensionless data.
        type: boolean
        default: false
    required: [name]
...
