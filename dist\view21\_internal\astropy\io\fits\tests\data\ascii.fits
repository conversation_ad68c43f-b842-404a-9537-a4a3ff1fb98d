SIMPLE  =                    T / file does conform to FITS standard             BITPIX  =                   16 / number of bits per data pixel                  NAXIS   =                    0 / number of data axes                            EXTEND  =                    T / FITS dataset may contain extensions            COMMENT   FITS (Flexible Image Transport System) format defined in Astronomy andCOMMENT   Astrophysics Supplement Series v44/p363, v44/p371, v73/p359, v73/p365.COMMENT   Contact the NASA Science Office of Standards and Technology for the   COMMENT   FITS Definition document #100 and other FITS information.             END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             XTENSION= 'TABLE   '           / ASCII table extension                          BITPIX  =                    8 / 8-bit ASCII characters                         NAXIS   =                    2 / 2-dimensional ASCII table                      NAXIS1  =                   16 / width of table in characters                   NAXIS2  =                    5                                                  PCOUNT  =                    0 / no group parameters (required keyword)         GCOUNT  =                    1 / one data group (required)                      TFIELDS =                    2                                                  TTYPE1  = 'a       '           / label for field   1                            TBCOL1  =                    1 / beginning column of field   1                  TFORM1  = 'E10.4   '           / Fortran-77 format of field                     TUNIT1  = 'pixels  '           / physical unit of field                         TTYPE2  = 'b       '           / label for field   2                            TBCOL2  =                   12 / beginning column of field   2                  TFORM2  = 'I5      '           / Fortran-77 format of field                     TUNIT2  = 'counts  '           / physical unit of field                         TNULL1  = '*       '           / string representing an undefined value         TNULL2  = '*       '           / string representing an undefined value         HISTORY   This FITS file was created by the FCREATE task.                       HISTORY   fcreate3.0d at 23/4/97 9:21:56.                                       END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             .10123E+02    37.52000E+01    23.15610E+02    17*          *    .34500E+03   345                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                