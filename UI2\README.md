# 拉曼光谱分析系统

这是一个基于 PyQt6 的拉曼光谱分析系统前端界面。

## 功能特点

- 模块化设计，包含菜单栏、侧边栏和主显示区域
- 支持光谱数据的加载、显示和处理
- 提供多种算法处理功能：基线校准、数据平滑、归一化、寻峰、还原
- 支持测量点规划和实时测量控制
- 支持中英文界面切换
- 支持光谱图的缩放和平移操作

## 系统要求

- Python 3.8 或更高版本
- PyQt6
- NumPy
- Requests

## 安装

1. 克隆项目到本地：
```bash
git clone [项目地址]
cd [项目目录]
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 创建图标目录：
```bash
mkdir ui.64
```
将所需的图标文件放入 `ui.64` 目录。

## 运行

```bash
python main.py
```

## 项目结构

- `main.py`: 主程序入口
- `menu_bar.py`: 顶部菜单栏实现
- `sidebar.py`: 侧边栏实现
- `editor.py`: 主显示区域实现
- `backend_api.py`: 后端 API 接口实现

## 后端接口

系统通过 `backend_api.py` 与后端服务进行通信，主要接口包括：

- 光谱数据加载
- 算法处理
- 测量控制
- 仪器参数设置
- 数据保存

## 开发说明

1. 界面定制
   - 修改 `menu_bar.py` 可以自定义菜单项
   - 修改 `sidebar.py` 可以自定义侧边栏功能
   - 修改 `editor.py` 可以自定义主显示区域

2. 后端集成
   - 在 `backend_api.py` 中实现与后端的具体通信逻辑
   - 根据实际后端 API 修改接口地址和参数

3. 国际化
   - 在 `menu_bar.py` 中实现语言切换功能
   - 在 `sidebar.py` 和 `editor.py` 中实现界面文本的更新

## 注意事项

1. 确保后端服务正常运行
2. 检查 `ui.64` 目录中的图标文件是否完整
3. 根据实际需求修改后端 API 地址

## 许可证

[许可证信息] 