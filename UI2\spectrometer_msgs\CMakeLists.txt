cmake_minimum_required(VERSION 3.8)
project(spectrometer_msgs)
# 查找依赖
find_package(ament_cmake REQUIRED)
find_package(std_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)
# 声明ROS接口文件
set(msg_files
  "msg/SpectrometerData.msg"
  "msg/ColorData.msg"
)
set(srv_files
  "srv/SetIntegrationTime.srv"
  "srv/SetExternalIO.srv"
)
# 生成接口
rosidl_generate_interfaces(${PROJECT_NAME}
  ${msg_files}
  ${srv_files}
  DEPENDENCIES std_msgs
)
# 导出依赖
ament_export_dependencies(rosidl_default_runtime)
ament_package()
