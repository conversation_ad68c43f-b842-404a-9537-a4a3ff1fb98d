#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import matplotlib

import matplotlib.pyplot as plt

matplotlib.use('TkAgg')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'Noto Sans CJK', 'Noto Sans CJK JP', 'Noto Sans CJK TC', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


from matplotlib.animation import FuncAnimation
import matplotlib.font_manager as fm
from matplotlib.widgets import Button
from scipy.signal import savgol_filter
from scipy.sparse import diags
from scipy import sparse
from scipy.sparse.linalg import spsolve
from scipy.optimize import curve_fit
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import time
import os

# 导入新的文件读取器
try:
    from spectrum_file_reader import SpectrumFileReader
except ImportError:
    # 如果导入失败，尝试相对导入
    import sys
    sys.path.append(os.path.dirname(__file__))
    from spectrum_file_reader import SpectrumFileReader




class SpectrumProcessor:
    """光谱数据处理类"""
    
    @staticmethod
    def smooth(wavelength, intensity, window_length=17, polyorder=3):
        """
        平滑处理 - 使用Savitzky-Golay滤波
        
        参数:
            wavelength: 波长数组
            intensity: 强度数组
            window_length: 窗口长度，必须为奇数且大于polyorder
            polyorder: 多项式阶数
        返回:
            处理后的强度数组
        """
        try:
            # 确保window_length为奇数且合理
            if window_length % 2 == 0:
                window_length += 1
            if window_length > len(intensity) // 2:
                window_length = min(15, len(intensity) // 3)
                if window_length % 2 == 0:
                    window_length -= 1
            if window_length < polyorder + 2:
                window_length = polyorder + 2
                if window_length % 2 == 0:
                    window_length += 1
                
            return savgol_filter(intensity, window_length, polyorder)
        except Exception as e:
            print(f"平滑处理错误: {e}")
            return intensity.copy()
    
    @staticmethod
    def normalize(wavelength, intensity, method='max'):
        """
        归一化处理 - 简单最大值归一化
        
        参数:
            wavelength: 波长数组
            intensity: 强度数组
            method: 归一化方法，'max'为最大值归一化
        返回:
            处理后的强度数组
        """
        try:
            # 检查数据是否有效
            if np.all(intensity == 0):
                return intensity.copy()
            
            # 首先进行基线校正，确保所有数据为正
            # 找到最小值，如果是负数则整体上移
            min_val = np.min(intensity)
            adjusted_intensity = intensity.copy()
            if min_val < 0:
                adjusted_intensity -= min_val  # 移除负值
            
            # 最大值归一化
            max_val = np.max(adjusted_intensity)
            if max_val <= 0:
                return intensity.copy()
                
            # 执行归一化 - 简单地除以最大值
            normalized = adjusted_intensity / max_val
            
            return normalized
            
        except Exception as e:
            print(f"归一化处理错误: {e}")
            return intensity.copy()
    
    @staticmethod
    #def baseline_correction(wavelength, intensity, lam=1e5, p=0.00663, niter=10):
    def baseline_correction(wavelength, intensity, lam=1e5, p=0.01, niter=20):
        """
        基线校正 - 使用Asymmetric Least Squares (ALS)算法
        
        参数:
            wavelength: 波长数组
            intensity: 强度数组
            lam: 平滑参数
            p: 非对称权重参数
            niter: 迭代次数
        返回:
            处理后的强度数组
        """
        try:
            L = len(intensity)
            
            # 防止输入数据过少
            if L < 10:
                return intensity.copy()
                
            # 创建差分矩阵。
            D = diags([1, -2, 1], [0, -1, -2], shape=(L, L-2))
            #D = sparse.diags([1, -2, 1], [0, -1, -2], shape=(L, L - 2)).tocsc()

            # 初始化权重
            w = np.ones(L)
            
            # 迭代求解
            for i in range(niter):
                W = diags(w, 0)
                Z = W + lam * D.dot(D.transpose())
                z = spsolve(Z, w * intensity)
                w = p * (intensity > z) + (1-p) * (intensity < z)
            corrected = intensity - z
            corrected[corrected < 0] = 0  # 将负值设为0
            # 返回校正后的数据
            return corrected
        except Exception as e:
            print(f"基线校正错误: {e}")
            return intensity.copy()


class SpectrometerViewer:
    def __init__(self):
        # 选择文件
        root = tk.Tk()
        root.withdraw()
        file_path = filedialog.askopenfilename(
            title='请选择拉曼光谱文件',
            filetypes=[('光谱文件', '*.csv *.txt *.nod'), ('CSV文件', '*.csv'), ('TXT文件', '*.txt'), ('NOD文件', '*.nod'), ('所有文件', '*.*')]
        )
        if not file_path:
            print('未选择文件，程序退出')
            exit(0)
        
        # 使用新的文件读取器读取文件
        try:
            reader = SpectrumFileReader()
            file_info, spectrum_data = reader.read_file(file_path)
            
            # 保存文件信息
            self.file_info = file_info
            
            # 提取光谱数据
            self.wavelength = spectrum_data[:, 0]  # 拉曼位移
            self.intensity = spectrum_data[:, 1]   # 强度
            self.intensity_original = self.intensity.copy()
            self.intensity_raw = self.intensity.copy()
            
            # 显示文件信息
            print("文件信息:")
            print(reader.get_file_info_display())
            
        except Exception as e:
            print(f'文件读取失败: {e}')
            messagebox.showerror('错误', f'文件读取失败: {e}')
            exit(1)
            
        print(f"光谱数据形状: {spectrum_data.shape}")
        print(f"拉曼位移范围: {np.min(self.wavelength):.2f} - {np.max(self.wavelength):.2f}")
        print(f"强度范围: {np.min(self.intensity):.2f} - {np.max(self.intensity):.2f}")
        
        self.integration_time = 0
        self.peak_wavelength = 0
        self.peak_intensity = 0
        self.data_lock = threading.Lock()
        self.new_data_available = True
        self.processor = SpectrumProcessor()
        self.processing_mode = "none"
        self.is_smoothed = False
        self.is_normalized = False
        self.is_baseline_corrected = False
        self.max_history = 5
        self.intensity_history = []
        self.timestamp_history = []
        self.display_raw = True
        self.refresh_rate = 1.0
        self.y_min = 0.0
        self.y_max = np.max(self.intensity) * 1.1 if np.max(self.intensity) > 0 else 2000.0
        self.fixed_y_axis = False
        # 初始化历史数据
        self.intensity_history.append(self.intensity.copy())
        self.timestamp_history.append(0)
        print('拉曼光谱数据加载完成')

    def spectrum_callback(self, msg):
        """接收光谱数据的回调函数"""
        with self.data_lock:
            # 更新数据
            self.wavelength = np.array(msg.wavelength)
            self.intensity = np.array(msg.intensity)
            self.intensity_raw = np.array(msg.intensity_raw)
            
            # 保存原始数据副本，用于还原
            self.intensity_original = self.intensity.copy()
            
            self.integration_time = msg.integration_time
            self.peak_wavelength = msg.peak_wavelength
            self.peak_intensity = msg.peak_intensity
            
            # 重置处理状态
            self.is_smoothed = False
            self.is_normalized = False
            self.is_baseline_corrected = False
            
            # 根据当前处理模式应用相应的处理
            if self.processing_mode == "smooth":
                self.intensity = self.processor.smooth(self.wavelength, self.intensity)
                self.is_smoothed = True
            elif self.processing_mode == "normalize":
                self.intensity = self.processor.normalize(self.wavelength, self.intensity)
                self.is_normalized = True
            elif self.processing_mode == "baseline":
                self.intensity = self.processor.baseline_correction(self.intensity, len(self.intensity))
                self.is_baseline_corrected = True
            # 如果模式是none，则保持原始数据不变
            
            # 更新峰值（如果数据已处理）
            if self.processing_mode != "none":
                peak_idx = np.argmax(self.intensity)
                self.peak_wavelength = self.wavelength[peak_idx]
                self.peak_intensity = self.intensity[peak_idx]
            
            # 添加到历史数据，仅保存最近的几条
            self.intensity_history.append(self.intensity.copy())
            self.timestamp_history.append(time.time())
            
            if len(self.intensity_history) > self.max_history:
                self.intensity_history.pop(0)
                self.timestamp_history.pop(0)
                
            self.new_data_available = True
            
        print(f'收到新数据 - 峰值: {self.peak_wavelength:.1f}nm @ {self.peak_intensity:.1f}')
    
    def apply_smooth(self, event):
        """应用平滑处理为当前处理模式"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
            
            if self.processing_mode == "smooth":
                print("平滑处理模式已激活")
                return
                
            # 设置当前处理模式为平滑
            self.processing_mode = "smooth"
            
            # 应用平滑处理到当前数据
            self.intensity = self.processor.smooth(self.wavelength, self.intensity_original.copy())
            self.is_smoothed = True
            self.is_normalized = False
            self.is_baseline_corrected = False
            
            # 更新峰值
            peak_idx = np.argmax(self.intensity)
            self.peak_wavelength = self.wavelength[peak_idx]
            self.peak_intensity = self.intensity[peak_idx]
            
            # 更新历史数据
            self.intensity_history = []
            self.timestamp_history = []
            self.intensity_history.append(self.intensity.copy())
            self.timestamp_history.append(time.time())
            
            self.new_data_available = True
            
        print("激活平滑处理模式 - 所有后续数据将应用平滑处理")
    
    def apply_normalize(self, event):
        """应用归一化处理为当前处理模式"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
                
            if self.processing_mode == "normalize":
                print("归一化处理模式已激活")
                return
                
            # 设置当前处理模式为归一化
            self.processing_mode = "normalize"
            
            # 应用归一化处理到当前数据
            self.intensity = self.processor.normalize(self.wavelength, self.intensity_original.copy())
            self.is_smoothed = False
            self.is_normalized = True
            self.is_baseline_corrected = False
            
            # 更新峰值 - 归一化后峰值范围在0-1
            peak_idx = np.argmax(self.intensity)
            self.peak_wavelength = self.wavelength[peak_idx]
            self.peak_intensity = self.intensity[peak_idx]
            
            # 更新历史数据
            self.intensity_history = []
            self.timestamp_history = []
            self.intensity_history.append(self.intensity.copy())
            self.timestamp_history.append(time.time())
            
            self.new_data_available = True
            
            # 在归一化模式下不需要调整Y轴，将在update_plot中处理
            
        print("激活归一化处理模式 - 所有后续数据将应用最大值归一化处理(0-1范围)")
    
    def apply_baseline_correction(self, event):
        """应用基线校正为当前处理模式"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
                
            if self.processing_mode == "baseline":
                print("基线校正处理模式已激活")
                return
                
            # 设置当前处理模式为基线校正
            self.processing_mode = "baseline"
            
            # 应用基线校正到当前数据
            self.intensity = self.processor.baseline_correction(self.wavelength, self.intensity_original.copy())
            self.is_smoothed = False
            self.is_normalized = False
            self.is_baseline_corrected = True
            
            # 更新峰值
            peak_idx = np.argmax(self.intensity)
            self.peak_wavelength = self.wavelength[peak_idx]
            self.peak_intensity = self.intensity[peak_idx]
            
            # 更新历史数据
            self.intensity_history = []
            self.timestamp_history = []
            self.intensity_history.append(self.intensity.copy())
            self.timestamp_history.append(time.time())
            
            self.new_data_available = True
            
        print("激活基线校正模式 - 所有后续数据将应用基线校正处理")
    
    def apply_restore(self, event):
        """还原原始数据模式"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0 or self.intensity_original is None:
                return
            
            # 设置当前处理模式为无处理
            self.processing_mode = "none"
            
            # 还原为原始数据
            self.intensity = self.intensity_original.copy()
            
            # 重置处理状态
            self.is_smoothed = False
            self.is_normalized = False
            self.is_baseline_corrected = False
            
            # 重置线条样式
            self.line_processed.set_linewidth(2.0)
            self.line_processed.set_alpha(1.0)
            self.line_raw.set_linewidth(1.0)
            self.line_raw.set_alpha(0.6)
            
            # 更新峰值
            peak_idx = np.argmax(self.intensity)
            self.peak_wavelength = self.wavelength[peak_idx]
            self.peak_intensity = self.intensity[peak_idx]
            
            # 更新历史数据
            self.intensity_history = []
            self.timestamp_history = []
            self.intensity_history.append(self.intensity.copy())
            self.timestamp_history.append(time.time())
            
            self.new_data_available = True
            
        print("激活原始数据模式 - 所有后续数据将保持原始状态")
    
    def run_plot_window(self):
        """运行交互式绘图窗口的函数"""
        # 创建交互式图表
        plt.ion()  # 打开交互模式
        self.fig = plt.figure(figsize=(10, 7))  # 稍微增加高度以放置按钮
        
        # 添加绘图区域和按钮区域
        self.ax = self.fig.add_axes([0.1, 0.2, 0.8, 0.7])  # [left, bottom, width, height]
        
        # 为数据曲线创建线对象
        self.line_processed, = self.ax.plot([], [], 'b-', linewidth=2, label='处理后光谱')
        if self.display_raw:
            self.line_raw, = self.ax.plot([], [], 'r--', linewidth=2, alpha=0.6, label='原始光谱')
        
        # 为历史数据创建线对象列表
        self.history_lines = []
        for i in range(self.max_history):
            line, = self.ax.plot([], [], 'g-', linewidth=2, alpha=0.3)
            self.history_lines.append(line)
        
        # 创建峰值标记
        self.peak_point, = self.ax.plot([], [], 'ro', markersize=6)
        self.peak_text = self.ax.text(0, 0, '', fontsize=9)
        
        # 创建标题和标签
        #self.title = self.ax.set_title('拉曼光谱 (等待数据...)')
        # 根据当前坐标系统设置正确的标签
        if hasattr(self, '_use_pixel_coordinates') and getattr(self, '_use_pixel_coordinates', False):
            self.ax.set_xlabel('像素坐标')
        else:
            self.ax.set_xlabel('拉曼位移 (cm-1)')
        self.ax.set_ylabel('强度 (a.u.)')
        self.ax.grid(True, linestyle='--', alpha=0.7)
        self.ax.legend(loc='upper right')
        
        # 添加鼠标跟踪功能
        self.cursor_annotation = self.ax.annotate('', xy=(0, 0), xytext=(10, 10),
                                   textcoords='offset points',
                                   bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                                   arrowprops=dict(arrowstyle='->'),
                                   visible=False)
        
        self.cursor_point, = self.ax.plot([], [], 'go', markersize=4, alpha=0.7, visible=False)
        
        # 连接鼠标移动事件
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        
        # 如果使用固定Y轴范围，则立即设置
        if self.fixed_y_axis:
            self.ax.set_ylim(self.y_min, self.y_max)
            print(f'使用固定Y轴范围: {self.y_min} - {self.y_max}')
        
        # 添加按钮
        smooth_ax = self.fig.add_axes([0.15, 0.05, 0.15, 0.05])
        normalize_ax = self.fig.add_axes([0.35, 0.05, 0.15, 0.05])
        baseline_ax = self.fig.add_axes([0.55, 0.05, 0.15, 0.05])
        restore_ax = self.fig.add_axes([0.75, 0.05, 0.15, 0.05])
        
        self.smooth_button = Button(smooth_ax, '平滑')
        self.normalize_button = Button(normalize_ax, '归一化')
        self.baseline_button = Button(baseline_ax, '基线校正')
        self.restore_button = Button(restore_ax, '还原')
        
        # 绑定按钮事件
        self.smooth_button.on_clicked(self.apply_smooth)
        self.normalize_button.on_clicked(self.apply_normalize)
        self.baseline_button.on_clicked(self.apply_baseline_correction)
        self.restore_button.on_clicked(self.apply_restore)
        
        # 创建动画更新函数
        def update_plot(_):
            if not self.new_data_available:
                return self.line_processed, 
                
            with self.data_lock:
                if self.wavelength is None or len(self.wavelength) == 0:
                    return self.line_processed,
                
                # 更新处理后的光谱线
                self.line_processed.set_data(self.wavelength, self.intensity)
                
                # 更新原始光谱线
                if self.display_raw:
                    self.line_raw.set_data(self.wavelength, self.intensity_raw)
                
                # 更新历史数据线
                for i, (line, hist_data) in enumerate(zip(self.history_lines, self.intensity_history[:-1])):
                    alpha = 0.1 + 0.2 * (i / max(1, len(self.intensity_history) - 1))
                    line.set_data(self.wavelength, hist_data)
                    line.set_alpha(alpha)
                
                # 更新峰值点
                peak_idx = np.argmax(self.intensity)
                peak_x = self.wavelength[peak_idx]
                peak_y = self.intensity[peak_idx]
                self.peak_point.set_data([peak_x], [peak_y])
                
                # 更新峰值文本
                self.peak_text.set_position((peak_x + 10, peak_y))
                # 根据当前坐标系统设置正确的标签
                if hasattr(self, '_use_pixel_coordinates') and getattr(self, '_use_pixel_coordinates', False):
                    # 像素坐标
                    x_label = "像素"
                    x_unit = ""
                else:
                    # 拉曼位移（默认）
                    x_label = "拉曼位移"
                    x_unit = "cm-1"
                self.peak_text.set_text(f'{peak_x:.1f}{x_unit}\n{peak_y:.1f}')
                
                # 自动调整坐标轴范围
                visible_indices = np.where((self.wavelength >= 380) & (self.wavelength <= 780))[0]
                if len(visible_indices) > 10:
                    # 如果有足够的可见光数据点，就显示可见光范围
                    self.ax.set_xlim(380, 780)
                    #self.ax.set_xticks(np.arange(380, 781))
                else:
                    # 否则显示整个范围并稍微扩展一点
                    min_x = np.min(self.wavelength) * 0.95
                    max_x = np.max(self.wavelength) * 1.05
                    self.ax.set_xlim(min_x, max_x)
                
                # 为归一化模式特别优化Y轴显示范围
                if self.processing_mode == "normalize" and not self.fixed_y_axis:
                    # 归一化数据范围是0-1，需要专门设置Y轴显示范围
                    # 设置两个Y轴：左侧为归一化数据(0-1)，右侧为原始数据
                    if not hasattr(self, 'ax2'):
                        self.ax2 = self.ax.twinx()  # 创建一个共享x轴的新y轴
                        self.ax2.set_ylabel('原始强度 (a.u.)')
                    
                    # 显示归一化数据的Y轴(左侧)
                    self.ax.set_ylim(0, 1.2)  # 留出一点空间在顶部
                    self.ax.set_ylabel('归一化强度 (0-1)')
                    
                    # 显示原始数据的Y轴(右侧)
                    raw_max = np.max(self.intensity_raw) if np.any(self.intensity_raw) else 1.0
                    if raw_max > 0:
                        self.ax2.set_ylim(0, raw_max * 1.1)
                    self.ax2.set_visible(True)
                else:
                    # 非归一化模式，隐藏第二个Y轴
                    if hasattr(self, 'ax2'):
                        self.ax2.set_visible(False)
                    
                    # 恢复标准Y轴标签
                    self.ax.set_ylabel('强度 (a.u.)')
                    
                    if not self.fixed_y_axis:
                        all_data = [self.intensity]
                        if self.display_raw:
                            all_data.append(self.intensity_raw)
                        all_data.extend(self.intensity_history)
                        
                        max_y = max([np.max(d) if len(d) > 0 else 0 for d in all_data if d is not None]) * 1.1
                        self.ax.set_ylim(0, max_y)
                
                # 如果有数据超出Y轴范围，输出警告日志
                if self.fixed_y_axis:
                    max_intensity = max(np.max(self.intensity), np.max(self.intensity_raw) if self.display_raw else 0)
                    if max_intensity > self.y_max:
                        print(f'数据超出Y轴范围: {max_intensity:.1f} > {self.y_max:.1f}')
                print(len(self.wavelength))

                # 自动缩放x轴和y轴，让数据尽量占满屏幕
                if len(self.wavelength) > 0 and len(self.intensity) > 0:
                    min_x = np.min(self.wavelength)
                    max_x = np.max(self.wavelength)
                    x_margin = (max_x - min_x) * 0.02 if max_x > min_x else 1
                    self.ax.set_xlim(min_x - x_margin, max_x + x_margin)

                    min_y = np.min(self.intensity)
                    max_y = np.max(self.intensity)
                    y_margin = (max_y - min_y) * 0.05 if max_y > min_y else 1
                    self.ax.set_ylim(min_y - y_margin, max_y + y_margin)
                
                self.new_data_available = False
                
            return self.line_processed,
        
        # 创建动画
        self.ani = FuncAnimation(
            self.fig, update_plot, interval=1000/self.refresh_rate,
            blit=False, cache_frame_data=False
        )
        
        # 显示图表
        plt.show(block=True)  # 阻塞直到窗口关闭

    def on_mouse_move(self, event):
        """处理鼠标移动事件，显示鼠标位置对应的坐标和强度值"""
        if event.inaxes != self.ax:
            # 如果鼠标不在坐标轴内，隐藏注释
            self.cursor_annotation.set_visible(False)
            self.cursor_point.set_visible(False)
            self.fig.canvas.draw_idle()
            return
            
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
            if self.intensity is None or len(self.intensity) == 0:
                return
            
            # 获取鼠标位置的x坐标
            x = event.xdata
            
            # 找到最接近的点
            nearest_idx = np.abs(self.wavelength - x).argmin()
            nearest_x = self.wavelength[nearest_idx]
            nearest_y = self.intensity[nearest_idx]
            
            # 更新光标点位置
            self.cursor_point.set_data([nearest_x], [nearest_y])
            self.cursor_point.set_visible(True)
            
            # 根据当前坐标系统设置正确的标签
            # 注意：spectrometer_viewer.py 主要用于实时显示，通常使用拉曼位移
            # 但为了兼容性，我们检查是否有像素坐标设置
            if hasattr(self, '_use_pixel_coordinates') and getattr(self, '_use_pixel_coordinates', False):
                # 像素坐标
                x_label = "像素"
                x_unit = ""
            else:
                # 拉曼位移（默认）
                x_label = "拉曼位移"
                x_unit = "cm-1"
            
            # 设置文本内容
            self.cursor_annotation.xy = (nearest_x, nearest_y)
            self.cursor_annotation.set_text(f'{x_label}: {nearest_x:.2f}{x_unit}\n强度: {nearest_y:.2f}')
            self.cursor_annotation.set_visible(True)
            
            # 只有当鼠标移动显著距离时才更新图形，避免频繁刷新
            self.fig.canvas.draw_idle()

def main():
    viewer = SpectrometerViewer()
    viewer.run_plot_window()

if __name__ == '__main__':
    main()