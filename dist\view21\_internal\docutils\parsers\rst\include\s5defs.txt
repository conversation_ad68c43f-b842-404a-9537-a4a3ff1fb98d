.. Definitions of interpreted text roles (classes) for S5/HTML data.
.. This data file has been placed in the public domain.

.. Colours
   =======

.. role:: black
.. role:: gray
.. role:: silver
.. role:: white

.. role:: maroon
.. role:: red
.. role:: magenta
.. role:: fuchsia
.. role:: pink
.. role:: orange
.. role:: yellow
.. role:: lime
.. role:: green
.. role:: olive
.. role:: teal
.. role:: cyan
.. role:: aqua
.. role:: blue
.. role:: navy
.. role:: purple


.. Text Sizes
   ==========

.. role:: huge
.. role:: big
.. role:: small
.. role:: tiny


.. Display in Slides (Presentation Mode) Only
   ==========================================

.. role:: slide
   :class: slide-display


.. Display in Outline Mode Only
   ============================

.. role:: outline


.. Display in Print Only
   =====================

.. role:: print


.. Display in Handout Mode Only
   ============================

.. role:: handout


.. Incremental Display
   ===================

.. role:: incremental
.. default-role:: incremental
