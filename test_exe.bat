@echo off
echo ========================================
echo View21 拉曼光谱分析系统 - 打包测试
echo ========================================
echo.

echo 检查打包文件是否存在...
if exist "dist\view21\view21.exe" (
    echo ✓ 目录版本打包文件存在: dist\view21\view21.exe
) else (
    echo ✗ 目录版本打包文件不存在
    goto :error
)

if exist "dist\view21.exe" (
    echo ✓ 单文件版本存在: dist\view21.exe （但可能有DLL问题）
) else (
    echo ✗ 单文件版本不存在
)

echo.
echo 检查关键依赖文件...
if exist "dist\view21\_internal\UserApplication.dll" (
    echo ✓ 光谱仪驱动文件存在
) else (
    echo ✗ 光谱仪驱动文件缺失
)

if exist "dist\view21\_internal\SiUSBXp.dll" (
    echo ✓ USB驱动文件存在
) else (
    echo ✗ USB驱动文件缺失
)

if exist "dist\view21\_internal\logo48.ico" (
    echo ✓ 应用图标存在
) else (
    echo ✗ 应用图标缺失
)

echo.
echo ========================================
echo 测试完成！
echo.
echo 推荐使用: dist\view21\view21.exe
echo 这是目录版本，包含所有必要的依赖文件
echo.
echo 如果要测试运行，请手动双击:
echo   dist\view21\view21.exe
echo ========================================
pause
goto :end

:error
echo.
echo 打包可能未完成或失败，请检查打包过程
pause

:end
