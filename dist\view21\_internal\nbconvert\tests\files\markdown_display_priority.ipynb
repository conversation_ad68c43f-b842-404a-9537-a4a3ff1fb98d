{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7f21298cc828>]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXkAAAEACAYAAABWLgY0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ngg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f21541826a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import set_matplotlib_formats\n", "# generate pdf and png images\n", "set_matplotlib_formats('pdf', 'png')\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "\n", "plt.plot(range(5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [Root]", "language": "python", "name": "Python [Root]"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 0}