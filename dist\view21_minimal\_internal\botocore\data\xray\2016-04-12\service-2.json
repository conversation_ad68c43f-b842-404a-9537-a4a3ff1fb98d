{"version": "2.0", "metadata": {"apiVersion": "2016-04-12", "endpointPrefix": "xray", "protocol": "rest-json", "serviceFullName": "AWS X-Ray", "serviceId": "XRay", "signatureVersion": "v4", "uid": "xray-2016-04-12"}, "operations": {"BatchGetTraces": {"name": "BatchGetTraces", "http": {"method": "POST", "requestUri": "/Traces"}, "input": {"shape": "BatchGetTracesRequest"}, "output": {"shape": "BatchGetTracesResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves a list of traces specified by ID. Each trace is a collection of segment documents that originates from a single request. Use <code>GetTraceSummaries</code> to get a list of trace IDs.</p>"}, "CreateGroup": {"name": "CreateGroup", "http": {"method": "POST", "requestUri": "/CreateGroup"}, "input": {"shape": "CreateGroupRequest"}, "output": {"shape": "CreateGroupResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Creates a group resource with a name and a filter expression. </p>"}, "CreateSamplingRule": {"name": "CreateSamplingRule", "http": {"method": "POST", "requestUri": "/CreateSamplingRule"}, "input": {"shape": "CreateSamplingRuleRequest"}, "output": {"shape": "CreateSamplingRuleResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}, {"shape": "RuleLimitExceededException"}], "documentation": "<p>Creates a rule to control sampling behavior for instrumented applications. Services retrieve rules with <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_GetSamplingRules.html\">GetSamplingRules</a>, and evaluate each rule in ascending order of <i>priority</i> for each request. If a rule matches, the service records a trace, borrowing it from the reservoir size. After 10 seconds, the service reports back to X-Ray with <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_GetSamplingTargets.html\">GetSamplingTargets</a> to get updated versions of each in-use rule. The updated rule contains a trace quota that the service can use instead of borrowing from the reservoir.</p>"}, "DeleteGroup": {"name": "DeleteGroup", "http": {"method": "POST", "requestUri": "/DeleteGroup"}, "input": {"shape": "DeleteGroupRequest"}, "output": {"shape": "DeleteGroupResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Deletes a group resource.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "POST", "requestUri": "/DeleteResourcePolicy"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "output": {"shape": "DeleteResourcePolicyResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InvalidPolicyRevisionIdException"}, {"shape": "ThrottledException"}], "documentation": "<p>Deletes a resource policy from the target Amazon Web Services account.</p>"}, "DeleteSamplingRule": {"name": "DeleteSamplingRule", "http": {"method": "POST", "requestUri": "/DeleteSamplingRule"}, "input": {"shape": "DeleteSamplingRuleRequest"}, "output": {"shape": "DeleteSamplingRuleResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Deletes a sampling rule.</p>"}, "GetEncryptionConfig": {"name": "GetEncryptionConfig", "http": {"method": "POST", "requestUri": "/EncryptionConfig"}, "input": {"shape": "GetEncryptionConfigRequest"}, "output": {"shape": "GetEncryptionConfigResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves the current encryption configuration for X-Ray data.</p>"}, "GetGroup": {"name": "GetGroup", "http": {"method": "POST", "requestUri": "/GetGroup"}, "input": {"shape": "GetGroupRequest"}, "output": {"shape": "GetGroupResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves group resource details.</p>"}, "GetGroups": {"name": "GetGroups", "http": {"method": "POST", "requestUri": "/Groups"}, "input": {"shape": "GetGroupsRequest"}, "output": {"shape": "GetGroupsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves all active group details.</p>"}, "GetInsight": {"name": "GetInsight", "http": {"method": "POST", "requestUri": "/Insight"}, "input": {"shape": "GetInsightRequest"}, "output": {"shape": "GetInsightResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves the summary information of an insight. This includes impact to clients and root cause services, the top anomalous services, the category, the state of the insight, and the start and end time of the insight.</p>"}, "GetInsightEvents": {"name": "GetInsightEvents", "http": {"method": "POST", "requestUri": "/InsightEvents"}, "input": {"shape": "GetInsightEventsRequest"}, "output": {"shape": "GetInsightEventsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>X-Ray reevaluates insights periodically until they're resolved, and records each intermediate state as an event. You can review an insight's events in the Impact Timeline on the Inspect page in the X-Ray console.</p>"}, "GetInsightImpactGraph": {"name": "GetInsightImpactGraph", "http": {"method": "POST", "requestUri": "/InsightImpactGraph"}, "input": {"shape": "GetInsightImpactGraphRequest"}, "output": {"shape": "GetInsightImpactGraphResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves a service graph structure filtered by the specified insight. The service graph is limited to only structural information. For a complete service graph, use this API with the GetServiceGraph API.</p>"}, "GetInsightSummaries": {"name": "GetInsightSummaries", "http": {"method": "POST", "requestUri": "/InsightSummaries"}, "input": {"shape": "GetInsightSummariesRequest"}, "output": {"shape": "GetInsightSummariesResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves the summaries of all insights in the specified group matching the provided filter values.</p>"}, "GetSamplingRules": {"name": "GetSamplingRules", "http": {"method": "POST", "requestUri": "/GetSamplingRules"}, "input": {"shape": "GetSamplingRulesRequest"}, "output": {"shape": "GetSamplingRulesResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves all sampling rules.</p>"}, "GetSamplingStatisticSummaries": {"name": "GetSamplingStatisticSummaries", "http": {"method": "POST", "requestUri": "/SamplingStatisticSummaries"}, "input": {"shape": "GetSamplingStatisticSummariesRequest"}, "output": {"shape": "GetSamplingStatisticSummariesResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves information about recent sampling results for all sampling rules.</p>"}, "GetSamplingTargets": {"name": "GetSamplingTargets", "http": {"method": "POST", "requestUri": "/SamplingTargets"}, "input": {"shape": "GetSamplingTargetsRequest"}, "output": {"shape": "GetSamplingTargetsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Requests a sampling quota for rules that the service is using to sample requests. </p>"}, "GetServiceGraph": {"name": "GetServiceGraph", "http": {"method": "POST", "requestUri": "/ServiceGraph"}, "input": {"shape": "GetServiceGraphRequest"}, "output": {"shape": "GetServiceGraphResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves a document that describes services that process incoming requests, and downstream services that they call as a result. Root services process incoming requests and make calls to downstream services. Root services are applications that use the <a href=\"https://docs.aws.amazon.com/xray/index.html\">Amazon Web Services X-Ray SDK</a>. Downstream services can be other applications, Amazon Web Services resources, HTTP web APIs, or SQL databases.</p>"}, "GetTimeSeriesServiceStatistics": {"name": "GetTimeSeriesServiceStatistics", "http": {"method": "POST", "requestUri": "/TimeSeriesServiceStatistics"}, "input": {"shape": "GetTimeSeriesServiceStatisticsRequest"}, "output": {"shape": "GetTimeSeriesServiceStatisticsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Get an aggregation of service statistics defined by a specific time range.</p>"}, "GetTraceGraph": {"name": "GetTraceGraph", "http": {"method": "POST", "requestUri": "/TraceGraph"}, "input": {"shape": "GetTraceGraphRequest"}, "output": {"shape": "GetTraceGraphResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves a service graph for one or more specific trace IDs.</p>"}, "GetTraceSummaries": {"name": "GetTraceSummaries", "http": {"method": "POST", "requestUri": "/TraceSummaries"}, "input": {"shape": "GetTraceSummariesRequest"}, "output": {"shape": "GetTraceSummariesResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Retrieves IDs and annotations for traces available for a specified time frame using an optional filter. To get the full traces, pass the trace IDs to <code>BatchGetTraces</code>.</p> <p>A filter expression can target traced requests that hit specific service nodes or edges, have errors, or come from a known user. For example, the following filter expression targets traces that pass through <code>api.example.com</code>:</p> <p> <code>service(\"api.example.com\")</code> </p> <p>This filter expression finds traces that have an annotation named <code>account</code> with the value <code>12345</code>:</p> <p> <code>annotation.account = \"12345\"</code> </p> <p>For a full list of indexed fields and keywords that you can use in filter expressions, see <a href=\"https://docs.aws.amazon.com/xray/latest/devguide/xray-console-filters.html\">Using Filter Expressions</a> in the <i>Amazon Web Services X-Ray Developer Guide</i>.</p>"}, "ListResourcePolicies": {"name": "ListResourcePolicies", "http": {"method": "POST", "requestUri": "/ListResourcePolicies"}, "input": {"shape": "ListResourcePoliciesRequest"}, "output": {"shape": "ListResourcePoliciesResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Returns the list of resource policies in the target Amazon Web Services account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/ListTagsForResource"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of tags that are applied to the specified Amazon Web Services X-Ray group or sampling rule.</p>"}, "PutEncryptionConfig": {"name": "PutEncryptionConfig", "http": {"method": "POST", "requestUri": "/PutEncryptionConfig"}, "input": {"shape": "PutEncryptionConfigRequest"}, "output": {"shape": "PutEncryptionConfigResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Updates the encryption configuration for X-Ray data.</p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/PutResourcePolicy"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResult"}, "errors": [{"shape": "MalformedPolicyDocumentException"}, {"shape": "LockoutPreventionException"}, {"shape": "InvalidPolicyRevisionIdException"}, {"shape": "PolicySizeLimitExceededException"}, {"shape": "PolicyCountLimitExceededException"}, {"shape": "ThrottledException"}], "documentation": "<p> Sets the resource policy to grant one or more Amazon Web Services services and accounts permissions to access X-Ray. Each resource policy will be associated with a specific Amazon Web Services account. Each Amazon Web Services account can have a maximum of 5 resource policies, and each policy name must be unique within that account. The maximum size of each resource policy is 5KB. </p>"}, "PutTelemetryRecords": {"name": "PutTelemetryRecords", "http": {"method": "POST", "requestUri": "/TelemetryRecords"}, "input": {"shape": "PutTelemetryRecordsRequest"}, "output": {"shape": "PutTelemetryRecordsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Used by the Amazon Web Services X-Ray daemon to upload telemetry.</p>"}, "PutTraceSegments": {"name": "PutTraceSegments", "http": {"method": "POST", "requestUri": "/TraceSegments"}, "input": {"shape": "PutTraceSegmentsRequest"}, "output": {"shape": "PutTraceSegmentsResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Uploads segment documents to Amazon Web Services X-Ray. The <a href=\"https://docs.aws.amazon.com/xray/index.html\">X-Ray SDK</a> generates segment documents and sends them to the X-Ray daemon, which uploads them in batches. A segment document can be a completed segment, an in-progress segment, or an array of subsegments.</p> <p>Segments must include the following fields. For the full segment document schema, see <a href=\"https://docs.aws.amazon.com/xray/latest/devguide/xray-api-segmentdocuments.html\">Amazon Web Services X-Ray Segment Documents</a> in the <i>Amazon Web Services X-Ray Developer Guide</i>.</p> <p class=\"title\"> <b>Required segment document fields</b> </p> <ul> <li> <p> <code>name</code> - The name of the service that handled the request.</p> </li> <li> <p> <code>id</code> - A 64-bit identifier for the segment, unique among segments in the same trace, in 16 hexadecimal digits.</p> </li> <li> <p> <code>trace_id</code> - A unique identifier that connects all segments and subsegments originating from a single client request.</p> </li> <li> <p> <code>start_time</code> - Time the segment or subsegment was created, in floating point seconds in epoch time, accurate to milliseconds. For example, <code>1480615200.010</code> or <code>1.480615200010E9</code>.</p> </li> <li> <p> <code>end_time</code> - Time the segment or subsegment was closed. For example, <code>1480615200.090</code> or <code>1.480615200090E9</code>. Specify either an <code>end_time</code> or <code>in_progress</code>.</p> </li> <li> <p> <code>in_progress</code> - Set to <code>true</code> instead of specifying an <code>end_time</code> to record that a segment has been started, but is not complete. Send an in-progress segment when your application receives a request that will take a long time to serve, to trace that the request was received. When the response is sent, send the complete segment to overwrite the in-progress segment.</p> </li> </ul> <p>A <code>trace_id</code> consists of three numbers separated by hyphens. For example, 1-58406520-a006649127e371903a2de979. This includes:</p> <p class=\"title\"> <b>Trace ID Format</b> </p> <ul> <li> <p>The version number, for instance, <code>1</code>.</p> </li> <li> <p>The time of the original request, in Unix epoch time, in 8 hexadecimal digits. For example, 10:00AM December 2nd, 2016 PST in epoch time is <code>1480615200</code> seconds, or <code>58406520</code> in hexadecimal.</p> </li> <li> <p>A 96-bit identifier for the trace, globally unique, in 24 hexadecimal digits.</p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/TagResource"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Applies tags to an existing Amazon Web Services X-Ray group or sampling rule.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/UntagResource"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes tags from an Amazon Web Services X-Ray group or sampling rule. You cannot edit or delete system tags (those with an <code>aws:</code> prefix).</p>"}, "UpdateGroup": {"name": "UpdateGroup", "http": {"method": "POST", "requestUri": "/UpdateGroup"}, "input": {"shape": "UpdateGroupRequest"}, "output": {"shape": "UpdateGroupResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Updates a group resource.</p>"}, "UpdateSamplingRule": {"name": "UpdateSamplingRule", "http": {"method": "POST", "requestUri": "/UpdateSamplingRule"}, "input": {"shape": "UpdateSamplingRuleRequest"}, "output": {"shape": "UpdateSamplingRuleResult"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottledException"}], "documentation": "<p>Modifies a sampling rule's configuration.</p>"}}, "shapes": {"Alias": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The canonical name of the alias.</p>"}, "Names": {"shape": "AliasNames", "documentation": "<p>A list of names for the alias, including the canonical name.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of the alias.</p>"}}, "documentation": "<p>An alias for an edge.</p>"}, "AliasList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "AliasNames": {"type": "list", "member": {"shape": "String"}}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "AnnotationKey": {"type": "string"}, "AnnotationValue": {"type": "structure", "members": {"NumberValue": {"shape": "NullableDouble", "documentation": "<p>Value for a Number annotation.</p>"}, "BooleanValue": {"shape": "NullableBoolean", "documentation": "<p>Value for a Boolean annotation.</p>"}, "StringValue": {"shape": "String", "documentation": "<p>Value for a String annotation.</p>"}}, "documentation": "<p>Value of a segment annotation. Has one of three value types: Number, Boolean, or String.</p>"}, "Annotations": {"type": "map", "key": {"shape": "AnnotationKey"}, "value": {"shape": "ValuesWithServiceIds"}}, "AnomalousService": {"type": "structure", "members": {"ServiceId": {"shape": "ServiceId"}}, "documentation": "<p>The service within the service graph that has anomalously high fault rates. </p>"}, "AnomalousServiceList": {"type": "list", "member": {"shape": "AnomalousService"}}, "AttributeKey": {"type": "string", "max": 32, "min": 1}, "AttributeMap": {"type": "map", "key": {"shape": "AttributeKey"}, "value": {"shape": "AttributeValue"}, "max": 5}, "AttributeValue": {"type": "string", "max": 32, "min": 1}, "AvailabilityZoneDetail": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of a corresponding Availability Zone.</p>"}}, "documentation": "<p>A list of Availability Zones corresponding to the segments in a trace.</p>"}, "BackendConnectionErrors": {"type": "structure", "members": {"TimeoutCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "ConnectionRefusedCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "HTTPCode4XXCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "HTTPCode5XXCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "UnknownHostCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "OtherCount": {"shape": "NullableInteger", "documentation": "<p/>"}}, "documentation": "<p/>"}, "BatchGetTracesRequest": {"type": "structure", "required": ["TraceIds"], "members": {"TraceIds": {"shape": "TraceIdList", "documentation": "<p>Specify the trace IDs of requests for which to retrieve segments.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "BatchGetTracesResult": {"type": "structure", "members": {"Traces": {"shape": "TraceList", "documentation": "<p>Full traces for the specified requests.</p>"}, "UnprocessedTraceIds": {"shape": "UnprocessedTraceIdList", "documentation": "<p>Trace IDs of requests that haven't been processed.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "Boolean": {"type": "boolean"}, "BorrowCount": {"type": "integer", "min": 0}, "ClientID": {"type": "string", "max": 24, "min": 24}, "CreateGroupRequest": {"type": "structure", "required": ["GroupName"], "members": {"GroupName": {"shape": "GroupName", "documentation": "<p>The case-sensitive name of the new group. Default is a reserved name and names must be unique.</p>"}, "FilterExpression": {"shape": "FilterExpression", "documentation": "<p>The filter expression defining criteria by which to group traces.</p>"}, "InsightsConfiguration": {"shape": "InsightsConfiguration", "documentation": "<p>The structure containing configurations related to insights.</p> <ul> <li> <p>The InsightsEnabled boolean can be set to true to enable insights for the new group or false to disable insights for the new group.</p> </li> <li> <p>The NotificationsEnabled boolean can be set to true to enable insights notifications for the new group. Notifications may only be enabled on a group with InsightsEnabled set to true.</p> </li> </ul>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map that contains one or more tag keys and tag values to attach to an X-Ray group. For more information about ways to use tags, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> in the <i>Amazon Web Services General Reference</i>.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>Maximum number of user-applied tags per resource: 50</p> </li> <li> <p>Maximum tag key length: 128 Unicode characters</p> </li> <li> <p>Maximum tag value length: 256 Unicode characters</p> </li> <li> <p>Valid values for key and value: a-z, A-Z, 0-9, space, and the following characters: _ . : / = + - and @</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Don't use <code>aws:</code> as a prefix for keys; it's reserved for Amazon Web Services use.</p> </li> </ul>"}}}, "CreateGroupResult": {"type": "structure", "members": {"Group": {"shape": "Group", "documentation": "<p>The group that was created. Contains the name of the group that was created, the Amazon Resource Name (ARN) of the group that was generated based on the group name, the filter expression, and the insight configuration that was assigned to the group.</p>"}}}, "CreateSamplingRuleRequest": {"type": "structure", "required": ["SamplingRule"], "members": {"SamplingRule": {"shape": "SamplingRule", "documentation": "<p>The rule definition.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map that contains one or more tag keys and tag values to attach to an X-Ray sampling rule. For more information about ways to use tags, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> in the <i>Amazon Web Services General Reference</i>.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>Maximum number of user-applied tags per resource: 50</p> </li> <li> <p>Maximum tag key length: 128 Unicode characters</p> </li> <li> <p>Maximum tag value length: 256 Unicode characters</p> </li> <li> <p>Valid values for key and value: a-z, A-Z, 0-9, space, and the following characters: _ . : / = + - and @</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Don't use <code>aws:</code> as a prefix for keys; it's reserved for Amazon Web Services use.</p> </li> </ul>"}}}, "CreateSamplingRuleResult": {"type": "structure", "members": {"SamplingRuleRecord": {"shape": "SamplingRuleRecord", "documentation": "<p>The saved rule definition and metadata.</p>"}}}, "DeleteGroupRequest": {"type": "structure", "members": {"GroupName": {"shape": "GroupName", "documentation": "<p>The case-sensitive name of the group.</p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The ARN of the group that was generated on creation.</p>"}}}, "DeleteGroupResult": {"type": "structure", "members": {}}, "DeleteResourcePolicyRequest": {"type": "structure", "required": ["PolicyName"], "members": {"PolicyName": {"shape": "PolicyName", "documentation": "<p>The name of the resource policy to delete.</p>"}, "PolicyRevisionId": {"shape": "PolicyRevisionId", "documentation": "<p>Specifies a specific policy revision to delete. Provide a <code>PolicyRevisionId</code> to ensure an atomic delete operation. If the provided revision id does not match the latest policy revision id, an <code>InvalidPolicyRevisionIdException</code> exception is returned. </p>"}}}, "DeleteResourcePolicyResult": {"type": "structure", "members": {}}, "DeleteSamplingRuleRequest": {"type": "structure", "members": {"RuleName": {"shape": "String", "documentation": "<p>The name of the sampling rule. Specify a rule by either name or ARN, but not both.</p>"}, "RuleARN": {"shape": "String", "documentation": "<p>The ARN of the sampling rule. Specify a rule by either name or ARN, but not both.</p>"}}}, "DeleteSamplingRuleResult": {"type": "structure", "members": {"SamplingRuleRecord": {"shape": "SamplingRuleRecord", "documentation": "<p>The deleted rule definition and metadata.</p>"}}}, "Double": {"type": "double"}, "EC2InstanceId": {"type": "string", "max": 20}, "Edge": {"type": "structure", "members": {"ReferenceId": {"shape": "NullableInteger", "documentation": "<p>Identifier of the edge. Unique within a service map.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The start time of the first segment on the edge.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end time of the last segment on the edge.</p>"}, "SummaryStatistics": {"shape": "EdgeStatistics", "documentation": "<p>Response statistics for segments on the edge.</p>"}, "ResponseTimeHistogram": {"shape": "Histogram", "documentation": "<p>A histogram that maps the spread of client response times on an edge. Only populated for synchronous edges.</p>"}, "Aliases": {"shape": "AliasList", "documentation": "<p><PERSON><PERSON> for the edge.</p>"}, "EdgeType": {"shape": "String", "documentation": "<p>Describes an asynchronous connection, with a value of <code>link</code>.</p>"}, "ReceivedEventAgeHistogram": {"shape": "Histogram", "documentation": "<p>A histogram that maps the spread of event age when received by consumers. Age is calculated each time an event is received. Only populated when <i>EdgeType</i> is <code>link</code>.</p>"}}, "documentation": "<p>Information about a connection between two services. An edge can be a synchronous connection, such as typical call between client and service, or an asynchronous link, such as a Lambda function which retrieves an event from an SNS queue.</p>"}, "EdgeList": {"type": "list", "member": {"shape": "Edge"}}, "EdgeStatistics": {"type": "structure", "members": {"OkCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of requests that completed with a 2xx Success status code.</p>"}, "ErrorStatistics": {"shape": "ErrorStatistics", "documentation": "<p>Information about requests that failed with a 4xx Client Error status code.</p>"}, "FaultStatistics": {"shape": "FaultStatistics", "documentation": "<p>Information about requests that failed with a 5xx Server Error status code.</p>"}, "TotalCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The total number of completed requests.</p>"}, "TotalResponseTime": {"shape": "NullableDouble", "documentation": "<p>The aggregate response time of completed requests.</p>"}}, "documentation": "<p>Response statistics for an edge.</p>"}, "EncryptionConfig": {"type": "structure", "members": {"KeyId": {"shape": "String", "documentation": "<p>The ID of the KMS key used for encryption, if applicable.</p>"}, "Status": {"shape": "EncryptionStatus", "documentation": "<p>The encryption status. While the status is <code>UPDATING</code>, X-Ray may encrypt data with a combination of the new and old settings.</p>"}, "Type": {"shape": "EncryptionType", "documentation": "<p>The type of encryption. Set to <code>KMS</code> for encryption with KMS keys. Set to <code>NONE</code> for default encryption.</p>"}}, "documentation": "<p>A configuration document that specifies encryption configuration settings.</p>"}, "EncryptionKeyId": {"type": "string", "max": 3000, "min": 1}, "EncryptionStatus": {"type": "string", "enum": ["UPDATING", "ACTIVE"]}, "EncryptionType": {"type": "string", "enum": ["NONE", "KMS"]}, "EntitySelectorExpression": {"type": "string", "max": 500, "min": 1}, "ErrorMessage": {"type": "string"}, "ErrorRootCause": {"type": "structure", "members": {"Services": {"shape": "ErrorRootCauseServices", "documentation": "<p>A list of services corresponding to an error. A service identifies a segment and it contains a name, account ID, type, and inferred flag.</p>"}, "ClientImpacting": {"shape": "NullableBoolean", "documentation": "<p>A flag that denotes that the root cause impacts the trace client.</p>"}}, "documentation": "<p>The root cause of a trace summary error.</p>"}, "ErrorRootCauseEntity": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the entity.</p>"}, "Exceptions": {"shape": "RootCauseExceptions", "documentation": "<p>The types and messages of the exceptions.</p>"}, "Remote": {"shape": "NullableBoolean", "documentation": "<p>A flag that denotes a remote subsegment.</p>"}}, "documentation": "<p>A collection of segments and corresponding subsegments associated to a trace summary error.</p>"}, "ErrorRootCauseEntityPath": {"type": "list", "member": {"shape": "ErrorRootCauseEntity"}}, "ErrorRootCauseService": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The service name.</p>"}, "Names": {"shape": "ServiceNames", "documentation": "<p>A collection of associated service names.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type associated to the service.</p>"}, "AccountId": {"shape": "String", "documentation": "<p>The account ID associated to the service.</p>"}, "EntityPath": {"shape": "ErrorRootCauseEntityPath", "documentation": "<p>The path of root cause entities found on the service. </p>"}, "Inferred": {"shape": "NullableBoolean", "documentation": "<p>A Boolean value indicating if the service is inferred from the trace.</p>"}}, "documentation": "<p>A collection of fields identifying the services in a trace summary error.</p>"}, "ErrorRootCauseServices": {"type": "list", "member": {"shape": "ErrorRootCauseService"}}, "ErrorRootCauses": {"type": "list", "member": {"shape": "ErrorRootCause"}}, "ErrorStatistics": {"type": "structure", "members": {"ThrottleCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of requests that failed with a 419 throttling status code.</p>"}, "OtherCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of requests that failed with untracked 4xx Client Error status codes.</p>"}, "TotalCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The total number of requests that failed with a 4xx Client Error status code.</p>"}}, "documentation": "<p>Information about requests that failed with a 4xx Client Error status code.</p>"}, "EventSummaryText": {"type": "string"}, "FaultRootCause": {"type": "structure", "members": {"Services": {"shape": "FaultRootCauseServices", "documentation": "<p>A list of corresponding services. A service identifies a segment and it contains a name, account ID, type, and inferred flag.</p>"}, "ClientImpacting": {"shape": "NullableBoolean", "documentation": "<p>A flag that denotes that the root cause impacts the trace client.</p>"}}, "documentation": "<p>The root cause information for a trace summary fault.</p>"}, "FaultRootCauseEntity": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the entity.</p>"}, "Exceptions": {"shape": "RootCauseExceptions", "documentation": "<p>The types and messages of the exceptions.</p>"}, "Remote": {"shape": "NullableBoolean", "documentation": "<p>A flag that denotes a remote subsegment.</p>"}}, "documentation": "<p>A collection of segments and corresponding subsegments associated to a trace summary fault error.</p>"}, "FaultRootCauseEntityPath": {"type": "list", "member": {"shape": "FaultRootCauseEntity"}}, "FaultRootCauseService": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The service name.</p>"}, "Names": {"shape": "ServiceNames", "documentation": "<p>A collection of associated service names.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type associated to the service.</p>"}, "AccountId": {"shape": "String", "documentation": "<p>The account ID associated to the service.</p>"}, "EntityPath": {"shape": "FaultRootCauseEntityPath", "documentation": "<p>The path of root cause entities found on the service. </p>"}, "Inferred": {"shape": "NullableBoolean", "documentation": "<p>A Boolean value indicating if the service is inferred from the trace.</p>"}}, "documentation": "<p>A collection of fields identifying the services in a trace summary fault.</p>"}, "FaultRootCauseServices": {"type": "list", "member": {"shape": "FaultRootCauseService"}}, "FaultRootCauses": {"type": "list", "member": {"shape": "FaultRootCause"}}, "FaultStatistics": {"type": "structure", "members": {"OtherCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of requests that failed with untracked 5xx Server Error status codes.</p>"}, "TotalCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The total number of requests that failed with a 5xx Server Error status code.</p>"}}, "documentation": "<p>Information about requests that failed with a 5xx Server Error status code.</p>"}, "FilterExpression": {"type": "string"}, "FixedRate": {"type": "double", "max": 1, "min": 0}, "ForecastStatistics": {"type": "structure", "members": {"FaultCountHigh": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The upper limit of fault counts for a service.</p>"}, "FaultCountLow": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The lower limit of fault counts for a service.</p>"}}, "documentation": "<p>The predicted high and low fault count. This is used to determine if a service has become anomalous and if an insight should be created.</p>"}, "GetEncryptionConfigRequest": {"type": "structure", "members": {}}, "GetEncryptionConfigResult": {"type": "structure", "members": {"EncryptionConfig": {"shape": "EncryptionConfig", "documentation": "<p>The encryption configuration document.</p>"}}}, "GetGroupRequest": {"type": "structure", "members": {"GroupName": {"shape": "GroupName", "documentation": "<p>The case-sensitive name of the group.</p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The ARN of the group that was generated on creation.</p>"}}}, "GetGroupResult": {"type": "structure", "members": {"Group": {"shape": "Group", "documentation": "<p>The group that was requested. Contains the name of the group, the ARN of the group, the filter expression, and the insight configuration assigned to the group.</p>"}}}, "GetGroupsNextToken": {"type": "string", "max": 100, "min": 1}, "GetGroupsRequest": {"type": "structure", "members": {"NextToken": {"shape": "GetGroupsNextToken", "documentation": "<p>Pagination token.</p>"}}}, "GetGroupsResult": {"type": "structure", "members": {"Groups": {"shape": "GroupSummaryList", "documentation": "<p>The collection of all active groups.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetInsightEventsMaxResults": {"type": "integer", "max": 50, "min": 1}, "GetInsightEventsRequest": {"type": "structure", "required": ["InsightId"], "members": {"InsightId": {"shape": "InsightId", "documentation": "<p>The insight's unique identifier. Use the GetInsightSummaries action to retrieve an InsightId.</p>"}, "MaxResults": {"shape": "GetInsightEventsMaxResults", "documentation": "<p>Used to retrieve at most the specified value of events.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specify the pagination token returned by a previous request to retrieve the next page of events. </p>"}}}, "GetInsightEventsResult": {"type": "structure", "members": {"InsightEvents": {"shape": "InsightEventList", "documentation": "<p>A detailed description of the event. This includes the time of the event, client and root cause impact statistics, and the top anomalous service at the time of the event.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Use this token to retrieve the next page of insight events.</p>"}}}, "GetInsightImpactGraphRequest": {"type": "structure", "required": ["InsightId", "StartTime", "EndTime"], "members": {"InsightId": {"shape": "InsightId", "documentation": "<p>The insight's unique identifier. Use the GetInsightSummaries action to retrieve an InsightId.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The estimated start time of the insight, in Unix time seconds. The StartTime is inclusive of the value provided and can't be more than 30 days old.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The estimated end time of the insight, in Unix time seconds. The EndTime is exclusive of the value provided. The time range between the start time and end time can't be more than six hours. </p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specify the pagination token returned by a previous request to retrieve the next page of results. </p>"}}}, "GetInsightImpactGraphResult": {"type": "structure", "members": {"InsightId": {"shape": "InsightId", "documentation": "<p>The insight's unique identifier.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The provided start time.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The provided end time. </p>"}, "ServiceGraphStartTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the service graph started.</p>"}, "ServiceGraphEndTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the service graph ended.</p>"}, "Services": {"shape": "InsightImpactGraphServiceList", "documentation": "<p>The Amazon Web Services instrumented services related to the insight.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Pagination token.</p>"}}}, "GetInsightRequest": {"type": "structure", "required": ["InsightId"], "members": {"InsightId": {"shape": "InsightId", "documentation": "<p>The insight's unique identifier. Use the GetInsightSummaries action to retrieve an InsightId.</p>"}}}, "GetInsightResult": {"type": "structure", "members": {"Insight": {"shape": "Insight", "documentation": "<p>The summary information of an insight.</p>"}}}, "GetInsightSummariesMaxResults": {"type": "integer", "max": 100, "min": 1}, "GetInsightSummariesRequest": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"States": {"shape": "InsightStateList", "documentation": "<p>The list of insight states. </p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The Amazon Resource Name (ARN) of the group. Required if the GroupName isn't provided.</p>"}, "GroupName": {"shape": "GroupName", "documentation": "<p>The name of the group. Required if the GroupARN isn't provided.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The beginning of the time frame in which the insights started. The start time can't be more than 30 days old.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end of the time frame in which the insights ended. The end time can't be more than 30 days old.</p>"}, "MaxResults": {"shape": "GetInsightSummariesMaxResults", "documentation": "<p>The maximum number of results to display.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Pagination token.</p>"}}}, "GetInsightSummariesResult": {"type": "structure", "members": {"InsightSummaries": {"shape": "InsightSummaryList", "documentation": "<p>The summary of each insight within the group matching the provided filters. The summary contains the InsightID, start and end time, the root cause service, the root cause and client impact statistics, the top anomalous services, and the status of the insight.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Pagination token.</p>"}}}, "GetSamplingRulesRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetSamplingRulesResult": {"type": "structure", "members": {"SamplingRuleRecords": {"shape": "SamplingRuleRecordList", "documentation": "<p>Rule definitions and metadata.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetSamplingStatisticSummariesRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetSamplingStatisticSummariesResult": {"type": "structure", "members": {"SamplingStatisticSummaries": {"shape": "SamplingStatisticSummaryList", "documentation": "<p>Information about the number of requests instrumented for each sampling rule.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetSamplingTargetsRequest": {"type": "structure", "required": ["SamplingStatisticsDocuments"], "members": {"SamplingStatisticsDocuments": {"shape": "SamplingStatisticsDocumentList", "documentation": "<p>Information about rules that the service is using to sample requests.</p>"}}}, "GetSamplingTargetsResult": {"type": "structure", "members": {"SamplingTargetDocuments": {"shape": "SamplingTargetDocumentList", "documentation": "<p>Updated rules that the service should use to sample requests.</p>"}, "LastRuleModification": {"shape": "Timestamp", "documentation": "<p>The last time a user changed the sampling rule configuration. If the sampling rule configuration changed since the service last retrieved it, the service should call <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_GetSamplingRules.html\">GetSamplingRules</a> to get the latest version.</p>"}, "UnprocessedStatistics": {"shape": "UnprocessedStatisticsList", "documentation": "<p>Information about <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_SamplingStatisticsDocument.html\">SamplingStatisticsDocument</a> that X-Ray could not process.</p>"}}}, "GetServiceGraphRequest": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The start of the time frame for which to generate a graph.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end of the timeframe for which to generate a graph.</p>"}, "GroupName": {"shape": "GroupName", "documentation": "<p>The name of a group based on which you want to generate a graph.</p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The Amazon Resource Name (ARN) of a group based on which you want to generate a graph.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetServiceGraphResult": {"type": "structure", "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The start of the time frame for which the graph was generated.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end of the time frame for which the graph was generated.</p>"}, "Services": {"shape": "ServiceList", "documentation": "<p>The services that have processed a traced request during the specified time frame.</p>"}, "ContainsOldGroupVersions": {"shape": "Boolean", "documentation": "<p>A flag indicating whether the group's filter expression has been consistent, or if the returned service graph may show traces from an older version of the group's filter expression.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetTimeSeriesServiceStatisticsRequest": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The start of the time frame for which to aggregate statistics.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end of the time frame for which to aggregate statistics.</p>"}, "GroupName": {"shape": "GroupName", "documentation": "<p>The case-sensitive name of the group for which to pull statistics from.</p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The Amazon Resource Name (ARN) of the group for which to pull statistics from.</p>"}, "EntitySelectorExpression": {"shape": "EntitySelectorExpression", "documentation": "<p>A filter expression defining entities that will be aggregated for statistics. Supports ID, service, and edge functions. If no selector expression is specified, edge statistics are returned. </p>"}, "Period": {"shape": "NullableInteger", "documentation": "<p>Aggregation period in seconds.</p>"}, "ForecastStatistics": {"shape": "NullableBoolean", "documentation": "<p>The forecasted high and low fault count values. Forecast enabled requests require the EntitySelectorExpression ID be provided.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetTimeSeriesServiceStatisticsResult": {"type": "structure", "members": {"TimeSeriesServiceStatistics": {"shape": "TimeSeriesServiceStatisticsList", "documentation": "<p>The collection of statistics.</p>"}, "ContainsOldGroupVersions": {"shape": "Boolean", "documentation": "<p>A flag indicating whether or not a group's filter expression has been consistent, or if a returned aggregation might show statistics from an older version of the group's filter expression.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetTraceGraphRequest": {"type": "structure", "required": ["TraceIds"], "members": {"TraceIds": {"shape": "TraceIdList", "documentation": "<p>Trace IDs of requests for which to generate a service graph.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetTraceGraphResult": {"type": "structure", "members": {"Services": {"shape": "ServiceList", "documentation": "<p>The services that have processed one of the specified requests.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Pagination token.</p>"}}}, "GetTraceSummariesRequest": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The start of the time frame for which to retrieve traces.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end of the time frame for which to retrieve traces.</p>"}, "TimeRangeType": {"shape": "TimeRangeType", "documentation": "<p>A parameter to indicate whether to query trace summaries by TraceId, Event (trace update time), or Service (segment end time).</p>"}, "Sampling": {"shape": "NullableBoolean", "documentation": "<p>Set to <code>true</code> to get summaries for only a subset of available traces.</p>"}, "SamplingStrategy": {"shape": "SamplingStrategy", "documentation": "<p>A parameter to indicate whether to enable sampling on trace summaries. Input parameters are Name and Value.</p>"}, "FilterExpression": {"shape": "FilterExpression", "documentation": "<p>Specify a filter expression to retrieve trace summaries for services or requests that meet certain requirements.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Specify the pagination token returned by a previous request to retrieve the next page of results.</p>"}}}, "GetTraceSummariesResult": {"type": "structure", "members": {"TraceSummaries": {"shape": "TraceSummaryList", "documentation": "<p>Trace IDs and annotations for traces that were found in the specified time frame.</p>"}, "ApproximateTime": {"shape": "Timestamp", "documentation": "<p>The start time of this page of results.</p>"}, "TracesProcessedCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The total number of traces processed, including traces that did not match the specified filter expression.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>If the requested time frame contained more than one page of results, you can use this token to retrieve the next page. The first page contains the most recent results, closest to the end of the time frame.</p>"}}}, "Group": {"type": "structure", "members": {"GroupName": {"shape": "String", "documentation": "<p>The unique case-sensitive name of the group.</p>"}, "GroupARN": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the group generated based on the GroupName.</p>"}, "FilterExpression": {"shape": "String", "documentation": "<p>The filter expression defining the parameters to include traces.</p>"}, "InsightsConfiguration": {"shape": "InsightsConfiguration", "documentation": "<p>The structure containing configurations related to insights.</p> <ul> <li> <p>The InsightsEnabled boolean can be set to true to enable insights for the group or false to disable insights for the group.</p> </li> <li> <p>The NotificationsEnabled boolean can be set to true to enable insights notifications through Amazon EventBridge for the group.</p> </li> </ul>"}}, "documentation": "<p>Details and metadata for a group.</p>"}, "GroupARN": {"type": "string", "max": 400, "min": 1}, "GroupName": {"type": "string", "max": 32, "min": 1}, "GroupSummary": {"type": "structure", "members": {"GroupName": {"shape": "String", "documentation": "<p>The unique case-sensitive name of the group.</p>"}, "GroupARN": {"shape": "String", "documentation": "<p>The ARN of the group generated based on the GroupName.</p>"}, "FilterExpression": {"shape": "String", "documentation": "<p>The filter expression defining the parameters to include traces.</p>"}, "InsightsConfiguration": {"shape": "InsightsConfiguration", "documentation": "<p>The structure containing configurations related to insights.</p> <ul> <li> <p>The InsightsEnabled boolean can be set to true to enable insights for the group or false to disable insights for the group.</p> </li> <li> <p>The NotificationsEnabled boolean can be set to true to enable insights notifications. Notifications can only be enabled on a group with InsightsEnabled set to true.</p> </li> </ul>"}}, "documentation": "<p>Details for a group without metadata.</p>"}, "GroupSummaryList": {"type": "list", "member": {"shape": "GroupSummary"}}, "HTTPMethod": {"type": "string", "max": 10}, "Histogram": {"type": "list", "member": {"shape": "HistogramEntry"}}, "HistogramEntry": {"type": "structure", "members": {"Value": {"shape": "Double", "documentation": "<p>The value of the entry.</p>"}, "Count": {"shape": "Integer", "documentation": "<p>The prevalence of the entry.</p>"}}, "documentation": "<p>An entry in a histogram for a statistic. A histogram maps the range of observed values on the X axis, and the prevalence of each value on the Y axis.</p>"}, "Host": {"type": "string", "max": 64}, "Hostname": {"type": "string", "max": 255}, "Http": {"type": "structure", "members": {"HttpURL": {"shape": "String", "documentation": "<p>The request URL.</p>"}, "HttpStatus": {"shape": "NullableInteger", "documentation": "<p>The response status.</p>"}, "HttpMethod": {"shape": "String", "documentation": "<p>The request method.</p>"}, "UserAgent": {"shape": "String", "documentation": "<p>The request's user agent string.</p>"}, "ClientIp": {"shape": "String", "documentation": "<p>The IP address of the requestor.</p>"}}, "documentation": "<p>Information about an HTTP request.</p>"}, "Insight": {"type": "structure", "members": {"InsightId": {"shape": "InsightId", "documentation": "<p>The insights unique identifier. </p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The Amazon Resource Name (ARN) of the group that the insight belongs to.</p>"}, "GroupName": {"shape": "GroupName", "documentation": "<p>The name of the group that the insight belongs to.</p>"}, "RootCauseServiceId": {"shape": "ServiceId"}, "Categories": {"shape": "InsightCategoryList", "documentation": "<p>The categories that label and describe the type of insight.</p>"}, "State": {"shape": "InsightState", "documentation": "<p>The current state of the insight.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the insight began.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the insight ended.</p>"}, "Summary": {"shape": "InsightSummaryText", "documentation": "<p>A brief description of the insight.</p>"}, "ClientRequestImpactStatistics": {"shape": "RequestImpactStatistics", "documentation": "<p>The impact statistics of the client side service. This includes the number of requests to the client service and whether the requests were faults or okay.</p>"}, "RootCauseServiceRequestImpactStatistics": {"shape": "RequestImpactStatistics", "documentation": "<p>The impact statistics of the root cause service. This includes the number of requests to the client service and whether the requests were faults or okay.</p>"}, "TopAnomalousServices": {"shape": "AnomalousServiceList", "documentation": "<p>The service within the insight that is most impacted by the incident.</p>"}}, "documentation": "<p>When fault rates go outside of the expected range, X-Ray creates an insight. Insights tracks emergent issues within your applications.</p>"}, "InsightCategory": {"type": "string", "enum": ["FAULT"]}, "InsightCategoryList": {"type": "list", "member": {"shape": "InsightCategory"}}, "InsightEvent": {"type": "structure", "members": {"Summary": {"shape": "EventSummaryText", "documentation": "<p>A brief description of the event.</p>"}, "EventTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the event was recorded.</p>"}, "ClientRequestImpactStatistics": {"shape": "RequestImpactStatistics", "documentation": "<p>The impact statistics of the client side service. This includes the number of requests to the client service and whether the requests were faults or okay.</p>"}, "RootCauseServiceRequestImpactStatistics": {"shape": "RequestImpactStatistics", "documentation": "<p>The impact statistics of the root cause service. This includes the number of requests to the client service and whether the requests were faults or okay.</p>"}, "TopAnomalousServices": {"shape": "AnomalousServiceList", "documentation": "<p>The service during the event that is most impacted by the incident.</p>"}}, "documentation": "<p>X-Ray reevaluates insights periodically until they are resolved, and records each intermediate state in an event. You can review incident events in the Impact Timeline on the Inspect page in the X-Ray console.</p>"}, "InsightEventList": {"type": "list", "member": {"shape": "InsightEvent"}}, "InsightId": {"type": "string", "pattern": "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}"}, "InsightImpactGraphEdge": {"type": "structure", "members": {"ReferenceId": {"shape": "NullableInteger", "documentation": "<p>Identifier of the edge. Unique within a service map.</p>"}}, "documentation": "<p>The connection between two service in an insight impact graph.</p>"}, "InsightImpactGraphEdgeList": {"type": "list", "member": {"shape": "InsightImpactGraphEdge"}}, "InsightImpactGraphService": {"type": "structure", "members": {"ReferenceId": {"shape": "NullableInteger", "documentation": "<p>Identifier for the service. Unique within the service map.</p>"}, "Type": {"shape": "String", "documentation": "<p>Identifier for the service. Unique within the service map.</p> <ul> <li> <p>Amazon Web Services Resource - The type of an Amazon Web Services resource. For example, AWS::EC2::Instance for an application running on Amazon EC2 or AWS::DynamoDB::Table for an Amazon DynamoDB table that the application used. </p> </li> <li> <p>Amazon Web Services Service - The type of an Amazon Web Services service. For example, AWS::DynamoDB for downstream calls to Amazon DynamoDB that didn't target a specific table. </p> </li> <li> <p>Amazon Web Services Service - The type of an Amazon Web Services service. For example, AWS::DynamoDB for downstream calls to Amazon DynamoDB that didn't target a specific table. </p> </li> <li> <p>remote - A downstream service of indeterminate type.</p> </li> </ul>"}, "Name": {"shape": "String", "documentation": "<p>The canonical name of the service.</p>"}, "Names": {"shape": "ServiceNames", "documentation": "<p>A list of names for the service, including the canonical name.</p>"}, "AccountId": {"shape": "String", "documentation": "<p>Identifier of the Amazon Web Services account in which the service runs.</p>"}, "Edges": {"shape": "InsightImpactGraphEdgeList", "documentation": "<p>Connections to downstream services.</p>"}}, "documentation": "<p>Information about an application that processed requests, users that made requests, or downstream services, resources, and applications that an application used. </p>"}, "InsightImpactGraphServiceList": {"type": "list", "member": {"shape": "InsightImpactGraphService"}}, "InsightState": {"type": "string", "enum": ["ACTIVE", "CLOSED"]}, "InsightStateList": {"type": "list", "member": {"shape": "InsightState"}, "max": 1, "min": 0}, "InsightSummary": {"type": "structure", "members": {"InsightId": {"shape": "InsightId", "documentation": "<p>The insights unique identifier. </p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The Amazon Resource Name (ARN) of the group that the insight belongs to.</p>"}, "GroupName": {"shape": "GroupName", "documentation": "<p>The name of the group that the insight belongs to.</p>"}, "RootCauseServiceId": {"shape": "ServiceId"}, "Categories": {"shape": "InsightCategoryList", "documentation": "<p> Categories The categories that label and describe the type of insight.</p>"}, "State": {"shape": "InsightState", "documentation": "<p>The current state of the insight.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the insight began.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, at which the insight ended.</p>"}, "Summary": {"shape": "InsightSummaryText", "documentation": "<p>A brief description of the insight.</p>"}, "ClientRequestImpactStatistics": {"shape": "RequestImpactStatistics", "documentation": "<p>The impact statistics of the client side service. This includes the number of requests to the client service and whether the requests were faults or okay. </p>"}, "RootCauseServiceRequestImpactStatistics": {"shape": "RequestImpactStatistics", "documentation": "<p>The impact statistics of the root cause service. This includes the number of requests to the client service and whether the requests were faults or okay. </p>"}, "TopAnomalousServices": {"shape": "AnomalousServiceList", "documentation": "<p>The service within the insight that is most impacted by the incident.</p>"}, "LastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The time, in Unix seconds, that the insight was last updated.</p>"}}, "documentation": "<p>Information that describes an insight.</p>"}, "InsightSummaryList": {"type": "list", "member": {"shape": "InsightSummary"}}, "InsightSummaryText": {"type": "string"}, "InsightsConfiguration": {"type": "structure", "members": {"InsightsEnabled": {"shape": "NullableBoolean", "documentation": "<p>Set the InsightsEnabled value to true to enable insights or false to disable insights.</p>"}, "NotificationsEnabled": {"shape": "NullableBoolean", "documentation": "<p>Set the NotificationsEnabled value to true to enable insights notifications. Notifications can only be enabled on a group with InsightsEnabled set to true.</p>"}}, "documentation": "<p>The structure containing configurations related to insights.</p>"}, "InstanceIdDetail": {"type": "structure", "members": {"Id": {"shape": "String", "documentation": "<p>The ID of a corresponding EC2 instance.</p>"}}, "documentation": "<p>A list of EC2 instance IDs corresponding to the segments in a trace. </p>"}, "Integer": {"type": "integer"}, "InvalidPolicyRevisionIdException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A policy revision id was provided which does not match the latest policy revision. This exception is also if a policy revision id of 0 is provided via <code>PutResourcePolicy</code> and a policy with the same name already exists.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request is missing required parameters or has invalid parameters.</p>", "exception": true}, "ListResourcePoliciesRequest": {"type": "structure", "members": {"NextToken": {"shape": "ResourcePolicyNextToken", "documentation": "<p>Not currently supported.</p>"}}}, "ListResourcePoliciesResult": {"type": "structure", "members": {"ResourcePolicies": {"shape": "ResourcePolicyList", "documentation": "<p>The list of resource policies in the target Amazon Web Services account.</p>"}, "NextToken": {"shape": "ResourcePolicyNextToken", "documentation": "<p>Pagination token. Not currently supported.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Number (ARN) of an X-Ray group or sampling rule.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token. If multiple pages of results are returned, use the <code>NextToken</code> value returned with the current page of results as the value of this parameter to get the next page of results.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>A list of tags, as key and value pairs, that is associated with the specified X-Ray group or sampling rule.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token. If multiple pages of results are returned, use the <code>NextToken</code> value returned with the current page of results to get the next page of results.</p>"}}}, "LockoutPreventionException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The provided resource policy would prevent the caller of this request from calling PutResourcePolicy in the future.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "MalformedPolicyDocumentException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Invalid policy document provided in request.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "NullableBoolean": {"type": "boolean"}, "NullableDouble": {"type": "double"}, "NullableInteger": {"type": "integer"}, "NullableLong": {"type": "long"}, "PolicyCountLimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Exceeded the maximum number of resource policies for a target Amazon Web Services account.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "PolicyDocument": {"type": "string"}, "PolicyName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\w+=,.@-]+"}, "PolicyRevisionId": {"type": "string"}, "PolicySizeLimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Exceeded the maximum size for a resource policy.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Priority": {"type": "integer", "max": 9999, "min": 1}, "PutEncryptionConfigRequest": {"type": "structure", "required": ["Type"], "members": {"KeyId": {"shape": "EncryptionKeyId", "documentation": "<p>An Amazon Web Services KMS key in one of the following formats:</p> <ul> <li> <p> <b><PERSON><PERSON></b> - The name of the key. For example, <code>alias/<PERSON><PERSON><PERSON></code>.</p> </li> <li> <p> <b>Key ID</b> - The KMS key ID of the key. For example, <code>ae4aa6d49-a4d8-9df9-a475-4ff6d7898456</code>. Amazon Web Services X-Ray does not support asymmetric KMS keys.</p> </li> <li> <p> <b>ARN</b> - The full Amazon Resource Name of the key ID or alias. For example, <code>arn:aws:kms:us-east-2:************:key/ae4aa6d49-a4d8-9df9-a475-4ff6d7898456</code>. Use this format to specify a key in a different account.</p> </li> </ul> <p>Omit this key if you set <code>Type</code> to <code>NONE</code>.</p>"}, "Type": {"shape": "EncryptionType", "documentation": "<p>The type of encryption. Set to <code>KMS</code> to use your own key for encryption. Set to <code>NONE</code> for default encryption.</p>"}}}, "PutEncryptionConfigResult": {"type": "structure", "members": {"EncryptionConfig": {"shape": "EncryptionConfig", "documentation": "<p>The new encryption configuration.</p>"}}}, "PutResourcePolicyRequest": {"type": "structure", "required": ["PolicyName", "PolicyDocument"], "members": {"PolicyName": {"shape": "PolicyName", "documentation": "<p>The name of the resource policy. Must be unique within a specific Amazon Web Services account.</p>"}, "PolicyDocument": {"shape": "PolicyDocument", "documentation": "<p>The resource policy document, which can be up to 5kb in size.</p>"}, "PolicyRevisionId": {"shape": "PolicyRevisionId", "documentation": "<p>Specifies a specific policy revision, to ensure an atomic create operation. By default the resource policy is created if it does not exist, or updated with an incremented revision id. The revision id is unique to each policy in the account.</p> <p>If the policy revision id does not match the latest revision id, the operation will fail with an <code>InvalidPolicyRevisionIdException</code> exception. You can also provide a <code>PolicyRevisionId</code> of 0. In this case, the operation will fail with an <code>InvalidPolicyRevisionIdException</code> exception if a resource policy with the same name already exists. </p>"}, "BypassPolicyLockoutCheck": {"shape": "Boolean", "documentation": "<p>A flag to indicate whether to bypass the resource policy lockout safety check.</p> <important> <p>Setting this value to true increases the risk that the policy becomes unmanageable. Do not set this value to true indiscriminately.</p> </important> <p>Use this parameter only when you include a policy in the request and you intend to prevent the principal that is making the request from making a subsequent <code>PutResourcePolicy</code> request.</p> <p>The default value is false.</p>"}}}, "PutResourcePolicyResult": {"type": "structure", "members": {"ResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p>The resource policy document, as provided in the <code>PutResourcePolicyRequest</code>.</p>"}}}, "PutTelemetryRecordsRequest": {"type": "structure", "required": ["TelemetryRecords"], "members": {"TelemetryRecords": {"shape": "TelemetryRecordList", "documentation": "<p/>"}, "EC2InstanceId": {"shape": "EC2InstanceId", "documentation": "<p/>"}, "Hostname": {"shape": "Hostname", "documentation": "<p/>"}, "ResourceARN": {"shape": "ResourceARN", "documentation": "<p/>"}}}, "PutTelemetryRecordsResult": {"type": "structure", "members": {}}, "PutTraceSegmentsRequest": {"type": "structure", "required": ["TraceSegmentDocuments"], "members": {"TraceSegmentDocuments": {"shape": "TraceSegmentDocumentList", "documentation": "<p>A string containing a JSON document defining one or more segments or subsegments.</p>"}}}, "PutTraceSegmentsResult": {"type": "structure", "members": {"UnprocessedTraceSegments": {"shape": "UnprocessedTraceSegmentList", "documentation": "<p>Segments that failed processing.</p>"}}}, "RequestCount": {"type": "integer", "min": 0}, "RequestImpactStatistics": {"type": "structure", "members": {"FaultCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of requests that have resulted in a fault,</p>"}, "OkCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of successful requests.</p>"}, "TotalCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The total number of requests to the service.</p>"}}, "documentation": "<p>Statistics that describe how the incident has impacted a service.</p>"}, "ReservoirSize": {"type": "integer", "min": 0}, "ResourceARN": {"type": "string", "max": 500}, "ResourceARNDetail": {"type": "structure", "members": {"ARN": {"shape": "String", "documentation": "<p>The ARN of a corresponding resource.</p>"}}, "documentation": "<p>A list of resources ARNs corresponding to the segments in a trace.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "ResourceName": {"shape": "AmazonResourceName"}}, "documentation": "<p>The resource was not found. Verify that the name or Amazon Resource Name (ARN) of the resource is correct.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourcePolicy": {"type": "structure", "members": {"PolicyName": {"shape": "PolicyName", "documentation": "<p>The name of the resource policy. Must be unique within a specific Amazon Web Services account.</p>"}, "PolicyDocument": {"shape": "PolicyDocument", "documentation": "<p>The resource policy document, which can be up to 5kb in size.</p>"}, "PolicyRevisionId": {"shape": "PolicyRevisionId", "documentation": "<p>Returns the current policy revision id for this policy name.</p>"}, "LastUpdatedTime": {"shape": "Timestamp", "documentation": "<p>When the policy was last updated, in Unix time seconds.</p>"}}, "documentation": "<p>A resource policy grants one or more Amazon Web Services services and accounts permissions to access X-Ray. Each resource policy is associated with a specific Amazon Web Services account.</p>"}, "ResourcePolicyList": {"type": "list", "member": {"shape": "ResourcePolicy"}}, "ResourcePolicyNextToken": {"type": "string", "max": 100, "min": 1}, "ResponseTimeRootCause": {"type": "structure", "members": {"Services": {"shape": "ResponseTimeRootCauseServices", "documentation": "<p>A list of corresponding services. A service identifies a segment and contains a name, account ID, type, and inferred flag.</p>"}, "ClientImpacting": {"shape": "NullableBoolean", "documentation": "<p>A flag that denotes that the root cause impacts the trace client.</p>"}}, "documentation": "<p>The root cause information for a response time warning.</p>"}, "ResponseTimeRootCauseEntity": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the entity.</p>"}, "Coverage": {"shape": "NullableDouble", "documentation": "<p>The type and messages of the exceptions.</p>"}, "Remote": {"shape": "NullableBoolean", "documentation": "<p>A flag that denotes a remote subsegment.</p>"}}, "documentation": "<p>A collection of segments and corresponding subsegments associated to a response time warning.</p>"}, "ResponseTimeRootCauseEntityPath": {"type": "list", "member": {"shape": "ResponseTimeRootCauseEntity"}}, "ResponseTimeRootCauseService": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The service name.</p>"}, "Names": {"shape": "ServiceNames", "documentation": "<p>A collection of associated service names.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type associated to the service.</p>"}, "AccountId": {"shape": "String", "documentation": "<p>The account ID associated to the service.</p>"}, "EntityPath": {"shape": "ResponseTimeRootCauseEntityPath", "documentation": "<p>The path of root cause entities found on the service. </p>"}, "Inferred": {"shape": "NullableBoolean", "documentation": "<p>A Boolean value indicating if the service is inferred from the trace.</p>"}}, "documentation": "<p>A collection of fields identifying the service in a response time warning.</p>"}, "ResponseTimeRootCauseServices": {"type": "list", "member": {"shape": "ResponseTimeRootCauseService"}}, "ResponseTimeRootCauses": {"type": "list", "member": {"shape": "ResponseTimeRootCause"}}, "RootCauseException": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the exception.</p>"}, "Message": {"shape": "String", "documentation": "<p>The message of the exception.</p>"}}, "documentation": "<p>The exception associated with a root cause.</p>"}, "RootCauseExceptions": {"type": "list", "member": {"shape": "RootCauseException"}}, "RuleLimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have reached the maximum number of sampling rules.</p>", "exception": true}, "RuleName": {"type": "string", "max": 32, "min": 1}, "SampledCount": {"type": "integer", "min": 0}, "SamplingRule": {"type": "structure", "required": ["ResourceARN", "Priority", "FixedRate", "ReservoirSize", "ServiceName", "ServiceType", "Host", "HTTPMethod", "URLPath", "Version"], "members": {"RuleName": {"shape": "RuleName", "documentation": "<p>The name of the sampling rule. Specify a rule by either name or ARN, but not both.</p>"}, "RuleARN": {"shape": "String", "documentation": "<p>The ARN of the sampling rule. Specify a rule by either name or ARN, but not both.</p>"}, "ResourceARN": {"shape": "ResourceARN", "documentation": "<p>Matches the ARN of the Amazon Web Services resource on which the service runs.</p>"}, "Priority": {"shape": "Priority", "documentation": "<p>The priority of the sampling rule.</p>"}, "FixedRate": {"shape": "FixedRate", "documentation": "<p>The percentage of matching requests to instrument, after the reservoir is exhausted.</p>"}, "ReservoirSize": {"shape": "ReservoirSize", "documentation": "<p>A fixed number of matching requests to instrument per second, prior to applying the fixed rate. The reservoir is not used directly by services, but applies to all services using the rule collectively.</p>"}, "ServiceName": {"shape": "ServiceName", "documentation": "<p>Matches the <code>name</code> that the service uses to identify itself in segments.</p>"}, "ServiceType": {"shape": "ServiceType", "documentation": "<p>Matches the <code>origin</code> that the service uses to identify its type in segments.</p>"}, "Host": {"shape": "Host", "documentation": "<p>Matches the hostname from a request URL.</p>"}, "HTTPMethod": {"shape": "HTTPMethod", "documentation": "<p>Matches the HTTP method of a request.</p>"}, "URLPath": {"shape": "URLPath", "documentation": "<p>Matches the path from a request URL.</p>"}, "Version": {"shape": "Version", "documentation": "<p>The version of the sampling rule format (<code>1</code>).</p>"}, "Attributes": {"shape": "AttributeMap", "documentation": "<p>Matches attributes derived from the request.</p>"}}, "documentation": "<p>A sampling rule that services use to decide whether to instrument a request. Rule fields can match properties of the service, or properties of a request. The service can ignore rules that don't match its properties.</p>"}, "SamplingRuleRecord": {"type": "structure", "members": {"SamplingRule": {"shape": "SamplingRule", "documentation": "<p>The sampling rule.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>When the rule was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>When the rule was last modified.</p>"}}, "documentation": "<p>A <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_SamplingRule.html\">SamplingRule</a> and its metadata.</p>"}, "SamplingRuleRecordList": {"type": "list", "member": {"shape": "SamplingRuleRecord"}}, "SamplingRuleUpdate": {"type": "structure", "members": {"RuleName": {"shape": "RuleName", "documentation": "<p>The name of the sampling rule. Specify a rule by either name or ARN, but not both.</p>"}, "RuleARN": {"shape": "String", "documentation": "<p>The ARN of the sampling rule. Specify a rule by either name or ARN, but not both.</p>"}, "ResourceARN": {"shape": "ResourceARN", "documentation": "<p>Matches the ARN of the Amazon Web Services resource on which the service runs.</p>"}, "Priority": {"shape": "NullableInteger", "documentation": "<p>The priority of the sampling rule.</p>"}, "FixedRate": {"shape": "NullableDouble", "documentation": "<p>The percentage of matching requests to instrument, after the reservoir is exhausted.</p>"}, "ReservoirSize": {"shape": "NullableInteger", "documentation": "<p>A fixed number of matching requests to instrument per second, prior to applying the fixed rate. The reservoir is not used directly by services, but applies to all services using the rule collectively.</p>"}, "Host": {"shape": "Host", "documentation": "<p>Matches the hostname from a request URL.</p>"}, "ServiceName": {"shape": "ServiceName", "documentation": "<p>Matches the <code>name</code> that the service uses to identify itself in segments.</p>"}, "ServiceType": {"shape": "ServiceType", "documentation": "<p>Matches the <code>origin</code> that the service uses to identify its type in segments.</p>"}, "HTTPMethod": {"shape": "HTTPMethod", "documentation": "<p>Matches the HTTP method of a request.</p>"}, "URLPath": {"shape": "URLPath", "documentation": "<p>Matches the path from a request URL.</p>"}, "Attributes": {"shape": "AttributeMap", "documentation": "<p>Matches attributes derived from the request.</p>"}}, "documentation": "<p>A document specifying changes to a sampling rule's configuration.</p>"}, "SamplingStatisticSummary": {"type": "structure", "members": {"RuleName": {"shape": "String", "documentation": "<p>The name of the sampling rule.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>The start time of the reporting window.</p>"}, "RequestCount": {"shape": "Integer", "documentation": "<p>The number of requests that matched the rule.</p>"}, "BorrowCount": {"shape": "Integer", "documentation": "<p>The number of requests recorded with borrowed reservoir quota.</p>"}, "SampledCount": {"shape": "Integer", "documentation": "<p>The number of requests recorded.</p>"}}, "documentation": "<p>Aggregated request sampling data for a sampling rule across all services for a 10-second window.</p>"}, "SamplingStatisticSummaryList": {"type": "list", "member": {"shape": "SamplingStatisticSummary"}}, "SamplingStatisticsDocument": {"type": "structure", "required": ["RuleName", "ClientID", "Timestamp", "RequestCount", "SampledCount"], "members": {"RuleName": {"shape": "RuleName", "documentation": "<p>The name of the sampling rule.</p>"}, "ClientID": {"shape": "ClientID", "documentation": "<p>A unique identifier for the service in hexadecimal.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>The current time.</p>"}, "RequestCount": {"shape": "RequestCount", "documentation": "<p>The number of requests that matched the rule.</p>"}, "SampledCount": {"shape": "SampledCount", "documentation": "<p>The number of requests recorded.</p>"}, "BorrowCount": {"shape": "BorrowCount", "documentation": "<p>The number of requests recorded with borrowed reservoir quota.</p>"}}, "documentation": "<p>Request sampling results for a single rule from a service. Results are for the last 10 seconds unless the service has been assigned a longer reporting interval after a previous call to <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_GetSamplingTargets.html\">GetSamplingTargets</a>.</p>"}, "SamplingStatisticsDocumentList": {"type": "list", "member": {"shape": "SamplingStatisticsDocument"}, "max": 25}, "SamplingStrategy": {"type": "structure", "members": {"Name": {"shape": "SamplingStrategyName", "documentation": "<p>The name of a sampling rule.</p>"}, "Value": {"shape": "NullableDouble", "documentation": "<p>The value of a sampling rule.</p>"}}, "documentation": "<p>The name and value of a sampling rule to apply to a trace summary.</p>"}, "SamplingStrategyName": {"type": "string", "enum": ["PartialScan", "FixedRate"]}, "SamplingTargetDocument": {"type": "structure", "members": {"RuleName": {"shape": "String", "documentation": "<p>The name of the sampling rule.</p>"}, "FixedRate": {"shape": "Double", "documentation": "<p>The percentage of matching requests to instrument, after the reservoir is exhausted.</p>"}, "ReservoirQuota": {"shape": "NullableInteger", "documentation": "<p>The number of requests per second that X-Ray allocated for this service.</p>"}, "ReservoirQuotaTTL": {"shape": "Timestamp", "documentation": "<p>When the reservoir quota expires.</p>"}, "Interval": {"shape": "NullableInteger", "documentation": "<p>The number of seconds for the service to wait before getting sampling targets again.</p>"}}, "documentation": "<p>Temporary changes to a sampling rule configuration. To meet the global sampling target for a rule, X-Ray calculates a new reservoir for each service based on the recent sampling results of all services that called <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_GetSamplingTargets.html\">GetSamplingTargets</a>.</p>"}, "SamplingTargetDocumentList": {"type": "list", "member": {"shape": "SamplingTargetDocument"}}, "Segment": {"type": "structure", "members": {"Id": {"shape": "SegmentId", "documentation": "<p>The segment's ID.</p>"}, "Document": {"shape": "SegmentDocument", "documentation": "<p>The segment document.</p>"}}, "documentation": "<p>A segment from a trace that has been ingested by the X-Ray service. The segment can be compiled from documents uploaded with <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_PutTraceSegments.html\">PutTraceSegments</a>, or an <code>inferred</code> segment for a downstream service, generated from a subsegment sent by the service that called it.</p> <p>For the full segment document schema, see <a href=\"https://docs.aws.amazon.com/xray/latest/devguide/xray-api-segmentdocuments.html\">Amazon Web Services X-Ray Segment Documents</a> in the <i>Amazon Web Services X-Ray Developer Guide</i>.</p>"}, "SegmentDocument": {"type": "string", "min": 1}, "SegmentId": {"type": "string"}, "SegmentList": {"type": "list", "member": {"shape": "Segment"}}, "Service": {"type": "structure", "members": {"ReferenceId": {"shape": "NullableInteger", "documentation": "<p>Identifier for the service. Unique within the service map.</p>"}, "Name": {"shape": "String", "documentation": "<p>The canonical name of the service.</p>"}, "Names": {"shape": "ServiceNames", "documentation": "<p>A list of names for the service, including the canonical name.</p>"}, "Root": {"shape": "NullableBoolean", "documentation": "<p>Indicates that the service was the first service to process a request.</p>"}, "AccountId": {"shape": "String", "documentation": "<p>Identifier of the Amazon Web Services account in which the service runs.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of service.</p> <ul> <li> <p>Amazon Web Services Resource - The type of an Amazon Web Services resource. For example, <code>AWS::EC2::Instance</code> for an application running on Amazon EC2 or <code>AWS::DynamoDB::Table</code> for an Amazon DynamoDB table that the application used.</p> </li> <li> <p>Amazon Web Services Service - The type of an Amazon Web Services service. For example, <code>AWS::DynamoDB</code> for downstream calls to Amazon DynamoDB that didn't target a specific table.</p> </li> <li> <p> <code>client</code> - Represents the clients that sent requests to a root service.</p> </li> <li> <p> <code>remote</code> - A downstream service of indeterminate type.</p> </li> </ul>"}, "State": {"shape": "String", "documentation": "<p>The service's state.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The start time of the first segment that the service generated.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end time of the last segment that the service generated.</p>"}, "Edges": {"shape": "EdgeList", "documentation": "<p>Connections to downstream services.</p>"}, "SummaryStatistics": {"shape": "ServiceStatistics", "documentation": "<p>Aggregated statistics for the service.</p>"}, "DurationHistogram": {"shape": "Histogram", "documentation": "<p>A histogram that maps the spread of service durations.</p>"}, "ResponseTimeHistogram": {"shape": "Histogram", "documentation": "<p>A histogram that maps the spread of service response times.</p>"}}, "documentation": "<p>Information about an application that processed requests, users that made requests, or downstream services, resources, and applications that an application used.</p>"}, "ServiceId": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p/>"}, "Names": {"shape": "ServiceNames", "documentation": "<p/>"}, "AccountId": {"shape": "String", "documentation": "<p/>"}, "Type": {"shape": "String", "documentation": "<p/>"}}, "documentation": "<p/>"}, "ServiceIds": {"type": "list", "member": {"shape": "ServiceId"}}, "ServiceList": {"type": "list", "member": {"shape": "Service"}}, "ServiceName": {"type": "string", "max": 64}, "ServiceNames": {"type": "list", "member": {"shape": "String"}}, "ServiceStatistics": {"type": "structure", "members": {"OkCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The number of requests that completed with a 2xx Success status code.</p>"}, "ErrorStatistics": {"shape": "ErrorStatistics", "documentation": "<p>Information about requests that failed with a 4xx Client Error status code.</p>"}, "FaultStatistics": {"shape": "FaultStatistics", "documentation": "<p>Information about requests that failed with a 5xx Server Error status code.</p>"}, "TotalCount": {"shape": "<PERSON>ullable<PERSON><PERSON>", "documentation": "<p>The total number of completed requests.</p>"}, "TotalResponseTime": {"shape": "NullableDouble", "documentation": "<p>The aggregate response time of completed requests.</p>"}}, "documentation": "<p>Response statistics for a service.</p>"}, "ServiceType": {"type": "string", "max": 64}, "String": {"type": "string"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>A tag key, such as <code>Stage</code> or <code>Name</code>. A tag key cannot be empty. The key can be a maximum of 128 characters, and can contain only Unicode letters, numbers, or separators, or the following special characters: <code>+ - = . _ : /</code> </p>"}, "Value": {"shape": "TagValue", "documentation": "<p>An optional tag value, such as <code>Production</code> or <code>test-only</code>. The value can be a maximum of 255 characters, and contain only Unicode letters, numbers, or separators, or the following special characters: <code>+ - = . _ : /</code> </p>"}}, "documentation": "<p>A map that contains tag keys and tag values to attach to an Amazon Web Services X-Ray group or sampling rule. For more information about ways to use tags, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> in the <i>Amazon Web Services General Reference</i>.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>Maximum number of user-applied tags per resource: 50</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Don't use <code>aws:</code> as a prefix for keys; it's reserved for Amazon Web Services use. You cannot edit or delete system tags.</p> </li> </ul>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Number (ARN) of an X-Ray group or sampling rule.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map that contains one or more tag keys and tag values to attach to an X-Ray group or sampling rule. For more information about ways to use tags, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> in the <i>Amazon Web Services General Reference</i>.</p> <p>The following restrictions apply to tags:</p> <ul> <li> <p>Maximum number of user-applied tags per resource: 50</p> </li> <li> <p>Maximum tag key length: 128 Unicode characters</p> </li> <li> <p>Maximum tag value length: 256 Unicode characters</p> </li> <li> <p>Valid values for key and value: a-z, A-Z, 0-9, space, and the following characters: _ . : / = + - and @</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Don't use <code>aws:</code> as a prefix for keys; it's reserved for Amazon Web Services use. You cannot edit or delete system tags.</p> </li> </ul>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TelemetryRecord": {"type": "structure", "required": ["Timestamp"], "members": {"Timestamp": {"shape": "Timestamp", "documentation": "<p/>"}, "SegmentsReceivedCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "SegmentsSentCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "SegmentsSpilloverCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "SegmentsRejectedCount": {"shape": "NullableInteger", "documentation": "<p/>"}, "BackendConnectionErrors": {"shape": "BackendConnectionErrors", "documentation": "<p/>"}}, "documentation": "<p/>"}, "TelemetryRecordList": {"type": "list", "member": {"shape": "TelemetryRecord"}}, "ThrottledException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request exceeds the maximum number of requests per second.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TimeRangeType": {"type": "string", "enum": ["TraceId", "Event", "Service"]}, "TimeSeriesServiceStatistics": {"type": "structure", "members": {"Timestamp": {"shape": "Timestamp", "documentation": "<p>Timestamp of the window for which statistics are aggregated.</p>"}, "EdgeSummaryStatistics": {"shape": "EdgeStatistics"}, "ServiceSummaryStatistics": {"shape": "ServiceStatistics"}, "ServiceForecastStatistics": {"shape": "ForecastStatistics", "documentation": "<p>The forecasted high and low fault count values.</p>"}, "ResponseTimeHistogram": {"shape": "Histogram", "documentation": "<p>The response time histogram for the selected entities.</p>"}}, "documentation": "<p>A list of TimeSeriesStatistic structures.</p>"}, "TimeSeriesServiceStatisticsList": {"type": "list", "member": {"shape": "TimeSeriesServiceStatistics"}}, "Timestamp": {"type": "timestamp"}, "Token": {"type": "string", "max": 2000, "min": 1}, "TooManyTagsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "ResourceName": {"shape": "AmazonResourceName"}}, "documentation": "<p>You have exceeded the maximum number of tags you can apply to this resource.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Trace": {"type": "structure", "members": {"Id": {"shape": "TraceId", "documentation": "<p>The unique identifier for the request that generated the trace's segments and subsegments.</p>"}, "Duration": {"shape": "NullableDouble", "documentation": "<p>The length of time in seconds between the start time of the root segment and the end time of the last segment that completed.</p>"}, "LimitExceeded": {"shape": "NullableBoolean", "documentation": "<p>LimitExceeded is set to true when the trace has exceeded the <code>Trace document size</code> limit. For more information about this limit and other X-Ray limits and quotas, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/xray.html\">Amazon Web Services X-Ray endpoints and quotas</a>.</p>"}, "Segments": {"shape": "SegmentList", "documentation": "<p>Segment documents for the segments and subsegments that comprise the trace.</p>"}}, "documentation": "<p>A collection of segment documents with matching trace IDs.</p>"}, "TraceAvailabilityZones": {"type": "list", "member": {"shape": "AvailabilityZoneDetail"}}, "TraceId": {"type": "string", "max": 35, "min": 1}, "TraceIdList": {"type": "list", "member": {"shape": "TraceId"}}, "TraceInstanceIds": {"type": "list", "member": {"shape": "InstanceIdDetail"}}, "TraceList": {"type": "list", "member": {"shape": "Trace"}}, "TraceResourceARNs": {"type": "list", "member": {"shape": "ResourceARNDetail"}}, "TraceSegmentDocument": {"type": "string"}, "TraceSegmentDocumentList": {"type": "list", "member": {"shape": "TraceSegmentDocument"}}, "TraceSummary": {"type": "structure", "members": {"Id": {"shape": "TraceId", "documentation": "<p>The unique identifier for the request that generated the trace's segments and subsegments.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The start time of a trace, based on the earliest trace segment start time.</p>"}, "Duration": {"shape": "NullableDouble", "documentation": "<p>The length of time in seconds between the start time of the root segment and the end time of the last segment that completed.</p>"}, "ResponseTime": {"shape": "NullableDouble", "documentation": "<p>The length of time in seconds between the start and end times of the root segment. If the service performs work asynchronously, the response time measures the time before the response is sent to the user, while the duration measures the amount of time before the last traced activity completes.</p>"}, "HasFault": {"shape": "NullableBoolean", "documentation": "<p>The root segment document has a 500 series error.</p>"}, "HasError": {"shape": "NullableBoolean", "documentation": "<p>The root segment document has a 400 series error.</p>"}, "HasThrottle": {"shape": "NullableBoolean", "documentation": "<p>One or more of the segment documents has a 429 throttling error.</p>"}, "IsPartial": {"shape": "NullableBoolean", "documentation": "<p>One or more of the segment documents is in progress.</p>"}, "Http": {"shape": "Http", "documentation": "<p>Information about the HTTP request served by the trace.</p>"}, "Annotations": {"shape": "Annotations", "documentation": "<p>Annotations from the trace's segment documents.</p>"}, "Users": {"shape": "TraceUsers", "documentation": "<p>Users from the trace's segment documents.</p>"}, "ServiceIds": {"shape": "ServiceIds", "documentation": "<p>Service IDs from the trace's segment documents.</p>"}, "ResourceARNs": {"shape": "TraceResourceARNs", "documentation": "<p>A list of resource ARNs for any resource corresponding to the trace segments.</p>"}, "InstanceIds": {"shape": "TraceInstanceIds", "documentation": "<p>A list of EC2 instance IDs for any instance corresponding to the trace segments.</p>"}, "AvailabilityZones": {"shape": "TraceAvailabilityZones", "documentation": "<p>A list of Availability Zones for any zone corresponding to the trace segments.</p>"}, "EntryPoint": {"shape": "ServiceId", "documentation": "<p>The root of a trace.</p>"}, "FaultRootCauses": {"shape": "FaultRootCauses", "documentation": "<p>A collection of FaultRootCause structures corresponding to the trace segments.</p>"}, "ErrorRootCauses": {"shape": "Error<PERSON>oot<PERSON><PERSON><PERSON>", "documentation": "<p>A collection of ErrorRootCause structures corresponding to the trace segments.</p>"}, "ResponseTimeRootCauses": {"shape": "ResponseTimeRootCauses", "documentation": "<p>A collection of ResponseTimeRootCause structures corresponding to the trace segments.</p>"}, "Revision": {"shape": "Integer", "documentation": "<p>The revision number of a trace.</p>"}, "MatchedEventTime": {"shape": "Timestamp", "documentation": "<p>The matched time stamp of a defined event.</p>"}}, "documentation": "<p>Metadata generated from the segment documents in a trace.</p>"}, "TraceSummaryList": {"type": "list", "member": {"shape": "TraceSummary"}}, "TraceUser": {"type": "structure", "members": {"UserName": {"shape": "String", "documentation": "<p>The user's name.</p>"}, "ServiceIds": {"shape": "ServiceIds", "documentation": "<p>Services that the user's request hit.</p>"}}, "documentation": "<p>Information about a user recorded in segment documents.</p>"}, "TraceUsers": {"type": "list", "member": {"shape": "TraceUser"}}, "URLPath": {"type": "string", "max": 128}, "UnprocessedStatistics": {"type": "structure", "members": {"RuleName": {"shape": "String", "documentation": "<p>The name of the sampling rule.</p>"}, "ErrorCode": {"shape": "String", "documentation": "<p>The error code.</p>"}, "Message": {"shape": "String", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Sampling statistics from a call to <a href=\"https://docs.aws.amazon.com/xray/latest/api/API_GetSamplingTargets.html\">GetSamplingTargets</a> that X-Ray could not process.</p>"}, "UnprocessedStatisticsList": {"type": "list", "member": {"shape": "UnprocessedStatistics"}}, "UnprocessedTraceIdList": {"type": "list", "member": {"shape": "TraceId"}}, "UnprocessedTraceSegment": {"type": "structure", "members": {"Id": {"shape": "String", "documentation": "<p>The segment's ID.</p>"}, "ErrorCode": {"shape": "String", "documentation": "<p>The error that caused processing to fail.</p>"}, "Message": {"shape": "String", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Information about a segment that failed processing.</p>"}, "UnprocessedTraceSegmentList": {"type": "list", "member": {"shape": "UnprocessedTraceSegment"}}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Number (ARN) of an X-Ray group or sampling rule.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>Keys for one or more tags that you want to remove from an X-Ray group or sampling rule.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateGroupRequest": {"type": "structure", "members": {"GroupName": {"shape": "GroupName", "documentation": "<p>The case-sensitive name of the group.</p>"}, "GroupARN": {"shape": "GroupARN", "documentation": "<p>The ARN that was generated upon creation.</p>"}, "FilterExpression": {"shape": "FilterExpression", "documentation": "<p>The updated filter expression defining criteria by which to group traces.</p>"}, "InsightsConfiguration": {"shape": "InsightsConfiguration", "documentation": "<p>The structure containing configurations related to insights.</p> <ul> <li> <p>The InsightsEnabled boolean can be set to true to enable insights for the group or false to disable insights for the group.</p> </li> <li> <p>The NotificationsEnabled boolean can be set to true to enable insights notifications for the group. Notifications can only be enabled on a group with InsightsEnabled set to true.</p> </li> </ul>"}}}, "UpdateGroupResult": {"type": "structure", "members": {"Group": {"shape": "Group", "documentation": "<p>The group that was updated. Contains the name of the group that was updated, the ARN of the group that was updated, the updated filter expression, and the updated insight configuration assigned to the group.</p>"}}}, "UpdateSamplingRuleRequest": {"type": "structure", "required": ["SamplingRuleUpdate"], "members": {"SamplingRuleUpdate": {"shape": "SamplingRuleUpdate", "documentation": "<p>The rule and fields to change.</p>"}}}, "UpdateSamplingRuleResult": {"type": "structure", "members": {"SamplingRuleRecord": {"shape": "SamplingRuleRecord", "documentation": "<p>The updated rule definition and metadata.</p>"}}}, "ValueWithServiceIds": {"type": "structure", "members": {"AnnotationValue": {"shape": "AnnotationValue", "documentation": "<p>Values of the annotation.</p>"}, "ServiceIds": {"shape": "ServiceIds", "documentation": "<p>Services to which the annotation applies.</p>"}}, "documentation": "<p>Information about a segment annotation.</p>"}, "ValuesWithServiceIds": {"type": "list", "member": {"shape": "ValueWithServiceIds"}}, "Version": {"type": "integer", "min": 1}}, "documentation": "<p>Amazon Web Services X-Ray provides APIs for managing debug traces and retrieving service maps and other data created by processing those traces.</p>"}