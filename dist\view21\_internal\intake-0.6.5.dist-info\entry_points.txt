[console_scripts]
intake = intake.cli.client.__main__:main
intake-server = intake.cli.server.__main__:main

[intake.drivers]
alias = intake.source.derived:AliasSource
catalog = intake.catalog.base:Catalog
csv = intake.source.csv:CSVSource
intake_remote = intake.catalog.remote:RemoteCatalog
json = intake.source.jsonfiles:JSONFileSource
jsonl = intake.source.jsonfiles:JSONLinesFileSource
ndzarr = intake.source.zarr:ZarrArraySource
numpy = intake.source.npy:NPySource
textfiles = intake.source.textfiles:TextFilesSource
tiled = intake.source.tiled:TiledSource
tiled_cat = intake.source.tiled:TiledCatalog
yaml_file_cat = intake.catalog.local:YAMLFileCatalog
yaml_files_cat = intake.catalog.local:YAMLFilesCatalog
zarr_cat = intake.catalog.zarr:ZarrGroupCatalog

