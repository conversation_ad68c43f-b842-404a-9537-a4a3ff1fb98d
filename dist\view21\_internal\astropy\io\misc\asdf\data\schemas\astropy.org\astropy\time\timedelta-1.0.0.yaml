%YAML 1.1
---
$schema: "http://stsci.edu/schemas/asdf/asdf-schema-1.0.0"
id: "http://astropy.org/schemas/astropy/time/time-1.1.0"
title: Represents an instance of TimeDelta from astropy
description: |
  Represents the time difference between two times.

type: object
properties:
    jd1:
      anyOf:
        - type: number
        - $ref: "tag:stsci.edu:asdf/core/ndarray-1.0.0"
      description: |
        Value representing first 64 bits of precision
    jd2:
      anyOf:
        - type: number
        - $ref: "tag:stsci.edu:asdf/core/ndarray-1.0.0"
      description: |
        Value representing second 64 bits of precision
    format:
      type: string
      description: |
        Format of time value representation.
    scale:
      type: string
      description: |
        Time scale of input value(s).
      enum: [tdb, tt, ut1, tcg, tcb, tai, local]
required: [jd1, jd2, format]
additionalProperties: False
...
