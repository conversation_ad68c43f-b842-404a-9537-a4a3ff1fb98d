../../Scripts/jsonschema.exe,sha256=yw9E6zd1vFQea8vYMs2ZC1rT2hj4bYZCMxUtGbpYobc,106351
jsonschema-4.4.0.dist-info/COPYING,sha256=T5KgFaE8TRoEC-8BiqE0MLTxvHO0Gxa7hGw0Z2bedDk,1057
jsonschema-4.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jsonschema-4.4.0.dist-info/METADATA,sha256=mf-oc4RNWKinypj-AgQJwf17Ejw4Jiu9mH1JDPc3LUM,7539
jsonschema-4.4.0.dist-info/RECORD,,
jsonschema-4.4.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema-4.4.0.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
jsonschema-4.4.0.dist-info/direct_url.json,sha256=lKku3LCokQQDOgVQ49XZwX1SILrJD_AsMC-avQ9nOkM,70
jsonschema-4.4.0.dist-info/entry_points.txt,sha256=vO7rX4Fs_xIVJy2pnAtKgTSxfpnozAVQ0DjCmpMxnWE,51
jsonschema-4.4.0.dist-info/top_level.txt,sha256=jGoNS61vDONU8U7p0Taf-y_8JVG1Z2CJ5Eif6zMN_cw,11
jsonschema/__init__.py,sha256=h0l2RPVM9kimU7-jTSKoEnguV3QGvrrQvlnJN3F6UPk,1561
jsonschema/__main__.py,sha256=Sfz1ZNeogymj_KZxq6JXY3F6O_1v28sLIiskusifQ5s,40
jsonschema/__pycache__/__init__.cpython-39.pyc,,
jsonschema/__pycache__/__main__.cpython-39.pyc,,
jsonschema/__pycache__/_format.cpython-39.pyc,,
jsonschema/__pycache__/_legacy_validators.cpython-39.pyc,,
jsonschema/__pycache__/_reflect.cpython-39.pyc,,
jsonschema/__pycache__/_types.cpython-39.pyc,,
jsonschema/__pycache__/_utils.cpython-39.pyc,,
jsonschema/__pycache__/_validators.cpython-39.pyc,,
jsonschema/__pycache__/cli.cpython-39.pyc,,
jsonschema/__pycache__/exceptions.cpython-39.pyc,,
jsonschema/__pycache__/protocols.cpython-39.pyc,,
jsonschema/__pycache__/validators.cpython-39.pyc,,
jsonschema/_format.py,sha256=MqdmiZPvQcseyH28byggqxnTUGB52oP9X1jiT5yVwDw,13156
jsonschema/_legacy_validators.py,sha256=-LlXuPD8n1vUI4PUxhLp5xMLPXQNl7PiL3KYQmulyco,7199
jsonschema/_reflect.py,sha256=qrE9u6y_d7MRIXWReN3Kiwkyytm3lQh6Pfdj9qvrbaY,4859
jsonschema/_types.py,sha256=_NDm3OxdPPWAqBSpfo4QVEA_oqfKMACg1QslVx0S900,5364
jsonschema/_utils.py,sha256=JsFatTW-dPS7V4H5Xdn9aw15HlNlSxvaO3iTsFqWs_Y,10415
jsonschema/_validators.py,sha256=bRgXtl4UpD5lmy5qZOtwe92IJC-_2BbUx8oZzKDw4zE,15434
jsonschema/benchmarks/__init__.py,sha256=A0sQrxDBVHSyQ-8ru3L11hMXf3q9gVuB9x_YgHb4R9M,70
jsonschema/benchmarks/__pycache__/__init__.cpython-39.pyc,,
jsonschema/benchmarks/__pycache__/issue232.cpython-39.pyc,,
jsonschema/benchmarks/__pycache__/json_schema_test_suite.cpython-39.pyc,,
jsonschema/benchmarks/issue232.py,sha256=r_V1CaY1rLHP0UCxoEeQhZe5kwQBkdQYdPKmxCj7DbE,495
jsonschema/benchmarks/json_schema_test_suite.py,sha256=PvfabpUYcF4_7csYDTcTauED8rnFEGYbdY5RqTXD08s,320
jsonschema/cli.py,sha256=ldAuYYfY9OvQLnAT5PEQrGQn7-fBy_rSY_V9ZesjP8g,8136
jsonschema/exceptions.py,sha256=utvZjE7HBABp7w5XXWie0EksGpmKD-Hb2yfdOQ93eMM,10268
jsonschema/protocols.py,sha256=le6gCn2Zr-j8RuGuI1mF78s483PWUaGcOG3GhnChv20,6012
jsonschema/schemas/draft2019-09.json,sha256=e3YbPhIfCgyh6ioLjizIVrz4AWBLgmjXG6yqICvAwTs,1785
jsonschema/schemas/draft2020-12.json,sha256=Qdp29a-3zgYtJI92JGOpL3ykfk4PkFsiS6av7vkd7Q8,2452
jsonschema/schemas/draft3.json,sha256=2LanCgvBrUT8Eyk37KszzCjFxuOw0UBFOeS-ahb5Crg,2699
jsonschema/schemas/draft4.json,sha256=d-VZ-zmogXIypnObMGPT_e88TPZ9Zb40jd2-Fuvs9j4,4355
jsonschema/schemas/draft6.json,sha256=wp386fVINcOgbAOzxdXsDtp3cGVo-cTffPvHVmpRAG0,4437
jsonschema/schemas/draft7.json,sha256=PVOSCIJhYGxVm2A_OFMpyfGrRbXWZ-uZBodFOwVdQF4,4819
jsonschema/schemas/vocabularies.json,sha256=SW7oOta6bhkEdVDPBKgvrosztMW_UyKs-s04pgpgXqs,12845
jsonschema/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema/tests/__pycache__/__init__.cpython-39.pyc,,
jsonschema/tests/__pycache__/_helpers.cpython-39.pyc,,
jsonschema/tests/__pycache__/_suite.cpython-39.pyc,,
jsonschema/tests/__pycache__/fuzz_validate.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_cli.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_deprecations.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_exceptions.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_format.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_jsonschema_test_suite.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_types.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_utils.cpython-39.pyc,,
jsonschema/tests/__pycache__/test_validators.cpython-39.pyc,,
jsonschema/tests/_helpers.py,sha256=3c-b9CK0cdGfhtuUhzM1AjtqPtR2VFvfcKC6G2g0a-0,157
jsonschema/tests/_suite.py,sha256=1uc_lOHcwxyfyL7DujRQMzPg3xvoQoVkg15ks3RwCjk,6482
jsonschema/tests/fuzz_validate.py,sha256=GeNlFQepS7ax7Sh90iISVYQXjUkPCUF0c20jEPgPx8s,1085
jsonschema/tests/test_cli.py,sha256=y52uBGTEgab6IhnTLSaA94xUTLLp3OKSQiy3qtiRMCQ,28674
jsonschema/tests/test_deprecations.py,sha256=paMq3Hd33zDfVsJpTd95MAOzI6y7IoUQ5brgp9qqVdU,3901
jsonschema/tests/test_exceptions.py,sha256=WOFFmvp9l9OgCR-bPx_VkLifuNNn7xnPeqpqk7Tjxf8,15700
jsonschema/tests/test_format.py,sha256=Gu4are4xUyRQc8YL0z-RlDOIc9_96ISv83hZRf8R2t0,3763
jsonschema/tests/test_jsonschema_test_suite.py,sha256=5Ej98xJe61PBw8uwanoY_D5zMY5wivy3dOjBn38t9uc,13605
jsonschema/tests/test_types.py,sha256=DyvSKPtuaIu93Lkde80PkJkNOKgvCbaDYAfHz0yxyL0,6803
jsonschema/tests/test_utils.py,sha256=lJRVYyQeZQTUCTU_M3BhlkxPMgjsc8KQCd7U_Qkook8,3749
jsonschema/tests/test_validators.py,sha256=qkNF5FSMqB9nDSrmsuckOx-MOGDXTUTDzdtODwjnznE,73618
jsonschema/validators.py,sha256=iJrjNm6J-6yyQBSu85HCfDyftrn-4loltqErfIcFtRk,34163
