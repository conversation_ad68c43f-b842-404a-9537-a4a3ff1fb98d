Metadata-Version: 2.1
Name: jsonschema
Version: 4.4.0
Summary: An implementation of JSON Schema validation for Python
Home-page: https://github.com/<PERSON>/jsonschema
Author: <PERSON>
Author-email: <PERSON>@<PERSON>Vines.com
License: MIT
Project-URL: Documentation, https://python-jsonschema.readthedocs.io/en/latest/
Project-URL: Source, https://github.com/Julian/jsonschema
Project-URL: Issues, https://github.com/Julian/jsonschema/issues/
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: COPYING
Requires-Dist: attrs (>=17.4.0)
Requires-Dist: pyrsistent (!=0.17.0,!=0.17.1,!=0.17.2,>=0.14.0)
Requires-Dist: importlib-metadata ; python_version < "3.8"
Requires-Dist: typing-extensions ; python_version < "3.8"
Requires-Dist: importlib-resources (>=1.4.0) ; python_version < "3.9"
Provides-Extra: format
Requires-Dist: fqdn ; extra == 'format'
Requires-Dist: idna ; extra == 'format'
Requires-Dist: isoduration ; extra == 'format'
Requires-Dist: jsonpointer (>1.13) ; extra == 'format'
Requires-Dist: rfc3339-validator ; extra == 'format'
Requires-Dist: rfc3987 ; extra == 'format'
Requires-Dist: uri-template ; extra == 'format'
Requires-Dist: webcolors (>=1.11) ; extra == 'format'
Provides-Extra: format_nongpl
Requires-Dist: fqdn ; extra == 'format_nongpl'
Requires-Dist: idna ; extra == 'format_nongpl'
Requires-Dist: isoduration ; extra == 'format_nongpl'
Requires-Dist: jsonpointer (>1.13) ; extra == 'format_nongpl'
Requires-Dist: rfc3339-validator ; extra == 'format_nongpl'
Requires-Dist: rfc3986-validator (>0.1.0) ; extra == 'format_nongpl'
Requires-Dist: uri-template ; extra == 'format_nongpl'
Requires-Dist: webcolors (>=1.11) ; extra == 'format_nongpl'

==========
jsonschema
==========

|PyPI| |Pythons| |CI| |ReadTheDocs| |Precommit| |Zenodo|

.. |PyPI| image:: https://img.shields.io/pypi/v/jsonschema.svg
   :alt: PyPI version
   :target: https://pypi.org/project/jsonschema/

.. |Pythons| image:: https://img.shields.io/pypi/pyversions/jsonschema.svg
   :alt: Supported Python versions
   :target: https://pypi.org/project/jsonschema/

.. |CI| image:: https://github.com/Julian/jsonschema/workflows/CI/badge.svg
  :alt: Build status
  :target: https://github.com/Julian/jsonschema/actions?query=workflow%3ACI

.. |ReadTheDocs| image:: https://readthedocs.org/projects/python-jsonschema/badge/?version=stable&style=flat
   :alt: ReadTheDocs status
   :target: https://python-jsonschema.readthedocs.io/en/stable/

.. |Precommit| image:: https://results.pre-commit.ci/badge/github/Julian/jsonschema/main.svg
   :alt: pre-commit.ci status
   :target: https://results.pre-commit.ci/latest/github/Julian/jsonschema/main

.. |Zenodo| image:: https://zenodo.org/badge/3072629.svg
   :target: https://zenodo.org/badge/latestdoi/3072629


``jsonschema`` is an implementation of the `JSON Schema
<https://json-schema.org>`_ specification for Python.

.. code-block:: python

    >>> from jsonschema import validate

    >>> # A sample schema, like what we'd get from json.load()
    >>> schema = {
    ...     "type" : "object",
    ...     "properties" : {
    ...         "price" : {"type" : "number"},
    ...         "name" : {"type" : "string"},
    ...     },
    ... }

    >>> # If no exception is raised by validate(), the instance is valid.
    >>> validate(instance={"name" : "Eggs", "price" : 34.99}, schema=schema)

    >>> validate(
    ...     instance={"name" : "Eggs", "price" : "Invalid"}, schema=schema,
    ... )                                   # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
        ...
    ValidationError: 'Invalid' is not of type 'number'

It can also be used from console:

.. code-block:: bash

    $ jsonschema --instance sample.json sample.schema

Features
--------

* Partial support for
  `Draft 2020-12 <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.Draft202012Validator>`_ and
  `Draft 2019-09 <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.Draft201909Validator>`_,
  except for ``dynamicRef`` / ``recursiveRef`` and ``$vocabulary`` (in-progress).
  Full support for
  `Draft 7 <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.Draft7Validator>`_,
  `Draft 6 <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.Draft6Validator>`_,
  `Draft 4 <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.Draft4Validator>`_
  and
  `Draft 3 <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.Draft3Validator>`_

* `Lazy validation <https://python-jsonschema.readthedocs.io/en/latest/validate/#jsonschema.protocols.Validator.iter_errors>`_
  that can iteratively report *all* validation errors.

* `Programmatic querying <https://python-jsonschema.readthedocs.io/en/latest/errors/>`_
  of which properties or items failed validation.


Installation
------------

``jsonschema`` is available on `PyPI <https://pypi.org/project/jsonschema/>`_. You can install using `pip <https://pip.pypa.io/en/stable/>`_:

.. code-block:: bash

    $ pip install jsonschema


Running the Test Suite
----------------------

If you have ``tox`` installed (perhaps via ``pip install tox`` or your
package manager), running ``tox`` in the directory of your source
checkout will run ``jsonschema``'s test suite on all of the versions
of Python ``jsonschema`` supports. If you don't have all of the
versions that ``jsonschema`` is tested under, you'll likely want to run
using ``tox``'s ``--skip-missing-interpreters`` option.

Of course you're also free to just run the tests on a single version with your
favorite test runner. The tests live in the ``jsonschema.tests`` package.


Benchmarks
----------

``jsonschema``'s benchmarks make use of `pyperf
<https://pyperf.readthedocs.io>`_. Running them can be done via::

      $ tox -e perf


Community
---------

The JSON Schema specification has `a Slack
<https://json-schema.slack.com>`_, with an `invite link on its home page
<https://json-schema.org/>`_. Many folks knowledgeable on authoring
schemas can be found there.

Otherwise, asking questions on Stack Overflow is another means of
getting help if you're stuck.

Contributing
------------

I'm Julian Berman.

``jsonschema`` is on `GitHub <https://github.com/Julian/jsonschema>`_.

Get in touch, via GitHub or otherwise, if you've got something to contribute,
it'd be most welcome!

You can also generally find me on Libera (nick: ``Julian``) in various
channels, including ``#python``.

If you feel overwhelmingly grateful, you can also `sponsor me
<https://github.com/sponsors/Julian/>`_.

And for companies who appreciate ``jsonschema`` and its continued support
and growth, ``jsonschema`` is also now supportable via `TideLift
<https://tidelift.com/subscription/pkg/pypi-jsonschema?utm_source=pypi-j
sonschema&utm_medium=referral&utm_campaign=readme>`_.


