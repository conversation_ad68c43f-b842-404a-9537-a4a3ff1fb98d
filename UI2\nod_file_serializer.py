#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NOD文件序列化器 - 仿照C#的序列化方式
模仿C#端NodFileType.cs的序列化和反序列化逻辑
"""

import pickle
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

class NodFileSerializer:
    """NOD文件序列化器，仿照C#的BinaryFormatter方式"""
    
    def __init__(self, delimiter: str = ';'):
        self.delimiter = delimiter
    
    def serialize_to_nod(self, spectrum_data: Dict[str, Any], output_path: str) -> bool:
        """
        将光谱数据序列化为NOD文件
        仿照C#的Save方法
        
        Args:
            spectrum_data: 包含光谱数据的字典
            output_path: 输出文件路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 1. 创建字节数组列表（仿照C#的List<byte[]>）
            file_data = []
            
            # 2. 添加文件头信息
            file_data.append("Data Generated by Neodots".encode('utf-16le'))
            file_data.append(f"File Version{self.delimiter}1.0".encode('utf-16le'))
            
            # 3. 添加光谱头信息
            self._add_spectrum_head(file_data, spectrum_data)
            
            # 4. 添加采集参数信息
            self._add_gather_parameters(file_data, spectrum_data)
            
            # 5. 添加光谱数据信息
            self._add_spectrum_metadata(file_data, spectrum_data)
            
            # 6. 添加光谱数据
            self._add_spectrum_data(file_data, spectrum_data)
            
            # 7. 使用pickle序列化（仿照C#的BinaryFormatter）
            return self._save_to_binary_file(file_data, output_path)
            
        except Exception as e:
            print(f"序列化NOD文件异常: {e}")
            return False
    
    def deserialize_from_nod(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        从NOD文件反序列化数据
        仿照C#的Open方法
        
        Args:
            file_path: NOD文件路径
            
        Returns:
            Dict或None: 解析的光谱数据
        """
        try:
            # 1. 反序列化二进制文件
            rows = self._load_from_binary_file(file_path)
            
            if rows is None:
                # 如果二进制反序列化失败，尝试文本模式读取
                return self._read_text_format(file_path)
            
            # 2. 将字节数组转换为文本行
            text_rows = []
            for row_bytes in rows:
                try:
                    # 使用UTF-16LE解码（对应C#的Unicode编码）
                    text_line = row_bytes.decode('utf-16le')
                    text_rows.append(text_line)
                except UnicodeDecodeError:
                    # 如果UTF-16LE失败，尝试其他编码
                    try:
                        text_line = row_bytes.decode('utf-8')
                        text_rows.append(text_line)
                    except UnicodeDecodeError:
                        continue
            
            # 3. 解析文本行
            return self._parse_text_rows(text_rows)
            
        except Exception as e:
            print(f"反序列化NOD文件异常: {e}")
            return None
    
    def _add_spectrum_head(self, file_data: List[bytes], spectrum_data: Dict[str, Any]):
        """添加光谱头信息"""
        try:
            # 样品名称
            if 'sample_name' in spectrum_data:
                row = f"样品名称{self.delimiter}{spectrum_data['sample_name']}"
                file_data.append(row.encode('utf-16le'))
            
            # 操作员
            if 'operator' in spectrum_data:
                row = f"操作员{self.delimiter}{spectrum_data['operator']}"
                file_data.append(row.encode('utf-16le'))
            
            # 扫描时间
            if 'scan_time' in spectrum_data:
                row = f"扫描时间{self.delimiter}{spectrum_data['scan_time']}"
                file_data.append(row.encode('utf-16le'))
            else:
                # 使用当前时间
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                row = f"扫描时间{self.delimiter}{current_time}"
                file_data.append(row.encode('utf-16le'))
                
        except Exception as e:
            print(f"添加光谱头信息异常: {e}")
    
    def _add_gather_parameters(self, file_data: List[bytes], spectrum_data: Dict[str, Any]):
        """添加采集参数信息"""
        try:
            gather_params = spectrum_data.get('gather_parameters', {})
            
            # 像素数
            if 'pixel_number' in gather_params:
                row = f"像素数{self.delimiter}{gather_params['pixel_number']}"
                file_data.append(row.encode('utf-16le'))
            
            # 积分时间
            if 'integration_time' in gather_params:
                row = f"积分时间{self.delimiter}{gather_params['integration_time']}"
                file_data.append(row.encode('utf-16le'))
            
            # 激光功率
            if 'laser_power' in gather_params:
                row = f"激光功率{self.delimiter}{gather_params['laser_power']}"
                file_data.append(row.encode('utf-16le'))
            
            # 扫描次数
            if 'scan_count' in gather_params:
                row = f"扫描次数{self.delimiter}{gather_params['scan_count']}"
                file_data.append(row.encode('utf-16le'))
                
        except Exception as e:
            print(f"添加采集参数异常: {e}")
    
    def _add_spectrum_metadata(self, file_data: List[bytes], spectrum_data: Dict[str, Any]):
        """添加光谱数据元信息"""
        try:
            metadata = spectrum_data.get('spectrum_metadata', {})
            
            for key, value in metadata.items():
                row = f"{key}{self.delimiter}{value}"
                file_data.append(row.encode('utf-16le'))
                
        except Exception as e:
            print(f"添加光谱元信息异常: {e}")
    
    def _add_spectrum_data(self, file_data: List[bytes], spectrum_data: Dict[str, Any]):
        """添加光谱数据"""
        try:
            # 添加数据头
            row = f"Raman Shift{self.delimiter}Intensity"
            file_data.append(row.encode('utf-16le'))
            
            # 获取光谱数据
            raman_shift = spectrum_data.get('raman_shift', [])
            intensity = spectrum_data.get('intensity', [])
            
            # 确保数据长度一致
            min_length = min(len(raman_shift), len(intensity))
            
            # 添加数据行
            for i in range(min_length):
                row = f"{raman_shift[i]:.4f}{self.delimiter}{intensity[i]:.4f}"
                file_data.append(row.encode('utf-16le'))
                
        except Exception as e:
            print(f"添加光谱数据异常: {e}")
    
    def _save_to_binary_file(self, file_data: List[bytes], file_path: str) -> bool:
        """保存为二进制文件（使用pickle模拟BinaryFormatter）"""
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(file_data, f)
            return True
        except Exception as e:
            print(f"保存二进制文件异常: {e}")
            return False
    
    def _load_from_binary_file(self, file_path: str) -> Optional[List[bytes]]:
        """从二进制文件加载（使用pickle模拟BinaryFormatter）"""
        try:
            with open(file_path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"加载二进制文件异常: {e}")
            return None
    
    def _read_text_format(self, file_path: str) -> Optional[Dict[str, Any]]:
        """读取文本格式的NOD文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            text_rows = [line.strip() for line in lines]
            return self._parse_text_rows(text_rows)
            
        except Exception as e:
            print(f"读取文本格式异常: {e}")
            return None
    
    def _parse_text_rows(self, text_rows: List[str]) -> Dict[str, Any]:
        """解析文本行"""
        try:
            result = {
                'file_info': {},
                'gather_parameters': {},
                'spectrum_metadata': {},
                'raman_shift': [],
                'intensity': []
            }
            
            data_started = False
            
            for row in text_rows:
                if not row or row.strip() == "":
                    continue
                
                parts = row.split(self.delimiter)
                
                if len(parts) >= 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    
                    # 检查是否是数据开始行
                    if (key == "Pixel" and len(parts) > 2 and parts[1].strip() == "Raman Shift") or \
                       (key == "Raman Shift" and value == "Intensity"):
                        data_started = True
                        continue
                    
                    if data_started:
                        # 解析光谱数据
                        try:
                            if len(parts) >= 2:
                                raman_shift = float(parts[0])
                                intensity = float(parts[1])
                                result['raman_shift'].append(raman_shift)
                                result['intensity'].append(intensity)
                        except ValueError:
                            continue
                    else:
                        # 解析元数据
                        if key in ['样品名称', 'sample_name']:
                            result['file_info']['sample_name'] = value
                        elif key in ['操作员', 'operator']:
                            result['file_info']['operator'] = value
                        elif key in ['扫描时间', '检测时间', 'scan_time']:
                            result['file_info']['scan_time'] = value
                        elif key in ['像素数', 'pixel_number']:
                            result['gather_parameters']['pixel_number'] = value
                        elif key in ['积分时间', 'integration_time']:
                            result['gather_parameters']['integration_time'] = value
                        elif key in ['激光功率', 'laser_power']:
                            result['gather_parameters']['laser_power'] = value
                        elif key in ['扫描次数', 'scan_count']:
                            result['gather_parameters']['scan_count'] = value
                        else:
                            result['spectrum_metadata'][key] = value
            
            return result
            
        except Exception as e:
            print(f"解析文本行异常: {e}")
            return None

# 示例使用函数
def create_sample_spectrum_data() -> Dict[str, Any]:
    """创建示例光谱数据"""
    import numpy as np
    
    # 生成示例数据
    raman_shift = np.linspace(200, 3000, 1000)
    intensity = np.random.random(1000) * 1000 + 500
    
    return {
        'sample_name': '测试样品',
        'operator': '测试操作员',
        'scan_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'gather_parameters': {
            'pixel_number': '1000',
            'integration_time': '1000ms',
            'laser_power': '50mW',
            'scan_count': '1'
        },
        'spectrum_metadata': {
            '文件版本': '1.0',
            '设备型号': 'RMS1000',
            '激光波长': '785nm'
        },
        'raman_shift': raman_shift.tolist(),
        'intensity': intensity.tolist()
    }

def test_serialization():
    """测试序列化功能"""
    print("测试NOD文件序列化...")
    
    # 创建序列化器
    serializer = NodFileSerializer()
    
    # 创建测试数据
    test_data = create_sample_spectrum_data()
    
    # 序列化
    output_path = 'test_serialized.nod'
    
    success = serializer.serialize_to_nod(test_data, output_path)
    
    if success:
        print(f"✅ 序列化成功: {output_path}")
        
        # 反序列化测试
        loaded_data = serializer.deserialize_from_nod(output_path)
        
        if loaded_data:
            print("✅ 反序列化成功")
            print(f"样品名称: {loaded_data['file_info'].get('sample_name')}")
            print(f"像素: {len(loaded_data['raman_shift'])}")
        else:
            print("❌ 反序列化失败")
    else:
        print("❌ 序列化失败")

if __name__ == "__main__":
    test_serialization()
