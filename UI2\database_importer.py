#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库导入器模块
用于处理光谱数据库的导入功能
"""

import sqlite3
import pandas as pd
import os
import json
from typing import Dict, List, Optional, Tuple
import numpy as np

class DatabaseImporter:
    """数据库导入器类"""
    
    def __init__(self, db_path: str = None):
        """
        初始化数据库导入器
        
        Args:
            db_path: 数据库文件路径，如果为None则使用默认路径
        """
        self.db_path = db_path or self._get_default_db_path()
        self.connection = None
        self.cursor = None
        
    def _get_default_db_path(self) -> str:
        """获取默认数据库路径"""
        # 在当前目录下创建database文件夹
        db_dir = os.path.join(os.path.dirname(__file__), "database")
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
        return os.path.join(db_dir, "spectrum_database.db")
    
    def connect(self) -> bool:
        """
        连接到数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.cursor = self.connection.cursor()
            self._create_tables()
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.cursor = None
    
    def _create_tables(self):
        """创建数据库表结构"""
        # 光谱数据表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS spectrum_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                sample_name TEXT,
                wavelength_data TEXT NOT NULL,  -- JSON格式存储波长数据
                intensity_data TEXT NOT NULL,   -- JSON格式存储强度数据
                file_info TEXT,                 -- JSON格式存储文件信息
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 峰值信息表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS peak_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spectrum_id INTEGER,
                peak_wavelength REAL,
                peak_intensity REAL,
                peak_index INTEGER,
                FOREIGN KEY (spectrum_id) REFERENCES spectrum_data (id)
            )
        ''')
        
        # 数据库元信息表
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS database_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                version TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_import_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.connection.commit()
    
    def import_spectrum_file(self, file_path: str, sample_name: str = None) -> bool:
        """
        导入单个光谱文件到数据库
        
        Args:
            file_path: 光谱文件路径
            sample_name: 样品名称，如果为None则使用文件名
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 确保数据库连接
            if not self.connection or not self.cursor:
                if not self.connect():
                    print(f"无法连接到数据库: {self.db_path}")
                    return False
            
            # 读取光谱文件
            from spectrum_file_reader import SpectrumFileReader
            reader = SpectrumFileReader()
            file_info, spectrum_data = reader.read_file(file_path)
            
            # 准备数据
            filename = os.path.basename(file_path)
            if sample_name is None:
                sample_name = os.path.splitext(filename)[0]
            
            wavelength_data = json.dumps(spectrum_data[:, 0].tolist())
            intensity_data = json.dumps(spectrum_data[:, 1].tolist())
            file_info_json = json.dumps(file_info)
            
            # 检查文件是否已存在
            self.cursor.execute(
                "SELECT id FROM spectrum_data WHERE filename = ?",
                (filename,)
            )
            existing = self.cursor.fetchone()
            
            if existing:
                # 更新现有记录
                self.cursor.execute('''
                    UPDATE spectrum_data 
                    SET sample_name = ?, wavelength_data = ?, intensity_data = ?, 
                        file_info = ?, updated_time = CURRENT_TIMESTAMP
                    WHERE filename = ?
                ''', (sample_name, wavelength_data, intensity_data, file_info_json, filename))
                spectrum_id = existing[0]
            else:
                # 插入新记录
                self.cursor.execute('''
                    INSERT INTO spectrum_data (filename, sample_name, wavelength_data, intensity_data, file_info)
                    VALUES (?, ?, ?, ?, ?)
                ''', (filename, sample_name, wavelength_data, intensity_data, file_info_json))
                spectrum_id = self.cursor.lastrowid
            
            # 清除旧的峰值信息
            self.cursor.execute("DELETE FROM peak_info WHERE spectrum_id = ?", (spectrum_id,))
            
            # 检测并存储峰值信息
            peaks = self._detect_peaks(spectrum_data[:, 1])
            for i, peak_idx in enumerate(peaks):
                peak_wavelength = spectrum_data[peak_idx, 0]
                peak_intensity = spectrum_data[peak_idx, 1]
                self.cursor.execute('''
                    INSERT INTO peak_info (spectrum_id, peak_wavelength, peak_intensity, peak_index)
                    VALUES (?, ?, ?, ?)
                ''', (spectrum_id, peak_wavelength, peak_intensity, peak_idx))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"导入文件失败 {file_path}: {e}")
            return False
    
    def _detect_peaks(self, intensity_data: np.ndarray, 
                     intensity_threshold: float = 100, 
                     snr_threshold: float = 2.0, 
                     distance: int = 10) -> np.ndarray:
        """
        检测峰值
        
        Args:
            intensity_data: 强度数据
            intensity_threshold: 强度阈值
            snr_threshold: 信噪比阈值
            distance: 峰值间最小距离
            
        Returns:
            np.ndarray: 峰值索引数组
        """
        try:
            from scipy.signal import find_peaks
            
            # 估算噪声
            noise = np.median(np.abs(intensity_data - np.median(intensity_data)))
            if noise == 0:
                noise = 1e-6
            
            # 检测峰值
            peaks, _ = find_peaks(
                intensity_data,
                height=intensity_threshold,
                distance=distance,
                prominence=snr_threshold * noise
            )
            return peaks
            
        except ImportError:
            # 如果没有scipy，使用简单的峰值检测
            peaks = []
            for i in range(1, len(intensity_data) - 1):
                if (intensity_data[i] > intensity_threshold and 
                    intensity_data[i] > intensity_data[i-1] and 
                    intensity_data[i] > intensity_data[i+1]):
                    peaks.append(i)
            return np.array(peaks)
    
    def import_multiple_files(self, file_paths: List[str]) -> Dict[str, bool]:
        """
        批量导入多个光谱文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            Dict[str, bool]: 每个文件的导入结果
        """
        results = {}
        
        if not self.connect():
            return {path: False for path in file_paths}
        
        try:
            for file_path in file_paths:
                results[file_path] = self.import_spectrum_file(file_path)
        finally:
            self.disconnect()
        
        return results
    
    def get_spectrum_list(self) -> List[Dict]:
        """
        获取数据库中的光谱列表
        
        Returns:
            List[Dict]: 光谱信息列表
        """
        if not self.connect():
            return []
        
        try:
            self.cursor.execute('''
                SELECT id, filename, sample_name, created_time, updated_time
                FROM spectrum_data
                ORDER BY created_time DESC
            ''')
            
            columns = ['id', 'filename', 'sample_name', 'created_time', 'updated_time']
            results = []
            
            for row in self.cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
            
        finally:
            self.disconnect()
    
    def get_spectrum_data(self, spectrum_id: int) -> Optional[Dict]:
        """
        获取指定光谱的完整数据
        
        Args:
            spectrum_id: 光谱ID
            
        Returns:
            Optional[Dict]: 光谱数据字典
        """
        if not self.connect():
            return None
        
        try:
            self.cursor.execute('''
                SELECT * FROM spectrum_data WHERE id = ?
            ''', (spectrum_id,))
            
            row = self.cursor.fetchone()
            if row:
                columns = [description[0] for description in self.cursor.description]
                data = dict(zip(columns, row))
                
                # 解析JSON数据
                data['wavelength_data'] = json.loads(data['wavelength_data'])
                data['intensity_data'] = json.loads(data['intensity_data'])
                data['file_info'] = json.loads(data['file_info'])
                
                return data
            
            return None
            
        finally:
            self.disconnect()
    
    def search_spectrum(self, keyword: str) -> List[Dict]:
        """
        搜索光谱数据
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            List[Dict]: 搜索结果
        """
        if not self.connect():
            return []
        
        try:
            self.cursor.execute('''
                SELECT id, filename, sample_name, created_time
                FROM spectrum_data
                WHERE filename LIKE ? OR sample_name LIKE ?
                ORDER BY created_time DESC
            ''', (f'%{keyword}%', f'%{keyword}%'))
            
            columns = ['id', 'filename', 'sample_name', 'created_time']
            results = []
            
            for row in self.cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
            
        finally:
            self.disconnect()
    
    def delete_spectrum(self, spectrum_id: int) -> bool:
        """
        删除指定的光谱数据
        
        Args:
            spectrum_id: 光谱ID
            
        Returns:
            bool: 删除是否成功
        """
        if not self.connect():
            return False
        
        try:
            # 删除峰值信息
            self.cursor.execute("DELETE FROM peak_info WHERE spectrum_id = ?", (spectrum_id,))
            
            # 删除光谱数据
            self.cursor.execute("DELETE FROM spectrum_data WHERE id = ?", (spectrum_id,))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"删除光谱数据失败: {e}")
            return False
        finally:
            self.disconnect()
    
    def get_database_info(self) -> Dict:
        """
        获取数据库信息
        
        Returns:
            Dict: 数据库信息
        """
        if not self.connect():
            return {}
        
        try:
            # 获取光谱数据统计
            self.cursor.execute("SELECT COUNT(*) FROM spectrum_data")
            spectrum_count = self.cursor.fetchone()[0]
            
            # 获取峰值数据统计
            self.cursor.execute("SELECT COUNT(*) FROM peak_info")
            peak_count = self.cursor.fetchone()[0]
            
            # 获取数据库大小
            db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            return {
                'spectrum_count': spectrum_count,
                'peak_count': peak_count,
                'database_size': db_size,
                'database_path': self.db_path
            }
            
        finally:
            self.disconnect() 