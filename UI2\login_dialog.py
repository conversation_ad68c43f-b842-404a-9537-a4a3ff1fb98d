#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import json
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QMessageBox, QCheckBox, QFrame, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap
from db_user_manager import DatabaseUserManager
from password_dialog import PasswordDialog

class LoginDialog(QDialog):
    """登录对话框"""
    login_successful = pyqtSignal(str, str)  # 用户名, 角色
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Neolily")
        self.setFixedSize(400, 500)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.Window)
        
        # 确保窗口可以接收焦点
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        
        # 添加关闭事件处理
        self.login_success = False
        
        # 初始化用户管理器
        self.user_manager = DatabaseUserManager()
        
        self.setup_ui()
        self.setup_connections()
        

    
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(12)
        layout.setContentsMargins(40, 40, 40, 40)
        
        #标题
        title_label = QLabel("NeoRaman")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;

            }
        """)
        layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("用户登录")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
            
            }
        """)
        layout.addWidget(subtitle_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        line.setStyleSheet("background-color: #ecf0f1; ")
        #line.setFixedHeight(2)
        layout.addWidget(line)
        
        # 用户名输入
        username_label = QLabel("用户名")
        username_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                font-weight: bold;
                margin-bottom: 12px;
            }
        """)
        layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        self.username_edit.setMinimumHeight(50)
        self.username_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                background: white;
                color: #2c3e50;
                margin-bottom: 15px;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QLineEdit::placeholder {
                color: #95a5a6;
            }
        """)
        layout.addWidget(self.username_edit)
        
        # 密码输入
        password_label = QLabel("密码")
        password_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                font-weight: bold;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(password_label)
        
        # 密码输入框和切换按钮的容器
        password_container = QHBoxLayout()
        password_container.setSpacing(10)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setMinimumHeight(40)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                background: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QLineEdit::placeholder {
                color: #95a5a6;
            }
        """)
        
        # 密码显示切换按钮
        self.password_toggle_btn = QPushButton()
        self.password_toggle_btn.setFixedSize(40, 40)
        self.password_toggle_btn.setStyleSheet("""
            QPushButton {
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                background: white;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #f8f9fa;
                border-color: #3498db;
            }
        """)
        self.update_password_toggle_icon()
        
        password_container.addWidget(self.password_edit)
        password_container.addWidget(self.password_toggle_btn)
        layout.addLayout(password_container)
        
        # 记住密码选项
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #7f8c8d;
                margin: 10px 0;
            }
        """)

        layout.addWidget(self.remember_checkbox)
        
        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setFixedHeight(45)
        self.login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px 0;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #21618c, stop:1 #1a5276);
            }
        """)
        layout.addWidget(self.login_btn)
        
        # 新增重置密码按钮
        self.reset_password_btn = QPushButton("重置密码")
        self.reset_password_btn.setFixedHeight(35)
        self.reset_password_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #ca6f1e;
            }
        """)
        layout.addWidget(self.reset_password_btn)
        

        
        # 操作提示
        operation_hint = QLabel("💡 点击输入框开始输入，按回车键快速登录")
        operation_hint.setAlignment(Qt.AlignmentFlag.AlignCenter)
        operation_hint.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #3498db;
                font-weight: bold;
            }
        """)

        
        # 登录信息
        login_info = QLabel("管理员: admin/123456 | 普通用户: user/3214")
        login_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        login_info.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
            }
        """)

        
        # 设置回车键登录
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.handle_login)
        
        # 设置焦点
        self.username_edit.setFocus()
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #e1e8ed;
                border-radius: 10px;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        self.password_toggle_btn.clicked.connect(self.toggle_password_visibility)
        self.login_btn.clicked.connect(self.handle_login)
        self.reset_password_btn.clicked.connect(self.show_reset_password_dialog)
    
    def toggle_password_visibility(self):
        """切换密码显示/隐藏"""
        if self.password_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.update_password_toggle_icon()
    
    def update_password_toggle_icon(self):
        """更新密码切换按钮图标"""
        if self.password_edit.echoMode() == QLineEdit.EchoMode.Password:
            # 显示眼睛图标（表示可以显示密码）
            self.password_toggle_btn.setText("👁")
        else:
            # 显示斜杠眼睛图标（表示可以隐藏密码）
            self.password_toggle_btn.setText("👁‍🗨")
    

    
    def handle_login(self):
        """处理登录"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "登录失败", "请输入用户名和密码")
            return
        
        # 使用用户管理器验证
        role = self.user_manager.authenticate_user(username, password)
        if role:
            # 登录成功
            self.login_success = True
            self.login_successful.emit(username, role)
            self.accept()
        else:
            QMessageBox.warning(self, "登录失败", "用户名或密码错误")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if not self.login_success:
            # 如果登录未成功，直接退出程序
            import sys
            sys.exit(0)
        else:
            event.accept()
    
    def update_user_password(self, username, new_password):
        """更新用户密码"""
        return self.user_manager.update_user_password(username, new_password)
    
    def show_reset_password_dialog(self):
        dialog = ResetPasswordDialog(self)
        dialog.exec()

# 新增重置密码对话框
class ResetPasswordDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("重置密码")
        self.setFixedSize(400, 320)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        self.user_manager = DatabaseUserManager()
        self.setup_ui()
        self.setup_connections()
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(18)
        layout.setContentsMargins(30, 30, 30, 30)
        title_label = QLabel("重置密码")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        rule_label = QLabel("新密码要求：必须包含大写字母、小写字母和数字")
        rule_label.setStyleSheet("color: #e67e22; font-size: 13px; margin-bottom: 5px;")
        layout.addWidget(rule_label)
        # 用户名
        user_layout = QHBoxLayout()
        user_label = QLabel("用户名:")
        user_label.setFixedWidth(80)
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        user_layout.addWidget(user_label)
        user_layout.addWidget(self.username_edit)
        layout.addLayout(user_layout)
        # 原始密码
        old_layout = QHBoxLayout()
        old_label = QLabel("原始密码:")
        old_label.setFixedWidth(80)
        self.old_password_edit = QLineEdit()
        self.old_password_edit.setPlaceholderText("请输入原始密码")
        self.old_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        old_layout.addWidget(old_label)
        old_layout.addWidget(self.old_password_edit)
        layout.addLayout(old_layout)
        # 新密码
        new_layout = QHBoxLayout()
        new_label = QLabel("新密码:")
        new_label.setFixedWidth(80)
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setPlaceholderText("请输入新密码")
        self.new_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        new_layout.addWidget(new_label)
        new_layout.addWidget(self.new_password_edit)
        layout.addLayout(new_layout)
        # 确认新密码
        confirm_layout = QHBoxLayout()
        confirm_label = QLabel("确认新密码:")
        confirm_label.setFixedWidth(80)
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText("请再次输入新密码")
        self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        confirm_layout.addWidget(confirm_label)
        confirm_layout.addWidget(self.confirm_password_edit)
        layout.addLayout(confirm_layout)
        # 按钮
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setFixedHeight(35)
        self.ok_btn.setStyleSheet("background-color: #3498db; color: white; border: none; border-radius: 5px; font-size: 14px; font-weight: bold;")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setFixedHeight(35)
        self.cancel_btn.setStyleSheet("background-color: #95a5a6; color: white; border: none; border-radius: 5px; font-size: 14px; font-weight: bold;")
        btn_layout.addStretch()
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addLayout(btn_layout)
        self.confirm_password_edit.returnPressed.connect(self.ok_btn.click)
    def setup_connections(self):
        self.ok_btn.clicked.connect(self.handle_reset)
        self.cancel_btn.clicked.connect(self.reject)
    def handle_reset(self):
        username = self.username_edit.text().strip()
        old_password = self.old_password_edit.text().strip()
        new_password = self.new_password_edit.text().strip()
        confirm_password = self.confirm_password_edit.text().strip()
        if not username:
            QMessageBox.warning(self, "错误", "请输入用户名")
            return
        if not old_password:
            QMessageBox.warning(self, "错误", "请输入原始密码")
            return
        if not new_password:
            QMessageBox.warning(self, "错误", "请输入新密码")
            return
        if not confirm_password:
            QMessageBox.warning(self, "错误", "请确认新密码")
            return
        if new_password != confirm_password:
            QMessageBox.warning(self, "错误", "两次输入的新密码不一致")
            return
        if len(new_password) < 6:
            QMessageBox.warning(self, "错误", "新密码长度不能少于6位")
            return
        if not (any(c.islower() for c in new_password) and any(c.isupper() for c in new_password) and any(c.isdigit() for c in new_password)):
            QMessageBox.warning(self, "错误", "新密码必须包含大写字母、小写字母和数字,且不少于6位")
            return
        # 验证原始密码
        if not self.user_manager.authenticate_user(username, old_password):
            QMessageBox.warning(self, "错误", "原始密码错误")
            return
        # 更新密码
        if self.user_manager.update_user_password(username, new_password):
            QMessageBox.information(self, "成功", "密码重置成功")
            self.accept()
        else:
            QMessageBox.warning(self, "错误", "用户不存在或重置失败") 