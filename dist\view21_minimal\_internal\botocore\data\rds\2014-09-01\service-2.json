{"version": "2.0", "metadata": {"apiVersion": "2014-09-01", "endpointPrefix": "rds", "serviceAbbreviation": "Amazon RDS", "serviceFullName": "Amazon Relational Database Service", "serviceId": "RDS", "signatureVersion": "v4", "xmlNamespace": "http://rds.amazonaws.com/doc/2014-09-01/", "protocol": "query"}, "documentation": "<fullname>Amazon Relational Database Service</fullname> <p> Amazon Relational Database Service (Amazon RDS) is a web service that makes it easier to set up, operate, and scale a relational database in the cloud. It provides cost-efficient, resizable capacity for an industry-standard relational database and manages common database administration tasks, freeing up developers to focus on what makes their applications and businesses unique. </p> <p> Amazon RDS gives you access to the capabilities of a MySQL, PostgreSQL, Microsoft SQL Server, or Oracle database server. This means the code, applications, and tools you already use today with your existing databases work with Amazon RDS without modification. Amazon RDS automatically backs up your database and maintains the database software that powers your DB instance. Amazon RDS is flexible: you can scale your database instance's compute resources and storage capacity to meet your application's demand. As with all Amazon Web Services, there are no up-front investments, and you pay only for the resources you use. </p> <p> This is an interface reference for Amazon RDS. It contains documentation for a programming or command line interface you can use to manage Amazon RDS. Note that Amazon RDS is asynchronous, which means that some interfaces may require techniques such as polling or callback functions to determine when a command has been applied. In this reference, the parameter descriptions indicate whether a command is applied immediately, on the next instance reboot, or during the maintenance window. For a summary of the Amazon RDS interfaces, go to <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Welcome.html#Welcome.Interfaces\">Available RDS Interfaces</a>. </p>", "operations": {"AddSourceIdentifierToSubscription": {"name": "AddSourceIdentifierToSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddSourceIdentifierToSubscriptionMessage", "documentation": "<p></p>"}, "output": {"shape": "AddSourceIdentifierToSubscriptionResult", "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventSubscriptions</a> action.</p>", "resultWrapper": "AddSourceIdentifierToSubscriptionResult"}, "errors": [{"shape": "SubscriptionNotFoundFault", "error": {"code": "SubscriptionNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The subscription name does not exist.</p>"}, {"shape": "SourceNotFoundFault", "error": {"code": "SourceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested source could not be found.</p>"}], "documentation": "<p>Adds a source identifier to an existing RDS event notification subscription.</p>"}, "AddTagsToResource": {"name": "AddTagsToResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddTagsToResourceMessage", "documentation": "<p></p>"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}], "documentation": "<p> Adds metadata tags to an Amazon RDS resource. These tags can also be used with cost allocation reporting to track cost associated with Amazon RDS resources, or used in Condition statement in IAM policy for Amazon RDS.</p> <p>For an overview on tagging Amazon RDS resources, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.Tagging.html\">Tagging Amazon RDS Resources</a>.</p>"}, "AuthorizeDBSecurityGroupIngress": {"name": "AuthorizeDBSecurityGroupIngress", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AuthorizeDBSecurityGroupIngressMessage", "documentation": "<p> </p>"}, "output": {"shape": "AuthorizeDBSecurityGroupIngressResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>DescribeDBSecurityGroups</a></li> <li><a>AuthorizeDBSecurityGroupIngress</a></li> <li><a>CreateDBSecurityGroup</a></li> <li><a>RevokeDBSecurityGroupIngress</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSecurityGroups</a> action.</p>", "resultWrapper": "AuthorizeDBSecurityGroupIngressResult"}, "errors": [{"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}, {"shape": "InvalidDBSecurityGroupStateFault", "error": {"code": "InvalidDBSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB security group does not allow deletion. </p>"}, {"shape": "AuthorizationAlreadyExistsFault", "error": {"code": "AuthorizationAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified CIDRIP or EC2 security group is already authorized for the specified DB security group. </p>"}, {"shape": "AuthorizationQuotaExceededFault", "error": {"code": "AuthorizationQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB security group authorization quota has been reached. </p>"}], "documentation": "<p> Enables ingress to a DBSecurityGroup using one of two forms of authorization. First, EC2 or VPC security groups can be added to the DBSecurityGroup if the application using the database is running on EC2 or VPC instances. Second, IP ranges are available if the application accessing your database is running on the Internet. Required parameters for this API are one of CIDR range, EC2SecurityGroupId for VPC, or (EC2SecurityGroupOwnerId and either EC2SecurityGroupName or EC2SecurityGroupId for non-VPC). </p> <note> You cannot authorize ingress from an EC2 security group in one Region to an Amazon RDS DB instance in another. You cannot authorize ingress from a VPC security group in one VPC to an Amazon RDS DB instance in another. </note> <p>For an overview of CIDR ranges, go to the <a href=\"http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Wikipedia Tutorial</a>. </p>"}, "CopyDBParameterGroup": {"name": "CopyDBParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopyDBParameterGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "CopyDBParameterGroupResult", "wrapper": true, "documentation": "<p>Contains the result of a successful invocation of the <a>CreateDBParameterGroup</a> action. </p> <p>This data type is used as a request parameter in the <a>DeleteDBParameterGroup</a> action, and as a response element in the <a>DescribeDBParameterGroups</a> action. </p>", "resultWrapper": "CopyDBParameterGroupResult"}, "errors": [{"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}, {"shape": "DBParameterGroupAlreadyExistsFault", "error": {"code": "DBParameterGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB parameter group with the same name exists. </p>"}, {"shape": "DBParameterGroupQuotaExceededFault", "error": {"code": "DBParameterGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB parameter groups. </p>"}], "documentation": "<p> Copies the specified DB parameter group. </p>"}, "CopyDBSnapshot": {"name": "CopyDBSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopyDBSnapshotMessage", "documentation": "<p> </p>"}, "output": {"shape": "CopyDBSnapshotResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSnapshot</a></li> <li><a>DeleteDBSnapshot</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSnapshots</a> action.</p>", "resultWrapper": "CopyDBSnapshotResult"}, "errors": [{"shape": "DBSnapshotAlreadyExistsFault", "error": {"code": "DBSnapshotAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> is already used by an existing snapshot. </p>"}, {"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}, {"shape": "InvalidDBSnapshotStateFault", "error": {"code": "InvalidDBSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB snapshot does not allow deletion. </p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB snapshots. </p>"}], "documentation": "<p> Copies the specified DBSnapshot. The source DBSnapshot must be in the \"available\" state. </p>"}, "CopyOptionGroup": {"name": "CopyOptionGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopyOptionGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "CopyOptionGroupResult", "wrapper": true, "documentation": "<p> </p>", "resultWrapper": "CopyOptionGroupResult"}, "errors": [{"shape": "OptionGroupAlreadyExistsFault", "error": {"code": "OptionGroupAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The option group you are trying to create already exists. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "OptionGroupQuotaExceededFault", "error": {"code": "OptionGroupQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The quota of 20 option groups was exceeded for this AWS account. </p>"}], "documentation": "<p>Copies the specified option group. </p>"}, "CreateDBInstance": {"name": "CreateDBInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDBInstanceMessage", "documentation": "<p> </p>"}, "output": {"shape": "CreateDBInstanceResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "CreateDBInstanceResult"}, "errors": [{"shape": "DBInstanceAlreadyExistsFault", "error": {"code": "DBInstanceAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> User already has a DB instance with the given identifier. </p>"}, {"shape": "InsufficientDBInstanceCapacityFault", "error": {"code": "InsufficientDBInstanceCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Specified DB instance class is not available in the specified Availability Zone. </p>"}, {"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}, {"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}, {"shape": "InstanceQuotaExceededFault", "error": {"code": "InstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB instances. </p>"}, {"shape": "StorageQuotaExceededFault", "error": {"code": "StorageQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed amount of storage available across all DB instances. </p>"}, {"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}, {"shape": "DBSubnetGroupDoesNotCoverEnoughAZs", "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB subnet group does not cover all Availability Zones after it is created because users' change. </p>"}, {"shape": "ProvisionedIopsNotAvailableInAZFault", "error": {"code": "ProvisionedIopsNotAvailableInAZFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Provisioned IOPS not available in the specified Availability Zone. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "StorageTypeNotSupportedFault", "error": {"code": "StorageTypeNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>StorageType</i> specified cannot be associated with the DB Instance. </p>"}, {"shape": "AuthorizationNotFoundFault", "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified CIDRIP or EC2 security group is not authorized for the specified DB security group. </p> <p>RDS may not also be authorized via IAM to perform necessary actions on your behalf.</p>"}], "documentation": "<p> Creates a new DB instance. </p>"}, "CreateDBInstanceReadReplica": {"name": "CreateDBInstanceReadReplica", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDBInstanceReadReplicaMessage"}, "output": {"shape": "CreateDBInstanceReadReplicaResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "CreateDBInstanceReadReplicaResult"}, "errors": [{"shape": "DBInstanceAlreadyExistsFault", "error": {"code": "DBInstanceAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> User already has a DB instance with the given identifier. </p>"}, {"shape": "InsufficientDBInstanceCapacityFault", "error": {"code": "InsufficientDBInstanceCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Specified DB instance class is not available in the specified Availability Zone. </p>"}, {"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}, {"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}, {"shape": "InstanceQuotaExceededFault", "error": {"code": "InstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB instances. </p>"}, {"shape": "StorageQuotaExceededFault", "error": {"code": "StorageQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed amount of storage available across all DB instances. </p>"}, {"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}, {"shape": "DBSubnetGroupDoesNotCoverEnoughAZs", "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB subnet group does not cover all Availability Zones after it is created because users' change. </p>"}, {"shape": "ProvisionedIopsNotAvailableInAZFault", "error": {"code": "ProvisionedIopsNotAvailableInAZFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Provisioned IOPS not available in the specified Availability Zone. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "DBSubnetGroupNotAllowedFault", "error": {"code": "DBSubnetGroupNotAllowedFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Indicates that the DBSubnetGroup should not be specified while creating read replicas that lie in the same region as the source instance. </p>"}, {"shape": "InvalidDBSubnetGroupFault", "error": {"code": "InvalidDBSubnetGroupFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Indicates the DBSubnetGroup does not belong to the same VPC as that of an existing cross region read replica of the same source instance. </p>"}, {"shape": "StorageTypeNotSupportedFault", "error": {"code": "StorageTypeNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>StorageType</i> specified cannot be associated with the DB Instance. </p>"}], "documentation": "<p> Creates a DB instance that acts as a Read Replica of a source DB instance. </p> <p> All Read Replica DB instances are created as Single-AZ deployments with backups disabled. All other DB instance attributes (including DB security groups and DB parameter groups) are inherited from the source DB instance, except as specified below. </p> <important> <p> The source DB instance must have backup retention enabled. </p> </important>"}, "CreateDBParameterGroup": {"name": "CreateDBParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDBParameterGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "CreateDBParameterGroupResult", "wrapper": true, "documentation": "<p>Contains the result of a successful invocation of the <a>CreateDBParameterGroup</a> action. </p> <p>This data type is used as a request parameter in the <a>DeleteDBParameterGroup</a> action, and as a response element in the <a>DescribeDBParameterGroups</a> action. </p>", "resultWrapper": "CreateDBParameterGroupResult"}, "errors": [{"shape": "DBParameterGroupQuotaExceededFault", "error": {"code": "DBParameterGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB parameter groups. </p>"}, {"shape": "DBParameterGroupAlreadyExistsFault", "error": {"code": "DBParameterGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB parameter group with the same name exists. </p>"}], "documentation": "<p> Creates a new DB parameter group. </p> <p> A DB parameter group is initially created with the default parameters for the database engine used by the DB instance. To provide custom values for any of the parameters, you must modify the group after creating it using <i>ModifyDBParameterGroup</i>. Once you've created a DB parameter group, you need to associate it with your DB instance using <i>ModifyDBInstance</i>. When you associate a new DB parameter group with a running DB instance, you need to reboot the DB instance without failover for the new DB parameter group and associated settings to take effect. </p> <important> <p>After you create a DB parameter group, you should wait at least 5 minutes before creating your first DB instance that uses that DB parameter group as the default parameter group. This allows Amazon RDS to fully complete the create action before the parameter group is used as the default for a new DB instance. This is especially important for parameters that are critical when creating the default database for a DB instance, such as the character set for the default database defined by the <code>character_set_database</code> parameter. You can use the <i>Parameter Groups</i> option of the <a href=\"https://console.aws.amazon.com/rds/\">Amazon RDS console</a> or the <i>DescribeDBParameters</i> command to verify that your DB parameter group has been created or modified.</p> </important>"}, "CreateDBSecurityGroup": {"name": "CreateDBSecurityGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDBSecurityGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "CreateDBSecurityGroupResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>DescribeDBSecurityGroups</a></li> <li><a>AuthorizeDBSecurityGroupIngress</a></li> <li><a>CreateDBSecurityGroup</a></li> <li><a>RevokeDBSecurityGroupIngress</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSecurityGroups</a> action.</p>", "resultWrapper": "CreateDBSecurityGroupResult"}, "errors": [{"shape": "DBSecurityGroupAlreadyExistsFault", "error": {"code": "DBSecurityGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB security group with the name specified in <i>DBSecurityGroupName</i> already exists. </p>"}, {"shape": "DBSecurityGroupQuotaExceededFault", "error": {"code": "QuotaExceeded.DBSecurityGroup", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB security groups. </p>"}, {"shape": "DBSecurityGroupNotSupportedFault", "error": {"code": "DBSecurityGroupNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB security group is not allowed for this action. </p>"}], "documentation": "<p> Creates a new DB security group. DB security groups control access to a DB instance. </p>"}, "CreateDBSnapshot": {"name": "CreateDBSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDBSnapshotMessage", "documentation": "<p> </p>"}, "output": {"shape": "CreateDBSnapshotResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSnapshot</a></li> <li><a>DeleteDBSnapshot</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSnapshots</a> action.</p>", "resultWrapper": "CreateDBSnapshotResult"}, "errors": [{"shape": "DBSnapshotAlreadyExistsFault", "error": {"code": "DBSnapshotAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> is already used by an existing snapshot. </p>"}, {"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB snapshots. </p>"}], "documentation": "<p> Creates a DBSnapshot. The source DBInstance must be in \"available\" state. </p>"}, "CreateDBSubnetGroup": {"name": "CreateDBSubnetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDBSubnetGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "CreateDBSubnetGroupResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSubnetGroup</a></li> <li><a>ModifyDBSubnetGroup</a></li> <li><a>DescribeDBSubnetGroups</a></li> <li><a>DeleteDBSubnetGroup</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSubnetGroups</a> action.</p>", "resultWrapper": "CreateDBSubnetGroupResult"}, "errors": [{"shape": "DBSubnetGroupAlreadyExistsFault", "error": {"code": "DBSubnetGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> is already used by an existing DB subnet group. </p>"}, {"shape": "DBSubnetGroupQuotaExceededFault", "error": {"code": "DBSubnetGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB subnet groups. </p>"}, {"shape": "DBSubnetQuotaExceededFault", "error": {"code": "DBSubnetQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of subnets in a DB subnet groups. </p>"}, {"shape": "DBSubnetGroupDoesNotCoverEnoughAZs", "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}], "documentation": "<p> Creates a new DB subnet group. DB subnet groups must contain at least one subnet in at least two AZs in the region. </p>"}, "CreateEventSubscription": {"name": "CreateEventSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEventSubscriptionMessage", "documentation": "<p></p>"}, "output": {"shape": "CreateEventSubscriptionResult", "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventSubscriptions</a> action.</p>", "resultWrapper": "CreateEventSubscriptionResult"}, "errors": [{"shape": "EventSubscriptionQuotaExceededFault", "error": {"code": "EventSubscriptionQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You have reached the maximum number of event subscriptions.</p>"}, {"shape": "SubscriptionAlreadyExistFault", "error": {"code": "SubscriptionAlreadyExist", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The supplied subscription name already exists.</p>"}, {"shape": "SNSInvalidTopicFault", "error": {"code": "SNSInvalidTopic", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>SNS has responded that there is a problem with the SND topic specified.</p>"}, {"shape": "SNSNoAuthorizationFault", "error": {"code": "SNSNoAuthorization", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You do not have permission to publish to the SNS topic ARN.</p>"}, {"shape": "SNSTopicArnNotFoundFault", "error": {"code": "SNSTopicArnNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The SNS topic ARN does not exist.</p>"}, {"shape": "SubscriptionCategoryNotFoundFault", "error": {"code": "SubscriptionCategoryNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The supplied category does not exist.</p>"}, {"shape": "SourceNotFoundFault", "error": {"code": "SourceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested source could not be found.</p>"}], "documentation": "<p>Creates an RDS event notification subscription. This action requires a topic ARN (Amazon Resource Name) created by either the RDS console, the SNS console, or the SNS API. To obtain an ARN with SNS, you must create a topic in Amazon SNS and subscribe to the topic. The ARN is displayed in the SNS console.</p> <p>You can specify the type of source (SourceType) you want to be notified of, provide a list of RDS sources (SourceIds) that triggers the events, and provide a list of event categories (EventCategories) for events you want to be notified of. For example, you can specify SourceType = db-instance, SourceIds = mydbinstance1, mydbinstance2 and EventCategories = Availability, Backup.</p> <p>If you specify both the SourceType and SourceIds, such as SourceType = db-instance and SourceIdentifier = myDBInstance1, you will be notified of all the db-instance events for the specified source. If you specify a SourceType but do not specify a SourceIdentifier, you will receive notice of the events for that source type for all your RDS sources. If you do not specify either the SourceType nor the SourceIdentifier, you will be notified of events generated from all RDS sources belonging to your customer account.</p>"}, "CreateOptionGroup": {"name": "CreateOptionGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateOptionGroupMessage", "documentation": "<p></p>"}, "output": {"shape": "CreateOptionGroupResult", "wrapper": true, "documentation": "<p> </p>", "resultWrapper": "CreateOptionGroupResult"}, "errors": [{"shape": "OptionGroupAlreadyExistsFault", "error": {"code": "OptionGroupAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The option group you are trying to create already exists. </p>"}, {"shape": "OptionGroupQuotaExceededFault", "error": {"code": "OptionGroupQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The quota of 20 option groups was exceeded for this AWS account. </p>"}], "documentation": "<p> Creates a new option group. You can create up to 20 option groups. </p>"}, "DeleteDBInstance": {"name": "DeleteDBInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDBInstanceMessage", "documentation": "<p> </p>"}, "output": {"shape": "DeleteDBInstanceResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "DeleteDBInstanceResult"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "DBSnapshotAlreadyExistsFault", "error": {"code": "DBSnapshotAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> is already used by an existing snapshot. </p>"}, {"shape": "SnapshotQuotaExceededFault", "error": {"code": "SnapshotQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB snapshots. </p>"}], "documentation": "<p> The DeleteDBInstance action deletes a previously provisioned DB instance. A successful response from the web service indicates the request was received correctly. When you delete a DB instance, all automated backups for that instance are deleted and cannot be recovered. Manual DB snapshots of the DB instance to be deleted are not deleted. </p> <p> If a final DB snapshot is requested the status of the RDS instance will be \"deleting\" until the DB snapshot is created. The API action <code>DescribeDBInstance</code> is used to monitor the status of this operation. The action cannot be canceled or reverted once submitted. </p>"}, "DeleteDBParameterGroup": {"name": "DeleteDBParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDBParameterGroupMessage", "documentation": "<p> </p>"}, "errors": [{"shape": "InvalidDBParameterGroupStateFault", "error": {"code": "InvalidDBParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB parameter group cannot be deleted because it is in use. </p>"}, {"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}], "documentation": "<p> Deletes a specified DBParameterGroup. The DBParameterGroup to be deleted cannot be associated with any DB instances. </p> <note> The specified DB parameter group cannot be associated with any DB instances. </note>"}, "DeleteDBSecurityGroup": {"name": "DeleteDBSecurityGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDBSecurityGroupMessage", "documentation": "<p> </p>"}, "errors": [{"shape": "InvalidDBSecurityGroupStateFault", "error": {"code": "InvalidDBSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB security group does not allow deletion. </p>"}, {"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}], "documentation": "<p> Deletes a DB security group. </p> <note>The specified DB security group must not be associated with any DB instances.</note>"}, "DeleteDBSnapshot": {"name": "DeleteDBSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDBSnapshotMessage", "documentation": "<p> </p>"}, "output": {"shape": "DeleteDBSnapshotResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSnapshot</a></li> <li><a>DeleteDBSnapshot</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSnapshots</a> action.</p>", "resultWrapper": "DeleteDBSnapshotResult"}, "errors": [{"shape": "InvalidDBSnapshotStateFault", "error": {"code": "InvalidDBSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB snapshot does not allow deletion. </p>"}, {"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}], "documentation": "<p> Deletes a DBSnapshot. If the snapshot is being copied, the copy operation is terminated. </p> <note>The DBSnapshot must be in the <code>available</code> state to be deleted.</note>"}, "DeleteDBSubnetGroup": {"name": "DeleteDBSubnetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDBSubnetGroupMessage", "documentation": "<p> </p>"}, "errors": [{"shape": "InvalidDBSubnetGroupStateFault", "error": {"code": "InvalidDBSubnetGroupStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB subnet group cannot be deleted because it is in use. </p>"}, {"shape": "InvalidDBSubnetStateFault", "error": {"code": "InvalidDBSubnetStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB subnet is not in the <i>available</i> state. </p>"}, {"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}], "documentation": "<p> Deletes a DB subnet group. </p> <note>The specified database subnet group must not be associated with any DB instances.</note>"}, "DeleteEventSubscription": {"name": "DeleteEventSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEventSubscriptionMessage", "documentation": "<p></p>"}, "output": {"shape": "DeleteEventSubscriptionResult", "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventSubscriptions</a> action.</p>", "resultWrapper": "DeleteEventSubscriptionResult"}, "errors": [{"shape": "SubscriptionNotFoundFault", "error": {"code": "SubscriptionNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The subscription name does not exist.</p>"}, {"shape": "InvalidEventSubscriptionStateFault", "error": {"code": "InvalidEventSubscriptionState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>This error can occur if someone else is modifying a subscription. You should retry the action.</p>"}], "documentation": "<p>Deletes an RDS event notification subscription.</p>"}, "DeleteOptionGroup": {"name": "DeleteOptionGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteOptionGroupMessage", "documentation": "<p> </p>"}, "errors": [{"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "InvalidOptionGroupStateFault", "error": {"code": "InvalidOptionGroupStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The option group is not in the <i>available</i> state. </p>"}], "documentation": "<p> Deletes an existing option group. </p>"}, "DescribeDBEngineVersions": {"name": "DescribeDBEngineVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBEngineVersionsMessage"}, "output": {"shape": "DBEngineVersionMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBEngineVersions</a> action. </p>", "resultWrapper": "DescribeDBEngineVersionsResult"}, "documentation": "<p> Returns a list of the available DB engines. </p>"}, "DescribeDBInstances": {"name": "DescribeDBInstances", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBInstancesMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBInstanceMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBInstances</a> action. </p>", "resultWrapper": "DescribeDBInstancesResult"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}], "documentation": "<p> Returns information about provisioned RDS instances. This API supports pagination. </p>"}, "DescribeDBLogFiles": {"name": "DescribeDBLogFiles", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBLogFilesMessage", "documentation": "<p> </p>"}, "output": {"shape": "DescribeDBLogFilesResponse", "documentation": "<p> The response from a call to <a>DescribeDBLogFiles</a>. </p>", "resultWrapper": "DescribeDBLogFilesResult"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}], "documentation": "<p> Returns a list of DB log files for the DB instance. </p>"}, "DescribeDBParameterGroups": {"name": "DescribeDBParameterGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBParameterGroupsMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBParameterGroupsMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBParameterGroups</a> action. </p>", "resultWrapper": "DescribeDBParameterGroupsResult"}, "errors": [{"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}], "documentation": "<p> Returns a list of <code>DBParameterGroup</code> descriptions. If a <code>DBParameterGroupName</code> is specified, the list will contain only the description of the specified DB parameter group. </p>"}, "DescribeDBParameters": {"name": "DescribeDBParameters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBParametersMessage"}, "output": {"shape": "DBParameterGroupDetails", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBParameters</a> action. </p>", "resultWrapper": "DescribeDBParametersResult"}, "errors": [{"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}], "documentation": "<p> Returns the detailed parameter list for a particular DB parameter group. </p>"}, "DescribeDBSecurityGroups": {"name": "DescribeDBSecurityGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBSecurityGroupsMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBSecurityGroupMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBSecurityGroups</a> action. </p>", "resultWrapper": "DescribeDBSecurityGroupsResult"}, "errors": [{"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}], "documentation": "<p> Returns a list of <code>DBSecurityGroup</code> descriptions. If a <code>DBSecurityGroupName</code> is specified, the list will contain only the descriptions of the specified DB security group. </p>"}, "DescribeDBSnapshots": {"name": "DescribeDBSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBSnapshotsMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBSnapshotMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBSnapshots</a> action. </p>", "resultWrapper": "DescribeDBSnapshotsResult"}, "errors": [{"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}], "documentation": "<p> Returns information about DB snapshots. This API supports pagination. </p>"}, "DescribeDBSubnetGroups": {"name": "DescribeDBSubnetGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDBSubnetGroupsMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBSubnetGroupMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBSubnetGroups</a> action. </p>", "resultWrapper": "DescribeDBSubnetGroupsResult"}, "errors": [{"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}], "documentation": "<p> Returns a list of DBSubnetGroup descriptions. If a DBSubnetGroupName is specified, the list will contain only the descriptions of the specified DBSubnetGroup. </p> <p>For an overview of CIDR ranges, go to the <a href=\"http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Wikipedia Tutorial</a>. </p>"}, "DescribeEngineDefaultParameters": {"name": "DescribeEngineDefaultParameters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEngineDefaultParametersMessage", "documentation": "<p> </p>"}, "output": {"shape": "DescribeEngineDefaultParametersResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeEngineDefaultParameters</a> action. </p>", "resultWrapper": "DescribeEngineDefaultParametersResult"}, "documentation": "<p> Returns the default engine and system parameter information for the specified database engine. </p>"}, "DescribeEventCategories": {"name": "DescribeEventCategories", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventCategoriesMessage", "documentation": "<p></p>"}, "output": {"shape": "EventCategoriesMessage", "documentation": "<p>Data returned from the <b>DescribeEventCategories</b> action.</p>", "resultWrapper": "DescribeEventCategoriesResult"}, "documentation": "<p>Displays a list of categories for all event source types, or, if specified, for a specified source type. You can see a list of the event categories and source types in the <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html\"> Events</a> topic in the Amazon RDS User Guide.</p>"}, "DescribeEventSubscriptions": {"name": "DescribeEventSubscriptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventSubscriptionsMessage", "documentation": "<p></p>"}, "output": {"shape": "EventSubscriptionsMessage", "documentation": "<p>Data returned by the <b>DescribeEventSubscriptions</b> action.</p>", "resultWrapper": "DescribeEventSubscriptionsResult"}, "errors": [{"shape": "SubscriptionNotFoundFault", "error": {"code": "SubscriptionNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The subscription name does not exist.</p>"}], "documentation": "<p>Lists all the subscription descriptions for a customer account. The description for a subscription includes SubscriptionName, SNSTopicARN, CustomerID, SourceType, SourceID, CreationTime, and Status. </p> <p>If you specify a SubscriptionName, lists the description for that subscription.</p>"}, "DescribeEvents": {"name": "DescribeEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventsMessage", "documentation": "<p> </p>"}, "output": {"shape": "EventsMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeEvents</a> action. </p>", "resultWrapper": "DescribeEventsResult"}, "documentation": "<p> Returns events related to DB instances, DB security groups, DB snapshots, and DB parameter groups for the past 14 days. Events specific to a particular DB instance, DB security group, database snapshot, or DB parameter group can be obtained by providing the name as a parameter. By default, the past hour of events are returned. </p>"}, "DescribeOptionGroupOptions": {"name": "DescribeOptionGroupOptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOptionGroupOptionsMessage", "documentation": "<p> </p>"}, "output": {"shape": "OptionGroupOptionsMessage", "documentation": "<p> </p>", "resultWrapper": "DescribeOptionGroupOptionsResult"}, "documentation": "<p> Describes all available options. </p>"}, "DescribeOptionGroups": {"name": "DescribeOptionGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOptionGroupsMessage", "documentation": "<p> </p>"}, "output": {"shape": "OptionGroups", "documentation": "<p> List of option groups. </p>", "resultWrapper": "DescribeOptionGroupsResult"}, "errors": [{"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}], "documentation": "<p> Describes the available option groups. </p>"}, "DescribeOrderableDBInstanceOptions": {"name": "DescribeOrderableDBInstanceOptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOrderableDBInstanceOptionsMessage", "documentation": "<p> </p>"}, "output": {"shape": "OrderableDBInstanceOptionsMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeOrderableDBInstanceOptions</a> action. </p>", "resultWrapper": "DescribeOrderableDBInstanceOptionsResult"}, "documentation": "<p> Returns a list of orderable DB instance options for the specified engine. </p>"}, "DescribeReservedDBInstances": {"name": "DescribeReservedDBInstances", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeReservedDBInstancesMessage", "documentation": "<p> </p>"}, "output": {"shape": "ReservedDBInstanceMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeReservedDBInstances</a> action. </p>", "resultWrapper": "DescribeReservedDBInstancesResult"}, "errors": [{"shape": "ReservedDBInstanceNotFoundFault", "error": {"code": "ReservedDBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified reserved DB Instance not found. </p>"}], "documentation": "<p> Returns information about reserved DB instances for this account, or about a specified reserved DB instance. </p>"}, "DescribeReservedDBInstancesOfferings": {"name": "DescribeReservedDBInstancesOfferings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeReservedDBInstancesOfferingsMessage", "documentation": "<p> </p>"}, "output": {"shape": "ReservedDBInstancesOfferingMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeReservedDBInstancesOfferings</a> action. </p>", "resultWrapper": "DescribeReservedDBInstancesOfferingsResult"}, "errors": [{"shape": "ReservedDBInstancesOfferingNotFoundFault", "error": {"code": "ReservedDBInstancesOfferingNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified offering does not exist. </p>"}], "documentation": "<p> Lists available reserved DB instance offerings. </p>"}, "DownloadDBLogFilePortion": {"name": "DownloadDBLogFilePortion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DownloadDBLogFilePortionMessage", "documentation": "<p> </p>"}, "output": {"shape": "DownloadDBLogFilePortionDetails", "documentation": "<p>This data type is used as a response element to <a>DownloadDBLogFilePortion</a>.</p>", "resultWrapper": "DownloadDBLogFilePortionResult"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}], "documentation": "<p> Downloads all or a portion of the specified log file. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceMessage", "documentation": "<p></p>"}, "output": {"shape": "TagListMessage", "documentation": "<p></p>", "resultWrapper": "ListTagsForResourceResult"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}], "documentation": "<p> Lists all tags on an Amazon RDS resource.</p> <p>For an overview on tagging an Amazon RDS resource, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.Tagging.html\">Tagging Amazon RDS Resources</a>.</p>"}, "ModifyDBInstance": {"name": "ModifyDBInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyDBInstanceMessage", "documentation": "<p> </p>"}, "output": {"shape": "ModifyDBInstanceResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "ModifyDBInstanceResult"}, "errors": [{"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "InvalidDBSecurityGroupStateFault", "error": {"code": "InvalidDBSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB security group does not allow deletion. </p>"}, {"shape": "DBInstanceAlreadyExistsFault", "error": {"code": "DBInstanceAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> User already has a DB instance with the given identifier. </p>"}, {"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}, {"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}, {"shape": "InsufficientDBInstanceCapacityFault", "error": {"code": "InsufficientDBInstanceCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Specified DB instance class is not available in the specified Availability Zone. </p>"}, {"shape": "StorageQuotaExceededFault", "error": {"code": "StorageQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed amount of storage available across all DB instances. </p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB subnet group does not cover all Availability Zones after it is created because users' change. </p>"}, {"shape": "ProvisionedIopsNotAvailableInAZFault", "error": {"code": "ProvisionedIopsNotAvailableInAZFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Provisioned IOPS not available in the specified Availability Zone. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "DBUpgradeDependencyFailureFault", "error": {"code": "DBUpgradeDependencyFailure", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB upgrade failed because a resource the DB depends on could not be modified. </p>"}, {"shape": "StorageTypeNotSupportedFault", "error": {"code": "StorageTypeNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>StorageType</i> specified cannot be associated with the DB Instance. </p>"}, {"shape": "AuthorizationNotFoundFault", "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified CIDRIP or EC2 security group is not authorized for the specified DB security group. </p> <p>RDS may not also be authorized via IAM to perform necessary actions on your behalf.</p>"}], "documentation": "<p> Modify settings for a DB instance. You can change one or more database configuration parameters by specifying these parameters and the new values in the request. </p>"}, "ModifyDBParameterGroup": {"name": "ModifyDBParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyDBParameterGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBParameterGroupNameMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>ModifyDBParameterGroup</a> or <a>ResetDBParameterGroup</a> action. </p>", "resultWrapper": "ModifyDBParameterGroupResult"}, "errors": [{"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}, {"shape": "InvalidDBParameterGroupStateFault", "error": {"code": "InvalidDBParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB parameter group cannot be deleted because it is in use. </p>"}], "documentation": "<p> Modifies the parameters of a DB parameter group. To modify more than one parameter, submit a list of the following: <code>ParameterName</code>, <code>ParameterValue</code>, and <code>ApplyMethod</code>. A maximum of 20 parameters can be modified in a single request. </p> <note> <p> Changes to dynamic parameters are applied immediately. Changes to static parameters require a reboot without failover to the DB instance associated with the parameter group before the change can take effect. </p> </note> <important> <p>After you modify a DB parameter group, you should wait at least 5 minutes before creating your first DB instance that uses that DB parameter group as the default parameter group. This allows Amazon RDS to fully complete the modify action before the parameter group is used as the default for a new DB instance. This is especially important for parameters that are critical when creating the default database for a DB instance, such as the character set for the default database defined by the <code>character_set_database</code> parameter. You can use the <i>Parameter Groups</i> option of the <a href=\"https://console.aws.amazon.com/rds/\">Amazon RDS console</a> or the <i>DescribeDBParameters</i> command to verify that your DB parameter group has been created or modified.</p> </important>"}, "ModifyDBSubnetGroup": {"name": "ModifyDBSubnetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyDBSubnetGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "ModifyDBSubnetGroupResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSubnetGroup</a></li> <li><a>ModifyDBSubnetGroup</a></li> <li><a>DescribeDBSubnetGroups</a></li> <li><a>DeleteDBSubnetGroup</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSubnetGroups</a> action.</p>", "resultWrapper": "ModifyDBSubnetGroupResult"}, "errors": [{"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}, {"shape": "DBSubnetQuotaExceededFault", "error": {"code": "DBSubnetQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of subnets in a DB subnet groups. </p>"}, {"shape": "SubnetAlreadyInUse", "error": {"code": "SubnetAlreadyInUse", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB subnet is already in use in the Availability Zone. </p>"}, {"shape": "DBSubnetGroupDoesNotCoverEnoughAZs", "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}], "documentation": "<p> Modifies an existing DB subnet group. DB subnet groups must contain at least one subnet in at least two AZs in the region. </p>"}, "ModifyEventSubscription": {"name": "ModifyEventSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyEventSubscriptionMessage", "documentation": "<p></p>"}, "output": {"shape": "ModifyEventSubscriptionResult", "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventSubscriptions</a> action.</p>", "resultWrapper": "ModifyEventSubscriptionResult"}, "errors": [{"shape": "EventSubscriptionQuotaExceededFault", "error": {"code": "EventSubscriptionQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You have reached the maximum number of event subscriptions.</p>"}, {"shape": "SubscriptionNotFoundFault", "error": {"code": "SubscriptionNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The subscription name does not exist.</p>"}, {"shape": "SNSInvalidTopicFault", "error": {"code": "SNSInvalidTopic", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>SNS has responded that there is a problem with the SND topic specified.</p>"}, {"shape": "SNSNoAuthorizationFault", "error": {"code": "SNSNoAuthorization", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You do not have permission to publish to the SNS topic ARN.</p>"}, {"shape": "SNSTopicArnNotFoundFault", "error": {"code": "SNSTopicArnNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The SNS topic ARN does not exist.</p>"}, {"shape": "SubscriptionCategoryNotFoundFault", "error": {"code": "SubscriptionCategoryNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The supplied category does not exist.</p>"}], "documentation": "<p>Modifies an existing RDS event notification subscription. Note that you cannot modify the source identifiers using this call; to change source identifiers for a subscription, use the <a>AddSourceIdentifierToSubscription</a> and <a>RemoveSourceIdentifierFromSubscription</a> calls.</p> <p>You can see a list of the event categories for a given SourceType in the <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html\">Events</a> topic in the Amazon RDS User Guide or by using the <b>DescribeEventCategories</b> action.</p>"}, "ModifyOptionGroup": {"name": "ModifyOptionGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ModifyOptionGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "ModifyOptionGroupResult", "wrapper": true, "documentation": "<p> </p>", "resultWrapper": "ModifyOptionGroupResult"}, "errors": [{"shape": "InvalidOptionGroupStateFault", "error": {"code": "InvalidOptionGroupStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The option group is not in the <i>available</i> state. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}], "documentation": "<p> Modifies an existing option group. </p>"}, "PromoteReadReplica": {"name": "PromoteReadReplica", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PromoteReadReplicaMessage", "documentation": "<p> </p>"}, "output": {"shape": "PromoteReadReplicaResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "PromoteReadReplicaResult"}, "errors": [{"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}], "documentation": "<p> Promotes a Read Replica DB instance to a standalone DB instance. </p> <note><p>We recommend that you enable automated backups on your Read Replica before promoting the Read Replica. This ensures that no backup is taken during the promotion process. Once the instance is promoted to a primary instance, backups are taken based on your backup settings.</p></note>"}, "PurchaseReservedDBInstancesOffering": {"name": "PurchaseReservedDBInstancesOffering", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PurchaseReservedDBInstancesOfferingMessage", "documentation": "<p> </p>"}, "output": {"shape": "PurchaseReservedDBInstancesOfferingResult", "wrapper": true, "documentation": "<p> This data type is used as a response element in the <a>DescribeReservedDBInstances</a> and <a>PurchaseReservedDBInstancesOffering</a> actions. </p>", "resultWrapper": "PurchaseReservedDBInstancesOfferingResult"}, "errors": [{"shape": "ReservedDBInstancesOfferingNotFoundFault", "error": {"code": "ReservedDBInstancesOfferingNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified offering does not exist. </p>"}, {"shape": "ReservedDBInstanceAlreadyExistsFault", "error": {"code": "ReservedDBInstanceAlreadyExists", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> User already has a reservation with the given identifier. </p>"}, {"shape": "ReservedDBInstanceQuotaExceededFault", "error": {"code": "ReservedDBInstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would exceed the user's DB Instance quota. </p>"}], "documentation": "<p> Purchases a reserved DB instance offering. </p>"}, "RebootDBInstance": {"name": "RebootDBInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RebootDBInstanceMessage", "documentation": "<p> </p>"}, "output": {"shape": "RebootDBInstanceResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "RebootDBInstanceResult"}, "errors": [{"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}], "documentation": "<p> Rebooting a DB instance restarts the database engine service. A reboot also applies to the DB instance any modifications to the associated DB parameter group that were pending. Rebooting a DB instance results in a momentary outage of the instance, during which the DB instance status is set to rebooting. If the RDS instance is configured for MultiAZ, it is possible that the reboot will be conducted through a failover. An Amazon RDS event is created when the reboot is completed. </p> <p> If your DB instance is deployed in multiple Availability Zones, you can force a failover from one AZ to the other during the reboot. You might force a failover to test the availability of your DB instance deployment or to restore operations to the original AZ after a failover occurs. </p> <p> The time required to reboot is a function of the specific database engine's crash recovery process. To improve the reboot time, we recommend that you reduce database activities as much as possible during the reboot process to reduce rollback activity for in-transit transactions. </p>"}, "RemoveSourceIdentifierFromSubscription": {"name": "RemoveSourceIdentifierFromSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveSourceIdentifierFromSubscriptionMessage", "documentation": "<p></p>"}, "output": {"shape": "RemoveSourceIdentifierFromSubscriptionResult", "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventSubscriptions</a> action.</p>", "resultWrapper": "RemoveSourceIdentifierFromSubscriptionResult"}, "errors": [{"shape": "SubscriptionNotFoundFault", "error": {"code": "SubscriptionNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The subscription name does not exist.</p>"}, {"shape": "SourceNotFoundFault", "error": {"code": "SourceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested source could not be found.</p>"}], "documentation": "<p>Removes a source identifier from an existing RDS event notification subscription.</p>"}, "RemoveTagsFromResource": {"name": "RemoveTagsFromResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveTagsFromResourceMessage", "documentation": "<p></p>"}, "errors": [{"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}], "documentation": "<p> Removes metadata tags from an Amazon RDS resource.</p> <p>For an overview on tagging an Amazon RDS resource, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.Tagging.html\">Tagging Amazon RDS Resources</a>.</p>"}, "ResetDBParameterGroup": {"name": "ResetDBParameterGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResetDBParameterGroupMessage", "documentation": "<p> </p>"}, "output": {"shape": "DBParameterGroupNameMessage", "documentation": "<p> Contains the result of a successful invocation of the <a>ModifyDBParameterGroup</a> or <a>ResetDBParameterGroup</a> action. </p>", "resultWrapper": "ResetDBParameterGroupResult"}, "errors": [{"shape": "InvalidDBParameterGroupStateFault", "error": {"code": "InvalidDBParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB parameter group cannot be deleted because it is in use. </p>"}, {"shape": "DBParameterGroupNotFoundFault", "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}], "documentation": "<p> Modifies the parameters of a DB parameter group to the engine/system default value. To reset specific parameters submit a list of the following: <code>ParameterName</code> and <code>ApplyMethod</code>. To reset the entire DB parameter group, specify the <code>DBParameterGroup</code> name and <code>ResetAllParameters</code> parameters. When resetting the entire group, dynamic parameters are updated immediately and static parameters are set to <code>pending-reboot</code> to take effect on the next DB instance restart or <code>RebootDBInstance</code> request. </p>"}, "RestoreDBInstanceFromDBSnapshot": {"name": "RestoreDBInstanceFromDBSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreDBInstanceFromDBSnapshotMessage", "documentation": "<p> </p>"}, "output": {"shape": "RestoreDBInstanceFromDBSnapshotResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "RestoreDBInstanceFromDBSnapshotResult"}, "errors": [{"shape": "DBInstanceAlreadyExistsFault", "error": {"code": "DBInstanceAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> User already has a DB instance with the given identifier. </p>"}, {"shape": "DBSnapshotNotFoundFault", "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}, {"shape": "InstanceQuotaExceededFault", "error": {"code": "InstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB instances. </p>"}, {"shape": "InsufficientDBInstanceCapacityFault", "error": {"code": "InsufficientDBInstanceCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Specified DB instance class is not available in the specified Availability Zone. </p>"}, {"shape": "InvalidDBSnapshotStateFault", "error": {"code": "InvalidDBSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB snapshot does not allow deletion. </p>"}, {"shape": "StorageQuotaExceededFault", "error": {"code": "StorageQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed amount of storage available across all DB instances. </p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB subnet group does not cover all Availability Zones after it is created because users' change. </p>"}, {"shape": "InvalidRestoreFault", "error": {"code": "InvalidRestoreFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Cannot restore from vpc backup to non-vpc DB instance. </p>"}, {"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}, {"shape": "DBSubnetGroupDoesNotCoverEnoughAZs", "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}, {"shape": "ProvisionedIopsNotAvailableInAZFault", "error": {"code": "ProvisionedIopsNotAvailableInAZFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Provisioned IOPS not available in the specified Availability Zone. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "StorageTypeNotSupportedFault", "error": {"code": "StorageTypeNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>StorageType</i> specified cannot be associated with the DB Instance. </p>"}, {"shape": "AuthorizationNotFoundFault", "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified CIDRIP or EC2 security group is not authorized for the specified DB security group. </p> <p>RDS may not also be authorized via IAM to perform necessary actions on your behalf.</p>"}], "documentation": "<p>Creates a new DB instance from a DB snapshot. The target database is created from the source database restore point with the same configuration as the original source database, except that the new RDS instance is created with the default security group. </p> <p>If your intent is to replace your original DB instance with the new, restored DB instance, then rename your original DB instance before you call the RestoreDBInstanceFromDBSnapshot action. RDS does not allow two DB instances with the same name. Once you have renamed your original DB instance with a different identifier, then you can pass the original name of the DB instance as the DBInstanceIdentifier in the call to the RestoreDBInstanceFromDBSnapshot action. The result is that you will replace the original DB instance with the DB instance created from the snapshot.</p>"}, "RestoreDBInstanceToPointInTime": {"name": "RestoreDBInstanceToPointInTime", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreDBInstanceToPointInTimeMessage", "documentation": "<p> </p>"}, "output": {"shape": "RestoreDBInstanceToPointInTimeResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>", "resultWrapper": "RestoreDBInstanceToPointInTimeResult"}, "errors": [{"shape": "DBInstanceAlreadyExistsFault", "error": {"code": "DBInstanceAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> User already has a DB instance with the given identifier. </p>"}, {"shape": "DBInstanceNotFoundFault", "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, {"shape": "InstanceQuotaExceededFault", "error": {"code": "InstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB instances. </p>"}, {"shape": "InsufficientDBInstanceCapacityFault", "error": {"code": "InsufficientDBInstanceCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Specified DB instance class is not available in the specified Availability Zone. </p>"}, {"shape": "InvalidDBInstanceStateFault", "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, {"shape": "PointInTimeRestoreNotEnabledFault", "error": {"code": "PointInTimeRestoreNotEnabled", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>SourceDBInstanceIdentifier</i> refers to a DB instance with <i>BackupRetentionPeriod</i> equal to 0. </p>"}, {"shape": "StorageQuotaExceededFault", "error": {"code": "StorageQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed amount of storage available across all DB instances. </p>"}, {"shape": "InvalidVPCNetworkStateFault", "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB subnet group does not cover all Availability Zones after it is created because users' change. </p>"}, {"shape": "InvalidRestoreFault", "error": {"code": "InvalidRestoreFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Cannot restore from vpc backup to non-vpc DB instance. </p>"}, {"shape": "DBSubnetGroupNotFoundFault", "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}, {"shape": "DBSubnetGroupDoesNotCoverEnoughAZs", "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, {"shape": "InvalidSubnet", "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}, {"shape": "ProvisionedIopsNotAvailableInAZFault", "error": {"code": "ProvisionedIopsNotAvailableInAZFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Provisioned IOPS not available in the specified Availability Zone. </p>"}, {"shape": "OptionGroupNotFoundFault", "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, {"shape": "StorageTypeNotSupportedFault", "error": {"code": "StorageTypeNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>StorageType</i> specified cannot be associated with the DB Instance. </p>"}, {"shape": "AuthorizationNotFoundFault", "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified CIDRIP or EC2 security group is not authorized for the specified DB security group. </p> <p>RDS may not also be authorized via IAM to perform necessary actions on your behalf.</p>"}], "documentation": "<p> Restores a DB instance to an arbitrary point-in-time. Users can restore to any point in time before the LatestRestorableTime for up to BackupRetentionPeriod days. The target database is created from the source database with the same configuration as the original database except that the DB instance is created with the default DB security group. </p>"}, "RevokeDBSecurityGroupIngress": {"name": "RevokeDBSecurityGroupIngress", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RevokeDBSecurityGroupIngressMessage", "documentation": "<p> </p>"}, "output": {"shape": "RevokeDBSecurityGroupIngressResult", "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>DescribeDBSecurityGroups</a></li> <li><a>AuthorizeDBSecurityGroupIngress</a></li> <li><a>CreateDBSecurityGroup</a></li> <li><a>RevokeDBSecurityGroupIngress</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSecurityGroups</a> action.</p>", "resultWrapper": "RevokeDBSecurityGroupIngressResult"}, "errors": [{"shape": "DBSecurityGroupNotFoundFault", "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}, {"shape": "AuthorizationNotFoundFault", "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified CIDRIP or EC2 security group is not authorized for the specified DB security group. </p> <p>RDS may not also be authorized via IAM to perform necessary actions on your behalf.</p>"}, {"shape": "InvalidDBSecurityGroupStateFault", "error": {"code": "InvalidDBSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB security group does not allow deletion. </p>"}], "documentation": "<p> Revokes ingress from a DBSecurityGroup for previously authorized IP ranges or EC2 or VPC Security Groups. Required parameters for this API are one of CIDRIP, EC2SecurityGroupId for VPC, or (EC2SecurityGroupOwnerId and either EC2SecurityGroupName or EC2SecurityGroupId). </p>"}}, "shapes": {"AddSourceIdentifierToSubscriptionMessage": {"type": "structure", "required": ["SubscriptionName", "SourceIdentifier"], "members": {"SubscriptionName": {"shape": "String", "documentation": "<p>The name of the RDS event notification subscription you want to add a source identifier to.</p>"}, "SourceIdentifier": {"shape": "String", "documentation": "<p> The identifier of the event source to be added. An identifier must begin with a letter and must contain only ASCII letters, digits, and hyphens; it cannot end with a hyphen or contain two consecutive hyphens. </p> <p>Constraints:</p> <ul> <li>If the source type is a DB instance, then a <code>DBInstanceIdentifier</code> must be supplied.</li> <li>If the source type is a DB security group, a <code>DBSecurityGroupName</code> must be supplied.</li> <li>If the source type is a DB parameter group, a <code>DBParameterGroupName</code> must be supplied.</li> <li>If the source type is a DB snapshot, a <code>DBSnapshotIdentifier</code> must be supplied.</li> </ul>"}}, "documentation": "<p></p>"}, "AddTagsToResourceMessage": {"type": "structure", "required": ["ResourceName", "Tags"], "members": {"ResourceName": {"shape": "String", "documentation": "<p>The Amazon RDS resource the tags will be added to. This value is an Amazon Resource Name (ARN). For information about creating an ARN, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.html#USER_Tagging.ARN\"> Constructing an RDS Amazon Resource Name (ARN)</a>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to be assigned to the Amazon RDS resource.</p>"}}, "documentation": "<p></p>"}, "ApplyMethod": {"type": "string", "enum": ["immediate", "pending-reboot"]}, "AuthorizationAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "AuthorizationAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified CIDRIP or EC2 security group is already authorized for the specified DB security group. </p>"}, "AuthorizationNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "AuthorizationNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified CIDRIP or EC2 security group is not authorized for the specified DB security group. </p> <p>RDS may not also be authorized via IAM to perform necessary actions on your behalf.</p>"}, "AuthorizationQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "AuthorizationQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB security group authorization quota has been reached. </p>"}, "AuthorizeDBSecurityGroupIngressMessage": {"type": "structure", "required": ["DBSecurityGroupName"], "members": {"DBSecurityGroupName": {"shape": "String", "documentation": "<p> The name of the DB security group to add authorization to. </p>"}, "CIDRIP": {"shape": "String", "documentation": "<p> The IP range to authorize. </p>"}, "EC2SecurityGroupName": {"shape": "String", "documentation": "<p> Name of the EC2 security group to authorize. For VPC DB security groups, <code>EC2SecurityGroupId</code> must be provided. Otherwise, EC2SecurityGroupOwnerId and either <code>EC2SecurityGroupName</code> or <code>EC2SecurityGroupId</code> must be provided. </p>"}, "EC2SecurityGroupId": {"shape": "String", "documentation": "<p> Id of the EC2 security group to authorize. For VPC DB security groups, <code>EC2SecurityGroupId</code> must be provided. Otherwise, EC2SecurityGroupOwnerId and either <code>EC2SecurityGroupName</code> or <code>EC2SecurityGroupId</code> must be provided. </p>"}, "EC2SecurityGroupOwnerId": {"shape": "String", "documentation": "<p> AWS Account Number of the owner of the EC2 security group specified in the EC2SecurityGroupName parameter. The AWS Access Key ID is not an acceptable value. For VPC DB security groups, <code>EC2SecurityGroupId</code> must be provided. Otherwise, EC2SecurityGroupOwnerId and either <code>EC2SecurityGroupName</code> or <code>EC2SecurityGroupId</code> must be provided. </p>"}}, "documentation": "<p> </p>"}, "AvailabilityZone": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p> The name of the availability zone. </p>"}}, "wrapper": true, "documentation": "<p> Contains Availability Zone information. </p> <p> This data type is used as an element in the following data type: <ul> <li><a>OrderableDBInstanceOption</a></li> </ul> </p>"}, "AvailabilityZoneList": {"type": "list", "member": {"shape": "AvailabilityZone", "locationName": "AvailabilityZone"}}, "Boolean": {"type": "boolean"}, "BooleanOptional": {"type": "boolean"}, "CharacterSet": {"type": "structure", "members": {"CharacterSetName": {"shape": "String", "documentation": "<p> The name of the character set. </p>"}, "CharacterSetDescription": {"shape": "String", "documentation": "<p> The description of the character set. </p>"}}, "documentation": "<p> This data type is used as a response element in the action <a>DescribeDBEngineVersions</a>. </p>"}, "CopyDBParameterGroupMessage": {"type": "structure", "required": ["SourceDBParameterGroupIdentifier", "TargetDBParameterGroupIdentifier", "TargetDBParameterGroupDescription"], "members": {"SourceDBParameterGroupIdentifier": {"shape": "String", "documentation": "<p> The identifier or ARN for the source DB parameter group. </p> <p>Constraints:</p> <ul> <li>Must specify a valid DB parameter group.</li> <li>If the source DB parameter group is in the same region as the copy, specify a valid DB parameter group identifier, for example <code>my-db-param-group</code>, or a valid ARN.</li> <li>If the source DB parameter group is in a different region than the copy, specify a valid DB parameter group ARN, for example <code>arn:aws:rds:us-west-2:123456789012:pg:special-parameters</code>.</li> </ul>"}, "TargetDBParameterGroupIdentifier": {"shape": "String", "documentation": "<p>The identifier for the copied DB parameter group.</p> <p>Constraints:</p> <ul> <li>Cannot be null, empty, or blank</li> <li>Must contain from 1 to 255 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <code>my-db-parameter-group</code></p>"}, "TargetDBParameterGroupDescription": {"shape": "String", "documentation": "<p>A description for the copied DB parameter group.</p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CopyDBSnapshotMessage": {"type": "structure", "required": ["SourceDBSnapshotIdentifier", "TargetDBSnapshotIdentifier"], "members": {"SourceDBSnapshotIdentifier": {"shape": "String", "documentation": "<p> The identifier for the source DB snapshot. </p> <p>Constraints:</p> <ul> <li>Must specify a valid system snapshot in the \"available\" state.</li> <li>If the source snapshot is in the same region as the copy, specify a valid DB snapshot identifier.</li> <li>If the source snapshot is in a different region than the copy, specify a valid DB snapshot ARN. For more information, go to <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_CopySnapshot.html\"> Copying a DB Snapshot</a>.</li> </ul> <p>Example: <code>rds:mydb-2012-04-02-00-01</code></p> <p>Example: <code>arn:aws:rds:rr-regn-1:123456789012:snapshot:mysql-instance1-snapshot-20130805</code></p>"}, "TargetDBSnapshotIdentifier": {"shape": "String", "documentation": "<p> The identifier for the copied snapshot. </p> <p>Constraints:</p> <ul> <li>Cannot be null, empty, or blank</li> <li>Must contain from 1 to 255 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <code>my-db-snapshot</code></p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CopyOptionGroupMessage": {"type": "structure", "required": ["SourceOptionGroupIdentifier", "TargetOptionGroupIdentifier", "TargetOptionGroupDescription"], "members": {"SourceOptionGroupIdentifier": {"shape": "String", "documentation": "<p>The identifier or ARN for the source option group. </p> <p>Constraints:</p> <ul> <li>Must specify a valid option group.</li> <li>If the source option group is in the same region as the copy, specify a valid option group identifier, for example <code>my-option-group</code>, or a valid ARN.</li> <li>If the source option group is in a different region than the copy, specify a valid option group ARN, for example <code>arn:aws:rds:us-west-2:123456789012:og:special-options</code>.</li> </ul>"}, "TargetOptionGroupIdentifier": {"shape": "String", "documentation": "<p>The identifier for the copied option group. </p> <p>Constraints:</p> <ul> <li>Cannot be null, empty, or blank</li> <li>Must contain from 1 to 255 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <code>my-option-group</code></p>"}, "TargetOptionGroupDescription": {"shape": "String", "documentation": "<p>The description for the copied option group. </p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CreateDBInstanceMessage": {"type": "structure", "required": ["DBInstanceIdentifier", "AllocatedStorage", "DBInstanceClass", "Engine", "MasterUsername", "MasterUserPassword"], "members": {"DBName": {"shape": "String", "documentation": "<p>The meaning of this parameter differs according to the database engine you use.</p> <p>Type: String</p> <p><b>MySQL</b></p> <p>The name of the database to create when the DB instance is created. If this parameter is not specified, no database is created in the DB instance. </p> <p>Constraints:</p> <ul> <li>Must contain 1 to 64 alphanumeric characters</li> <li>Cannot be a word reserved by the specified database engine</li> </ul> <p><b>PostgreSQL</b></p> <p>The name of the database to create when the DB instance is created. If this parameter is not specified, no database is created in the DB instance. </p> <p>Constraints:</p> <ul> <li>Must contain 1 to 63 alphanumeric characters</li> <li>Must begin with a letter or an underscore. Subsequent characters can be letters, underscores, or digits (0-9).</li> <li>Cannot be a word reserved by the specified database engine</li> </ul> <p><b>Oracle</b></p> <p> The Oracle System ID (SID) of the created DB instance. </p> <p>Default: <code>ORCL</code></p> <p>Constraints:</p> <ul> <li>Cannot be longer than 8 characters</li> </ul> <p><b>SQL Server</b></p> <p>Not applicable. Must be null.</p>"}, "DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier. This parameter is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens (1 to 15 for SQL Server).</li> <li>First character must be a letter.</li> <li>Cannot end with a hyphen or contain two consecutive hyphens.</li> </ul> <p>Example: <code>mydbinstance</code></p>"}, "AllocatedStorage": {"shape": "IntegerOptional", "documentation": "<p> The amount of storage (in gigabytes) to be initially allocated for the database instance. </p> <p> Type: Integer</p> <p><b>MySQL</b></p> <p> Constraints: Must be an integer from 5 to 3072.</p> <p><b>PostgreSQL</b></p> <p> Constraints: Must be an integer from 5 to 3072.</p> <p><b>Oracle</b></p> <p> Constraints: Must be an integer from 10 to 3072.</p> <p><b>SQL Server</b></p> <p> Constraints: Must be an integer from 200 to 1024 (Standard Edition and Enterprise Edition) or from 20 to 1024 (Express Edition and Web Edition)</p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The compute and memory capacity of the DB instance. </p> <p> Valid Values: <code>db.t1.micro | db.m1.small | db.m1.medium | db.m1.large | db.m1.xlarge | db.m2.xlarge |db.m2.2xlarge | db.m2.4xlarge | db.m3.medium | db.m3.large | db.m3.xlarge | db.m3.2xlarge | db.r3.large | db.r3.xlarge | db.r3.2xlarge | db.r3.4xlarge | db.r3.8xlarge | db.t2.micro | db.t2.small | db.t2.medium</code> </p>"}, "Engine": {"shape": "String", "documentation": "<p> The name of the database engine to be used for this instance. </p> <p> Valid Values: <code>MySQL</code> | <code>oracle-se1</code> | <code>oracle-se</code> | <code>oracle-ee</code> | <code>sqlserver-ee</code> | <code>sqlserver-se</code> | <code>sqlserver-ex</code> | <code>sqlserver-web</code> | <code>postgres</code> </p> <p> Not every database engine is available for every AWS region. </p>"}, "MasterUsername": {"shape": "String", "documentation": "<p> The name of master user for the client DB instance. </p> <p><b>MySQL</b></p> <p>Constraints:</p> <ul> <li>Must be 1 to 16 alphanumeric characters.</li> <li>First character must be a letter.</li> <li>Cannot be a reserved word for the chosen database engine.</li> </ul> <p>Type: String</p> <p><b>Oracle</b></p> <p>Constraints:</p> <ul> <li>Must be 1 to 30 alphanumeric characters.</li> <li>First character must be a letter.</li> <li>Cannot be a reserved word for the chosen database engine.</li> </ul> <p><b>SQL Server</b></p> <p>Constraints:</p> <ul> <li>Must be 1 to 128 alphanumeric characters.</li> <li>First character must be a letter.</li> <li>Cannot be a reserved word for the chosen database engine.</li> </ul>"}, "MasterUserPassword": {"shape": "String", "documentation": "<p> The password for the master database user. Can be any printable ASCII character except \"/\", \"\"\", or \"@\". </p> <p>Type: String</p> <p><b>MySQL</b></p> <p> Constraints: Must contain from 8 to 41 characters. </p> <p><b>Oracle</b></p> <p> Constraints: Must contain from 8 to 30 characters. </p> <p><b>SQL Server</b></p> <p> Constraints: Must contain from 8 to 128 characters. </p>"}, "DBSecurityGroups": {"shape": "DBSecurityGroupNameList", "documentation": "<p> A list of DB security groups to associate with this DB instance. </p> <p> Default: The default DB security group for the database engine. </p>"}, "VpcSecurityGroupIds": {"shape": "VpcSecurityGroupIdList", "documentation": "<p> A list of EC2 VPC security groups to associate with this DB instance. </p> <p> Default: The default EC2 VPC security group for the DB subnet group's VPC. </p>"}, "AvailabilityZone": {"shape": "String", "documentation": "<p> The EC2 Availability Zone that the database instance will be created in. For information on regions and Availability Zones, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Concepts.RegionsAndAvailabilityZones.html\">Regions and Availability Zones</a>. </p> <p> Default: A random, system-chosen Availability Zone in the endpoint's region. </p> <p> Example: <code>us-east-1d</code> </p> <p> Constraint: The AvailabilityZone parameter cannot be specified if the MultiAZ parameter is set to <code>true</code>. The specified Availability Zone must be in the same region as the current endpoint. </p>"}, "DBSubnetGroupName": {"shape": "String", "documentation": "<p> A DB subnet group to associate with this DB instance. </p> <p> If there is no DB subnet group, then it is a non-VPC DB instance. </p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p> The weekly time range (in UTC) during which system maintenance can occur. For more information, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Concepts.DBMaintenance.html\">DB Instance Maintenance</a>. </p> <p> Format: <code>ddd:hh24:mi-ddd:hh24:mi</code> </p> <p> Default: A 30-minute window selected at random from an 8-hour block of time per region, occurring on a random day of the week. To see the time blocks available, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/AdjustingTheMaintenanceWindow.html\"> Adjusting the Preferred Maintenance Window</a> in the Amazon RDS User Guide. </p> <p>Valid Days: Mon, <PERSON>e, Wed, Thu, Fri, Sat, Sun</p> <p>Constraints: Minimum 30-minute window.</p>"}, "DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group to associate with this DB instance. If this argument is omitted, the default DBParameterGroup for the specified engine will be used. </p> <p> Constraints: </p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "BackupRetentionPeriod": {"shape": "IntegerOptional", "documentation": "<p> The number of days for which automated backups are retained. Setting this parameter to a positive number enables backups. Setting this parameter to 0 disables automated backups. </p> <p> Default: 1 </p> <p>Constraints:</p> <ul> <li>Must be a value from 0 to 35</li> <li>Cannot be set to 0 if the DB instance is a source to Read Replicas</li> </ul>"}, "PreferredBackupWindow": {"shape": "String", "documentation": "<p> The daily time range during which automated backups are created if automated backups are enabled, using the <code>BackupRetentionPeriod</code> parameter. For more information, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.BackingUpAndRestoringAmazonRDSInstances.html\">DB Instance Backups</a>. </p> <p> Default: A 30-minute window selected at random from an 8-hour block of time per region. See the Amazon RDS User Guide for the time blocks for each region from which the default backup windows are assigned. </p> <p> Constraints: Must be in the format <code>hh24:mi-hh24:mi</code>. Times should be Universal Time Coordinated (UTC). Must not conflict with the preferred maintenance window. Must be at least 30 minutes. </p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> The port number on which the database accepts connections. </p> <p><b>MySQL</b></p> <p> Default: <code>3306</code> </p> <p> Valid Values: <code>1150-65535</code> </p> <p>Type: Integer </p> <p><b>PostgreSQL</b></p> <p> Default: <code>5432</code> </p> <p> Valid Values: <code>1150-65535</code> </p> <p>Type: Integer </p> <p><b>Oracle</b></p> <p> Default: <code>1521</code> </p> <p> Valid Values: <code>1150-65535</code> </p> <p><b>SQL Server</b></p> <p> Default: <code>1433</code> </p> <p> Valid Values: <code>1150-65535</code> except for <code>1434</code>, <code>3389</code>, <code>47001</code>, <code>49152</code>, and <code>49152</code> through <code>49156</code>. </p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> Specifies if the DB instance is a Multi-AZ deployment. You cannot set the AvailabilityZone parameter if the MultiAZ parameter is set to true. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> The version number of the database engine to use. </p> <p> The following are the database engines and major and minor versions that are available with Amazon RDS. Not every database engine is available for every AWS region. </p> <p><b>MySQL</b></p> <ul> <li> <b>Version 5.1:</b> <code> 5.1.45 | 5.1.49 | 5.1.50 | 5.1.57 | 5.1.61 | 5.1.62 | 5.1.63 | 5.1.69 | 5.1.71 | 5.1.73</code> </li> <li> <b>Version 5.5:</b> <code> 5.5.12 | 5.5.20 | 5.5.23 | 5.5.25a | 5.5.27 | 5.5.31 | 5.5.33 | 5.5.37 | 5.5.38 | 5.5.8</code> </li> <li> <b>Version 5.6:</b> <code> 5.6.12 | 5.6.13 | 5.6.17 | 5.6.19 | 5.6.21</code> </li> </ul> <p><b>Oracle Database Enterprise Edition (oracle-ee)</b></p> <ul> <li> <b>Version 11.2:</b> <code> 11.2.0.2.v3 | 11.2.0.2.v4 | 11.2.0.2.v5 | 11.2.0.2.v6 | 11.2.0.2.v7 | 11.2.0.3.v1 | 11.2.0.4.v1</code> </li> </ul> <p><b>Oracle Database Standard Edition (oracle-se)</b></p> <ul> <li> <b>Version 11.2:</b> <code> 11.2.0.2.v3 | 11.2.0.2.v4 | 11.2.0.2.v5 | 11.2.0.2.v6 | 11.2.0.2.v7 | 11.2.0.3.v1 | 11.2.0.4.v1</code> </li> </ul> <p><b>Oracle Database Standard Edition One (oracle-se1)</b></p> <ul> <li> <b>Version 11.2:</b> <code> 11.2.0.2.v3 | 11.2.0.2.v4 | 11.2.0.2.v5 | 11.2.0.2.v6 | 11.2.0.2.v7 | 11.2.0.3.v1 | 11.2.0.4.v1</code> </li> </ul> <p><b>PostgreSQL</b></p> <ul> <li> <b>Version 9.3:</b> <code> 9.3.1 | 9.3.2 | 9.3.3</code> </li> </ul> <p><b>Microsoft SQL Server Enterprise Edition (sqlserver-ee)</b></p> <ul> <li> <b>Version 10.5:</b> <code> 10.50.2789.0.v1</code> </li> <li> <b>Version 11.0:</b> <code> 11.00.2100.60.v1</code> </li> </ul> <p><b>Microsoft SQL Server Express Edition (sqlserver-ex)</b></p> <ul> <li> <b>Version 10.5:</b> <code> 10.50.2789.0.v1</code> </li> <li> <b>Version 11.0:</b> <code> 11.00.2100.60.v1</code> </li> </ul> <p><b>Microsoft SQL Server Standard Edition (sqlserver-se)</b></p> <ul> <li> <b>Version 10.5:</b> <code> 10.50.2789.0.v1</code> </li> <li> <b>Version 11.0:</b> <code> 11.00.2100.60.v1</code> </li> </ul> <p><b>Microsoft SQL Server Web Edition (sqlserver-web)</b></p> <ul> <li> <b>Version 10.5:</b> <code> 10.50.2789.0.v1</code> </li> <li> <b>Version 11.0:</b> <code> 11.00.2100.60.v1</code> </li> </ul>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p> Indicates that minor engine upgrades will be applied automatically to the DB instance during the maintenance window. </p> <p>Default: <code>true</code></p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> License model information for this DB instance. </p> <p> Valid values: <code>license-included</code> | <code>bring-your-own-license</code> | <code>general-public-license</code> </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> The amount of Provisioned IOPS (input/output operations per second) to be initially allocated for the DB instance. </p> <p> Constraints: To use PIOPS, this value must be an integer greater than 1000.</p>"}, "OptionGroupName": {"shape": "String", "documentation": "<p> Indicates that the DB instance should be associated with the specified option group. </p> <p> Permanent options, such as the TDE option for Oracle Advanced Security TDE, cannot be removed from an option group, and that option group cannot be removed from a DB instance once it is associated with a DB instance </p>"}, "CharacterSetName": {"shape": "String", "documentation": "<p> For supported engines, indicates that the DB instance should be associated with the specified CharacterSet. </p>"}, "PubliclyAccessible": {"shape": "BooleanOptional", "documentation": "<p> Specifies the accessibility options for the DB instance. A value of true specifies an Internet-facing instance with a publicly resolvable DNS name, which resolves to a public IP address. A value of false specifies an internal instance with a DNS name that resolves to a private IP address. </p> <p> Default: The default behavior varies depending on whether a VPC has been requested or not. The following list shows the default behavior in each case. </p> <ul> <li> <b>Default VPC:</b>true</li> <li> <b>VPC:</b>false</li> </ul> <p> If no DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be publicly accessible. If a specific DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be private. </p>"}, "Tags": {"shape": "TagList"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type to be associated with the DB Instance. </p> <p> Valid values: <code>standard | gp2 | io1</code> </p> <p> If you specify <code>io1</code>, you must also include a value for the <code>Iops</code> parameter. </p> <p> Default: <code>io1</code> if the <code>Iops</code> parameter is specified; otherwise <code>standard</code> </p>"}, "TdeCredentialArn": {"shape": "String", "documentation": "<p> The ARN from the Key Store with which to associate the instance for TDE encryption. </p>"}, "TdeCredentialPassword": {"shape": "String", "documentation": "<p> The password for the given ARN from the Key Store in order to access the device. </p>"}}, "documentation": "<p> </p>"}, "CreateDBInstanceReadReplicaMessage": {"type": "structure", "required": ["DBInstanceIdentifier", "SourceDBInstanceIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier of the Read Replica. This is the unique key that identifies a DB instance. This parameter is stored as a lowercase string. </p>"}, "SourceDBInstanceIdentifier": {"shape": "String", "documentation": "<p> The identifier of the DB instance that will act as the source for the Read Replica. Each DB instance can have up to five Read Replicas. </p> <p>Constraints:</p> <ul> <li>Must be the identifier of an existing DB instance.</li> <li>Can specify a DB instance that is a MySQL Read Replica only if the source is running MySQL 5.6.</li> <li>Can specify a DB instance that is a PostgreSQL Read Replica only if the source is running PostgreSQL 9.3.5.</li> <li>The specified DB instance must have automatic backups enabled, its backup retention period must be greater than 0.</li> <li>If the source DB instance is in the same region as the Read Replica, specify a valid DB instance identifier.</li> <li>If the source DB instance is in a different region than the Read Replica, specify a valid DB instance ARN. For more information, go to <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.html#USER_Tagging.ARN\"> Constructing a Amazon RDS Amazon Resource Name (ARN)</a>.</li> </ul>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The compute and memory capacity of the Read Replica. </p> <p> Valid Values: <code>db.m1.small | db.m1.medium | db.m1.large | db.m1.xlarge | db.m2.xlarge |db.m2.2xlarge | db.m2.4xlarge | db.m3.medium | db.m3.large | db.m3.xlarge | db.m3.2xlarge | db.r3.large | db.r3.xlarge | db.r3.2xlarge | db.r3.4xlarge | db.r3.8xlarge | db.t2.micro | db.t2.small | db.t2.medium</code> </p> <p>Default: Inherits from the source DB instance.</p>"}, "AvailabilityZone": {"shape": "String", "documentation": "<p> The Amazon EC2 Availability Zone that the Read Replica will be created in. </p> <p> Default: A random, system-chosen Availability Zone in the endpoint's region. </p> <p> Example: <code>us-east-1d</code> </p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> The port number that the DB instance uses for connections. </p> <p>Default: Inherits from the source DB instance</p> <p>Valid Values: <code>1150-65535</code></p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p> Indicates that minor engine upgrades will be applied automatically to the Read Replica during the maintenance window. </p> <p>Default: Inherits from the source DB instance</p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> The amount of Provisioned IOPS (input/output operations per second) to be initially allocated for the DB instance. </p>"}, "OptionGroupName": {"shape": "String", "documentation": "<p> The option group the DB instance will be associated with. If omitted, the default option group for the engine specified will be used. </p>"}, "PubliclyAccessible": {"shape": "BooleanOptional", "documentation": "<p> Specifies the accessibility options for the DB instance. A value of true specifies an Internet-facing instance with a publicly resolvable DNS name, which resolves to a public IP address. A value of false specifies an internal instance with a DNS name that resolves to a private IP address. </p> <p> Default: The default behavior varies depending on whether a VPC has been requested or not. The following list shows the default behavior in each case. </p> <ul> <li> <b>Default VPC:</b>true</li> <li> <b>VPC:</b>false</li> </ul> <p> If no DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be publicly accessible. If a specific DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be private. </p>"}, "Tags": {"shape": "TagList"}, "DBSubnetGroupName": {"shape": "String", "documentation": "<p> Specifies a DB subnet group for the DB instance. The new DB instance will be created in the VPC associated with the DB subnet group. If no DB subnet group is specified, then the new DB instance is not created in a VPC. </p> <p>Constraints:</p> <ul> <li>Can only be specified if the source DB instance identifier specifies a DB instance in another region.</li> <li>The specified DB subnet group must be in the same region in which the operation is running.</li> <li> All Read Replicas in one region that are created from the same source DB instance must either: <ul> <li>Specify DB subnet groups from the same VPC. All these Read Replicas will be created in the same VPC.</li> <li>Not specify a DB subnet group. All these Read Replicas will be created outside of any VPC.</li> </ul> </li> </ul>"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type to be associated with the DB Instance Read Replica. </p> <p> Valid values: <code>standard | gp2 | io1</code> </p> <p> If you specify <code>io1</code>, you must also include a value for the <code>Iops</code> parameter. </p> <p> Default: <code>io1</code> if the <code>Iops</code> parameter is specified; otherwise <code>standard</code> </p>"}}}, "CreateDBParameterGroupMessage": {"type": "structure", "required": ["DBParameterGroupName", "DBParameterGroupFamily", "Description"], "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group. </p> <p> Constraints: </p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <note>This value is stored as a lower-case string.</note>"}, "DBParameterGroupFamily": {"shape": "String", "documentation": "<p> The DB parameter group family name. A DB parameter group can be associated with one and only one DB parameter group family, and can be applied only to a DB instance running a database engine and engine version compatible with that DB parameter group family. </p>"}, "Description": {"shape": "String", "documentation": "<p> The description for the DB parameter group. </p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CreateDBSecurityGroupMessage": {"type": "structure", "required": ["DBSecurityGroupName", "DBSecurityGroupDescription"], "members": {"DBSecurityGroupName": {"shape": "String", "documentation": "<p> The name for the DB security group. This value is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> <li>Must not be \"Default\"</li> <li>May not contain spaces</li> </ul> <p>Example: <code>mysecuritygroup</code></p>"}, "DBSecurityGroupDescription": {"shape": "String", "documentation": "<p> The description for the DB security group. </p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CreateDBSnapshotMessage": {"type": "structure", "required": ["DBSnapshotIdentifier", "DBInstanceIdentifier"], "members": {"DBSnapshotIdentifier": {"shape": "String", "documentation": "<p> The identifier for the DB snapshot. </p> <p>Constraints:</p> <ul> <li>Cannot be null, empty, or blank</li> <li>Must contain from 1 to 255 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <code>my-snapshot-id</code></p>"}, "DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier. This is the unique key that identifies a DB instance. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CreateDBSubnetGroupMessage": {"type": "structure", "required": ["DBSubnetGroupName", "DBSubnetGroupDescription", "SubnetIds"], "members": {"DBSubnetGroupName": {"shape": "String", "documentation": "<p> The name for the DB subnet group. This value is stored as a lowercase string. </p> <p>Constraints: Must contain no more than 255 alphanumeric characters or hyphens. Must not be \"Default\".</p> <p>Example: <code>mySubnetgroup</code></p>"}, "DBSubnetGroupDescription": {"shape": "String", "documentation": "<p> The description for the DB subnet group. </p>"}, "SubnetIds": {"shape": "SubnetIdentifierList", "documentation": "<p> The EC2 Subnet IDs for the DB subnet group. </p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "CreateEventSubscriptionMessage": {"type": "structure", "required": ["SubscriptionName", "SnsTopicArn"], "members": {"SubscriptionName": {"shape": "String", "documentation": "<p>The name of the subscription. </p> <p>Constraints: The name must be less than 255 characters. </p>"}, "SnsTopicArn": {"shape": "String", "documentation": "<p> The Amazon Resource Name (ARN) of the SNS topic created for event notification. The ARN is created by Amazon SNS when you create a topic and subscribe to it. </p>"}, "SourceType": {"shape": "String", "documentation": "<p> The type of source that will be generating the events. For example, if you want to be notified of events generated by a DB instance, you would set this parameter to db-instance. if this value is not specified, all events are returned. </p> <p>Valid values: db-instance | db-parameter-group | db-security-group | db-snapshot</p>"}, "EventCategories": {"shape": "EventCategoriesList", "documentation": "<p> A list of event categories for a SourceType that you want to subscribe to. You can see a list of the categories for a given SourceType in the <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html\">Events</a> topic in the Amazon RDS User Guide or by using the <b>DescribeEventCategories</b> action. </p>"}, "SourceIds": {"shape": "SourceIdsList", "documentation": "<p> The list of identifiers of the event sources for which events will be returned. If not specified, then all sources are included in the response. An identifier must begin with a letter and must contain only ASCII letters, digits, and hyphens; it cannot end with a hyphen or contain two consecutive hyphens. </p> <p>Constraints:</p> <ul> <li>If SourceIds are supplied, SourceType must also be provided.</li> <li>If the source type is a DB instance, then a <code>DBInstanceIdentifier</code> must be supplied.</li> <li>If the source type is a DB security group, a <code>DBSecurityGroupName</code> must be supplied.</li> <li>If the source type is a DB parameter group, a <code>DBParameterGroupName</code> must be supplied.</li> <li>If the source type is a DB snapshot, a <code>DBSnapshotIdentifier</code> must be supplied.</li> </ul>"}, "Enabled": {"shape": "BooleanOptional", "documentation": "<p> A Boolean value; set to <b>true</b> to activate the subscription, set to <b>false</b> to create the subscription but not active it. </p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p></p>"}, "CreateOptionGroupMessage": {"type": "structure", "required": ["OptionGroupName", "EngineName", "MajorEngineVersion", "OptionGroupDescription"], "members": {"OptionGroupName": {"shape": "String", "documentation": "<p> Specifies the name of the option group to be created. </p> <p> Constraints: </p> <ul> <li>Must be 1 to 255 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <code>myoptiongroup</code></p>"}, "EngineName": {"shape": "String", "documentation": "<p> Specifies the name of the engine that this option group should be associated with. </p>"}, "MajorEngineVersion": {"shape": "String", "documentation": "<p> Specifies the major version of the engine that this option group should be associated with. </p>"}, "OptionGroupDescription": {"shape": "String", "documentation": "<p> The description of the option group. </p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p></p>"}, "DBEngineVersion": {"type": "structure", "members": {"Engine": {"shape": "String", "documentation": "<p> The name of the database engine. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> The version number of the database engine. </p>"}, "DBParameterGroupFamily": {"shape": "String", "documentation": "<p> The name of the DB parameter group family for the database engine. </p>"}, "DBEngineDescription": {"shape": "String", "documentation": "<p> The description of the database engine. </p>"}, "DBEngineVersionDescription": {"shape": "String", "documentation": "<p> The description of the database engine version. </p>"}, "DefaultCharacterSet": {"shape": "CharacterSet", "documentation": "<p> The default character set for new instances of this engine version, if the <code>CharacterSetName</code> parameter of the CreateDBInstance API is not specified. </p>"}, "SupportedCharacterSets": {"shape": "SupportedCharacterSetsList", "documentation": "<p> A list of the character sets supported by this engine for the <code>CharacterSetName</code> parameter of the CreateDBInstance API. </p>"}}, "documentation": "<p> This data type is used as a response element in the action <a>DescribeDBEngineVersions</a>. </p>"}, "DBEngineVersionList": {"type": "list", "member": {"shape": "DBEngineVersion", "locationName": "DBEngineVersion"}}, "DBEngineVersionMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "DBEngineVersions": {"shape": "DBEngineVersionList", "documentation": "<p> A list of <code>DBEngineVersion</code> elements. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBEngineVersions</a> action. </p>"}, "DBInstance": {"type": "structure", "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> Contains a user-supplied database identifier. This is the unique key that identifies a DB instance. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> Contains the name of the compute and memory capacity class of the DB instance. </p>"}, "Engine": {"shape": "String", "documentation": "<p> Provides the name of the database engine to be used for this DB instance. </p>"}, "DBInstanceStatus": {"shape": "String", "documentation": "<p> Specifies the current state of this database. </p>"}, "MasterUsername": {"shape": "String", "documentation": "<p> Contains the master username for the DB instance. </p>"}, "DBName": {"shape": "String", "documentation": "<p>The meaning of this parameter differs according to the database engine you use. For example, this value returns either MySQL or PostgreSQL information when returning values from CreateDBInstanceReadReplica since Read Replicas are only supported for MySQL and PostgreSQL.</p> <p><b>MySQL, SQL Server, PostgreSQL</b></p> <p> Contains the name of the initial database of this instance that was provided at create time, if one was specified when the DB instance was created. This same name is returned for the life of the DB instance. </p> <p>Type: String</p> <p><b>Oracle</b></p> <p> Contains the Oracle System ID (SID) of the created DB instance. Not shown when the returned parameters do not apply to an Oracle DB instance. </p>"}, "Endpoint": {"shape": "Endpoint", "documentation": "<p> Specifies the connection endpoint. </p>"}, "AllocatedStorage": {"shape": "Integer", "documentation": "<p> Specifies the allocated storage size specified in gigabytes. </p>"}, "InstanceCreateTime": {"shape": "TStamp", "documentation": "<p> Provides the date and time the DB instance was created. </p>"}, "PreferredBackupWindow": {"shape": "String", "documentation": "<p> Specifies the daily time range during which automated backups are created if automated backups are enabled, as determined by the <code>BackupRetentionPeriod</code>. </p>"}, "BackupRetentionPeriod": {"shape": "Integer", "documentation": "<p> Specifies the number of days for which automatic DB snapshots are retained. </p>"}, "DBSecurityGroups": {"shape": "DBSecurityGroupMembershipList", "documentation": "<p> Provides List of DB security group elements containing only <code>DBSecurityGroup.Name</code> and <code>DBSecurityGroup.Status</code> subelements. </p>"}, "VpcSecurityGroups": {"shape": "VpcSecurityGroupMembershipList", "documentation": "<p> Provides List of VPC security group elements that the DB instance belongs to. </p>"}, "DBParameterGroups": {"shape": "DBParameterGroupStatusList", "documentation": "<p> Provides the list of DB parameter groups applied to this DB instance. </p>"}, "AvailabilityZone": {"shape": "String", "documentation": "<p> Specifies the name of the Availability Zone the DB instance is located in. </p>"}, "DBSubnetGroup": {"shape": "DBSubnetGroup", "documentation": "<p> Specifies information on the subnet group associated with the DB instance, including the name, description, and subnets in the subnet group. </p>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p> Specifies the weekly time range (in UTC) during which system maintenance can occur. </p>"}, "PendingModifiedValues": {"shape": "PendingModifiedValues", "documentation": "<p> Specifies that changes to the DB instance are pending. This element is only included when changes are pending. Specific changes are identified by subelements. </p>"}, "LatestRestorableTime": {"shape": "TStamp", "documentation": "<p> Specifies the latest time to which a database can be restored with point-in-time restore. </p>"}, "MultiAZ": {"shape": "Boolean", "documentation": "<p> Specifies if the DB instance is a Multi-AZ deployment. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> Indicates the database engine version. </p>"}, "AutoMinorVersionUpgrade": {"shape": "Boolean", "documentation": "<p> Indicates that minor version patches are applied automatically. </p>"}, "ReadReplicaSourceDBInstanceIdentifier": {"shape": "String", "documentation": "<p> Contains the identifier of the source DB instance if this DB instance is a Read Replica. </p>"}, "ReadReplicaDBInstanceIdentifiers": {"shape": "ReadReplicaDBInstanceIdentifierList", "documentation": "<p> Contains one or more identifiers of the Read Replicas associated with this DB instance. </p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> License model information for this DB instance. </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> Specifies the Provisioned IOPS (I/O operations per second) value. </p>"}, "OptionGroupMemberships": {"shape": "OptionGroupMembershipList", "documentation": "<p> Provides the list of option group memberships for this DB instance. </p>"}, "CharacterSetName": {"shape": "String", "documentation": "<p> If present, specifies the name of the character set that this instance is associated with. </p>"}, "SecondaryAvailabilityZone": {"shape": "String", "documentation": "<p> If present, specifies the name of the secondary Availability Zone for a DB instance with multi-AZ support. </p>"}, "PubliclyAccessible": {"shape": "Boolean", "documentation": "<p> Specifies the accessibility options for the DB instance. A value of true specifies an Internet-facing instance with a publicly resolvable DNS name, which resolves to a public IP address. A value of false specifies an internal instance with a DNS name that resolves to a private IP address. </p> <p> Default: The default behavior varies depending on whether a VPC has been requested or not. The following list shows the default behavior in each case. </p> <ul> <li> <b>Default VPC:</b>true</li> <li> <b>VPC:</b>false</li> </ul> <p> If no DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be publicly accessible. If a specific DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be private. </p>"}, "StatusInfos": {"shape": "DBInstanceStatusInfoList", "documentation": "<p> The status of a Read Replica. If the instance is not a Read Replica, this will be blank. </p>"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type associated with DB Instance. </p>"}, "TdeCredentialArn": {"shape": "String", "documentation": "<p> The ARN from the Key Store with which the instance is associated for TDE encryption. </p>"}}, "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBInstances</a> action.</p>"}, "DBInstanceAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "DBInstanceAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> User already has a DB instance with the given identifier. </p>"}, "DBInstanceList": {"type": "list", "member": {"shape": "DBInstance", "locationName": "DBInstance"}}, "DBInstanceMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}, "DBInstances": {"shape": "DBInstanceList", "documentation": "<p> A list of <a>DBInstance</a> instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBInstances</a> action. </p>"}, "DBInstanceNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "DBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBInstanceIdentifier</i> does not refer to an existing DB instance. </p>"}, "DBInstanceStatusInfo": {"type": "structure", "members": {"StatusType": {"shape": "String", "documentation": "<p> This value is currently \"read replication.\" </p>"}, "Normal": {"shape": "Boolean", "documentation": "<p> Boolean value that is true if the instance is operating normally, or false if the instance is in an error state. </p>"}, "Status": {"shape": "String", "documentation": "<p> Status of the DB instance. For a StatusType of read replica, the values can be replicating, error, stopped, or terminated. </p>"}, "Message": {"shape": "String", "documentation": "<p> Details of the error if there is an error for the instance. If the instance is not in an error state, this value is blank. </p>"}}, "documentation": "<p>Provides a list of status information for a DB instance.</p>"}, "DBInstanceStatusInfoList": {"type": "list", "member": {"shape": "DBInstanceStatusInfo", "locationName": "DBInstanceStatusInfo"}}, "DBParameterGroup": {"type": "structure", "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p>Provides the name of the DB parameter group. </p>"}, "DBParameterGroupFamily": {"shape": "String", "documentation": "<p>Provides the name of the DB parameter group family that this DB parameter group is compatible with. </p>"}, "Description": {"shape": "String", "documentation": "<p>Provides the customer-specified description for this DB parameter group. </p>"}}, "wrapper": true, "documentation": "<p>Contains the result of a successful invocation of the <a>CreateDBParameterGroup</a> action. </p> <p>This data type is used as a request parameter in the <a>DeleteDBParameterGroup</a> action, and as a response element in the <a>DescribeDBParameterGroups</a> action. </p>"}, "DBParameterGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "DBParameterGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB parameter group with the same name exists. </p>"}, "DBParameterGroupDetails": {"type": "structure", "members": {"Parameters": {"shape": "ParametersList", "documentation": "<p> A list of <a>Parameter</a> values. </p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBParameters</a> action. </p>"}, "DBParameterGroupList": {"type": "list", "member": {"shape": "DBParameterGroup", "locationName": "DBParameterGroup"}}, "DBParameterGroupNameMessage": {"type": "structure", "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>ModifyDBParameterGroup</a> or <a>ResetDBParameterGroup</a> action. </p>"}, "DBParameterGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "DBParameterGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBParameterGroupName</i> does not refer to an existing DB parameter group. </p>"}, "DBParameterGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "DBParameterGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB parameter groups. </p>"}, "DBParameterGroupStatus": {"type": "structure", "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DP parameter group. </p>"}, "ParameterApplyStatus": {"shape": "String", "documentation": "<p> The status of parameter updates. </p>"}}, "documentation": "<p> The status of the DB parameter group. </p> <p>This data type is used as a response element in the following actions:</p> <ul> <li><a>CreateDBInstance</a></li> <li><a>CreateDBInstanceReadReplica</a></li> <li><a>DeleteDBInstance</a></li> <li><a>ModifyDBInstance</a></li> <li><a>RebootDBInstance</a></li> <li><a>RestoreDBInstanceFromDBSnapshot</a></li> </ul>"}, "DBParameterGroupStatusList": {"type": "list", "member": {"shape": "DBParameterGroupStatus", "locationName": "DBParameterGroup"}}, "DBParameterGroupsMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "DBParameterGroups": {"shape": "DBParameterGroupList", "documentation": "<p> A list of <a>DBParameterGroup</a> instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBParameterGroups</a> action. </p>"}, "DBSecurityGroup": {"type": "structure", "members": {"OwnerId": {"shape": "String", "documentation": "<p> Provides the AWS ID of the owner of a specific DB security group. </p>"}, "DBSecurityGroupName": {"shape": "String", "documentation": "<p> Specifies the name of the DB security group. </p>"}, "DBSecurityGroupDescription": {"shape": "String", "documentation": "<p> Provides the description of the DB security group. </p>"}, "VpcId": {"shape": "String", "documentation": "<p> Provides the VpcId of the DB security group. </p>"}, "EC2SecurityGroups": {"shape": "EC2SecurityGroupList", "documentation": "<p> Contains a list of <a>EC2SecurityGroup</a> elements. </p>"}, "IPRanges": {"shape": "IPRangeList", "documentation": "<p> Contains a list of <a>IPRange</a> elements. </p>"}}, "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>DescribeDBSecurityGroups</a></li> <li><a>AuthorizeDBSecurityGroupIngress</a></li> <li><a>CreateDBSecurityGroup</a></li> <li><a>RevokeDBSecurityGroupIngress</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSecurityGroups</a> action.</p>"}, "DBSecurityGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "DBSecurityGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB security group with the name specified in <i>DBSecurityGroupName</i> already exists. </p>"}, "DBSecurityGroupMembership": {"type": "structure", "members": {"DBSecurityGroupName": {"shape": "String", "documentation": "<p> The name of the DB security group. </p>"}, "Status": {"shape": "String", "documentation": "<p> The status of the DB security group. </p>"}}, "documentation": "<p> This data type is used as a response element in the following actions: </p> <ul> <li><a>ModifyDBInstance</a></li> <li><a>RebootDBInstance</a></li> <li><a>RestoreDBInstanceFromDBSnapshot</a></li> <li><a>RestoreDBInstanceToPointInTime</a></li> </ul>"}, "DBSecurityGroupMembershipList": {"type": "list", "member": {"shape": "DBSecurityGroupMembership", "locationName": "DBSecurityGroup"}}, "DBSecurityGroupMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "DBSecurityGroups": {"shape": "DBSecurityGroups", "documentation": "<p> A list of <a>DBSecurityGroup</a> instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBSecurityGroups</a> action. </p>"}, "DBSecurityGroupNameList": {"type": "list", "member": {"shape": "String", "locationName": "DBSecurityGroupName"}}, "DBSecurityGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "DBSecurityGroupNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSecurityGroupName</i> does not refer to an existing DB security group. </p>"}, "DBSecurityGroupNotSupportedFault": {"type": "structure", "members": {}, "error": {"code": "DBSecurityGroupNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> A DB security group is not allowed for this action. </p>"}, "DBSecurityGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "QuotaExceeded.DBSecurityGroup", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB security groups. </p>"}, "DBSecurityGroups": {"type": "list", "member": {"shape": "DBSecurityGroup", "locationName": "DBSecurityGroup"}}, "DBSnapshot": {"type": "structure", "members": {"DBSnapshotIdentifier": {"shape": "String", "documentation": "<p> Specifies the identifier for the DB snapshot. </p>"}, "DBInstanceIdentifier": {"shape": "String", "documentation": "<p> Specifies the DB instance identifier of the DB instance this DB snapshot was created from. </p>"}, "SnapshotCreateTime": {"shape": "TStamp", "documentation": "<p> Provides the time (UTC) when the snapshot was taken. </p>"}, "Engine": {"shape": "String", "documentation": "<p> Specifies the name of the database engine. </p>"}, "AllocatedStorage": {"shape": "Integer", "documentation": "<p> Specifies the allocated storage size in gigabytes (GB). </p>"}, "Status": {"shape": "String", "documentation": "<p> Specifies the status of this DB snapshot. </p>"}, "Port": {"shape": "Integer", "documentation": "<p> Specifies the port that the database engine was listening on at the time of the snapshot. </p>"}, "AvailabilityZone": {"shape": "String", "documentation": "<p> Specifies the name of the Availability Zone the DB instance was located in at the time of the DB snapshot. </p>"}, "VpcId": {"shape": "String", "documentation": "<p> Provides the Vpc Id associated with the DB snapshot. </p>"}, "InstanceCreateTime": {"shape": "TStamp", "documentation": "<p> Specifies the time (UTC) when the snapshot was taken. </p>"}, "MasterUsername": {"shape": "String", "documentation": "<p> Provides the master username for the DB snapshot. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> Specifies the version of the database engine. </p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> License model information for the restored DB instance. </p>"}, "SnapshotType": {"shape": "String", "documentation": "<p> Provides the type of the DB snapshot. </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> Specifies the Provisioned IOPS (I/O operations per second) value of the DB instance at the time of the snapshot. </p>"}, "OptionGroupName": {"shape": "String", "documentation": "<p> Provides the option group name for the DB snapshot. </p>"}, "PercentProgress": {"shape": "Integer", "documentation": "<p> The percentage of the estimated data that has been transferred. </p>"}, "SourceRegion": {"shape": "String", "documentation": "<p> The region that the DB snapshot was created in or copied from. </p>"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type associated with DB Snapshot. </p>"}, "TdeCredentialArn": {"shape": "String", "documentation": "<p> The ARN from the Key Store with which to associate the instance for TDE encryption. </p>"}}, "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSnapshot</a></li> <li><a>DeleteDBSnapshot</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSnapshots</a> action.</p>"}, "DBSnapshotAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "DBSnapshotAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> is already used by an existing snapshot. </p>"}, "DBSnapshotList": {"type": "list", "member": {"shape": "DBSnapshot", "locationName": "DBSnapshot"}}, "DBSnapshotMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "DBSnapshots": {"shape": "DBSnapshotList", "documentation": "<p> A list of <a>DBSnapshot</a> instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBSnapshots</a> action. </p>"}, "DBSnapshotNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "DBSnapshotNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSnapshotIdentifier</i> does not refer to an existing DB snapshot. </p>"}, "DBSubnetGroup": {"type": "structure", "members": {"DBSubnetGroupName": {"shape": "String", "documentation": "<p> Specifies the name of the DB subnet group. </p>"}, "DBSubnetGroupDescription": {"shape": "String", "documentation": "<p> Provides the description of the DB subnet group. </p>"}, "VpcId": {"shape": "String", "documentation": "<p> Provides the VpcId of the DB subnet group. </p>"}, "SubnetGroupStatus": {"shape": "String", "documentation": "<p> Provides the status of the DB subnet group. </p>"}, "Subnets": {"shape": "SubnetList", "documentation": "<p> Contains a list of <a>Subnet</a> elements. </p>"}}, "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the following actions: </p> <ul> <li><a>CreateDBSubnetGroup</a></li> <li><a>ModifyDBSubnetGroup</a></li> <li><a>DescribeDBSubnetGroups</a></li> <li><a>DeleteDBSubnetGroup</a></li> </ul> <p>This data type is used as a response element in the <a>DescribeDBSubnetGroups</a> action.</p>"}, "DBSubnetGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "DBSubnetGroupAlreadyExists", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> is already used by an existing DB subnet group. </p>"}, "DBSubnetGroupDoesNotCoverEnoughAZs": {"type": "structure", "members": {}, "error": {"code": "DBSubnetGroupDoesNotCoverEnoughAZs", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Subnets in the DB subnet group should cover at least two Availability Zones unless there is only one Availability Zone. </p>"}, "DBSubnetGroupMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "DBSubnetGroups": {"shape": "DBSubnetGroups", "documentation": "<p> A list of <a>DBSubnetGroup</a> instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeDBSubnetGroups</a> action. </p>"}, "DBSubnetGroupNotAllowedFault": {"type": "structure", "members": {}, "error": {"code": "DBSubnetGroupNotAllowedFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Indicates that the DBSubnetGroup should not be specified while creating read replicas that lie in the same region as the source instance. </p>"}, "DBSubnetGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "DBSubnetGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> <i>DBSubnetGroupName</i> does not refer to an existing DB subnet group. </p>"}, "DBSubnetGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "DBSubnetGroupQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB subnet groups. </p>"}, "DBSubnetGroups": {"type": "list", "member": {"shape": "DBSubnetGroup", "locationName": "DBSubnetGroup"}}, "DBSubnetQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "DBSubnetQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of subnets in a DB subnet groups. </p>"}, "DBUpgradeDependencyFailureFault": {"type": "structure", "members": {}, "error": {"code": "DBUpgradeDependencyFailure", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB upgrade failed because a resource the DB depends on could not be modified. </p>"}, "DeleteDBInstanceMessage": {"type": "structure", "required": ["DBInstanceIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier for the DB instance to be deleted. This parameter isn't case sensitive. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "SkipFinalSnapshot": {"shape": "Boolean", "documentation": "<p> Determines whether a final DB snapshot is created before the DB instance is deleted. If <code>true</code> is specified, no DBSnapshot is created. If <code>false</code> is specified, a DB snapshot is created before the DB instance is deleted. </p> <p>Specify <code>true</code> when deleting a Read Replica.</p> <note>The FinalDBSnapshotIdentifier parameter must be specified if SkipFinalSnapshot is <code>false</code>.</note> <p>Default: <code>false</code></p>"}, "FinalDBSnapshotIdentifier": {"shape": "String", "documentation": "<p> The DBSnapshotIdentifier of the new DBSnapshot created when SkipFinalSnapshot is set to <code>false</code>. </p> <note> Specifying this parameter and also setting the SkipFinalShapshot parameter to true results in an error. </note> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> <li>Cannot be specified when deleting a Read Replica.</li> </ul>"}}, "documentation": "<p> </p>"}, "DeleteDBParameterGroupMessage": {"type": "structure", "required": ["DBParameterGroupName"], "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group. </p> <p>Constraints:</p> <ul> <li>Must be the name of an existing DB parameter group</li> <li>You cannot delete a default DB parameter group</li> <li>Cannot be associated with any DB instances</li> </ul>"}}, "documentation": "<p> </p>"}, "DeleteDBSecurityGroupMessage": {"type": "structure", "required": ["DBSecurityGroupName"], "members": {"DBSecurityGroupName": {"shape": "String", "documentation": "<p> The name of the DB security group to delete. </p> <note>You cannot delete the default DB security group.</note> <p> Constraints: </p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> <li>Must not be \"Default\"</li> <li>May not contain spaces</li> </ul>"}}, "documentation": "<p> </p>"}, "DeleteDBSnapshotMessage": {"type": "structure", "required": ["DBSnapshotIdentifier"], "members": {"DBSnapshotIdentifier": {"shape": "String", "documentation": "<p> The DBSnapshot identifier. </p> <p>Constraints: Must be the name of an existing DB snapshot in the <code>available</code> state.</p>"}}, "documentation": "<p> </p>"}, "DeleteDBSubnetGroupMessage": {"type": "structure", "required": ["DBSubnetGroupName"], "members": {"DBSubnetGroupName": {"shape": "String", "documentation": "<p> The name of the database subnet group to delete. </p> <note>You cannot delete the default subnet group.</note> <p> Constraints: </p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}}, "documentation": "<p> </p>"}, "DeleteEventSubscriptionMessage": {"type": "structure", "required": ["SubscriptionName"], "members": {"SubscriptionName": {"shape": "String", "documentation": "<p>The name of the RDS event notification subscription you want to delete.</p>"}}, "documentation": "<p></p>"}, "DeleteOptionGroupMessage": {"type": "structure", "required": ["OptionGroupName"], "members": {"OptionGroupName": {"shape": "String", "documentation": "<p> The name of the option group to be deleted. </p> <note>You cannot delete default option groups.</note>"}}, "documentation": "<p> </p>"}, "DescribeDBEngineVersionsMessage": {"type": "structure", "members": {"Engine": {"shape": "String", "documentation": "<p> The database engine to return. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> The database engine version to return. </p> <p>Example: <code>5.1.49</code></p>"}, "DBParameterGroupFamily": {"shape": "String", "documentation": "<p> The name of a specific DB parameter group family to return details for. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "Filters": {"shape": "FilterList", "documentation": "<p> Not currently supported. </p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more than the <code>MaxRecords</code> value is available, a pagination token called a marker is included in the response so that the following results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "DefaultOnly": {"shape": "Boolean", "documentation": "<p> Indicates that only the default version of the specified engine or engine and major version combination is returned. </p>"}, "ListSupportedCharacterSets": {"shape": "BooleanOptional", "documentation": "<p> If this parameter is specified, and if the requested engine supports the CharacterSetName parameter for CreateDBInstance, the response includes a list of supported character sets for each engine version. </p>"}}}, "DescribeDBInstancesMessage": {"type": "structure", "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The user-supplied instance identifier. If this parameter is specified, information from only the specific DB instance is returned. This parameter isn't case sensitive. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeDBInstances request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}}, "documentation": "<p> </p>"}, "DescribeDBLogFilesDetails": {"type": "structure", "members": {"LogFileName": {"shape": "String", "documentation": "<p> The name of the log file for the specified DB instance. </p>"}, "LastWritten": {"shape": "<PERSON>", "documentation": "<p> A POSIX timestamp when the last log entry was written. </p>"}, "Size": {"shape": "<PERSON>", "documentation": "<p> The size, in bytes, of the log file for the specified DB instance. </p>"}}, "documentation": "<p>This data type is used as a response element to <a>DescribeDBLogFiles</a>.</p>"}, "DescribeDBLogFilesList": {"type": "list", "member": {"shape": "DescribeDBLogFilesDetails", "locationName": "DescribeDBLogFilesDetails"}}, "DescribeDBLogFilesMessage": {"type": "structure", "required": ["DBInstanceIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The customer-assigned name of the DB instance that contains the log files you want to list. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "FilenameContains": {"shape": "String", "documentation": "<p> Filters the available log files for log file names that contain the specified string. </p>"}, "FileLastWritten": {"shape": "<PERSON>", "documentation": "<p> Filters the available log files for files written since the specified date, in POSIX timestamp format. </p>"}, "FileSize": {"shape": "<PERSON>", "documentation": "<p> Filters the available log files for files larger than the specified size. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token called a marker is included in the response so that the remaining results can be retrieved. </p>"}, "Marker": {"shape": "String", "documentation": "<p> The pagination token provided in the previous request. If this parameter is specified the response includes only records beyond the marker, up to MaxRecords. </p>"}}, "documentation": "<p> </p>"}, "DescribeDBLogFilesResponse": {"type": "structure", "members": {"DescribeDBLogFiles": {"shape": "DescribeDBLogFilesList", "documentation": "<p> The DB log files returned. </p>"}, "Marker": {"shape": "String", "documentation": "<p> A pagination token that can be used in a subsequent DescribeDBLogFiles request. </p>"}}, "documentation": "<p> The response from a call to <a>DescribeDBLogFiles</a>. </p>"}, "DescribeDBParameterGroupsMessage": {"type": "structure", "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of a specific DB parameter group to return details for. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous <code>DescribeDBParameterGroups</code> request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeDBParametersMessage": {"type": "structure", "required": ["DBParameterGroupName"], "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of a specific DB parameter group to return details for. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "Source": {"shape": "String", "documentation": "<p> The parameter types to return. </p> <p>Default: All parameter types returned</p> <p>Valid Values: <code>user | system | engine-default</code></p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous <code>DescribeDBParameters</code> request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}}, "DescribeDBSecurityGroupsMessage": {"type": "structure", "members": {"DBSecurityGroupName": {"shape": "String", "documentation": "<p> The name of the DB security group to return details for. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeDBSecurityGroups request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeDBSnapshotsMessage": {"type": "structure", "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> A DB instance identifier to retrieve the list of DB snapshots for. Cannot be used in conjunction with <code>DBSnapshotIdentifier</code>. This parameter is not case sensitive. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "DBSnapshotIdentifier": {"shape": "String", "documentation": "<p> A specific DB snapshot identifier to describe. Cannot be used in conjunction with <code>DBInstanceIdentifier</code>. This value is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> <li>If this is the identifier of an automated snapshot, the <code>SnapshotType</code> parameter must also be specified.</li> </ul>"}, "SnapshotType": {"shape": "String", "documentation": "<p> The type of snapshots that will be returned. Values can be \"automated\" or \"manual.\" If not specified, the returned results will include all snapshots types. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous <code>DescribeDBSnapshots</code> request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeDBSubnetGroupsMessage": {"type": "structure", "members": {"DBSubnetGroupName": {"shape": "String", "documentation": "<p> The name of the DB subnet group to return details for. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeDBSubnetGroups request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeEngineDefaultParametersMessage": {"type": "structure", "required": ["DBParameterGroupFamily"], "members": {"DBParameterGroupFamily": {"shape": "String", "documentation": "<p> The name of the DB parameter group family. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p> Not currently supported. </p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous <code>DescribeEngineDefaultParameters</code> request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeEventCategoriesMessage": {"type": "structure", "members": {"SourceType": {"shape": "String", "documentation": "<p> The type of source that will be generating the events. </p> <p>Valid values: db-instance | db-parameter-group | db-security-group | db-snapshot</p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}}, "documentation": "<p></p>"}, "DescribeEventSubscriptionsMessage": {"type": "structure", "members": {"SubscriptionName": {"shape": "String", "documentation": "<p>The name of the RDS event notification subscription you want to describe.</p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeOrderableDBInstanceOptions request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}}, "documentation": "<p></p>"}, "DescribeEventsMessage": {"type": "structure", "members": {"SourceIdentifier": {"shape": "String", "documentation": "<p> The identifier of the event source for which events will be returned. If not specified, then all sources are included in the response. </p> <p>Constraints:</p> <ul> <li>If SourceIdentifier is supplied, SourceType must also be provided.</li> <li>If the source type is <code>DBInstance</code>, then a <code>DBInstanceIdentifier</code> must be supplied.</li> <li>If the source type is <code>DBSecurityGroup</code>, a <code>DBSecurityGroupName</code> must be supplied.</li> <li>If the source type is <code>DBParameterGroup</code>, a <code>DBParameterGroupName</code> must be supplied.</li> <li>If the source type is <code>DBSnapshot</code>, a <code>DBSnapshotIdentifier</code> must be supplied.</li> <li>Cannot end with a hyphen or contain two consecutive hyphens.</li> </ul>"}, "SourceType": {"shape": "SourceType", "documentation": "<p> The event source to retrieve events for. If no value is specified, all events are returned. </p>"}, "StartTime": {"shape": "TStamp", "documentation": "<p> The beginning of the time interval to retrieve events for, specified in ISO 8601 format. For more information about ISO 8601, go to the <a href=\"http://en.wikipedia.org/wiki/ISO_8601\">ISO8601 Wikipedia page.</a> </p> <p>Example: 2009-07-08T18:00Z</p>"}, "EndTime": {"shape": "TStamp", "documentation": "<p> The end of the time interval for which to retrieve events, specified in ISO 8601 format. For more information about ISO 8601, go to the <a href=\"http://en.wikipedia.org/wiki/ISO_8601\">ISO8601 Wikipedia page.</a> </p> <p>Example: 2009-07-08T18:00Z</p>"}, "Duration": {"shape": "IntegerOptional", "documentation": "<p> The number of minutes to retrieve events for. </p> <p>Default: 60</p>"}, "EventCategories": {"shape": "EventCategoriesList", "documentation": "<p> A list of event categories that trigger notifications for a event notification subscription. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results may be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeEvents request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeOptionGroupOptionsMessage": {"type": "structure", "required": ["EngineName"], "members": {"EngineName": {"shape": "String", "documentation": "<p> A required parameter. Options available for the given Engine name will be described. </p>"}, "MajorEngineVersion": {"shape": "String", "documentation": "<p> If specified, filters the results to include only options for the specified major engine version. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>.</p>"}}, "documentation": "<p> </p>"}, "DescribeOptionGroupsMessage": {"type": "structure", "members": {"OptionGroupName": {"shape": "String", "documentation": "<p> The name of the option group to describe. Cannot be supplied together with EngineName or MajorEngineVersion. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeOptionGroups request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "EngineName": {"shape": "String", "documentation": "<p> Filters the list of option groups to only include groups associated with a specific database engine. </p>"}, "MajorEngineVersion": {"shape": "String", "documentation": "<p> Filters the list of option groups to only include groups associated with a specific database engine version. If specified, then EngineName must also be specified. </p>"}}, "documentation": "<p> </p>"}, "DescribeOrderableDBInstanceOptionsMessage": {"type": "structure", "required": ["Engine"], "members": {"Engine": {"shape": "String", "documentation": "<p> The name of the engine to retrieve DB instance options for. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> The engine version filter value. Specify this parameter to show only the available offerings matching the specified engine version. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The DB instance class filter value. Specify this parameter to show only the available offerings matching the specified DB instance class. </p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> The license model filter value. Specify this parameter to show only the available offerings matching the specified license model. </p>"}, "Vpc": {"shape": "BooleanOptional", "documentation": "<p> The VPC filter value. Specify this parameter to show only the available VPC or non-VPC offerings. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more records exist than the specified <code>MaxRecords</code> value, a pagination token called a marker is included in the response so that the remaining results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeOrderableDBInstanceOptions request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}}, "documentation": "<p> </p>"}, "DescribeReservedDBInstancesMessage": {"type": "structure", "members": {"ReservedDBInstanceId": {"shape": "String", "documentation": "<p> The reserved DB instance identifier filter value. Specify this parameter to show only the reservation that matches the specified reservation ID. </p>"}, "ReservedDBInstancesOfferingId": {"shape": "String", "documentation": "<p> The offering identifier filter value. Specify this parameter to show only purchased reservations matching the specified offering identifier. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The DB instance class filter value. Specify this parameter to show only those reservations matching the specified DB instances class. </p>"}, "Duration": {"shape": "String", "documentation": "<p> The duration filter value, specified in years or seconds. Specify this parameter to show only reservations for this duration. </p> <p>Valid Values: <code>1 | 3 | 31536000 | 94608000</code></p>"}, "ProductDescription": {"shape": "String", "documentation": "<p> The product description filter value. Specify this parameter to show only those reservations matching the specified product description. </p>"}, "OfferingType": {"shape": "String", "documentation": "<p> The offering type filter value. Specify this parameter to show only the available offerings matching the specified offering type. </p> <p>Valid Values: <code>\"Light Utilization\" | \"Medium Utilization\" | \"Heavy Utilization\" </code></p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> The Multi-AZ filter value. Specify this parameter to show only those reservations matching the specified Multi-AZ parameter. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more than the <code>MaxRecords</code> value is available, a pagination token called a marker is included in the response so that the following results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "DescribeReservedDBInstancesOfferingsMessage": {"type": "structure", "members": {"ReservedDBInstancesOfferingId": {"shape": "String", "documentation": "<p> The offering identifier filter value. Specify this parameter to show only the available offering that matches the specified reservation identifier. </p> <p>Example: <code>438012d3-4052-4cc7-b2e3-8d3372e0e706</code></p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The DB instance class filter value. Specify this parameter to show only the available offerings matching the specified DB instance class. </p>"}, "Duration": {"shape": "String", "documentation": "<p> Duration filter value, specified in years or seconds. Specify this parameter to show only reservations for this duration. </p> <p>Valid Values: <code>1 | 3 | 31536000 | 94608000</code></p>"}, "ProductDescription": {"shape": "String", "documentation": "<p> Product description filter value. Specify this parameter to show only the available offerings matching the specified product description. </p>"}, "OfferingType": {"shape": "String", "documentation": "<p> The offering type filter value. Specify this parameter to show only the available offerings matching the specified offering type. </p> <p>Valid Values: <code>\"Light Utilization\" | \"Medium Utilization\" | \"Heavy Utilization\" </code></p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> The Multi-AZ filter value. Specify this parameter to show only the available offerings matching the specified Multi-AZ parameter. </p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}, "MaxRecords": {"shape": "IntegerOptional", "documentation": "<p> The maximum number of records to include in the response. If more than the <code>MaxRecords</code> value is available, a pagination token called a marker is included in the response so that the following results can be retrieved. </p> <p>Default: 100</p> <p>Constraints: minimum 20, maximum 100</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> </p>"}, "Double": {"type": "double"}, "DownloadDBLogFilePortionDetails": {"type": "structure", "members": {"LogFileData": {"shape": "String", "documentation": "<p> Entries from the specified log file. </p>"}, "Marker": {"shape": "String", "documentation": "<p> A pagination token that can be used in a subsequent DownloadDBLogFilePortion request. </p>"}, "AdditionalDataPending": {"shape": "Boolean", "documentation": "<p> Boolean value that if true, indicates there is more data to be downloaded. </p>"}}, "documentation": "<p>This data type is used as a response element to <a>DownloadDBLogFilePortion</a>.</p>"}, "DownloadDBLogFilePortionMessage": {"type": "structure", "required": ["DBInstanceIdentifier", "LogFileName"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The customer-assigned name of the DB instance that contains the log files you want to list. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "LogFileName": {"shape": "String", "documentation": "<p> The name of the log file to be downloaded. </p>"}, "Marker": {"shape": "String", "documentation": "<p> The pagination token provided in the previous request or \"0\". If the Marker parameter is specified the response includes only records beyond the marker until the end of the file or up to NumberOfLines. </p>"}, "NumberOfLines": {"shape": "Integer", "documentation": "<p> The number of lines to download. </p> <p>If the NumberOfLines parameter is specified, then the block of lines returned can be from the beginning or the end of the log file, depending on the value of the Marker parameter. <ul> <li><p>If neither Marker or NumberOfLines are specified, the entire log file is returned.</p></li> <li><p>If NumberOfLines is specified and Mark<PERSON> is not specified, then the most recent lines from the end of the log file are returned.</p></li> <li><p>If Marker is specified as \"0\", then the specified number of lines from the beginning of the log file are returned.</p></li> <li><p>You can download the log file in blocks of lines by specifying the size of the block using the NumberOfLines parameter, and by specifying a value of \"0\" for the Marker parameter in your first request. Include the Marker value returned in the response as the Marker value for the next request, continuing until the AdditionalDataPending response element returns false.</p></li> </ul> </p>"}}, "documentation": "<p> </p>"}, "EC2SecurityGroup": {"type": "structure", "members": {"Status": {"shape": "String", "documentation": "<p> Provides the status of the EC2 security group. Status can be \"authorizing\", \"authorized\", \"revoking\", and \"revoked\". </p>"}, "EC2SecurityGroupName": {"shape": "String", "documentation": "<p> Specifies the name of the EC2 security group. </p>"}, "EC2SecurityGroupId": {"shape": "String", "documentation": "<p> Specifies the id of the EC2 security group. </p>"}, "EC2SecurityGroupOwnerId": {"shape": "String", "documentation": "<p> Specifies the AWS ID of the owner of the EC2 security group specified in the <code>EC2SecurityGroupName</code> field. </p>"}}, "documentation": "<p> This data type is used as a response element in the following actions: </p> <ul> <li><a>AuthorizeDBSecurityGroupIngress</a></li> <li><a>DescribeDBSecurityGroups</a></li> <li><a>RevokeDBSecurityGroupIngress</a></li> </ul>"}, "EC2SecurityGroupList": {"type": "list", "member": {"shape": "EC2SecurityGroup", "locationName": "EC2SecurityGroup"}}, "Endpoint": {"type": "structure", "members": {"Address": {"shape": "String", "documentation": "<p> Specifies the DNS address of the DB instance. </p>"}, "Port": {"shape": "Integer", "documentation": "<p> Specifies the port that the database engine is listening on. </p>"}}, "documentation": "<p> This data type is used as a response element in the following actions: </p> <ul> <li><a>CreateDBInstance</a></li> <li><a>DescribeDBInstances</a></li> <li><a>DeleteDBInstance</a></li> </ul>"}, "EngineDefaults": {"type": "structure", "members": {"DBParameterGroupFamily": {"shape": "String", "documentation": "<p> Specifies the name of the DB parameter group family which the engine default parameters apply to. </p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous EngineDefaults request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}, "Parameters": {"shape": "ParametersList", "documentation": "<p> Contains a list of engine default parameters. </p>"}}, "wrapper": true, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeEngineDefaultParameters</a> action. </p>"}, "Event": {"type": "structure", "members": {"SourceIdentifier": {"shape": "String", "documentation": "<p> Provides the identifier for the source of the event. </p>"}, "SourceType": {"shape": "SourceType", "documentation": "<p> Specifies the source type for this event. </p>"}, "Message": {"shape": "String", "documentation": "<p> Provides the text of this event. </p>"}, "EventCategories": {"shape": "EventCategoriesList", "documentation": "<p> Specifies the category for the event. </p>"}, "Date": {"shape": "TStamp", "documentation": "<p> Specifies the date and time of the event. </p>"}}, "documentation": "<p> This data type is used as a response element in the <a>DescribeEvents</a> action. </p>"}, "EventCategoriesList": {"type": "list", "member": {"shape": "String", "locationName": "EventCategory"}}, "EventCategoriesMap": {"type": "structure", "members": {"SourceType": {"shape": "String", "documentation": "<p>The source type that the returned categories belong to</p>"}, "EventCategories": {"shape": "EventCategoriesList", "documentation": "<p>The event categories for the specified source type</p>"}}, "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventCategories</a> action.</p>"}, "EventCategoriesMapList": {"type": "list", "member": {"shape": "EventCategoriesMap", "locationName": "EventCategoriesMap"}}, "EventCategoriesMessage": {"type": "structure", "members": {"EventCategoriesMapList": {"shape": "EventCategoriesMapList", "documentation": "<p>A list of EventCategoriesMap data types.</p>"}}, "documentation": "<p>Data returned from the <b>DescribeEventCategories</b> action.</p>"}, "EventList": {"type": "list", "member": {"shape": "Event", "locationName": "Event"}}, "EventSubscription": {"type": "structure", "members": {"CustomerAwsId": {"shape": "String", "documentation": "<p>The AWS customer account associated with the RDS event notification subscription.</p>"}, "CustSubscriptionId": {"shape": "String", "documentation": "<p>The RDS event notification subscription Id.</p>"}, "SnsTopicArn": {"shape": "String", "documentation": "<p>The topic ARN of the RDS event notification subscription.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the RDS event notification subscription.</p> <p>Constraints:</p> <p>Can be one of the following: creating | modifying | deleting | active | no-permission | topic-not-exist</p> <p>The status \"no-permission\" indicates that RDS no longer has permission to post to the SNS topic. The status \"topic-not-exist\" indicates that the topic was deleted after the subscription was created.</p>"}, "SubscriptionCreationTime": {"shape": "String", "documentation": "<p>The time the RDS event notification subscription was created.</p>"}, "SourceType": {"shape": "String", "documentation": "<p>The source type for the RDS event notification subscription.</p>"}, "SourceIdsList": {"shape": "SourceIdsList", "documentation": "<p>A list of source IDs for the RDS event notification subscription.</p>"}, "EventCategoriesList": {"shape": "EventCategoriesList", "documentation": "<p>A list of event categories for the RDS event notification subscription.</p>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>A Boolean value indicating if the subscription is enabled. True indicates the subscription is enabled.</p>"}}, "wrapper": true, "documentation": "<p>Contains the results of a successful invocation of the <a>DescribeEventSubscriptions</a> action.</p>"}, "EventSubscriptionQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "EventSubscriptionQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You have reached the maximum number of event subscriptions.</p>"}, "EventSubscriptionsList": {"type": "list", "member": {"shape": "EventSubscription", "locationName": "EventSubscription"}}, "EventSubscriptionsMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous DescribeOrderableDBInstanceOptions request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "EventSubscriptionsList": {"shape": "EventSubscriptionsList", "documentation": "<p>A list of EventSubscriptions data types.</p>"}}, "documentation": "<p>Data returned by the <b>DescribeEventSubscriptions</b> action.</p>"}, "EventsMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous Events request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}, "Events": {"shape": "EventList", "documentation": "<p> A list of <a>Event</a> instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeEvents</a> action. </p>"}, "Filter": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {"shape": "String", "documentation": "<p>This parameter is not currently supported.</p>"}, "Values": {"shape": "FilterValueList", "documentation": "<p>This parameter is not currently supported.</p>"}}}, "FilterList": {"type": "list", "member": {"shape": "Filter", "locationName": "Filter"}}, "FilterValueList": {"type": "list", "member": {"shape": "String", "locationName": "Value"}}, "IPRange": {"type": "structure", "members": {"Status": {"shape": "String", "documentation": "<p> Specifies the status of the IP range. Status can be \"authorizing\", \"authorized\", \"revoking\", and \"revoked\". </p>"}, "CIDRIP": {"shape": "String", "documentation": "<p> Specifies the IP range. </p>"}}, "documentation": "<p> This data type is used as a response element in the <a>DescribeDBSecurityGroups</a> action. </p>"}, "IPRangeList": {"type": "list", "member": {"shape": "IPRange", "locationName": "IPRange"}}, "InstanceQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "InstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB instances. </p>"}, "InsufficientDBInstanceCapacityFault": {"type": "structure", "members": {}, "error": {"code": "InsufficientDBInstanceCapacity", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Specified DB instance class is not available in the specified Availability Zone. </p>"}, "Integer": {"type": "integer"}, "IntegerOptional": {"type": "integer"}, "InvalidDBInstanceStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBInstanceState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The specified DB instance is not in the <i>available</i> state. </p>"}, "InvalidDBParameterGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBParameterGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB parameter group cannot be deleted because it is in use. </p>"}, "InvalidDBSecurityGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBSecurityGroupState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB security group does not allow deletion. </p>"}, "InvalidDBSnapshotStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBSnapshotState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The state of the DB snapshot does not allow deletion. </p>"}, "InvalidDBSubnetGroupFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBSubnetGroupFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Indicates the DBSubnetGroup does not belong to the same VPC as that of an existing cross region read replica of the same source instance. </p>"}, "InvalidDBSubnetGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBSubnetGroupStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB subnet group cannot be deleted because it is in use. </p>"}, "InvalidDBSubnetStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidDBSubnetStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB subnet is not in the <i>available</i> state. </p>"}, "InvalidEventSubscriptionStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidEventSubscriptionState", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>This error can occur if someone else is modifying a subscription. You should retry the action.</p>"}, "InvalidOptionGroupStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidOptionGroupStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The option group is not in the <i>available</i> state. </p>"}, "InvalidRestoreFault": {"type": "structure", "members": {}, "error": {"code": "InvalidRestoreFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Cannot restore from vpc backup to non-vpc DB instance. </p>"}, "InvalidSubnet": {"type": "structure", "members": {}, "error": {"code": "InvalidSubnet", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The requested subnet is invalid, or multiple subnets were requested that are not all in a common VPC. </p>"}, "InvalidVPCNetworkStateFault": {"type": "structure", "members": {}, "error": {"code": "InvalidVPCNetworkStateFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> DB subnet group does not cover all Availability Zones after it is created because users' change. </p>"}, "KeyList": {"type": "list", "member": {"shape": "String"}}, "ListTagsForResourceMessage": {"type": "structure", "required": ["ResourceName"], "members": {"ResourceName": {"shape": "String", "documentation": "<p>The Amazon RDS resource with tags to be listed. This value is an Amazon Resource Name (ARN). For information about creating an ARN, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.html#USER_Tagging.ARN\"> Constructing an RDS Amazon Resource Name (ARN)</a>.</p>"}, "Filters": {"shape": "FilterList", "documentation": "<p>This parameter is not currently supported.</p>"}}, "documentation": "<p></p>"}, "Long": {"type": "long"}, "ModifyDBInstanceMessage": {"type": "structure", "required": ["DBInstanceIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier. This value is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must be the identifier for an existing DB instance</li> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "AllocatedStorage": {"shape": "IntegerOptional", "documentation": "<p> The new storage capacity of the RDS instance. Changing this setting does not result in an outage and the change is applied during the next maintenance window unless <code>ApplyImmediately</code> is set to <code>true</code> for this request. </p> <p><b>MySQL</b></p> <p>Default: Uses existing setting</p> <p>Valid Values: 5-3072</p> <p>Constraints: Value supplied must be at least 10% greater than the current value. Values that are not at least 10% greater than the existing value are rounded up so that they are 10% greater than the current value.</p> <p>Type: Integer</p> <p><b>PostgreSQL</b></p> <p>Default: Uses existing setting</p> <p>Valid Values: 5-3072</p> <p>Constraints: Value supplied must be at least 10% greater than the current value. Values that are not at least 10% greater than the existing value are rounded up so that they are 10% greater than the current value.</p> <p>Type: Integer</p> <p><b>Oracle</b></p> <p>Default: Uses existing setting</p> <p>Valid Values: 10-3072</p> <p>Constraints: Value supplied must be at least 10% greater than the current value. Values that are not at least 10% greater than the existing value are rounded up so that they are 10% greater than the current value.</p> <p><b>SQL Server</b></p> <p>Cannot be modified.</p> <p> If you choose to migrate your DB instance from using standard storage to using Provisioned IOPS, or from using Provisioned IOPS to using standard storage, the process can take time. The duration of the migration depends on several factors such as database load, storage size, storage type (standard or Provisioned IOPS), amount of IOPS provisioned (if any), and the number of prior scale storage operations. Typical migration times are under 24 hours, but the process can take up to several days in some cases. During the migration, the DB instance will be available for use, but may experience performance degradation. While the migration takes place, nightly backups for the instance will be suspended. No other Amazon RDS operations can take place for the instance, including modifying the instance, rebooting the instance, deleting the instance, creating a read replica for the instance, and creating a DB snapshot of the instance. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The new compute and memory capacity of the DB instance. To determine the instance classes that are available for a particular DB engine, use the <a>DescribeOrderableDBInstanceOptions</a> action. </p> <p> Passing a value for this setting causes an outage during the change and is applied during the next maintenance window, unless <code>ApplyImmediately</code> is specified as <code>true</code> for this request. </p> <p>Default: Uses existing setting</p> <p>Valid Values: <code>db.t1.micro | db.m1.small | db.m1.medium | db.m1.large | db.m1.xlarge | db.m2.xlarge | db.m2.2xlarge | db.m2.4xlarge | db.m3.medium | db.m3.large | db.m3.xlarge | db.m3.2xlarge | db.r3.large | db.r3.xlarge | db.r3.2xlarge | db.r3.4xlarge | db.r3.8xlarge | db.t2.micro | db.t2.small | db.t2.medium</code></p>"}, "DBSecurityGroups": {"shape": "DBSecurityGroupNameList", "documentation": "<p> A list of DB security groups to authorize on this DB instance. Changing this setting does not result in an outage and the change is asynchronously applied as soon as possible. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "VpcSecurityGroupIds": {"shape": "VpcSecurityGroupIdList", "documentation": "<p> A list of EC2 VPC security groups to authorize on this DB instance. This change is asynchronously applied as soon as possible. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "ApplyImmediately": {"shape": "Boolean", "documentation": "<p>Specifies whether the modifications in this request and any pending modifications are asynchronously applied as soon as possible, regardless of the <code>PreferredMaintenanceWindow</code> setting for the DB instance. </p> <p> If this parameter is set to <code>false</code>, changes to the DB instance are applied during the next maintenance window. Some parameter changes can cause an outage and will be applied on the next call to <a>RebootDBInstance</a>, or the next failure reboot. Review the table of parameters in <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.DBInstance.html#Overview.DBInstance.Modifying\">Modifying a DB Instance and Using the Apply Immediately Parameter</a> to see the impact that setting <code>ApplyImmediately</code> to <code>true</code> or <code>false</code> has for each modified parameter and to determine when the changes will be applied. </p> <p>Default: <code>false</code></p>"}, "MasterUserPassword": {"shape": "String", "documentation": "<p> The new password for the DB instance master user. Can be any printable ASCII character except \"/\", \"\"\", or \"@\".</p> <p> Changing this parameter does not result in an outage and the change is asynchronously applied as soon as possible. Between the time of the request and the completion of the request, the <code>MasterUserPassword</code> element exists in the <code>PendingModifiedValues</code> element of the operation response. </p> <p>Default: Uses existing setting</p> <p>Constraints: Must be 8 to 41 alphanumeric characters (MySQL), 8 to 30 alphanumeric characters (Oracle), or 8 to 128 alphanumeric characters (SQL Server).</p> <note> Amazon RDS API actions never return the password, so this action provides a way to regain access to a master instance user if the password is lost. This includes restoring privileges that may have been accidentally revoked. </note>"}, "DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group to apply to the DB instance. Changing this setting does not result in an outage. The parameter group name itself is changed immediately, but the actual parameter changes are not applied until you reboot the instance without failover. The db instance will NOT be rebooted automatically and the parameter changes will NOT be applied during the next maintenance window. </p> <p>Default: Uses existing setting</p> <p>Constraints: The DB parameter group must be in the same DB parameter group family as this DB instance.</p>"}, "BackupRetentionPeriod": {"shape": "IntegerOptional", "documentation": "<p> The number of days to retain automated backups. Setting this parameter to a positive number enables backups. Setting this parameter to 0 disables automated backups. </p> <p>Changing this parameter can result in an outage if you change from 0 to a non-zero value or from a non-zero value to 0. These changes are applied during the next maintenance window unless the <code>ApplyImmediately</code> parameter is set to <code>true</code> for this request. If you change the parameter from one non-zero value to another non-zero value, the change is asynchronously applied as soon as possible.</p> <p>Default: Uses existing setting</p> <p>Constraints:</p> <ul> <li>Must be a value from 0 to 35</li> <li>Can be specified for a MySQL Read Replica only if the source is running MySQL 5.6</li> <li>Can be specified for a PostgreSQL Read Replica only if the source is running PostgreSQL 9.3.5</li> <li>Cannot be set to 0 if the DB instance is a source to Read Replicas</li> </ul>"}, "PreferredBackupWindow": {"shape": "String", "documentation": "<p> The daily time range during which automated backups are created if automated backups are enabled, as determined by the <code>BackupRetentionPeriod</code>. Changing this parameter does not result in an outage and the change is asynchronously applied as soon as possible. </p> <p>Constraints:</p> <ul> <li>Must be in the format hh24:mi-hh24:mi</li> <li>Times should be Universal Time Coordinated (UTC)</li> <li>Must not conflict with the preferred maintenance window</li> <li>Must be at least 30 minutes</li> </ul>"}, "PreferredMaintenanceWindow": {"shape": "String", "documentation": "<p> The weekly time range (in UTC) during which system maintenance can occur, which may result in an outage. Changing this parameter does not result in an outage, except in the following situation, and the change is asynchronously applied as soon as possible. If there are pending actions that cause a reboot, and the maintenance window is changed to include the current time, then changing this parameter will cause a reboot of the DB instance. If moving this window to the current time, there must be at least 30 minutes between the current time and end of the window to ensure pending changes are applied. </p> <p>Default: Uses existing setting</p> <p>Format: ddd:hh24:mi-ddd:hh24:mi</p> <p>Valid Days: Mon | Tue | Wed | Thu | Fri | Sat | Sun</p> <p>Constraints: Must be at least 30 minutes</p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> Specifies if the DB instance is a Multi-AZ deployment. Changing this parameter does not result in an outage and the change is applied during the next maintenance window unless the <code>ApplyImmediately</code> parameter is set to <code>true</code> for this request. </p> <p>Constraints: Cannot be specified if the DB instance is a Read Replica.</p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> The version number of the database engine to upgrade to. Changing this parameter results in an outage and the change is applied during the next maintenance window unless the <code>ApplyImmediately</code> parameter is set to <code>true</code> for this request. </p> <p> For major version upgrades, if a non-default DB parameter group is currently in use, a new DB parameter group in the DB parameter group family for the new engine version must be specified. The new DB parameter group can be the default for that DB parameter group family. </p> <p>For a list of valid engine versions, see <a>CreateDBInstance</a>.</p>"}, "AllowMajorVersionUpgrade": {"shape": "Boolean", "documentation": "<p> Indicates that major version upgrades are allowed. Changing this parameter does not result in an outage and the change is asynchronously applied as soon as possible. </p> <p>Constraints: This parameter must be set to true when specifying a value for the EngineVersion parameter that is a different major version than the DB instance's current version.</p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p> Indicates that minor version upgrades will be applied automatically to the DB instance during the maintenance window. Changing this parameter does not result in an outage except in the following case and the change is asynchronously applied as soon as possible. An outage will result if this parameter is set to <code>true</code> during the maintenance window, and a newer minor version is available, and RDS has enabled auto patching for that engine version. </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> The new Provisioned IOPS (I/O operations per second) value for the RDS instance. Changing this setting does not result in an outage and the change is applied during the next maintenance window unless the <code>ApplyImmediately</code> parameter is set to <code>true</code> for this request. </p> <p>Default: Uses existing setting</p> <p>Constraints: Value supplied must be at least 10% greater than the current value. Values that are not at least 10% greater than the existing value are rounded up so that they are 10% greater than the current value. If you are migrating from Provisioned IOPS to standard storage, set this value to 0. The DB instance will require a reboot for the change in storage type to take effect.</p> <p><b>SQL Server</b></p> <p>Setting the IOPS value for the SQL Server database engine is not supported.</p> <p>Type: Integer</p> <p> If you choose to migrate your DB instance from using standard storage to using Provisioned IOPS, or from using Provisioned IOPS to using standard storage, the process can take time. The duration of the migration depends on several factors such as database load, storage size, storage type (standard or Provisioned IOPS), amount of IOPS provisioned (if any), and the number of prior scale storage operations. Typical migration times are under 24 hours, but the process can take up to several days in some cases. During the migration, the DB instance will be available for use, but may experience performance degradation. While the migration takes place, nightly backups for the instance will be suspended. No other Amazon RDS operations can take place for the instance, including modifying the instance, rebooting the instance, deleting the instance, creating a read replica for the instance, and creating a DB snapshot of the instance. </p>"}, "OptionGroupName": {"shape": "String", "documentation": "<p> Indicates that the DB instance should be associated with the specified option group. Changing this parameter does not result in an outage except in the following case and the change is applied during the next maintenance window unless the <code>ApplyImmediately</code> parameter is set to <code>true</code> for this request. If the parameter change results in an option group that enables OEM, this change can cause a brief (sub-second) period during which new connections are rejected but existing connections are not interrupted. </p> <p> Permanent options, such as the TDE option for Oracle Advanced Security TDE, cannot be removed from an option group, and that option group cannot be removed from a DB instance once it is associated with a DB instance </p>"}, "NewDBInstanceIdentifier": {"shape": "String", "documentation": "<p> The new DB instance identifier for the DB instance when renaming a DB instance. When you change the DB instance identifier, an instance reboot will occur immediately if you set <code>Apply Immediately</code> to true, or will occur during the next maintenance window if <code>Apply Immediately</code> to false. This value is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type to be associated with the DB Instance. </p> <p> Valid values: <code>standard | gp2 | io1</code> </p> <p> If you specify <code>io1</code>, you must also include a value for the <code>Iops</code> parameter. </p> <p> Default: <code>io1</code> if the <code>Iops</code> parameter is specified; otherwise <code>standard</code> </p>"}, "TdeCredentialArn": {"shape": "String", "documentation": "<p> The ARN from the Key Store with which to associate the instance for TDE encryption. </p>"}, "TdeCredentialPassword": {"shape": "String", "documentation": "<p> The password for the given ARN from the Key Store in order to access the device. </p>"}}, "documentation": "<p> </p>"}, "ModifyDBParameterGroupMessage": {"type": "structure", "required": ["DBParameterGroupName", "Parameters"], "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group. </p> <p>Constraints:</p> <ul> <li>Must be the name of an existing DB parameter group</li> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "Parameters": {"shape": "ParametersList", "documentation": "<p> An array of parameter names, values, and the apply method for the parameter update. At least one parameter name, value, and apply method must be supplied; subsequent arguments are optional. A maximum of 20 parameters may be modified in a single request. </p> <p>Valid Values (for the application method): <code>immediate | pending-reboot</code></p> <note>You can use the immediate value with dynamic parameters only. You can use the pending-reboot value for both dynamic and static parameters, and changes are applied when you reboot the DB instance without failover. </note>"}}, "documentation": "<p> </p>"}, "ModifyDBSubnetGroupMessage": {"type": "structure", "required": ["DBSubnetGroupName", "SubnetIds"], "members": {"DBSubnetGroupName": {"shape": "String", "documentation": "<p> The name for the DB subnet group. This value is stored as a lowercase string. </p> <p>Constraints: Must contain no more than 255 alphanumeric characters or hyphens. Must not be \"Default\".</p> <p>Example: <code>mySubnetgroup</code></p>"}, "DBSubnetGroupDescription": {"shape": "String", "documentation": "<p> The description for the DB subnet group. </p>"}, "SubnetIds": {"shape": "SubnetIdentifierList", "documentation": "<p> The EC2 subnet IDs for the DB subnet group. </p>"}}, "documentation": "<p> </p>"}, "ModifyEventSubscriptionMessage": {"type": "structure", "required": ["SubscriptionName"], "members": {"SubscriptionName": {"shape": "String", "documentation": "<p>The name of the RDS event notification subscription.</p>"}, "SnsTopicArn": {"shape": "String", "documentation": "<p> The Amazon Resource Name (ARN) of the SNS topic created for event notification. The ARN is created by Amazon SNS when you create a topic and subscribe to it. </p>"}, "SourceType": {"shape": "String", "documentation": "<p> The type of source that will be generating the events. For example, if you want to be notified of events generated by a DB instance, you would set this parameter to db-instance. if this value is not specified, all events are returned. </p> <p>Valid values: db-instance | db-parameter-group | db-security-group | db-snapshot</p>"}, "EventCategories": {"shape": "EventCategoriesList", "documentation": "<p> A list of event categories for a SourceType that you want to subscribe to. You can see a list of the categories for a given SourceType in the <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html\">Events</a> topic in the Amazon RDS User Guide or by using the <b>DescribeEventCategories</b> action. </p>"}, "Enabled": {"shape": "BooleanOptional", "documentation": "<p> A Boolean value; set to <b>true</b> to activate the subscription. </p>"}}, "documentation": "<p></p>"}, "ModifyOptionGroupMessage": {"type": "structure", "required": ["OptionGroupName"], "members": {"OptionGroupName": {"shape": "String", "documentation": "<p> The name of the option group to be modified. </p> <p> Permanent options, such as the TDE option for Oracle Advanced Security TDE, cannot be removed from an option group, and that option group cannot be removed from a DB instance once it is associated with a DB instance </p>"}, "OptionsToInclude": {"shape": "OptionConfigurationList", "documentation": "<p> Options in this list are added to the option group or, if already present, the specified configuration is used to update the existing configuration. </p>"}, "OptionsToRemove": {"shape": "OptionNamesList", "documentation": "<p> Options in this list are removed from the option group. </p>"}, "ApplyImmediately": {"shape": "Boolean", "documentation": "<p> Indicates whether the changes should be applied immediately, or during the next maintenance window for each instance associated with the option group. </p>"}}, "documentation": "<p> </p>"}, "Option": {"type": "structure", "members": {"OptionName": {"shape": "String", "documentation": "<p> The name of the option. </p>"}, "OptionDescription": {"shape": "String", "documentation": "<p> The description of the option. </p>"}, "Persistent": {"shape": "Boolean", "documentation": "<p> Indicate if this option is persistent. </p>"}, "Permanent": {"shape": "Boolean", "documentation": "<p>Indicate if this option is permanent.</p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> If required, the port configured for this option to use. </p>"}, "OptionSettings": {"shape": "OptionSettingConfigurationList", "documentation": "<p> The option settings for this option. </p>"}, "DBSecurityGroupMemberships": {"shape": "DBSecurityGroupMembershipList", "documentation": "<p> If the option requires access to a port, then this DB security group allows access to the port. </p>"}, "VpcSecurityGroupMemberships": {"shape": "VpcSecurityGroupMembershipList", "documentation": "<p> If the option requires access to a port, then this VPC security group allows access to the port. </p>"}}, "documentation": "<p> Option details. </p>"}, "OptionConfiguration": {"type": "structure", "required": ["OptionName"], "members": {"OptionName": {"shape": "String", "documentation": "<p> The configuration of options to include in a group. </p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> The optional port for the option. </p>"}, "DBSecurityGroupMemberships": {"shape": "DBSecurityGroupNameList", "documentation": "<p> A list of DBSecurityGroupMemebrship name strings used for this option. </p>"}, "VpcSecurityGroupMemberships": {"shape": "VpcSecurityGroupIdList", "documentation": "<p> A list of VpcSecurityGroupMemebrship name strings used for this option. </p>"}, "OptionSettings": {"shape": "OptionSettingsList", "documentation": "<p> The option settings to include in an option group. </p>"}}, "documentation": "<p> A list of all available options </p>"}, "OptionConfigurationList": {"type": "list", "member": {"shape": "OptionConfiguration", "locationName": "OptionConfiguration"}}, "OptionGroup": {"type": "structure", "members": {"OptionGroupName": {"shape": "String", "documentation": "<p> Specifies the name of the option group. </p>"}, "OptionGroupDescription": {"shape": "String", "documentation": "<p>Provides a description of the option group. </p>"}, "EngineName": {"shape": "String", "documentation": "<p> Engine name that this option group can be applied to. </p>"}, "MajorEngineVersion": {"shape": "String", "documentation": "<p> Indicates the major engine version associated with this option group. </p>"}, "Options": {"shape": "OptionsList", "documentation": "<p> Indicates what options are available in the option group. </p>"}, "AllowsVpcAndNonVpcInstanceMemberships": {"shape": "Boolean", "documentation": "<p>Indicates whether this option group can be applied to both VPC and non-VPC instances. The value <code>true</code> indicates the option group can be applied to both VPC and non-VPC instances. </p>"}, "VpcId": {"shape": "String", "documentation": "<p>If <b>AllowsVpcAndNonVpcInstanceMemberships</b> is <code>false</code>, this field is blank. If <b>AllowsVpcAndNonVpcInstanceMemberships</b> is <code>true</code> and this field is blank, then this option group can be applied to both VPC and non-VPC instances. If this field contains a value, then this option group can only be applied to instances that are in the VPC indicated by this field. </p>"}}, "wrapper": true, "documentation": "<p> </p>"}, "OptionGroupAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "OptionGroupAlreadyExistsFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The option group you are trying to create already exists. </p>"}, "OptionGroupMembership": {"type": "structure", "members": {"OptionGroupName": {"shape": "String", "documentation": "<p> The name of the option group that the instance belongs to. </p>"}, "Status": {"shape": "String", "documentation": "<p> The status of the DB instance's option group membership (e.g. in-sync, pending, pending-maintenance, applying). </p>"}}, "documentation": "<p> Provides information on the option groups the DB instance is a member of. </p>"}, "OptionGroupMembershipList": {"type": "list", "member": {"shape": "OptionGroupMembership", "locationName": "OptionGroupMembership"}}, "OptionGroupNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "OptionGroupNotFoundFault", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified option group could not be found. </p>"}, "OptionGroupOption": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p> The name of the option. </p>"}, "Description": {"shape": "String", "documentation": "<p> The description of the option. </p>"}, "EngineName": {"shape": "String", "documentation": "<p>The name of the engine that this option can be applied to. </p>"}, "MajorEngineVersion": {"shape": "String", "documentation": "<p> Indicates the major engine version that the option is available for. </p>"}, "MinimumRequiredMinorEngineVersion": {"shape": "String", "documentation": "<p> The minimum required engine version for the option to be applied. </p>"}, "PortRequired": {"shape": "Boolean", "documentation": "<p> Specifies whether the option requires a port. </p>"}, "DefaultPort": {"shape": "IntegerOptional", "documentation": "<p> If the option requires a port, specifies the default port for the option. </p>"}, "OptionsDependedOn": {"shape": "OptionsDependedOn", "documentation": "<p> List of all options that are prerequisites for this option. </p>"}, "Persistent": {"shape": "Boolean", "documentation": "<p> A persistent option cannot be removed from the option group once the option group is used, but this option can be removed from the db instance while modifying the related data and assigning another option group without this option. </p>"}, "Permanent": {"shape": "Boolean", "documentation": "<p> A permanent option cannot be removed from the option group once the option group is used, and it cannot be removed from the db instance after assigning an option group with this permanent option. </p>"}, "OptionGroupOptionSettings": {"shape": "OptionGroupOptionSettingsList", "documentation": "<p> Specifies the option settings that are available (and the default value) for each option in an option group. </p>"}}, "documentation": "<p> Available option. </p>"}, "OptionGroupOptionSetting": {"type": "structure", "members": {"SettingName": {"shape": "String", "documentation": "<p> The name of the option group option. </p>"}, "SettingDescription": {"shape": "String", "documentation": "<p> The description of the option group option. </p>"}, "DefaultValue": {"shape": "String", "documentation": "<p> The default value for the option group option. </p>"}, "ApplyType": {"shape": "String", "documentation": "<p> The DB engine specific parameter type for the option group option. </p>"}, "AllowedValues": {"shape": "String", "documentation": "<p> Indicates the acceptable values for the option group option. </p>"}, "IsModifiable": {"shape": "Boolean", "documentation": "<p> Boolean value where true indicates that this option group option can be changed from the default value. </p>"}}, "documentation": "<p>Option group option settings are used to display settings available for each option with their default values and other information. These values are used with the DescribeOptionGroupOptions action. </p>"}, "OptionGroupOptionSettingsList": {"type": "list", "member": {"shape": "OptionGroupOptionSetting", "locationName": "OptionGroupOptionSetting"}}, "OptionGroupOptionsList": {"type": "list", "member": {"shape": "OptionGroupOption", "locationName": "OptionGroupOption"}, "documentation": "<p> List of available option group options. </p>"}, "OptionGroupOptionsMessage": {"type": "structure", "members": {"OptionGroupOptions": {"shape": "OptionGroupOptionsList"}, "Marker": {"shape": "String", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>.</p>"}}, "documentation": "<p> </p>"}, "OptionGroupQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "OptionGroupQuotaExceededFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The quota of 20 option groups was exceeded for this AWS account. </p>"}, "OptionGroups": {"type": "structure", "members": {"OptionGroupsList": {"shape": "OptionGroupsList", "documentation": "<p> List of option groups. </p>"}, "Marker": {"shape": "String", "documentation": "<p>An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}}, "documentation": "<p> List of option groups. </p>"}, "OptionGroupsList": {"type": "list", "member": {"shape": "OptionGroup", "locationName": "OptionGroup"}}, "OptionNamesList": {"type": "list", "member": {"shape": "String"}}, "OptionSetting": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p> The name of the option that has settings that you can set. </p>"}, "Value": {"shape": "String", "documentation": "<p> The current value of the option setting. </p>"}, "DefaultValue": {"shape": "String", "documentation": "<p> The default value of the option setting. </p>"}, "Description": {"shape": "String", "documentation": "<p> The description of the option setting. </p>"}, "ApplyType": {"shape": "String", "documentation": "<p> The DB engine specific parameter type. </p>"}, "DataType": {"shape": "String", "documentation": "<p> The data type of the option setting. </p>"}, "AllowedValues": {"shape": "String", "documentation": "<p> The allowed values of the option setting. </p>"}, "IsModifiable": {"shape": "Boolean", "documentation": "<p> A Boolean value that, when true, indicates the option setting can be modified from the default. </p>"}, "IsCollection": {"shape": "Boolean", "documentation": "<p> Indicates if the option setting is part of a collection. </p>"}}, "documentation": "<p> Option settings are the actual settings being applied or configured for that option. It is used when you modify an option group or describe option groups. For example, the NATIVE_NETWORK_ENCRYPTION option has a setting called SQLNET.ENCRYPTION_SERVER that can have several different values. </p>"}, "OptionSettingConfigurationList": {"type": "list", "member": {"shape": "OptionSetting", "locationName": "OptionSetting"}}, "OptionSettingsList": {"type": "list", "member": {"shape": "OptionSetting", "locationName": "OptionSetting"}}, "OptionsDependedOn": {"type": "list", "member": {"shape": "String", "locationName": "OptionName"}}, "OptionsList": {"type": "list", "member": {"shape": "Option", "locationName": "Option"}}, "OrderableDBInstanceOption": {"type": "structure", "members": {"Engine": {"shape": "String", "documentation": "<p> The engine type of the orderable DB instance. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> The engine version of the orderable DB instance. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The DB instance Class for the orderable DB instance </p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> The license model for the orderable DB instance. </p>"}, "AvailabilityZones": {"shape": "AvailabilityZoneList", "documentation": "<p> A list of availability zones for the orderable DB instance. </p>"}, "MultiAZCapable": {"shape": "Boolean", "documentation": "<p> Indicates whether this orderable DB instance is multi-AZ capable. </p>"}, "ReadReplicaCapable": {"shape": "Boolean", "documentation": "<p> Indicates whether this orderable DB instance can have a Read Replica. </p>"}, "Vpc": {"shape": "Boolean", "documentation": "<p> Indicates whether this is a VPC orderable DB instance. </p>"}, "StorageType": {"shape": "String", "documentation": "<p> The storage type for this orderable DB instance. </p>"}, "SupportsIops": {"shape": "Boolean", "documentation": "<p> Indicates whether this orderable DB instance supports provisioned IOPS. </p>"}}, "wrapper": true, "documentation": "<p> Contains a list of available options for a DB instance </p> <p> This data type is used as a response element in the <a>DescribeOrderableDBInstanceOptions</a> action. </p>"}, "OrderableDBInstanceOptionsList": {"type": "list", "member": {"shape": "OrderableDBInstanceOption", "locationName": "OrderableDBInstanceOption"}}, "OrderableDBInstanceOptionsMessage": {"type": "structure", "members": {"OrderableDBInstanceOptions": {"shape": "OrderableDBInstanceOptionsList", "documentation": "<p>An <a>OrderableDBInstanceOption</a> structure containing information about orderable options for the DB instance.</p>"}, "Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous OrderableDBInstanceOptions request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code> . </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeOrderableDBInstanceOptions</a> action. </p>"}, "Parameter": {"type": "structure", "members": {"ParameterName": {"shape": "String", "documentation": "<p> Specifies the name of the parameter. </p>"}, "ParameterValue": {"shape": "String", "documentation": "<p> Specifies the value of the parameter. </p>"}, "Description": {"shape": "String", "documentation": "<p> Provides a description of the parameter. </p>"}, "Source": {"shape": "String", "documentation": "<p> Indicates the source of the parameter value. </p>"}, "ApplyType": {"shape": "String", "documentation": "<p> Specifies the engine specific parameters type. </p>"}, "DataType": {"shape": "String", "documentation": "<p> Specifies the valid data type for the parameter. </p>"}, "AllowedValues": {"shape": "String", "documentation": "<p> Specifies the valid range of values for the parameter. </p>"}, "IsModifiable": {"shape": "Boolean", "documentation": "<p> Indicates whether (<code>true</code>) or not (<code>false</code>) the parameter can be modified. Some parameters have security or operational implications that prevent them from being changed. </p>"}, "MinimumEngineVersion": {"shape": "String", "documentation": "<p> The earliest engine version to which the parameter can apply. </p>"}, "ApplyMethod": {"shape": "ApplyMethod", "documentation": "<p> Indicates when to apply parameter updates. </p>"}}, "documentation": "<p> This data type is used as a request parameter in the <a>ModifyDBParameterGroup</a> and <a>ResetDBParameterGroup</a> actions. </p> <p>This data type is used as a response element in the <a>DescribeEngineDefaultParameters</a> and <a>DescribeDBParameters</a> actions.</p>"}, "ParametersList": {"type": "list", "member": {"shape": "Parameter", "locationName": "Parameter"}}, "PendingModifiedValues": {"type": "structure", "members": {"DBInstanceClass": {"shape": "String", "documentation": "<p> Contains the new <code>DBInstanceClass</code> for the DB instance that will be applied or is in progress. </p>"}, "AllocatedStorage": {"shape": "IntegerOptional", "documentation": "<p> Contains the new <code>AllocatedStorage</code> size for the DB instance that will be applied or is in progress. </p>"}, "MasterUserPassword": {"shape": "String", "documentation": "<p> Contains the pending or in-progress change of the master credentials for the DB instance. </p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> Specifies the pending port for the DB instance. </p>"}, "BackupRetentionPeriod": {"shape": "IntegerOptional", "documentation": "<p> Specifies the pending number of days for which automated backups are retained. </p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> Indicates that the Single-AZ DB instance is to change to a Multi-AZ deployment. </p>"}, "EngineVersion": {"shape": "String", "documentation": "<p> Indicates the database engine version. </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> Specifies the new Provisioned IOPS value for the DB instance that will be applied or is being applied. </p>"}, "DBInstanceIdentifier": {"shape": "String", "documentation": "<p> Contains the new <code>DBInstanceIdentifier</code> for the DB instance that will be applied or is in progress. </p>"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type to be associated with the DB instance. </p>"}}, "documentation": "<p> This data type is used as a response element in the <a>ModifyDBInstance</a> action. </p>"}, "PointInTimeRestoreNotEnabledFault": {"type": "structure", "members": {}, "error": {"code": "PointInTimeRestoreNotEnabled", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>SourceDBInstanceIdentifier</i> refers to a DB instance with <i>BackupRetentionPeriod</i> equal to 0. </p>"}, "PromoteReadReplicaMessage": {"type": "structure", "required": ["DBInstanceIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier. This value is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must be the identifier for an existing Read Replica DB instance</li> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <copy>mydbinstance</copy></p>"}, "BackupRetentionPeriod": {"shape": "IntegerOptional", "documentation": "<p> The number of days to retain automated backups. Setting this parameter to a positive number enables backups. Setting this parameter to 0 disables automated backups. </p> <p> Default: 1 </p> <p>Constraints:</p> <ul> <li>Must be a value from 0 to 8</li> </ul>"}, "PreferredBackupWindow": {"shape": "String", "documentation": "<p> The daily time range during which automated backups are created if automated backups are enabled, using the <code>BackupRetentionPeriod</code> parameter. </p> <p> Default: A 30-minute window selected at random from an 8-hour block of time per region. See the Amazon RDS User Guide for the time blocks for each region from which the default backup windows are assigned. </p> <p> Constraints: Must be in the format <code>hh24:mi-hh24:mi</code>. Times should be Universal Time Coordinated (UTC). Must not conflict with the preferred maintenance window. Must be at least 30 minutes. </p>"}}, "documentation": "<p> </p>"}, "ProvisionedIopsNotAvailableInAZFault": {"type": "structure", "members": {}, "error": {"code": "ProvisionedIopsNotAvailableInAZFault", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Provisioned IOPS not available in the specified Availability Zone. </p>"}, "PurchaseReservedDBInstancesOfferingMessage": {"type": "structure", "required": ["ReservedDBInstancesOfferingId"], "members": {"ReservedDBInstancesOfferingId": {"shape": "String", "documentation": "<p> The ID of the Reserved DB instance offering to purchase. </p> <p>Example: 438012d3-4052-4cc7-b2e3-8d3372e0e706</p>"}, "ReservedDBInstanceId": {"shape": "String", "documentation": "<p> Customer-specified identifier to track this reservation. </p> <p>Example: myreservationID</p>"}, "DBInstanceCount": {"shape": "IntegerOptional", "documentation": "<p> The number of instances to reserve. </p> <p>Default: <code>1</code></p>"}, "Tags": {"shape": "TagList"}}, "documentation": "<p> </p>"}, "ReadReplicaDBInstanceIdentifierList": {"type": "list", "member": {"shape": "String", "locationName": "ReadReplicaDBInstanceIdentifier"}}, "RebootDBInstanceMessage": {"type": "structure", "required": ["DBInstanceIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> The DB instance identifier. This parameter is stored as a lowercase string. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "ForceFailover": {"shape": "BooleanOptional", "documentation": "<p> When <code>true</code>, the reboot will be conducted through a MultiAZ failover. </p> <p>Constraint: You cannot specify <code>true</code> if the instance is not configured for MultiAZ.</p>"}}, "documentation": "<p> </p>"}, "RecurringCharge": {"type": "structure", "members": {"RecurringChargeAmount": {"shape": "Double", "documentation": "<p> The amount of the recurring charge. </p>"}, "RecurringChargeFrequency": {"shape": "String", "documentation": "<p> The frequency of the recurring charge. </p>"}}, "wrapper": true, "documentation": "<p> This data type is used as a response element in the <a>DescribeReservedDBInstances</a> and <a>DescribeReservedDBInstancesOfferings</a> actions. </p>"}, "RecurringChargeList": {"type": "list", "member": {"shape": "RecurringCharge", "locationName": "RecurringCharge"}}, "RemoveSourceIdentifierFromSubscriptionMessage": {"type": "structure", "required": ["SubscriptionName", "SourceIdentifier"], "members": {"SubscriptionName": {"shape": "String", "documentation": "<p>The name of the RDS event notification subscription you want to remove a source identifier from.</p>"}, "SourceIdentifier": {"shape": "String", "documentation": "<p> The source identifier to be removed from the subscription, such as the <b>DB instance identifier</b> for a DB instance or the name of a security group. </p>"}}, "documentation": "<p></p>"}, "RemoveTagsFromResourceMessage": {"type": "structure", "required": ["ResourceName", "TagKeys"], "members": {"ResourceName": {"shape": "String", "documentation": "<p>The Amazon RDS resource the tags will be removed from. This value is an Amazon Resource Name (ARN). For information about creating an ARN, see <a href=\"http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.html#USER_Tagging.ARN\"> Constructing an RDS Amazon Resource Name (ARN)</a>.</p>"}, "TagKeys": {"shape": "KeyList", "documentation": "<p>The tag key (name) of the tag to be removed.</p>"}}, "documentation": "<p></p>"}, "ReservedDBInstance": {"type": "structure", "members": {"ReservedDBInstanceId": {"shape": "String", "documentation": "<p> The unique identifier for the reservation. </p>"}, "ReservedDBInstancesOfferingId": {"shape": "String", "documentation": "<p> The offering identifier. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The DB instance class for the reserved DB instance. </p>"}, "StartTime": {"shape": "TStamp", "documentation": "<p> The time the reservation started. </p>"}, "Duration": {"shape": "Integer", "documentation": "<p> The duration of the reservation in seconds. </p>"}, "FixedPrice": {"shape": "Double", "documentation": "<p> The fixed price charged for this reserved DB instance. </p>"}, "UsagePrice": {"shape": "Double", "documentation": "<p> The hourly price charged for this reserved DB instance. </p>"}, "CurrencyCode": {"shape": "String", "documentation": "<p> The currency code for the reserved DB instance. </p>"}, "DBInstanceCount": {"shape": "Integer", "documentation": "<p> The number of reserved DB instances. </p>"}, "ProductDescription": {"shape": "String", "documentation": "<p> The description of the reserved DB instance. </p>"}, "OfferingType": {"shape": "String", "documentation": "<p> The offering type of this reserved DB instance. </p>"}, "MultiAZ": {"shape": "Boolean", "documentation": "<p> Indicates if the reservation applies to Multi-AZ deployments. </p>"}, "State": {"shape": "String", "documentation": "<p> The state of the reserved DB instance. </p>"}, "RecurringCharges": {"shape": "RecurringChargeList", "documentation": "<p> The recurring price charged to run this reserved DB instance. </p>"}}, "wrapper": true, "documentation": "<p> This data type is used as a response element in the <a>DescribeReservedDBInstances</a> and <a>PurchaseReservedDBInstancesOffering</a> actions. </p>"}, "ReservedDBInstanceAlreadyExistsFault": {"type": "structure", "members": {}, "error": {"code": "ReservedDBInstanceAlreadyExists", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> User already has a reservation with the given identifier. </p>"}, "ReservedDBInstanceList": {"type": "list", "member": {"shape": "ReservedDBInstance", "locationName": "ReservedDBInstance"}}, "ReservedDBInstanceMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "ReservedDBInstances": {"shape": "ReservedDBInstanceList", "documentation": "<p> A list of reserved DB instances. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeReservedDBInstances</a> action. </p>"}, "ReservedDBInstanceNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "ReservedDBInstanceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> The specified reserved DB Instance not found. </p>"}, "ReservedDBInstanceQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "ReservedDBInstanceQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would exceed the user's DB Instance quota. </p>"}, "ReservedDBInstancesOffering": {"type": "structure", "members": {"ReservedDBInstancesOfferingId": {"shape": "String", "documentation": "<p> The offering identifier. </p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The DB instance class for the reserved DB instance. </p>"}, "Duration": {"shape": "Integer", "documentation": "<p> The duration of the offering in seconds. </p>"}, "FixedPrice": {"shape": "Double", "documentation": "<p> The fixed price charged for this offering. </p>"}, "UsagePrice": {"shape": "Double", "documentation": "<p> The hourly price charged for this offering. </p>"}, "CurrencyCode": {"shape": "String", "documentation": "<p> The currency code for the reserved DB instance offering. </p>"}, "ProductDescription": {"shape": "String", "documentation": "<p> The database engine used by the offering. </p>"}, "OfferingType": {"shape": "String", "documentation": "<p> The offering type. </p>"}, "MultiAZ": {"shape": "Boolean", "documentation": "<p> Indicates if the offering applies to Multi-AZ deployments. </p>"}, "RecurringCharges": {"shape": "RecurringChargeList", "documentation": "<p> The recurring price charged to run this reserved DB instance. </p>"}}, "wrapper": true, "documentation": "<p> This data type is used as a response element in the <a>DescribeReservedDBInstancesOfferings</a> action. </p>"}, "ReservedDBInstancesOfferingList": {"type": "list", "member": {"shape": "ReservedDBInstancesOffering", "locationName": "ReservedDBInstancesOffering"}}, "ReservedDBInstancesOfferingMessage": {"type": "structure", "members": {"Marker": {"shape": "String", "documentation": "<p> An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by <code>MaxRecords</code>. </p>"}, "ReservedDBInstancesOfferings": {"shape": "ReservedDBInstancesOfferingList", "documentation": "<p> A list of reserved DB instance offerings. </p>"}}, "documentation": "<p> Contains the result of a successful invocation of the <a>DescribeReservedDBInstancesOfferings</a> action. </p>"}, "ReservedDBInstancesOfferingNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "ReservedDBInstancesOfferingNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p> Specified offering does not exist. </p>"}, "ResetDBParameterGroupMessage": {"type": "structure", "required": ["DBParameterGroupName"], "members": {"DBParameterGroupName": {"shape": "String", "documentation": "<p> The name of the DB parameter group. </p> <p>Constraints:</p> <ul> <li>Must be 1 to 255 alphanumeric characters</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "ResetAllParameters": {"shape": "Boolean", "documentation": "<p> Specifies whether (<code>true</code>) or not (<code>false</code>) to reset all parameters in the DB parameter group to default values. </p> <p>Default: <code>true</code></p>"}, "Parameters": {"shape": "ParametersList", "documentation": "<p> An array of parameter names, values, and the apply method for the parameter update. At least one parameter name, value, and apply method must be supplied; subsequent arguments are optional. A maximum of 20 parameters may be modified in a single request. </p> <p><b>MySQL</b></p> <p>Valid Values (for Apply method): <code>immediate</code> | <code>pending-reboot</code></p> <p>You can use the immediate value with dynamic parameters only. You can use the <code>pending-reboot</code> value for both dynamic and static parameters, and changes are applied when DB instance reboots.</p> <p><b>Oracle</b></p> <p>Valid Values (for Apply method): <code>pending-reboot</code></p>"}}, "documentation": "<p> </p>"}, "RestoreDBInstanceFromDBSnapshotMessage": {"type": "structure", "required": ["DBInstanceIdentifier", "DBSnapshotIdentifier"], "members": {"DBInstanceIdentifier": {"shape": "String", "documentation": "<p> Name of the DB instance to create from the DB snapshot. This parameter isn't case sensitive. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 255 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul> <p>Example: <code>my-snapshot-id</code></p>"}, "DBSnapshotIdentifier": {"shape": "String", "documentation": "<p> The identifier for the DB snapshot to restore from. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The compute and memory capacity of the Amazon RDS DB instance. </p> <p>Valid Values: <code>db.t1.micro | db.m1.small | db.m1.medium | db.m1.large | db.m1.xlarge | db.m2.2xlarge | db.m2.4xlarge | db.m3.medium | db.m3.large | db.m3.xlarge | db.m3.2xlarge | db.r3.large | db.r3.xlarge | db.r3.2xlarge | db.r3.4xlarge | db.r3.8xlarge | db.t2.micro | db.t2.small | db.t2.medium</code></p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> The port number on which the database accepts connections. </p> <p>Default: The same port as the original DB instance</p> <p>Constraints: Value must be <code>1150-65535</code></p>"}, "AvailabilityZone": {"shape": "String", "documentation": "<p> The EC2 Availability Zone that the database instance will be created in. </p> <p>Default: A random, system-chosen Availability Zone.</p> <p>Constraint: You cannot specify the AvailabilityZone parameter if the MultiAZ parameter is set to <code>true</code>.</p> <p>Example: <code>us-east-1a</code></p>"}, "DBSubnetGroupName": {"shape": "String", "documentation": "<p> The DB subnet group name to use for the new instance. </p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> Specifies if the DB instance is a Multi-AZ deployment. </p> <p>Constraint: You cannot specify the AvailabilityZone parameter if the MultiAZ parameter is set to <code>true</code>.</p>"}, "PubliclyAccessible": {"shape": "BooleanOptional", "documentation": "<p> Specifies the accessibility options for the DB instance. A value of true specifies an Internet-facing instance with a publicly resolvable DNS name, which resolves to a public IP address. A value of false specifies an internal instance with a DNS name that resolves to a private IP address. </p> <p> Default: The default behavior varies depending on whether a VPC has been requested or not. The following list shows the default behavior in each case. </p> <ul> <li> <b>Default VPC:</b>true</li> <li> <b>VPC:</b>false</li> </ul> <p> If no DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be publicly accessible. If a specific DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be private. </p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p> Indicates that minor version upgrades will be applied automatically to the DB instance during the maintenance window. </p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> License model information for the restored DB instance. </p> <p> Default: Same as source. </p> <p> Valid values: <code>license-included</code> | <code>bring-your-own-license</code> | <code>general-public-license</code> </p>"}, "DBName": {"shape": "String", "documentation": "<p> The database name for the restored DB instance. </p> <note> <p>This parameter doesn't apply to the MySQL engine.</p> </note>"}, "Engine": {"shape": "String", "documentation": "<p> The database engine to use for the new instance. </p> <p>Default: The same as source</p> <p>Constraint: Must be compatible with the engine of the source</p> <p> Valid Values: <code>MySQL</code> | <code>oracle-se1</code> | <code>oracle-se</code> | <code>oracle-ee</code> | <code>sqlserver-ee</code> | <code>sqlserver-se</code> | <code>sqlserver-ex</code> | <code>sqlserver-web</code> | <code>postgres</code> </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> Specifies the amount of provisioned IOPS for the DB instance, expressed in I/O operations per second. If this parameter is not specified, the IOPS value will be taken from the backup. If this parameter is set to 0, the new instance will be converted to a non-PIOPS instance, which will take additional time, though your DB instance will be available for connections before the conversion starts. </p> <p> Constraints: Must be an integer greater than 1000.</p> <p><b>SQL Server</b></p> <p>Setting the IOPS value for the SQL Server database engine is not supported.</p>"}, "OptionGroupName": {"shape": "String", "documentation": "<p>The name of the option group to be used for the restored DB instance.</p> <p>Permanent options, such as the TDE option for Oracle Advanced Security TDE, cannot be removed from an option group, and that option group cannot be removed from a DB instance once it is associated with a DB instance </p>"}, "Tags": {"shape": "TagList"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type to be associated with the DB Instance. </p> <p> Valid values: <code>standard | gp2 | io1</code> </p> <p> If you specify <code>io1</code>, you must also include a value for the <code>Iops</code> parameter. </p> <p> Default: <code>io1</code> if the <code>Iops</code> parameter is specified; otherwise <code>standard</code> </p>"}, "TdeCredentialArn": {"shape": "String", "documentation": "<p> The ARN from the Key Store with which to associate the instance for TDE encryption. </p>"}, "TdeCredentialPassword": {"shape": "String", "documentation": "<p> The password for the given ARN from the Key Store in order to access the device. </p>"}}, "documentation": "<p> </p>"}, "RestoreDBInstanceToPointInTimeMessage": {"type": "structure", "required": ["SourceDBInstanceIdentifier", "TargetDBInstanceIdentifier"], "members": {"SourceDBInstanceIdentifier": {"shape": "String", "documentation": "<p> The identifier of the source DB instance from which to restore. </p> <p>Constraints:</p> <ul> <li>Must be the identifier of an existing database instance</li> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "TargetDBInstanceIdentifier": {"shape": "String", "documentation": "<p> The name of the new database instance to be created. </p> <p>Constraints:</p> <ul> <li>Must contain from 1 to 63 alphanumeric characters or hyphens</li> <li>First character must be a letter</li> <li>Cannot end with a hyphen or contain two consecutive hyphens</li> </ul>"}, "RestoreTime": {"shape": "TStamp", "documentation": "<p> The date and time to restore from. </p> <p>Valid Values: Value must be a UTC time</p> <p>Constraints:</p> <ul> <li>Must be before the latest restorable time for the DB instance</li> <li>Cannot be specified if UseLatestRestorableTime parameter is true</li> </ul> <p>Example: <code>2009-09-07T23:45:00Z</code></p>"}, "UseLatestRestorableTime": {"shape": "Boolean", "documentation": "<p> Specifies whether (<code>true</code>) or not (<code>false</code>) the DB instance is restored from the latest backup time. </p> <p>Default: <code>false</code></p> <p>Constraints: Cannot be specified if RestoreTime parameter is provided.</p>"}, "DBInstanceClass": {"shape": "String", "documentation": "<p> The compute and memory capacity of the Amazon RDS DB instance. </p> <p>Valid Values: <code>db.t1.micro | db.m1.small | db.m1.medium | db.m1.large | db.m1.xlarge | db.m2.2xlarge | db.m2.4xlarge | db.m3.medium | db.m3.large | db.m3.xlarge | db.m3.2xlarge | db.r3.large | db.r3.xlarge | db.r3.2xlarge | db.r3.4xlarge | db.r3.8xlarge | db.t2.micro | db.t2.small | db.t2.medium</code></p> <p>Default: The same DBInstanceClass as the original DB instance.</p>"}, "Port": {"shape": "IntegerOptional", "documentation": "<p> The port number on which the database accepts connections. </p> <p>Constraints: Value must be <code>1150-65535</code></p> <p>Default: The same port as the original DB instance.</p>"}, "AvailabilityZone": {"shape": "String", "documentation": "<p> The EC2 Availability Zone that the database instance will be created in. </p> <p>Default: A random, system-chosen Availability Zone.</p> <p>Constraint: You cannot specify the AvailabilityZone parameter if the MultiAZ parameter is set to true.</p> <p>Example: <code>us-east-1a</code></p>"}, "DBSubnetGroupName": {"shape": "String", "documentation": "<p> The DB subnet group name to use for the new instance. </p>"}, "MultiAZ": {"shape": "BooleanOptional", "documentation": "<p> Specifies if the DB instance is a Multi-AZ deployment. </p> <p>Constraint: You cannot specify the AvailabilityZone parameter if the MultiAZ parameter is set to <code>true</code>.</p>"}, "PubliclyAccessible": {"shape": "BooleanOptional", "documentation": "<p> Specifies the accessibility options for the DB instance. A value of true specifies an Internet-facing instance with a publicly resolvable DNS name, which resolves to a public IP address. A value of false specifies an internal instance with a DNS name that resolves to a private IP address. </p> <p> Default: The default behavior varies depending on whether a VPC has been requested or not. The following list shows the default behavior in each case. </p> <ul> <li> <b>Default VPC:</b>true</li> <li> <b>VPC:</b>false</li> </ul> <p> If no DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be publicly accessible. If a specific DB subnet group has been specified as part of the request and the PubliclyAccessible value has not been set, the DB instance will be private. </p>"}, "AutoMinorVersionUpgrade": {"shape": "BooleanOptional", "documentation": "<p> Indicates that minor version upgrades will be applied automatically to the DB instance during the maintenance window. </p>"}, "LicenseModel": {"shape": "String", "documentation": "<p> License model information for the restored DB instance. </p> <p> Default: Same as source. </p> <p> Valid values: <code>license-included</code> | <code>bring-your-own-license</code> | <code>general-public-license</code> </p>"}, "DBName": {"shape": "String", "documentation": "<p> The database name for the restored DB instance. </p> <note> <p>This parameter is not used for the MySQL engine.</p> </note>"}, "Engine": {"shape": "String", "documentation": "<p> The database engine to use for the new instance. </p> <p>Default: The same as source</p> <p>Constraint: Must be compatible with the engine of the source</p> <p> Valid Values: <code>MySQL</code> | <code>oracle-se1</code> | <code>oracle-se</code> | <code>oracle-ee</code> | <code>sqlserver-ee</code> | <code>sqlserver-se</code> | <code>sqlserver-ex</code> | <code>sqlserver-web</code> | <code>postgres</code> </p>"}, "Iops": {"shape": "IntegerOptional", "documentation": "<p> The amount of Provisioned IOPS (input/output operations per second) to be initially allocated for the DB instance. </p> <p> Constraints: Must be an integer greater than 1000.</p> <p><b>SQL Server</b></p> <p>Setting the IOPS value for the SQL Server database engine is not supported.</p>"}, "OptionGroupName": {"shape": "String", "documentation": "<p>The name of the option group to be used for the restored DB instance.</p> <p>Permanent options, such as the TDE option for Oracle Advanced Security TDE, cannot be removed from an option group, and that option group cannot be removed from a DB instance once it is associated with a DB instance </p>"}, "Tags": {"shape": "TagList"}, "StorageType": {"shape": "String", "documentation": "<p> Specifies storage type to be associated with the DB Instance. </p> <p> Valid values: <code>standard | gp2 | io1</code> </p> <p> If you specify <code>io1</code>, you must also include a value for the <code>Iops</code> parameter. </p> <p> Default: <code>io1</code> if the <code>Iops</code> parameter is specified; otherwise <code>standard</code> </p>"}, "TdeCredentialArn": {"shape": "String", "documentation": "<p> The ARN from the Key Store with which to associate the instance for TDE encryption. </p>"}, "TdeCredentialPassword": {"shape": "String", "documentation": "<p> The password for the given ARN from the Key Store in order to access the device. </p>"}}, "documentation": "<p> </p>"}, "RevokeDBSecurityGroupIngressMessage": {"type": "structure", "required": ["DBSecurityGroupName"], "members": {"DBSecurityGroupName": {"shape": "String", "documentation": "<p> The name of the DB security group to revoke ingress from. </p>"}, "CIDRIP": {"shape": "String", "documentation": "<p> The IP range to revoke access from. Must be a valid CIDR range. If <code>CIDRIP</code> is specified, <code>EC2SecurityGroupName</code>, <code>EC2SecurityGroupId</code> and <code>EC2SecurityGroupOwnerId</code> cannot be provided. </p>"}, "EC2SecurityGroupName": {"shape": "String", "documentation": "<p> The name of the EC2 security group to revoke access from. For VPC DB security groups, <code>EC2SecurityGroupId</code> must be provided. Otherwise, EC2SecurityGroupOwnerId and either <code>EC2SecurityGroupName</code> or <code>EC2SecurityGroupId</code> must be provided. </p>"}, "EC2SecurityGroupId": {"shape": "String", "documentation": "<p> The id of the EC2 security group to revoke access from. For VPC DB security groups, <code>EC2SecurityGroupId</code> must be provided. Otherwise, EC2SecurityGroupOwnerId and either <code>EC2SecurityGroupName</code> or <code>EC2SecurityGroupId</code> must be provided. </p>"}, "EC2SecurityGroupOwnerId": {"shape": "String", "documentation": "<p> The AWS Account Number of the owner of the EC2 security group specified in the <code>EC2SecurityGroupName</code> parameter. The AWS Access Key ID is not an acceptable value. For VPC DB security groups, <code>EC2SecurityGroupId</code> must be provided. Otherwise, EC2SecurityGroupOwnerId and either <code>EC2SecurityGroupName</code> or <code>EC2SecurityGroupId</code> must be provided. </p>"}}, "documentation": "<p> </p>"}, "SNSInvalidTopicFault": {"type": "structure", "members": {}, "error": {"code": "SNSInvalidTopic", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>SNS has responded that there is a problem with the SND topic specified.</p>"}, "SNSNoAuthorizationFault": {"type": "structure", "members": {}, "error": {"code": "SNSNoAuthorization", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>You do not have permission to publish to the SNS topic ARN.</p>"}, "SNSTopicArnNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "SNSTopicArnNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The SNS topic ARN does not exist.</p>"}, "SnapshotQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "SnapshotQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed number of DB snapshots. </p>"}, "SourceIdsList": {"type": "list", "member": {"shape": "String", "locationName": "SourceId"}}, "SourceNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "SourceNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The requested source could not be found.</p>"}, "SourceType": {"type": "string", "enum": ["db-instance", "db-parameter-group", "db-security-group", "db-snapshot"]}, "StorageQuotaExceededFault": {"type": "structure", "members": {}, "error": {"code": "StorageQuotaExceeded", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> Request would result in user exceeding the allowed amount of storage available across all DB instances. </p>"}, "StorageTypeNotSupportedFault": {"type": "structure", "members": {}, "error": {"code": "StorageTypeNotSupported", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> <i>StorageType</i> specified cannot be associated with the DB Instance. </p>"}, "String": {"type": "string"}, "Subnet": {"type": "structure", "members": {"SubnetIdentifier": {"shape": "String", "documentation": "<p> Specifies the identifier of the subnet. </p>"}, "SubnetAvailabilityZone": {"shape": "AvailabilityZone"}, "SubnetStatus": {"shape": "String", "documentation": "<p> Specifies the status of the subnet. </p>"}}, "documentation": "<p> This data type is used as a response element in the <a>DescribeDBSubnetGroups</a> action. </p>"}, "SubnetAlreadyInUse": {"type": "structure", "members": {}, "error": {"code": "SubnetAlreadyInUse", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p> The DB subnet is already in use in the Availability Zone. </p>"}, "SubnetIdentifierList": {"type": "list", "member": {"shape": "String", "locationName": "SubnetIdentifier"}}, "SubnetList": {"type": "list", "member": {"shape": "Subnet", "locationName": "Subnet"}}, "SubscriptionAlreadyExistFault": {"type": "structure", "members": {}, "error": {"code": "SubscriptionAlreadyExist", "httpStatusCode": 400, "senderFault": true}, "exception": true, "documentation": "<p>The supplied subscription name already exists.</p>"}, "SubscriptionCategoryNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "SubscriptionCategoryNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The supplied category does not exist.</p>"}, "SubscriptionNotFoundFault": {"type": "structure", "members": {}, "error": {"code": "SubscriptionNotFound", "httpStatusCode": 404, "senderFault": true}, "exception": true, "documentation": "<p>The subscription name does not exist.</p>"}, "SupportedCharacterSetsList": {"type": "list", "member": {"shape": "CharacterSet", "locationName": "CharacterSet"}}, "TStamp": {"type": "timestamp"}, "Tag": {"type": "structure", "members": {"Key": {"shape": "String", "documentation": "<p>A key is the required name of the tag. The string value can be from 1 to 128 Unicode characters in length and cannot be prefixed with \"aws:\" or \"rds:\". The string may only contain only the set of Unicode letters, digits, white-space, '_', '.', '/', '=', '+', '-' (Java regex: \"^([\\\\p{L}\\\\p{Z}\\\\p{N}_.:/=+\\\\-]*)$\").</p>"}, "Value": {"shape": "String", "documentation": "<p>A value is the optional value of the tag. The string value can be from 1 to 256 Unicode characters in length and cannot be prefixed with \"aws:\" or \"rds:\". The string may only contain only the set of Unicode letters, digits, white-space, '_', '.', '/', '=', '+', '-' (Java regex: \"^([\\\\p{L}\\\\p{Z}\\\\p{N}_.:/=+\\\\-]*)$\").</p>"}}, "documentation": "<p>Metadata assigned to an Amazon RDS resource consisting of a key-value pair.</p>"}, "TagList": {"type": "list", "member": {"shape": "Tag", "locationName": "Tag"}, "documentation": "<p>A list of tags.</p>"}, "TagListMessage": {"type": "structure", "members": {"TagList": {"shape": "TagList", "documentation": "<p>List of tags returned by the ListTagsForResource operation.</p>"}}, "documentation": "<p></p>"}, "VpcSecurityGroupIdList": {"type": "list", "member": {"shape": "String", "locationName": "VpcSecurityGroupId"}}, "VpcSecurityGroupMembership": {"type": "structure", "members": {"VpcSecurityGroupId": {"shape": "String", "documentation": "<p>The name of the VPC security group.</p>"}, "Status": {"shape": "String", "documentation": "<p> The status of the VPC security group. </p>"}}, "documentation": "<p>This data type is used as a response element for queries on VPC security group membership.</p>"}, "VpcSecurityGroupMembershipList": {"type": "list", "member": {"shape": "VpcSecurityGroupMembership", "locationName": "VpcSecurityGroupMembership"}}, "AddSourceIdentifierToSubscriptionResult": {"type": "structure", "members": {"EventSubscription": {"shape": "EventSubscription"}}}, "AuthorizeDBSecurityGroupIngressResult": {"type": "structure", "members": {"DBSecurityGroup": {"shape": "DBSecurityGroup"}}}, "CopyDBParameterGroupResult": {"type": "structure", "members": {"DBParameterGroup": {"shape": "DBParameterGroup"}}}, "CopyDBSnapshotResult": {"type": "structure", "members": {"DBSnapshot": {"shape": "DBSnapshot"}}}, "CopyOptionGroupResult": {"type": "structure", "members": {"OptionGroup": {"shape": "OptionGroup"}}}, "CreateDBInstanceResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "CreateDBInstanceReadReplicaResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "CreateDBParameterGroupResult": {"type": "structure", "members": {"DBParameterGroup": {"shape": "DBParameterGroup"}}}, "CreateDBSecurityGroupResult": {"type": "structure", "members": {"DBSecurityGroup": {"shape": "DBSecurityGroup"}}}, "CreateDBSnapshotResult": {"type": "structure", "members": {"DBSnapshot": {"shape": "DBSnapshot"}}}, "CreateDBSubnetGroupResult": {"type": "structure", "members": {"DBSubnetGroup": {"shape": "DBSubnetGroup"}}}, "CreateEventSubscriptionResult": {"type": "structure", "members": {"EventSubscription": {"shape": "EventSubscription"}}}, "CreateOptionGroupResult": {"type": "structure", "members": {"OptionGroup": {"shape": "OptionGroup"}}}, "DeleteDBInstanceResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "DeleteDBSnapshotResult": {"type": "structure", "members": {"DBSnapshot": {"shape": "DBSnapshot"}}}, "DeleteEventSubscriptionResult": {"type": "structure", "members": {"EventSubscription": {"shape": "EventSubscription"}}}, "DescribeEngineDefaultParametersResult": {"type": "structure", "members": {"EngineDefaults": {"shape": "EngineDefaults"}}}, "ModifyDBInstanceResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "ModifyDBSubnetGroupResult": {"type": "structure", "members": {"DBSubnetGroup": {"shape": "DBSubnetGroup"}}}, "ModifyEventSubscriptionResult": {"type": "structure", "members": {"EventSubscription": {"shape": "EventSubscription"}}}, "ModifyOptionGroupResult": {"type": "structure", "members": {"OptionGroup": {"shape": "OptionGroup"}}}, "PromoteReadReplicaResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "PurchaseReservedDBInstancesOfferingResult": {"type": "structure", "members": {"ReservedDBInstance": {"shape": "ReservedDBInstance"}}}, "RebootDBInstanceResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "RemoveSourceIdentifierFromSubscriptionResult": {"type": "structure", "members": {"EventSubscription": {"shape": "EventSubscription"}}}, "RestoreDBInstanceFromDBSnapshotResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "RestoreDBInstanceToPointInTimeResult": {"type": "structure", "members": {"DBInstance": {"shape": "DBInstance"}}}, "RevokeDBSecurityGroupIngressResult": {"type": "structure", "members": {"DBSecurityGroup": {"shape": "DBSecurityGroup"}}}}}