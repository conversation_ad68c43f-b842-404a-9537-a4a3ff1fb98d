import re
import csv
import os

def parse_nod_file(file_path):
    """
    Reads a .nod file, attempting to parse it either as a text file or by
    extracting strings from a C# BinaryFormatter stream.
    """
    try:
        # First, try to read as a UTF-8 text file (common for newer text files)
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return parse_text_nod(lines)
    except UnicodeDecodeError:
        # If UTF-8 fails, it might be another encoding or a binary file.
        # Try a different text encoding before giving up on text.
        try:
            with open(file_path, 'r', encoding='utf-16-le') as f:
                lines = f.readlines()
            return parse_text_nod(lines)
        except (UnicodeDecodeError, TypeError):
            # If text reading fails, treat it as a binary file.
            with open(file_path, 'rb') as f:
                content = f.read()
            return parse_binary_nod(content)
    except FileNotFoundError:
        return {"error": f"File not found: {file_path}"}, []
    except Exception as e:
        return {"error": f"An error occurred: {e}"}, []

def parse_text_nod(lines):
    """Parses .nod files that are in plain text format."""
    metadata = {}
    data_started = False
    raman_data = []
    delimiter = ';'

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # The header for the data section
        if "Raman Shift" in line and "Intensity" in line:
            data_started = True
            continue
        
        if not data_started:
            if delimiter in line:
                parts = line.split(delimiter, 1)
                if len(parts) == 2:
                    key, value = parts
                    metadata[key.strip()] = value.strip()
        else:
            if delimiter in line:
                parts = line.split(delimiter)
                try:
                    # Assumes Raman Shift is first, Intensity is second
                    raman_shift = float(parts[0])
                    intensity = float(parts[1])
                    raman_data.append((raman_shift, intensity))
                except (ValueError, IndexError):
                    # Skip malformed data lines
                    continue
    return metadata, raman_data

def parse_binary_nod(binary_content):
    """
    Heuristically parses .nod files that are C# BinaryFormatter streams
    by finding and decoding embedded UTF-16 LE strings.
    """
    metadata = {}
    raman_data = []
    
    # Regex to find potential UTF-16 LE strings. It looks for sequences of
    # (printable ASCII character + null byte), which is how UTF-16 LE encodes ASCII.
    # We assume strings are at least 3 characters long.
    string_pattern = re.compile(b'((?:[\x20-\x7E-\s]\x00){3,})')
    
    found_strings = []
    for match in string_pattern.finditer(binary_content):
        try:
            # Decode the matched byte sequence from UTF-16 LE
            found_strings.append(match.group(1).decode('utf-16-le'))
        except UnicodeDecodeError:
            continue

    # Process the extracted strings to build metadata and data
    data_started = False
    for s in found_strings:
        s = s.strip()
        if not s:
            continue
        
        # Use the data header string to switch from metadata to data parsing
        if "Raman Shift;Intensity" in s:
             data_started = True
             continue
        
        if not data_started:
            if ';' in s:
                parts = s.split(';', 1)
                if len(parts) == 2:
                    key = ''.join(filter(str.isprintable, parts[0])).strip()
                    value = ''.join(filter(str.isprintable, parts[1])).strip()
                    metadata[key] = value
        else:
            # For the data part, we expect "raman_shift;intensity"
            if ';' in s:
                parts = s.split(';')
                if len(parts) == 2:
                    try:
                        raman_shift = float(parts[0])
                        intensity = float(parts[1])
                        raman_data.append((raman_shift, intensity))
                    except ValueError:
                        # Skip if conversion to float fails
                        continue
                        
    return metadata, raman_data

def save_to_csv(data, file_path):
    """Saves the Raman data to a CSV file."""
    try:
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Raman Shift', 'Intensity'])  # Write header
            writer.writerows(data)
        print(f"Data successfully saved to {file_path}")
    except Exception as e:
        print(f"Error saving to CSV: {e}")

if __name__ == "__main__":
    # The file path provided in the user request
    file_to_read = r"nod\A01-莫西沙星标准品1mgmL+SN-P1-_2856_20250701-1.nod"
    
    metadata, raman_data = parse_nod_file(file_to_read)
    
    if metadata.get("error"):
        print(f"Error reading file: {metadata['error']}")
    else:
        print("--- Metadata ---")
        for key, value in metadata.items():
            print(f"{key}: {value}")
        
        print("\n--- Raman Data (Sample) ---")
        # Print the first 5 and last 5 data points as a sample
        if len(raman_data) > 10:
            for raman_shift, intensity in raman_data[:5]:
                print(f"Raman Shift: {raman_shift:.4f}, Intensity: {intensity:.4f}")
            print("...")
            for raman_shift, intensity in raman_data[-5:]:
                print(f"Raman Shift: {raman_shift:.4f}, Intensity: {intensity:.4f}")
        else:
             for raman_shift, intensity in raman_data:
                print(f"Raman Shift: {raman_shift:.4f}, Intensity: {intensity:.4f}")

        print(f"\nTotal data points found: {len(raman_data)}")

        # Save the full data to a CSV file
        output_dir = 'exported_data'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        base_filename = os.path.basename(file_to_read)
        output_csv_path = os.path.join(output_dir, base_filename.replace('.nod', '.csv'))
        save_to_csv(raman_data, output_csv_path)
