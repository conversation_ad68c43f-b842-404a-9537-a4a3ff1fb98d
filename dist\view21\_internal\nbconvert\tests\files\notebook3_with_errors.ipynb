{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook with errors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook contains a cell which deliberately throws an exception. This is to test if `nbconvert` stops conversion if the flag `--execute` is given without `--allow-errors`. In the cells before and after the one which raises the exception we compute a couple of numbers. If they exist in the output we know that the respective cells were executed."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["print(\"Hello world, my number is {}\".format(24 - 1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["print(\"Some text before the error\")\n", "raise RuntimeError(\"This is a deliberate exception\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["print(\"The answer to the question about life, the universe and everything is: {}\".format(43 - 1))"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 0}