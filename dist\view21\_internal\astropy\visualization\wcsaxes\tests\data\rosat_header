WCSAXES =                    2 / Number of coordinate axes                      
CRPIX1  =                240.5 / Pixel coordinate of reference point            
CRPIX2  =                120.5 / Pixel coordinate of reference point            
CDELT1  =               -0.675 / [deg] Coordinate increment at reference point  
CDELT2  =                0.675 / [deg] Coordinate increment at reference point  
CUNIT1  = 'deg'                / Units of coordinate increment and value        
CUNIT2  = 'deg'                / Units of coordinate increment and value        
CTYPE1  = 'GLON-AIT'           / galactic longitude, Hammer-<PERSON><PERSON>ff projection   
CTYPE2  = 'GLAT-AIT'           / galactic latitude, Hammer-<PERSON><PERSON>ff projection    
CRVAL1  =                  0.0 / [deg] Coordinate value at reference point      
CRVAL2  =                  0.0 / [deg] Coordinate value at reference point      
LONPOLE =                  0.0 / [deg] Native longitude of celestial pole       
LATPOLE =                 90.0 / [deg] Native latitude of celestial pole        