../../Scripts/fits2bitmap.exe,sha256=-_ahaOBz2kHRDKnwC7cmfk271ZAzcW-Livq1a7UkDNE,106375
../../Scripts/fitscheck.exe,sha256=Wgcnk9qMNehtyfY86Ebb2Vsu5hQwt4sfNFH4iUXIBTc,106367
../../Scripts/fitsdiff.exe,sha256=TaSsIGkBjBoHaVZHn9ONxr453f9DoxxEi-zwFP79tPE,106366
../../Scripts/fitsheader.exe,sha256=uSacehvnFABmpiOBIgBDag45OlbaMoDcfFr1srxN_Dk,106368
../../Scripts/fitsinfo.exe,sha256=rcZ6TF7-cezU33pUnbWLH8Vt83utaj6iLXsqYYCnGJE,106366
../../Scripts/samp_hub.exe,sha256=w_9WnGSzpbPS9oTczZaq744t9paynO7p-gmmY7jiVE4,106369
../../Scripts/showtable.exe,sha256=HZvmqS-J1XiJR1pNvA-T9tHIG5j04bbr7IWml2KVgQo,106365
../../Scripts/volint.exe,sha256=XCnYZqzdDYKLXdnNgtkUUWi9XIl1xi2_mWJCUtlTUyQ,106359
../../Scripts/wcslint.exe,sha256=voPQqR-rDGYsgNQ3GYpGJxEIGNPVMnvh6aP5ddl5MGo,106353
astropy-5.0.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
astropy-5.0.4.dist-info/LICENSE.rst,sha256=wk4RGC8YjrsP99khMrfTOUQRgW3U627Mz46R89wWjY4,1496
astropy-5.0.4.dist-info/METADATA,sha256=dq0CkgKcMgoYl8cP160ei6er6_PqCdP87ZqgoNIcQlw,7952
astropy-5.0.4.dist-info/RECORD,,
astropy-5.0.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy-5.0.4.dist-info/WHEEL,sha256=fVcVlLzi8CGi_Ul8vjMdn8gER25dn5GBg9E6k9z41-Y,100
astropy-5.0.4.dist-info/direct_url.json,sha256=pcDi-8H0G1QAJf6D44hJNJ8nDCTgR4eTtlZ4SF1u3No,67
astropy-5.0.4.dist-info/entry_points.txt,sha256=6naGSTd8yyJCGdqb13KEK9gPTSM6F6lgKIu6tjeN8dw,595
astropy-5.0.4.dist-info/top_level.txt,sha256=1x1TQBA_cdZt34TYpofZyvtgiaMGVAY3uBS-2PBXGTs,8
astropy/CITATION,sha256=CucDeglcY0nCJXx6wnm8GsaWbMJjA2gBwCI2uuoKmkQ,5939
astropy/__init__.py,sha256=nEQWkrq2Z6qhmpLtqJwD61EiKTLPFspkPlXZbD3ZKkA,7696
astropy/__pycache__/__init__.cpython-39.pyc,,
astropy/__pycache__/_version.cpython-39.pyc,,
astropy/__pycache__/conftest.cpython-39.pyc,,
astropy/__pycache__/logger.cpython-39.pyc,,
astropy/__pycache__/version.cpython-39.pyc,,
astropy/_erfa/__init__.py,sha256=uuLjhK-ZZUmIvBNBnwsU3TruffbC8A4g7fin04j5lNc,676
astropy/_erfa/__pycache__/__init__.cpython-39.pyc,,
astropy/_version.py,sha256=TvUkAOmui6hHwkiP7LlKra8QgcsG7jMfZQRYVgapdXs,147
astropy/compiler_version.cp39-win_amd64.pyd,sha256=bTfv9pZiGPv_s0ImFEu84f42Fr17Mub1FYUOYGOlaMY,10240
astropy/config/__init__.py,sha256=ILYCcqichpJORt6AHugUNHhm13EYs4lbV-aS_5LyhMQ,303
astropy/config/__pycache__/__init__.cpython-39.pyc,,
astropy/config/__pycache__/affiliated.cpython-39.pyc,,
astropy/config/__pycache__/configuration.cpython-39.pyc,,
astropy/config/__pycache__/paths.cpython-39.pyc,,
astropy/config/affiliated.py,sha256=YcRXqseSH2vGJjkSFONxigcNi9V0Lo0PawFgorWhH34,200
astropy/config/configuration.py,sha256=jZhRmVFeAubi9ADBYqnbGiijgUEQGgjOvXwW-l_2v-A,30602
astropy/config/paths.py,sha256=i_Sr5GEhGI2TkVitzOoUr6UKqyLFNw0dl_3mCByuooE,11099
astropy/config/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/config/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/config/tests/__pycache__/test_configs.cpython-39.pyc,,
astropy/config/tests/data/alias.cfg,sha256=d3h1sePCQFWzcf-6WoVUaf1YHw0Qzh0-4muuuPgV1sM,54
astropy/config/tests/data/deprecated.cfg,sha256=dxQhS8_DMP2gtuvj7vTiN65dWKSkxFD2peCs_8inl24,30
astropy/config/tests/data/empty.cfg,sha256=_GxA6LBV6vpRoY-h1d5TTNLtQny-wSNEQnzC-1MU32M,422
astropy/config/tests/data/not_empty.cfg,sha256=LA_5YRziJ5evBWv4K_iS-C5ABSAk4EuFMMoJdQiYWtQ,420
astropy/config/tests/test_configs.py,sha256=I0rgxdk3rTesvg95bS18FqmGGv5cLtIv_jSBOmJxhu4,15196
astropy/conftest.py,sha256=TfLo6ZpWO0Ou4C378fP7s_OFaDGNmizinmsnIUkqVjw,5061
astropy/constants/__init__.py,sha256=QkgmjxBpQt3A0oDW-9aci1Kiqw3VxpaFdFjFtXFNip4,1619
astropy/constants/__pycache__/__init__.cpython-39.pyc,,
astropy/constants/__pycache__/astropyconst13.cpython-39.pyc,,
astropy/constants/__pycache__/astropyconst20.cpython-39.pyc,,
astropy/constants/__pycache__/astropyconst40.cpython-39.pyc,,
astropy/constants/__pycache__/cgs.cpython-39.pyc,,
astropy/constants/__pycache__/codata2010.cpython-39.pyc,,
astropy/constants/__pycache__/codata2014.cpython-39.pyc,,
astropy/constants/__pycache__/codata2018.cpython-39.pyc,,
astropy/constants/__pycache__/config.cpython-39.pyc,,
astropy/constants/__pycache__/constant.cpython-39.pyc,,
astropy/constants/__pycache__/iau2012.cpython-39.pyc,,
astropy/constants/__pycache__/iau2015.cpython-39.pyc,,
astropy/constants/__pycache__/si.cpython-39.pyc,,
astropy/constants/__pycache__/utils.cpython-39.pyc,,
astropy/constants/astropyconst13.py,sha256=DSSX31L0pu3LLqpjhJkivNWv_VNdn3QhAiqHmmao-H8,483
astropy/constants/astropyconst20.py,sha256=zZfwhJXp7XsMxJVEYgySKqG8cWjjZPrXGF3r2HYAhDE,1863
astropy/constants/astropyconst40.py,sha256=KtpNlUEC1vu3hVrPXqQ2UR1cmuIunJKJjytxaLW0OVk,1863
astropy/constants/cgs.py,sha256=Hka3jjTg2xWsHK_QnfFaVS9oiT7d1q8HW6ZZfJJ10fw,568
astropy/constants/codata2010.py,sha256=O29r9T9J6UYIFtzO9mJPB3YjxMCuczbJAAcra7XVark,3963
astropy/constants/codata2014.py,sha256=GiRntpzanFXD4bECQveTnrX23Mu0pRn1hdYSwY3wiFc,3683
astropy/constants/codata2018.py,sha256=RT5Bom51QNYLdPQ8FPw_-a_1fqcXd10WSd1UGTPtQ1c,4042
astropy/constants/config.py,sha256=1ppbdcFUso9ehFtojuqS4mTD-RH1ZuXB4k8kfegbSBI,547
astropy/constants/constant.py,sha256=QncIsHmBJW6NxIVYRdUa9xAqgOBYzmzoHKzjLj07iqM,8602
astropy/constants/iau2012.py,sha256=KwUudmIjVqT7HXu4F6cPMZHkRafLRF-U2Q6gFVYHlXI,2467
astropy/constants/iau2015.py,sha256=ayMezz81H2-fedmN8_FAr1Yo4Cb94w3ZkyII8po2Ffk,3567
astropy/constants/si.py,sha256=1lnXUL5d9_iWbL9d-Sqc42TYOVcYLkLyzGOXpZ208mQ,548
astropy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/constants/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/constants/tests/__pycache__/test_constant.cpython-39.pyc,,
astropy/constants/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/constants/tests/__pycache__/test_prior_version.cpython-39.pyc,,
astropy/constants/tests/__pycache__/test_sciencestate.cpython-39.pyc,,
astropy/constants/tests/test_constant.py,sha256=qM4McPTrZ8qD4fUglgTZ1kE7HYGM0SJQrOGIYUhfFoQ,4600
astropy/constants/tests/test_pickle.py,sha256=mbhyvKgQBMCeq7-ykZoviTErAfkWiQvlRiEpv60cpVY,562
astropy/constants/tests/test_prior_version.py,sha256=OPkUU7L2CM29wt0oWkKkI8mATQu3MzLlwKuuYFOG0wo,5604
astropy/constants/tests/test_sciencestate.py,sha256=Fx1f0MCIBW-iTf3SYoR3nn-OPtnfmUzrwlLnYalJiyM,606
astropy/constants/utils.py,sha256=GFtaqZHoP0VFMMDgiw0zSaZ7IhV7UI6QmNHQ072DR0k,2412
astropy/convolution/__init__.py,sha256=A1IrPvrKQD4demwCIlual-65hOk7eE8VcValiQg0ASU,418
astropy/convolution/__pycache__/__init__.cpython-39.pyc,,
astropy/convolution/__pycache__/convolve.cpython-39.pyc,,
astropy/convolution/__pycache__/core.cpython-39.pyc,,
astropy/convolution/__pycache__/kernels.cpython-39.pyc,,
astropy/convolution/__pycache__/setup_package.cpython-39.pyc,,
astropy/convolution/__pycache__/utils.cpython-39.pyc,,
astropy/convolution/_convolve.cp39-win_amd64.pyd,sha256=FksWSgE5CsFmV4KZoYIMTwNKIYEFxZI8MHd7tMdXC7s,20480
astropy/convolution/convolve.py,sha256=boRj1hCsXsCUA9wJBSIedBmz5xF2SdNtdlcgBmX2ERQ,46546
astropy/convolution/core.py,sha256=kt-RQ8-J0RIwneYbqQWVxPCYfHxaVeB1kLA48ylKFyY,12126
astropy/convolution/kernels.py,sha256=KLEXX6kCJk1hoKW2fPta5LalKePZPq6fgK2UnmHfHr8,33934
astropy/convolution/setup_package.py,sha256=omNEmwM0BRYNgnpYqA7436cnfkoTsA8rayjz1z6sXew,851
astropy/convolution/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/convolution/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_convolve.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_convolve_fft.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_convolve_kernels.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_convolve_models.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_convolve_nddata.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_convolve_speeds.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_discretize.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_kernel_class.cpython-39.pyc,,
astropy/convolution/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/convolution/tests/test_convolve.py,sha256=DMbKOiqEkUhO4uYny-eWgzvA00FoXvRxfySkFLkLNQg,42812
astropy/convolution/tests/test_convolve_fft.py,sha256=a8o18IxhMlSJOmgi_rFJt5pKI34GsQS42hD5u03vOK8,35509
astropy/convolution/tests/test_convolve_kernels.py,sha256=EAps94X1iyoZwn9wWIlitrZW8XtphXUCVEoru_UQMeM,5072
astropy/convolution/tests/test_convolve_models.py,sha256=Tflyzk7WcbECY8tpZ_iqBtNnhwoJJQZX6NaqLFCu5Lk,3912
astropy/convolution/tests/test_convolve_nddata.py,sha256=qzMHHotz3wBatbukkBTdtz8n9M9ITR-T6pwLepo047I,1761
astropy/convolution/tests/test_convolve_speeds.py,sha256=EiAln50Xe9kvQ6EIB7bgU5cX06PU4enoB4mliI0jff4,11883
astropy/convolution/tests/test_discretize.py,sha256=cZ-2mxvYLVGbCK3ze-uPUYbZeb0Lfq17VNQ5qQ2DW3U,7804
astropy/convolution/tests/test_kernel_class.py,sha256=9w4axbysJE10z6abDUYWnN3MInOg3RQuasIqRQHeDyY,20936
astropy/convolution/tests/test_pickle.py,sha256=34_VOWILN4xIQzSUqJtHV28z2HGQbIaJVwv3d9k_DkQ,972
astropy/convolution/utils.py,sha256=vFfTYyKQ1na-uFhfZ-xgjVSAAqi5l0hPLtcBmnxmlO4,10907
astropy/coordinates/__init__.py,sha256=2Vd01p57KegpRhlp4d2_iLsqM1bqjBPgUTv0TXcoIS8,734
astropy/coordinates/__pycache__/__init__.cpython-39.pyc,,
astropy/coordinates/__pycache__/angle_formats.cpython-39.pyc,,
astropy/coordinates/__pycache__/angle_lextab.cpython-39.pyc,,
astropy/coordinates/__pycache__/angle_parsetab.cpython-39.pyc,,
astropy/coordinates/__pycache__/angle_utilities.cpython-39.pyc,,
astropy/coordinates/__pycache__/angles.cpython-39.pyc,,
astropy/coordinates/__pycache__/attributes.cpython-39.pyc,,
astropy/coordinates/__pycache__/baseframe.cpython-39.pyc,,
astropy/coordinates/__pycache__/calculation.cpython-39.pyc,,
astropy/coordinates/__pycache__/distances.cpython-39.pyc,,
astropy/coordinates/__pycache__/earth.cpython-39.pyc,,
astropy/coordinates/__pycache__/earth_orientation.cpython-39.pyc,,
astropy/coordinates/__pycache__/erfa_astrom.cpython-39.pyc,,
astropy/coordinates/__pycache__/errors.cpython-39.pyc,,
astropy/coordinates/__pycache__/funcs.cpython-39.pyc,,
astropy/coordinates/__pycache__/jparser.cpython-39.pyc,,
astropy/coordinates/__pycache__/matching.cpython-39.pyc,,
astropy/coordinates/__pycache__/matrix_utilities.cpython-39.pyc,,
astropy/coordinates/__pycache__/name_resolve.cpython-39.pyc,,
astropy/coordinates/__pycache__/orbital_elements.cpython-39.pyc,,
astropy/coordinates/__pycache__/representation.cpython-39.pyc,,
astropy/coordinates/__pycache__/sites.cpython-39.pyc,,
astropy/coordinates/__pycache__/sky_coordinate.cpython-39.pyc,,
astropy/coordinates/__pycache__/sky_coordinate_parsers.cpython-39.pyc,,
astropy/coordinates/__pycache__/solar_system.cpython-39.pyc,,
astropy/coordinates/__pycache__/spectral_coordinate.cpython-39.pyc,,
astropy/coordinates/__pycache__/spectral_quantity.cpython-39.pyc,,
astropy/coordinates/__pycache__/transformations.cpython-39.pyc,,
astropy/coordinates/angle_formats.py,sha256=FWPjgq6F8HpUZpU_ZmdjBcEaTl18aOK-M6ne-ARCF9w,19053
astropy/coordinates/angle_lextab.py,sha256=hxIv-OookWmQS3-n6NGlQbvgkMMLdJrQxuD80XrGhFA,3853
astropy/coordinates/angle_parsetab.py,sha256=N6f2aBi6IEQhF5N1RSpmz0B64yoPRwrPrVIVHemM-8o,7516
astropy/coordinates/angle_utilities.py,sha256=bGHtQp2WJuGgeJb5fuBtMjSkQAEc8q_GSGtqMmUrmUU,8798
astropy/coordinates/angles.py,sha256=xKQXkJYHsR0Usdvsy7YZdCD8HoDVzopqcKTHoFDT4fI,26374
astropy/coordinates/attributes.py,sha256=Px-0GJAZUr1FKVdHE3CYnHjTiNA3PuINtloas3uwwuA,18293
astropy/coordinates/baseframe.py,sha256=Q1maN3oOq-wUyYRTTitQlvu2iDvA7ePnrdptV7iSBIc,82610
astropy/coordinates/builtin_frames/__init__.py,sha256=E6HFQ9zvDL2lyJTM3q4wjLUIJOWUYun97tuCFx2QblI,5974
astropy/coordinates/builtin_frames/__pycache__/__init__.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/altaz.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/baseradec.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/cirs.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/cirs_observed_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/ecliptic.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/ecliptic_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/equatorial.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/fk4.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/fk4_fk5_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/fk5.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/galactic.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/galactic_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/galactocentric.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/gcrs.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/hadec.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/hcrs.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/icrs.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/icrs_cirs_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/icrs_fk5_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/icrs_observed_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/intermediate_rotation_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/itrs.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/lsr.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/skyoffset.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/supergalactic.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/supergalactic_transforms.cpython-39.pyc,,
astropy/coordinates/builtin_frames/__pycache__/utils.cpython-39.pyc,,
astropy/coordinates/builtin_frames/altaz.py,sha256=33ww2lw0SaBtuyPK3Q6-0BfSN41VusWxbvyrFj61fyI,5451
astropy/coordinates/builtin_frames/baseradec.py,sha256=Y2MI6JD4OTe1TZFfPuK72jCa2sRfkMrLtCuzg-mv8e8,2022
astropy/coordinates/builtin_frames/cirs.py,sha256=1E1WAqwZZ4PLhNh9PbThv_oWCL6TBsOYCql1zLE2Ayo,1492
astropy/coordinates/builtin_frames/cirs_observed_transforms.py,sha256=52nuMCqaxNDTYG2MyPte04ZUQ60l_75KKfFwectQF-0,3973
astropy/coordinates/builtin_frames/ecliptic.py,sha256=DXHD05PW1eSoMs8MykupcpKNE-3DftU-Bui-drGwOE8,9444
astropy/coordinates/builtin_frames/ecliptic_transforms.py,sha256=P26NzLIZxnhjtEqnrxw8jedeSI5OTOSd2wQw2ytokEk,11491
astropy/coordinates/builtin_frames/equatorial.py,sha256=FcOfsbClfj-OYWetah19TbK2yKF4-mtTjbKj1piXRsQ,4696
astropy/coordinates/builtin_frames/fk4.py,sha256=p8RLr0nq73P-s4qbRCG4V0ty_e4kNLzNPwA8vDdXSD4,7249
astropy/coordinates/builtin_frames/fk4_fk5_transforms.py,sha256=lXHKYE6LfDVMrT8ICcNsTNYtxzrn8jebjtcDcJgVUbY,2700
astropy/coordinates/builtin_frames/fk5.py,sha256=hsExBtXDycHjUhYz0yU1xMJ_vxraBB8wDQLrYREZDjE,2022
astropy/coordinates/builtin_frames/galactic.py,sha256=xf3yifxQk_m_fe-euEfStU6xNFAD-zyk4Q9FlJrYMTU,4077
astropy/coordinates/builtin_frames/galactic_transforms.py,sha256=F16FN_fxTfFmD2k5mAp5VfX-HPqrwWmOpHuUHVnx6GI,1921
astropy/coordinates/builtin_frames/galactocentric.py,sha256=kd-4ryDwPtoNXxeMo1KzuJCWY5QVe5JWdWNNlxKEpuY,25499
astropy/coordinates/builtin_frames/gcrs.py,sha256=S2GTKAK8riMPMwsUR_Wdcs8mZOLIvfv2BIeg5Qigil0,4939
astropy/coordinates/builtin_frames/hadec.py,sha256=6sHq_1od43zOif8bHHND_sMHxIH4QwRc7MmsffCZM8E,5597
astropy/coordinates/builtin_frames/hcrs.py,sha256=Z9eBdDFEA9GGQ6TMrqB-KWEmuaLASlQLWk4OJEjt7Xc,1564
astropy/coordinates/builtin_frames/icrs.py,sha256=s5r_73Lr1PefERfdqr5SPqrntaK_DPY2-mAbsHUf1KI,916
astropy/coordinates/builtin_frames/icrs_cirs_transforms.py,sha256=Hf1uTfuyR-c_QylTkkyEIXVr1ZsdsWf6aNJiYpWBDGk,11827
astropy/coordinates/builtin_frames/icrs_fk5_transforms.py,sha256=wdFtzuSQ19YBf-uJOa_hv5IsV-_d8W869k6xWP5viS8,1618
astropy/coordinates/builtin_frames/icrs_observed_transforms.py,sha256=WI642CGgOLq2T4bBv_MWPgpMvQJhSFa4h7vds5xEinY,4840
astropy/coordinates/builtin_frames/intermediate_rotation_transforms.py,sha256=s958veNjq9JhAWGhh5e5uHcHe2bZhkowBrr9W6CY54I,11902
astropy/coordinates/builtin_frames/itrs.py,sha256=zmyYpSMt7VgB-4OiqU-afpebX7tkP3EUKyWGzwzA3Ks,1479
astropy/coordinates/builtin_frames/lsr.py,sha256=3Psk83OJfayFkQIeGK1bMtH0Nj12RRX2Aa_Gy6HZGcA,11523
astropy/coordinates/builtin_frames/skyoffset.py,sha256=TQSiOhU8adDIjQLNFsXgD91dDPxY_4G6uJmaPeS3dHQ,8009
astropy/coordinates/builtin_frames/supergalactic.py,sha256=E1FrNn3fUqS4scoOt8x6D_o_ayQqU3cObDrwLPdIjKA,2582
astropy/coordinates/builtin_frames/supergalactic_transforms.py,sha256=uJqxNS_kbJcT1lJYqi48Sf95txA28wjJzi5IxpNDufU,973
astropy/coordinates/builtin_frames/utils.py,sha256=m9OyFBmXRho5mxUwJCXNM622JLAQD67IJ-zyxw6Uj2I,14550
astropy/coordinates/calculation.py,sha256=j6_wcjlW3MyxvakIWz5fKoHLVOUo40mK-zsFU5kxqhA,7002
astropy/coordinates/data/constellation_data_roman87.dat,sha256=PmRuPaf93DSuxj3igotZiQ8E9jLcOCBCJZYRzQ9jlBw,10799
astropy/coordinates/data/constellation_names.dat,sha256=wRuXg8Jg-fkZ-w4uDpfBA_u5nnwHSXObgGUd5IkU-Yk,1229
astropy/coordinates/data/sites.json,sha256=DhOM8whW4beIxxLPOG4C816rT-dtChFKzgKHWUL1M9A,425
astropy/coordinates/distances.py,sha256=g6zBPOl9M8zTfnw45xbV7ICm85MR-brodRuLj0K6n90,9666
astropy/coordinates/earth.py,sha256=3MOLzSyaCIBq3C4NLA2gwkVr010dIQDlgd5L6UxDreM,37539
astropy/coordinates/earth_orientation.py,sha256=I57V1N2hsoUH3HkEimHlJdYemHSeqlf0su126f6mH88,13939
astropy/coordinates/erfa_astrom.py,sha256=mNJjLbFz3fd5OIZ5S497IH6ZDiGlzF_kvJNaZyJ5J5Q,15054
astropy/coordinates/errors.py,sha256=2pRkVVQLR_GMniURcJb5RBiQjXTl76pMPQoVTR9vlWk,4500
astropy/coordinates/funcs.py,sha256=NDeslCYXV0tMUd-yw_CygLcfbPRozuiA2cSXVT7fiCQ,13745
astropy/coordinates/jparser.py,sha256=BWbFOgDIk2yTQFhMYvbP86Wtv4FRLYx2XqkAql2zfKA,1843
astropy/coordinates/matching.py,sha256=634rONXyvk1c7EofsaF4mINfnPoGYAix3Fyoqg6k_1E,20767
astropy/coordinates/matrix_utilities.py,sha256=SBnp3rmvt8-Z2OzVVxviLRgGxEwbLByBR2rP62-K70A,6778
astropy/coordinates/name_resolve.py,sha256=ihWqoAYTnqAx_5mjmBKlp0YVe6yNCwC_4a-aXiCt6F4,6812
astropy/coordinates/orbital_elements.py,sha256=gPaqbMcfNu2ehCwfGaPM96N-tm0E1WxbsJ8iCCoEZGU,7889
astropy/coordinates/representation.py,sha256=kfM_mk86RoQgrsdryVTzaZJodemlEShQTFESakFjZ8M,142271
astropy/coordinates/sites.py,sha256=K17udb69FP7POwOsu8lVAG60f9FeQ_XbV2PLW0i5fyQ,4993
astropy/coordinates/sky_coordinate.py,sha256=mYufDnyAa7l1Ssvs_aMDPDKkcuh7lvVtDcN7BeBmVCE,91720
astropy/coordinates/sky_coordinate_parsers.py,sha256=4_slGuaVx7OzqkMX_BcHeTsXrnGkKTWkOwO65UoxOH4,30300
astropy/coordinates/solar_system.py,sha256=anp646868mHgtumT0_kE4vO-y8gISxy9gbQSoRjEmgw,20323
astropy/coordinates/spectral_coordinate.py,sha256=XCGulK2VV1SoDp442GHXPb4W295u23hDYs7yiwiZRSU,31856
astropy/coordinates/spectral_quantity.py,sha256=pvmKxnpyle26r4XW34F_eEORs4TMHMYHEPukT1FfEgU,12122
astropy/coordinates/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/coordinates/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_angle_generators.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_angles.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_angular_separation.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_api_ape5.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_arrays.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_atc_replacements.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_celestial_transformations.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_distance.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_earth.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_erfa_astrom.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_finite_difference_velocities.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_formatting.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_frames.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_frames_with_velocity.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_funcs.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_geodetic_representations.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_iau_fullstack.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_icrs_observed_transformations.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_intermediate_transformations.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_matching.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_matrix_utilities.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_name_resolve.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_regression.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_representation.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_representation_arithmetic.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_representation_methods.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_shape_manipulation.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_sites.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_sky_coord.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_sky_coord_velocities.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_skyoffset_transformations.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_solar_system.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_spectral_coordinate.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_spectral_quantity.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_transformations.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_unit_representation.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/coordinates/tests/__pycache__/test_velocity_corrs.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__init__.py,sha256=0OBqQZrhJA1Eb8tcTelxQJcAVodACtxmvvx45TNIOno,324
astropy/coordinates/tests/accuracy/__pycache__/__init__.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/generate_ref_ast.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/generate_spectralcoord_ref.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/test_altaz_icrs.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/test_ecliptic.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/test_fk4_no_e_fk4.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/test_fk4_no_e_fk5.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/test_galactic_fk4.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/__pycache__/test_icrs_fk5.cpython-39.pyc,,
astropy/coordinates/tests/accuracy/data/fk4_no_e_fk4.csv,sha256=r81QZ_IN5dfLqYRkQ8uFvjpmFotdQviad-LAVMOTl-g,18969
astropy/coordinates/tests/accuracy/data/fk4_no_e_fk5.csv,sha256=ACTkFRdOch9uP73pd6mZ0mqJuXUE8r1otWEkS25mOaA,22596
astropy/coordinates/tests/accuracy/data/galactic_fk4.csv,sha256=rAjS7n0TPg15Nl7AMR603-um5NEv7Yqh1ReaGHhHkoc,20802
astropy/coordinates/tests/accuracy/data/icrs_fk5.csv,sha256=OamfG9ZtrgHmtuH61BhkGswePk6M_6NeyXIjs7hGw4M,20793
astropy/coordinates/tests/accuracy/data/rv.ecsv,sha256=yrJs0BtEYt-dwt8MHJsOVsV6x_hABVtRJgUZ5uxjxxc,8490
astropy/coordinates/tests/accuracy/generate_ref_ast.py,sha256=WcOPvlWbNjrqGz8TV9pDMFcg85k5I2-7bNQl4DTvtis,9464
astropy/coordinates/tests/accuracy/generate_spectralcoord_ref.py,sha256=qEPzQi2JN6MP6n95Q6YOeQN5D6TFDYYO82WjarDK8fM,3343
astropy/coordinates/tests/accuracy/test_altaz_icrs.py,sha256=E6-oRD3D2rcJOZNHiyPfkViewZIcKtolXVrko7apyk0,8839
astropy/coordinates/tests/accuracy/test_ecliptic.py,sha256=Z_71onWjlecn0FWTRbndSz-CrOGwEQpN6MJrAaUTBZY,10414
astropy/coordinates/tests/accuracy/test_fk4_no_e_fk4.py,sha256=nMxCdbXPDx0vTsqaA6_Lek9CVt8VJo4uqQ4g2z6zORs,2076
astropy/coordinates/tests/accuracy/test_fk4_no_e_fk5.py,sha256=kKKLE5NCwd_kUujESNDDQQKcOUe_RQS4R3gFny7sz1E,2148
astropy/coordinates/tests/accuracy/test_galactic_fk4.py,sha256=YG0kM7pdNWcG-NZNdjt4WYnCFELnyQPXqo42rtVBFO4,1941
astropy/coordinates/tests/accuracy/test_icrs_fk5.py,sha256=-BYXBJt4M5gjPhgpnc-qGKNvMDJCQmlojvgAC-s-WpI,1874
astropy/coordinates/tests/test_angle_generators.py,sha256=V2E87VS8lBBeWTGa0HMyHsjsFWO4cwVgKD85Afg8dCA,1138
astropy/coordinates/tests/test_angles.py,sha256=_oyylAaxI_V8kiOOd0TJf9NcLarV34Epeu_vhVupgho,35860
astropy/coordinates/tests/test_angular_separation.py,sha256=X2nk6MECDUC3IuJBhx6SzOUDhL7goyQ-BLOTiO6i2Wc,3190
astropy/coordinates/tests/test_api_ape5.py,sha256=yh1B4Hq20GrG_W6nhIcUDRLG5o2ldhne8nJ10W4k-EM,20855
astropy/coordinates/tests/test_arrays.py,sha256=kCkSYFlW2lh2Q9F159fikzC1gmKZCtKR3Fqir_OTXrs,8855
astropy/coordinates/tests/test_atc_replacements.py,sha256=9_AEML1ulrwqUlxXNaPyqum2OGQkzEiO4A2wuux61nI,1189
astropy/coordinates/tests/test_celestial_transformations.py,sha256=PX6QJKZBQXfcckVzFXWf_7mmfNcDnp7a8YQJcTnzoWE,15657
astropy/coordinates/tests/test_distance.py,sha256=95JptfyFRkqxTU2I0TsXMd8R68CBzsnztjSmsS5P3Lk,9392
astropy/coordinates/tests/test_earth.py,sha256=3gTvSPtR0QAvLC0hyn7nvfPkJgHiCKeIQX-0z2TMZug,17248
astropy/coordinates/tests/test_erfa_astrom.py,sha256=E2cZrwPL_vqvaRkEFwLySOCickfVsDrrSWFnLbWLwQQ,4172
astropy/coordinates/tests/test_finite_difference_velocities.py,sha256=YtXySXoB2-txzM6PQ4_KGJb0BT6yfRKVYZn5j2kPA0Q,9762
astropy/coordinates/tests/test_formatting.py,sha256=fLSR_dlrpO34B4zJ5ySbGyq5IcTqgE_Z4D52m8qDLzI,4826
astropy/coordinates/tests/test_frames.py,sha256=-sItALF-vC8NH635rxUMtDdYhZJ1fn85at-ADF8OFNw,51812
astropy/coordinates/tests/test_frames_with_velocity.py,sha256=ciMJQgmPlQ15V547QKxtSma2tBW7YVtNVKy1by48eEo,14286
astropy/coordinates/tests/test_funcs.py,sha256=ZcjrZQgrQaP_79MetmzeLdUzmhytVaUQhfm9rBlq1pY,6110
astropy/coordinates/tests/test_geodetic_representations.py,sha256=7KA9-re67U7DIEc60vJdoeV8ToGhiwLm49We3i_F75w,4556
astropy/coordinates/tests/test_iau_fullstack.py,sha256=m0Y-r_yd7DR8r6cdr_sBTU-nWkMon_0PJrD-ANYkiX4,7559
astropy/coordinates/tests/test_icrs_observed_transformations.py,sha256=XlSwkTfvv9ylOAFE1_jRI4EAQe7EIVlj7nntawyapB4,2896
astropy/coordinates/tests/test_intermediate_transformations.py,sha256=Ft9bSoYptuUBnWmi9d2mDarwyOVuW5QDkbzgYwNkWVw,38779
astropy/coordinates/tests/test_matching.py,sha256=o_hrXNavFSgfWAWI4qkCa5r7LME-Shy0E5KRlNGuFcE,13489
astropy/coordinates/tests/test_matrix_utilities.py,sha256=x7plX-a1UfpiZsOPGYgukHGefMppT0teZdNQk9Oqw9w,3516
astropy/coordinates/tests/test_name_resolve.py,sha256=eUXKYi30BFykZPnpq2L_k7WeuBe1fcZdRhUYDgZ4Qcs,6272
astropy/coordinates/tests/test_pickle.py,sha256=eJaGMav7UYLb8yDEqk0WydXuvOGJ677QTvGDT5uKTbs,1789
astropy/coordinates/tests/test_regression.py,sha256=IBXPVB1-kR24PxysmziHY_luwiEXppHS9YkREAIkvkw,26652
astropy/coordinates/tests/test_representation.py,sha256=lKOV1FMzTfJgJykkrtDPID168eXGwxw66SyJ7BE4Qfs,86227
astropy/coordinates/tests/test_representation_arithmetic.py,sha256=B2rK0F3_U-KjZseP9cO9wQciqtGIg-7a72ol6zD6O_w,61560
astropy/coordinates/tests/test_representation_methods.py,sha256=RGioj78o4m5xvUuJsGuYhKikIlEfzldwfc_Xy5p7wxQ,17325
astropy/coordinates/tests/test_shape_manipulation.py,sha256=WC7e2Xc-***********************************,18778
astropy/coordinates/tests/test_sites.py,sha256=JWFC6Jo9mV2brlkZloE17zGn9MATv7hnvsNtDpyIURY,6345
astropy/coordinates/tests/test_sky_coord.py,sha256=vDC99TT-c2N_uZfc3HAhxlohyAFBMMD-oVSh0L7PXIw,73760
astropy/coordinates/tests/test_sky_coord_velocities.py,sha256=qMq0Tfq7dPZN8BepaJgxdsi-we-ynMQ7OeL0kr99xu8,9567
astropy/coordinates/tests/test_skyoffset_transformations.py,sha256=Xzta4s7OkBLrpwz-XwdRo3SZNJeUpJMg4fVToN2zOQw,14857
astropy/coordinates/tests/test_solar_system.py,sha256=0OG6_f-opzmrKt_z91Z0ZLTjEo8kndFgSLT_HY31aWQ,20114
astropy/coordinates/tests/test_spectral_coordinate.py,sha256=iiZRafoZvjjDdnoHyV7mUwHFlffAVfrRPWv3iOvkTho,40550
astropy/coordinates/tests/test_spectral_quantity.py,sha256=cTuqBcM0B7QgPb4uRqfyqyYGOKPFpH97TTKlu77vlTM,9224
astropy/coordinates/tests/test_transformations.py,sha256=nMc1ip5ETHGe2bP3H7YHFNDJe8_DgVhFlQtfcQEeMSY,23450
astropy/coordinates/tests/test_unit_representation.py,sha256=KOpjdEC0ZJ9e546K_MeYYc3tZx8IuD4Xy_K0I7FJX08,3135
astropy/coordinates/tests/test_utils.py,sha256=QIJnAEs2PsSFgX1I3Z5Nf3fSxXvrukbdfXnHQViLjCg,1553
astropy/coordinates/tests/test_velocity_corrs.py,sha256=BzLr9M4aXLLmfVNjPQ0mX8sNLtzqPRNe-rX079h5zEU,22675
astropy/coordinates/transformations.py,sha256=xcWVKgYLE3R21zKlyPIUaK9B7zfiCFltkLyfe-PIPGY,66382
astropy/cosmology/__init__.py,sha256=WSp5G8lHqP2bwA731HwOrMskiNLX2DtXCOtgEoU8fT8,853
astropy/cosmology/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/__pycache__/connect.cpython-39.pyc,,
astropy/cosmology/__pycache__/core.cpython-39.pyc,,
astropy/cosmology/__pycache__/flrw.cpython-39.pyc,,
astropy/cosmology/__pycache__/funcs.cpython-39.pyc,,
astropy/cosmology/__pycache__/parameter.cpython-39.pyc,,
astropy/cosmology/__pycache__/parameters.cpython-39.pyc,,
astropy/cosmology/__pycache__/realizations.cpython-39.pyc,,
astropy/cosmology/__pycache__/units.cpython-39.pyc,,
astropy/cosmology/__pycache__/utils.cpython-39.pyc,,
astropy/cosmology/connect.py,sha256=4VypKEQCBz7-Om4d07kZced2CXCskIWDWRpk6dGvLl4,8783
astropy/cosmology/core.py,sha256=Cl2HuFdaMvI1bp9FUPcIb1GiF4xGixIetQoCs9G9bP4,12997
astropy/cosmology/flrw.py,sha256=qu20ykH9W0A3zrs8V8DX-e849TkZU8SlZfB2NNEFTCQ,113801
astropy/cosmology/funcs.py,sha256=w7xAopxh63kOyAwDZMd9-DK69k9y8jqegcbk_QgXsSo,15816
astropy/cosmology/io/__init__.py,sha256=-9_dTwsOJnZ-wU_zeAFMVdxnj3c3y-yKQjomCIPYjco,274
astropy/cosmology/io/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/io/__pycache__/ecsv.cpython-39.pyc,,
astropy/cosmology/io/__pycache__/mapping.cpython-39.pyc,,
astropy/cosmology/io/__pycache__/model.cpython-39.pyc,,
astropy/cosmology/io/__pycache__/table.cpython-39.pyc,,
astropy/cosmology/io/ecsv.py,sha256=GQ0u7Pw43AYSE4873KBYTmqBuXId11QGUd0pwhSgN8U,3766
astropy/cosmology/io/mapping.py,sha256=0EbvESFmxiKhnObagVSCyNs72GHSwGZ-Lrq7RBjrTNk,7321
astropy/cosmology/io/model.py,sha256=sbXNAYOcD84nHTw80WYO8eVrcdzDVf9RnpcQ9aK2TYw,9473
astropy/cosmology/io/table.py,sha256=awE6pOoOZTkV4320IEpoZ9FIzZo1bNrMr1dHe_ki3aU,10711
astropy/cosmology/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/cosmology/io/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/io/tests/__pycache__/base.cpython-39.pyc,,
astropy/cosmology/io/tests/__pycache__/test_ecsv.cpython-39.pyc,,
astropy/cosmology/io/tests/__pycache__/test_mapping.cpython-39.pyc,,
astropy/cosmology/io/tests/__pycache__/test_model.cpython-39.pyc,,
astropy/cosmology/io/tests/__pycache__/test_table.cpython-39.pyc,,
astropy/cosmology/io/tests/base.py,sha256=uT5EzoVFu8Ft3ZahtUsL_lYX0B_wlK-kQ0T-78kcDyk,4000
astropy/cosmology/io/tests/test_ecsv.py,sha256=n-D72G6xAKDoG_4a1loCZ0D_1dqo_ox0uoERWIO1r4g,8057
astropy/cosmology/io/tests/test_mapping.py,sha256=g4cnfqjnGrPEJKdOkKRrVzpmsIfqmhmi5LHe4FP6hvI,4844
astropy/cosmology/io/tests/test_model.py,sha256=w1t-uS_I3Zzh45iKyrxvb5-u7QcyzsiD5B3ZoTpWL-g,6422
astropy/cosmology/io/tests/test_table.py,sha256=W40doiKRib13FIQrtgaJWXyJkoTANutKDwLeYcmPEPM,7591
astropy/cosmology/parameter.py,sha256=4-eKyZJRbXwyJMFA3rrbDEtvnfW9V9aWwtrrFonEofE,8445
astropy/cosmology/parameters.py,sha256=qP3CxbaDXyCHkAH6kU4YHMSxNiacrnXbBbq259oDPEg,8174
astropy/cosmology/realizations.py,sha256=GilN9px11Qn9BkZ2CUFJDztHfKjPD6Tj2UnDG9K8Bzg,2848
astropy/cosmology/scalar_inv_efuncs.cp39-win_amd64.pyd,sha256=3HWHmHSbtiSaLqEqECbKf6kOJeY-juORZo_fjJRwEoU,99840
astropy/cosmology/tests/__init__.py,sha256=ojFAUS7iqeVhfsZDa7Tr-S8OYHG5OyUfgtGlUpRJUoI,132
astropy/cosmology/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/conftest.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_connect.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_core.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_cosmology.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_flrw.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_funcs.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_parameter.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_realizations.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_units.cpython-39.pyc,,
astropy/cosmology/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/cosmology/tests/conftest.py,sha256=0Uid5vyYhV7Ab75-tkWzYnRwHpKMcTG-_OjV-dAf3nI,3142
astropy/cosmology/tests/mypackage/__init__.py,sha256=Nxa0OOsvBSTtXR9w8lnDQxcmeUqSmgkh4oH4WxQZ3mE,28
astropy/cosmology/tests/mypackage/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/__pycache__/cosmology.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/cosmology.py,sha256=gIAWiH9zfTmJx1zyWN5LKr-lBCezqY_7jaFa771S3cQ,1334
astropy/cosmology/tests/mypackage/io/__init__.py,sha256=VlR_Z8y-IMWrrkBTMKMtOfcnL_3MOlNmy1kMPdzzpQ8,805
astropy/cosmology/tests/mypackage/io/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/__pycache__/astropy_convert.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/__pycache__/astropy_io.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/__pycache__/core.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/astropy_convert.py,sha256=vFKCuuQf8Rp_IDw02KRUZSzdj49Fd8dhPHH0DN1YTMY,4424
astropy/cosmology/tests/mypackage/io/astropy_io.py,sha256=LQZNyro-08Y3gVUYQLL_hUXx0aUr7ToHs_WL4__q0dI,2729
astropy/cosmology/tests/mypackage/io/core.py,sha256=HfafSA09Pkx7hux1UYEtQ7h5q7P8O0tUMwcKDXqE8j4,1731
astropy/cosmology/tests/mypackage/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/cosmology/tests/mypackage/io/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/tests/__pycache__/conftest.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/tests/__pycache__/test_astropy_convert.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/tests/__pycache__/test_astropy_io.cpython-39.pyc,,
astropy/cosmology/tests/mypackage/io/tests/conftest.py,sha256=I9QI5omp1ufNnvuXk6Leh89V4Q_wTjJBlFCj70jQXiI,874
astropy/cosmology/tests/mypackage/io/tests/test_astropy_convert.py,sha256=z6ahzcsc7mR8hTyJuO9h-Z4PgS_-6fUZaaF-ZLzVZ48,2676
astropy/cosmology/tests/mypackage/io/tests/test_astropy_io.py,sha256=V7-0YhJsv8Xi2WdDa60nCqwQoyeq0Ps8088U4xClTMs,3223
astropy/cosmology/tests/test_connect.py,sha256=Kvgpg02t0arQSC4Kf7MLLHt1OMHJyZGyigWV-4EL1-g,11165
astropy/cosmology/tests/test_core.py,sha256=qWfo6w5eY8rK3yTlI2pTaeksOBDd8omuAHwA80rPjfc,11465
astropy/cosmology/tests/test_cosmology.py,sha256=g7qMB33v6PghXYpeB2pPx1yLIVsYq-GdE1YOKZ0I2lU,63517
astropy/cosmology/tests/test_flrw.py,sha256=t8HBdMoF3vVQDo5vUIfZrWAvO4g0y8aJH_Tz_XHmBMI,22881
astropy/cosmology/tests/test_funcs.py,sha256=7o9ULmiXMF0dkfr4bIW695IaND-HOXABW4BMAyprj_M,11617
astropy/cosmology/tests/test_parameter.py,sha256=rYi4NKRUJ83IyKsjusZJU3tox4T_79U8373nQWYI9cQ,16450
astropy/cosmology/tests/test_pickle.py,sha256=OAjxfxPOTAF6husPcvkx7U4oBN-SdMrXWK1iDYnM2zU,1061
astropy/cosmology/tests/test_realizations.py,sha256=Tf3uKWjJueLg04d-KkZea4W_2fRHNNabHn4Uk7T9IS0,2052
astropy/cosmology/tests/test_units.py,sha256=t0dfTsbosRlyXg36XwlZOiZSrA0MWUm4aSKK4kNnsOs,19044
astropy/cosmology/tests/test_utils.py,sha256=qvaprvSCDLQKGSR17Z3Y-L_-wQrhllWnVBAHb7Vkp38,2328
astropy/cosmology/units.py,sha256=clyJA7g8lKdmzqLH9mYVezVHyOrEF-wQ2Tp1dzAWYpg,12970
astropy/cosmology/utils.py,sha256=DPaX-eE9RssZ7C_O_K61-peDpEODpKP2-EAQ9rWgmd0,4441
astropy/extern/__init__.py,sha256=FEc8xZMs4AvIUQhHhDq6dm_MmRLOWdgD8Oq-ZpK0oOk,457
astropy/extern/__pycache__/__init__.cpython-39.pyc,,
astropy/extern/__pycache__/_strptime.cpython-39.pyc,,
astropy/extern/_strptime.py,sha256=8Z_-N2xl9AyEpLF2eYJJDGcVYXlj5hBvdRhdbAWOa-8,22516
astropy/extern/configobj/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/extern/configobj/__pycache__/__init__.cpython-39.pyc,,
astropy/extern/configobj/__pycache__/configobj.cpython-39.pyc,,
astropy/extern/configobj/__pycache__/validate.cpython-39.pyc,,
astropy/extern/configobj/configobj.py,sha256=S7291wdvgsNkP8RtX2WXE5SWebA_QzMGLnoJR9rEoJQ,87652
astropy/extern/configobj/validate.py,sha256=bnQJMr5RthplMsHCNpmEjTuKI6IgBzF7kIcNeGuSxcI,46672
astropy/extern/jquery/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/extern/jquery/__pycache__/__init__.cpython-39.pyc,,
astropy/extern/jquery/data/css/jquery.dataTables.css,sha256=2wizTcUFbgPcr14dXfEXtY-1JPRLMYRabytFJ50j5uw,15423
astropy/extern/jquery/data/js/jquery-3.1.1.js,sha256=16cdPddA6VdVInumRGo6IbivbERE8p7CQR3HzTBuELA,267194
astropy/extern/jquery/data/js/jquery-3.1.1.min.js,sha256=hVVnYaiADRTO2PzUGmuLJr8BLUSjGIZsDYGmIJLv2b8,86709
astropy/extern/jquery/data/js/jquery.dataTables.js,sha256=lk-4-kSE2NTrFM0hFjmtpZ9BVKvZ1LKBvpK2ytmBm_0,447282
astropy/extern/jquery/data/js/jquery.dataTables.min.js,sha256=TX6POJQ2u5_aJmHTJ_XUL5vWCbuOw0AQdgUEzk4vYMc,82638
astropy/extern/ply/__init__.py,sha256=sx6iBIF__WKIeU0iw2WSoSqBhclHF5EhBTc0wDigTV8,103
astropy/extern/ply/__pycache__/__init__.cpython-39.pyc,,
astropy/extern/ply/__pycache__/cpp.cpython-39.pyc,,
astropy/extern/ply/__pycache__/ctokens.cpython-39.pyc,,
astropy/extern/ply/__pycache__/lex.cpython-39.pyc,,
astropy/extern/ply/__pycache__/yacc.cpython-39.pyc,,
astropy/extern/ply/__pycache__/ygen.cpython-39.pyc,,
astropy/extern/ply/cpp.py,sha256=KTg13R5SKeicwZm7bIPL44KcQBRcHsmeEGOwIBVvLko,33639
astropy/extern/ply/ctokens.py,sha256=GmyWYDY9nl6F1WJQ9rmcQFgh1FnADFlnp_TBjTcEsqU,3155
astropy/extern/ply/lex.py,sha256=babRISnIAfzHo7WqLYF2qGCSaH0btM8d3ztgHaK3SA0,42905
astropy/extern/ply/yacc.py,sha256=EF043rIHrXJYG6jcb15TI2SLwdCoNOQZXCN_1M3-I4k,137736
astropy/extern/ply/ygen.py,sha256=TRnkZgx5BBB43Qspu2J4gVtpeBut8xrTEZoLbNN0b6M,2246
astropy/io/__init__.py,sha256=qWiybQoOBpkfdNNZJVrwrDVxXOLSoVLOTL4N5f1MDFw,180
astropy/io/__pycache__/__init__.cpython-39.pyc,,
astropy/io/ascii/__init__.py,sha256=-avRP4AxjuuFcOiqI7n0dfHZ9CO3clyjSmqBSss6fn4,1621
astropy/io/ascii/__pycache__/__init__.cpython-39.pyc,,
astropy/io/ascii/__pycache__/basic.cpython-39.pyc,,
astropy/io/ascii/__pycache__/cds.cpython-39.pyc,,
astropy/io/ascii/__pycache__/connect.cpython-39.pyc,,
astropy/io/ascii/__pycache__/core.cpython-39.pyc,,
astropy/io/ascii/__pycache__/daophot.cpython-39.pyc,,
astropy/io/ascii/__pycache__/docs.cpython-39.pyc,,
astropy/io/ascii/__pycache__/ecsv.cpython-39.pyc,,
astropy/io/ascii/__pycache__/fastbasic.cpython-39.pyc,,
astropy/io/ascii/__pycache__/fixedwidth.cpython-39.pyc,,
astropy/io/ascii/__pycache__/html.cpython-39.pyc,,
astropy/io/ascii/__pycache__/ipac.cpython-39.pyc,,
astropy/io/ascii/__pycache__/latex.cpython-39.pyc,,
astropy/io/ascii/__pycache__/misc.cpython-39.pyc,,
astropy/io/ascii/__pycache__/mrt.cpython-39.pyc,,
astropy/io/ascii/__pycache__/qdp.cpython-39.pyc,,
astropy/io/ascii/__pycache__/rst.cpython-39.pyc,,
astropy/io/ascii/__pycache__/setup_package.cpython-39.pyc,,
astropy/io/ascii/__pycache__/sextractor.cpython-39.pyc,,
astropy/io/ascii/__pycache__/ui.cpython-39.pyc,,
astropy/io/ascii/basic.py,sha256=lwStVtKrCDhFnuNMIzbkoKym3umJQtq7Kmt_4zmWoHA,11025
astropy/io/ascii/cds.py,sha256=ryh3qHvklFe1LqZZ8HDZxSDW8V5p7MnDEhbGtznECSY,14483
astropy/io/ascii/connect.py,sha256=Na6BDXZsszpLK7RY2HbZ5HIh8g5lE5YHwjmDCHmc-Pk,1740
astropy/io/ascii/core.py,sha256=H7yc6wWULntCLXyYm30s94i_UiepCqFUYNLkXB-XnSw,63966
astropy/io/ascii/cparser.cp39-win_amd64.pyd,sha256=UUekUhklI7CmL7rv8aLL-5SGo42Yjfk7PR2DNqXyXHA,243712
astropy/io/ascii/daophot.py,sha256=AXaoT5z5LF1wmAwaTHeuga8HkBVMIR2hAZyMGM6ZjLY,14877
astropy/io/ascii/docs.py,sha256=BRQ69x8cMdTZx8TJ8NtweBHEiZHTEdPifBtXWliu7dE,7417
astropy/io/ascii/ecsv.py,sha256=dALXk5biPocyX7QzbppP6c508vpGEFehYHDTTtKRH64,19892
astropy/io/ascii/fastbasic.py,sha256=Bh_jQ7It28XRgQDhWVQwvrUTrrIoJq2B1c2_vgTKRhw,16348
astropy/io/ascii/fixedwidth.py,sha256=lXxS3NCDFefOgJOAlij-L_Gu7kF2Xmxje9Iu1-UxE5M,15496
astropy/io/ascii/html.py,sha256=Nbrm0B3x5kcYYku-YN4RrcFDXZC_N0adYJvkSQViwc8,17668
astropy/io/ascii/ipac.py,sha256=erj3P8FCVMPwI5KYQ1achubGwCPjCJuWkbCw4IV8uh4,21101
astropy/io/ascii/latex.py,sha256=Urd41113X-HylWL_I2mqKHEnMOyCI95t6LSfQbQcsyA,16923
astropy/io/ascii/misc.py,sha256=NuxoPOzcfIw_ujVBTKNx6HXZ35fuO1kssTVhnGRl768,4135
astropy/io/ascii/mrt.py,sha256=ymhtwjjbIyEy67Pt6WzGFQABxgexRPxCKPgBQCLGMi4,27998
astropy/io/ascii/qdp.py,sha256=AIMGHrjAaY6xreKer997WYrnNGG52jUw7co2vmNwpgU,20221
astropy/io/ascii/rst.py,sha256=YMObl2tpat1DXKSAtBKurQ-HnwAclUvJoAQR5uyLTAg,1704
astropy/io/ascii/setup_package.py,sha256=gfwirbWPKiaGnUK90tP5WCLr-x12-_myj3Cv1LaiuYw,446
astropy/io/ascii/sextractor.py,sha256=ZWQk5ROqzo5k7Xt8SJHDSwWXhPb1a04uAMgYy_8bmFQ,6345
astropy/io/ascii/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/io/ascii/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/common.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_c_reader.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_cds.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_cds_header_from_readme.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_compressed.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_connect.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_ecsv.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_fixedwidth.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_html.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_ipac_definitions.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_qdp.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_read.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_rst.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_types.cpython-39.pyc,,
astropy/io/ascii/tests/__pycache__/test_write.cpython-39.pyc,,
astropy/io/ascii/tests/common.py,sha256=ck8HAqG9vm0V4-RHoa0Yb-_ITtdDJrm5_FS8iHRHNQ4,2716
astropy/io/ascii/tests/data/apostrophe.rdb,sha256=rs9u8uv6jIkg0rHfB-dLszXTLf3JBGikepZViq4tqGE,92
astropy/io/ascii/tests/data/apostrophe.tab,sha256=VFjQLMZoD4tpqxDw7pPMxW6IevMWLXFjQhg4p7eJYFk,49
astropy/io/ascii/tests/data/bad.txt,sha256=jst6xQ1h2dE_ImcQzp7tDdfdDJCMNHwxq7JeyX43j2Y,153
astropy/io/ascii/tests/data/bars_at_ends.txt,sha256=aU4_ZBLKI5DnEOiiWBOu7SvtvCDKgteVFNqE0qpr7G0,253
astropy/io/ascii/tests/data/cds.dat,sha256=Ch1hTRaqUGjQIWvUaGvw4-B72Jlqs68vr2X48KLK1WU,2449
astropy/io/ascii/tests/data/cds/description/ReadMe,sha256=1Jgv2hIpeOYa00nL3gx1H5H03uk6fWEbysiIpe1KrY4,3649
astropy/io/ascii/tests/data/cds/description/table.dat,sha256=RkEOnYoaDh-dmz5bQJ10N7Wn063C5YfC1ta7pYwF3lE,114
astropy/io/ascii/tests/data/cds/glob/ReadMe,sha256=mL0CcTs08yPikIHQdQVxFUa74zxo04lcv6mAGGeR0F8,31441
astropy/io/ascii/tests/data/cds/glob/lmxbrefs.dat,sha256=PcSGuQBKM808MKcYct43rgyLnSaqciXRDueVakL0Ihk,32040
astropy/io/ascii/tests/data/cds/multi/ReadMe,sha256=RvuuGh_ub8Dvj6-36CDCMZLkG718ylAGy9HdpTDuCFg,3395
astropy/io/ascii/tests/data/cds/multi/lhs2065.dat,sha256=dTJdHu-ajmDybJvOPLOdLfxsLyfLMnXsHW4f-Kc7RJY,432
astropy/io/ascii/tests/data/cds/multi/lp944-20.dat,sha256=rKTSBaHkS2k4uLgWS1XyWhgSwDj6gufe5AFKIsJdRHg,434
astropy/io/ascii/tests/data/cds/null/ReadMe,sha256=qNhekjnqlDNGGgKP5aBz-3fCnPjndT9L0rvMyv7RaAk,3684
astropy/io/ascii/tests/data/cds/null/ReadMe1,sha256=EvrgXNC7zmBG45WDmOBZ3AxrUwIgzCE5uVdCW7Vp1UU,3960
astropy/io/ascii/tests/data/cds/null/table.dat,sha256=RkEOnYoaDh-dmz5bQJ10N7Wn063C5YfC1ta7pYwF3lE,114
astropy/io/ascii/tests/data/cds2.dat,sha256=WMxLXu4tBl4oRvMg8lvu2uEsXo_6Q8Fk11TLOr_Xok0,42714
astropy/io/ascii/tests/data/cdsFunctional.dat,sha256=ecftnn82jvcYgLR39XJLWk77IB7NWm8VTLzWrSXPCZ4,3679
astropy/io/ascii/tests/data/cdsFunctional2.dat,sha256=1TSwPTFVwP-4q_yyaDwKpj8tQ8EzPUfhS7DEhaUvsIk,2294
astropy/io/ascii/tests/data/cds_malformed.dat,sha256=q54LilMMCr48w3Ghzhah2CAEdp5gA3x6JaJDOb1Tqx0,2352
astropy/io/ascii/tests/data/commented_header.dat,sha256=YB7hqkoutRF3V971C726GAURbGogDsyZShSREeKeXOI,37
astropy/io/ascii/tests/data/commented_header2.dat,sha256=YTZmhncoIrmHoTP22dL94PRh2mJMw2LdYd1kbYTG0LQ,60
astropy/io/ascii/tests/data/conf_py.txt,sha256=Dzf5vb9XVA6ovdR325RhEvzV5AzRLpFl3gPOsMjt6io,321
astropy/io/ascii/tests/data/continuation.dat,sha256=LBEkswzRG8j7HrpsUArRslvRjMyfuppXXG3xHJD71hI,38
astropy/io/ascii/tests/data/daophot.dat,sha256=xIk5tcLTYDwXPpk4DluEkoJ_zuH-tY-RiVIIE2XSsz4,1923
astropy/io/ascii/tests/data/daophot.dat.gz,sha256=oMpmMBN7U_6HTKQzau_0s4nvC-tOnNi6cFL0U9sL2hg,793
astropy/io/ascii/tests/data/daophot2.dat,sha256=z74wewCKrAuaOH9Dwo-I4Rb-c3GA4jNqhE4r8OsHGog,2036
astropy/io/ascii/tests/data/daophot3.dat,sha256=IKyVJ1R6_PiPVLZbZvevxuqKJrghxr-aE2kxGCdSPiM,7920
astropy/io/ascii/tests/data/daophot4.dat,sha256=y8oC2Tbdoe0tBNWGb-BW3g9G7tAJkCFebS_n3mdpASE,7353
astropy/io/ascii/tests/data/fill_values.txt,sha256=3U8hdH06LreDnq_UiGqYiOezcB2dny3o0xfF9CPhUSA,18
astropy/io/ascii/tests/data/fixed_width_2_line.txt,sha256=-ytHbhrsHSAmbPrkDfpPTuFtyB9taWBHJqOcscjzkkk,100
astropy/io/ascii/tests/data/html.html,sha256=Y_h7SszOL73tMjICafMx_GWF9dE4EIHDp5Bsl9oUXAU,794
astropy/io/ascii/tests/data/html2.html,sha256=mueWMZaMO8d-EPAFVACiW_8IiK7GE5b7AX8WhudXz4k,378
astropy/io/ascii/tests/data/ipac.dat,sha256=ir5TyCiGCi1-1R-SzgL7AJcR7lRpLVMxqhat1fEzJCY,552
astropy/io/ascii/tests/data/ipac.dat.bz2,sha256=o9j--1EoMdmFQiLBq_a0v9syQiWnYMLqDd93u1vABOA,385
astropy/io/ascii/tests/data/ipac.dat.xz,sha256=35f2GVs0hYeJ3Ke7wlO0S9ZR1PwG5KHokc8qMG9__0M,320
astropy/io/ascii/tests/data/latex1.tex,sha256=BnP0uU6FUIVvXG-0JyJtYXYAV5VTu7L3_yiIoAR1Phg,279
astropy/io/ascii/tests/data/latex1.tex.gz,sha256=5AvB9MQ6fXhfOY4MNK2tRYJpAO4OmH3TaXVUfg5tcFE,198
astropy/io/ascii/tests/data/latex2.tex,sha256=BFEi9bwjhdB7RroF_wpwdCcSdnGlf9FYXBydR0vqDHE,465
astropy/io/ascii/tests/data/latex3.tex,sha256=mhCCefJvAaqXI2OjYLtiEeZFHzF2hDsu2Z_yfoHmR6s,109
astropy/io/ascii/tests/data/nls1_stackinfo.dbout,sha256=nVu_XgDHthRHOoSn0ugV-eRfyXRgvLOmTHHllR1m8UI,20440
astropy/io/ascii/tests/data/no_data_cds.dat,sha256=VTc5tMFQFcbnbX_Qv5SNTyftpfaN2a3wgocJjTdXzDw,2345
astropy/io/ascii/tests/data/no_data_daophot.dat,sha256=fJw6YKq8VulJmeJr3uXqyu5Kn0IfM3-RWV417Vt_KNw,545
astropy/io/ascii/tests/data/no_data_ipac.dat,sha256=oTC4_bIv7_D0elydC8Fp1VMJEWVHw_mBxuBUaNHGucA,517
astropy/io/ascii/tests/data/no_data_sextractor.dat,sha256=COrav-qGt5q8gTFCtY6CNjNKgIqRJ5UgCUGClWjl3p4,122
astropy/io/ascii/tests/data/no_data_with_header.dat,sha256=GiWVNGWrZx1UswEIqZUbVQD6QJlAmO-UY4UwBNp5M-E,6
astropy/io/ascii/tests/data/no_data_without_header.dat,sha256=kX-OOrSYuQd2csSjG7p6pXBnIXiv0kpLE0s--3usW9k,21
astropy/io/ascii/tests/data/sextractor.dat,sha256=6QCQpjR02CeaT2NndWZA8hDBXzNEDmeuP_RN7AhVrHE,369
astropy/io/ascii/tests/data/sextractor2.dat,sha256=QmetTMOfWvWRRFFOhIXVAd9OeRaE6i8D6NQ1dslVM2Y,739
astropy/io/ascii/tests/data/sextractor3.dat,sha256=JuYXAn_1_WcMqNMKd5sOVix2jBRk8Mxc2Mnbvx2NqIA,1110
astropy/io/ascii/tests/data/short.rdb,sha256=m2l50eJ_rrQ2aXmwqCEBoMz_CvY4YNsvIC9uOlZ5qEU,156
astropy/io/ascii/tests/data/short.rdb.bz2,sha256=U6JrvY2O4Icq7idFu2DrqdRCjCCNOm4hVslo2VJvub4,146
astropy/io/ascii/tests/data/short.rdb.gz,sha256=086Fg1H3Q3BQhLhfGBJAw_MKQ7iYvzSoVkrWQYrWwhc,148
astropy/io/ascii/tests/data/short.rdb.xz,sha256=MIEQmLpqdVF1Ym3LlV3wh5KLHyoQ6-pW11_2KCur2S4,192
astropy/io/ascii/tests/data/short.tab,sha256=Y1Zgl8e9thUqJ66PHIM-pQvdr7YpQoVLXeVptxLmgl8,122
astropy/io/ascii/tests/data/simple.txt,sha256=j5jdCVqjGV-uFZ5oW0W93seCtz97FzFKhGJBGqM7hKo,125
astropy/io/ascii/tests/data/simple2.txt,sha256=xi37aas-4XMNExgXclcDfiqPk344oeHzbmeFRBtTmHE,245
astropy/io/ascii/tests/data/simple3.txt,sha256=9FYVYXVR5zGPtCbO0IUwp5QkJR8Lzn1yMfX7l1aiX7M,100
astropy/io/ascii/tests/data/simple4.txt,sha256=_ohZxWXS28JJPTYDNfpeJa9XoLLiJn0jZuQpuTGzkVw,184
astropy/io/ascii/tests/data/simple5.txt,sha256=e1Mo63Vn_XLI4o0tmWt73j7-IUbtsddib8CUTzHcNug,239
astropy/io/ascii/tests/data/simple_csv.csv,sha256=wCwoEHcYGv3sczqgrPgFPQFVKEXku0qBMoeuf0hLjJw,17
astropy/io/ascii/tests/data/simple_csv_missing.csv,sha256=f9ousWSyjLxQkmRSFGP7fygQoAHcKpzNyZxctgk4Ofc,14
astropy/io/ascii/tests/data/space_delim_blank_lines.txt,sha256=jXHFWtMA2EF7ozN1JG0ARWW-UHRKY5MHZlmYw6sMqH8,235
astropy/io/ascii/tests/data/space_delim_no_header.dat,sha256=4DA0RA4bSAH9fyBrEA_zHJswg-Ud86YcwxEUQjSrvnw,24
astropy/io/ascii/tests/data/space_delim_no_names.dat,sha256=hx1uf0Y6QsVXMgPFDbblLaUG1Tla9z4JI3QQ3sXv-FY,8
astropy/io/ascii/tests/data/subtypes.ecsv,sha256=yUzalKc4JyOiVyT7K7ltNoUqtXDxa0zfmV0MB0DS44E,6878
astropy/io/ascii/tests/data/test4.dat,sha256=1W8QlLQzvXcwNXj0Xlx2_YhVz1-Du_n_jteIM_LznoE,736
astropy/io/ascii/tests/data/test5.dat,sha256=Y4mFF4oRw3msPjmlQdCWUiG5afhPIOVPVbDWgFnJkxM,1471
astropy/io/ascii/tests/data/vizier/ReadMe,sha256=8BQ6y9nrtDk37cv6qM-nDHdEF8ZLk1JPpSDhW5FdleU,4905
astropy/io/ascii/tests/data/vizier/table1.dat,sha256=yhaEo3V-Ju9sNNDW_ZIO4eGrh5G2wGXeH3EI3V74oVc,1457
astropy/io/ascii/tests/data/vizier/table5.dat,sha256=uRsmUbTgIgNlIBIZhluCAQw3Gx2r1COCT0F79J35kqQ,2793
astropy/io/ascii/tests/data/vots_spec.dat,sha256=5H647T1h_vgbujbKgzfQKs59c7xX04zAgCWrpn7K4uc,5947
astropy/io/ascii/tests/data/whitespace.dat,sha256=25uy9_U7pqqsbdeKSl0ST4IXeQYnfeULkBCK8dth00A,110
astropy/io/ascii/tests/test_c_reader.py,sha256=pYWIs-3cvORr-QPtOIuQiTo8R0sxALzZbbVy_e1IZ1k,63010
astropy/io/ascii/tests/test_cds.py,sha256=2-I5lOlQR0bTnl81MROcjo44pbbbPieRJgBld1dH0Rk,27364
astropy/io/ascii/tests/test_cds_header_from_readme.py,sha256=e7olYKfhuh_C1IjL69nhf-yTAs0ZRAyH4xUKKts8JaY,7672
astropy/io/ascii/tests/test_compressed.py,sha256=XMvn6qczysfo8CB6IHN22d0e7P3etdQPM8nacGtPxe0,1473
astropy/io/ascii/tests/test_connect.py,sha256=eLsDlW1Z7E9PAKJZWVQpkg42xVS6krpBncPrWwwWfoQ,3781
astropy/io/ascii/tests/test_ecsv.py,sha256=ah0qp2leK5keHWW7231IhWpY_by8sb8YNMwt6M-WqvQ,36231
astropy/io/ascii/tests/test_fixedwidth.py,sha256=7EZ3vKH8C-NaxTPoUHNZOR3SCUPPO_9N-KkkKxVLEpw,15663
astropy/io/ascii/tests/test_html.py,sha256=2aRmVF8Xipdkz-0fRIHuhZ4WsvzAatqpgqXKJqgsnuI,22379
astropy/io/ascii/tests/test_ipac_definitions.py,sha256=M_NJkCwXYC_N1HGRGckC7HpXVqMPUwjIaAOwKYpUSmM,4585
astropy/io/ascii/tests/test_qdp.py,sha256=RRft-u38rbxA1QL6FqBdsz1qfbNtP3O78OJ4uZRQIYc,9090
astropy/io/ascii/tests/test_read.py,sha256=i4o93MQcygTsee4TgouSXJspIBco4gkF2Cna7Dqbu5Y,62304
astropy/io/ascii/tests/test_rst.py,sha256=LXs-O6p7Ca7dF9TcrKPnrVGRx4eFR19TXu79UAZOj9k,4954
astropy/io/ascii/tests/test_types.py,sha256=TNS6uTOfHuPkANab76QEiyca2Th2zty--9sO6VXpqyM,2375
astropy/io/ascii/tests/test_write.py,sha256=sxNYjOVPdC-LVsfx9X46z_lLoPP85OC9tgbVjL_oNJY,29117
astropy/io/ascii/ui.py,sha256=0jfuoDLSqaolLP_ReJ08EElpbdwgY85J4y5_C0e7Z3o,35543
astropy/io/fits/__init__.py,sha256=hTToMGU2OZe_lB1uQBVg3s1-RBjviyssJ48TTXtuG-I,3294
astropy/io/fits/__pycache__/__init__.cpython-39.pyc,,
astropy/io/fits/__pycache__/card.cpython-39.pyc,,
astropy/io/fits/__pycache__/column.cpython-39.pyc,,
astropy/io/fits/__pycache__/connect.cpython-39.pyc,,
astropy/io/fits/__pycache__/convenience.cpython-39.pyc,,
astropy/io/fits/__pycache__/diff.cpython-39.pyc,,
astropy/io/fits/__pycache__/file.cpython-39.pyc,,
astropy/io/fits/__pycache__/fitsrec.cpython-39.pyc,,
astropy/io/fits/__pycache__/fitstime.cpython-39.pyc,,
astropy/io/fits/__pycache__/header.cpython-39.pyc,,
astropy/io/fits/__pycache__/setup_package.cpython-39.pyc,,
astropy/io/fits/__pycache__/util.cpython-39.pyc,,
astropy/io/fits/__pycache__/verify.cpython-39.pyc,,
astropy/io/fits/_utils.cp39-win_amd64.pyd,sha256=s9q5A4rnCIN1BeG2nmPoeeM3h48sWy5tUEB3sz9a_3o,29184
astropy/io/fits/card.py,sha256=p1VkA5WS6tvZQvna-er_pzYbkgL1FGbfHWtSrz2wWMI,49839
astropy/io/fits/column.py,sha256=up7zHwWE83TEnNJDsd3JpodqCb-vlbJC-dqKQLX5X3o,98365
astropy/io/fits/compression.cp39-win_amd64.pyd,sha256=8dGb2Bo11FNw1ekkwKn_HM9_KiOSwfCc6-U2QuMZPN0,680960
astropy/io/fits/connect.py,sha256=RY_znUY5EGwCn9_F27ka7KIwWDKymFTAZYZwatcLmEM,15558
astropy/io/fits/convenience.py,sha256=LGX-M6kuQGpjIFPUOdqk6pMAswBzU6QGhol1_76l5Bg,42789
astropy/io/fits/diff.py,sha256=HUJDFipiB3MOvbwB-5LjPn8-ELum1dcf5kzSgG5kI6M,60030
astropy/io/fits/file.py,sha256=-BrS0ff-p-pflignN0vgzi5MhLjsoZAoOIX-Jz3_hNA,24712
astropy/io/fits/fitsrec.py,sha256=24PJ7ThK8hrqw5kACXHRGD-srTDJ3c58roaar7oUMCY,53877
astropy/io/fits/fitstime.py,sha256=hoAg7KpjXdiNuDyyTMPB_TTYo_RHUTRcUvY9en0cUyo,23921
astropy/io/fits/hdu/__init__.py,sha256=ZQSBb14xDUqt8fjFQFxI31PIh1pIFYUbLnZ3UwmSnAk,697
astropy/io/fits/hdu/__pycache__/__init__.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/base.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/compressed.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/groups.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/hdulist.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/image.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/nonstandard.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/streaming.cpython-39.pyc,,
astropy/io/fits/hdu/__pycache__/table.cpython-39.pyc,,
astropy/io/fits/hdu/base.py,sha256=WZ828QAgisIqvMlgeQvaHLgui8IRFgUMBLvYmbtDjDY,60941
astropy/io/fits/hdu/compressed.py,sha256=BVNhMR0p9MI7PqCfRcBxdeLxrH0082No3tT3JFaOKKY,86285
astropy/io/fits/hdu/groups.py,sha256=TMaWRPCoF4q1RQx-xY0seBtjH7PnY-KMgix7qdlTgg8,21436
astropy/io/fits/hdu/hdulist.py,sha256=8sKnf36GCEGDhJCyd2_1ZqEAIKR3oCMWrr8ASK2reT8,56188
astropy/io/fits/hdu/image.py,sha256=FbLDp4qSOi6-2RWBtwVgXyOuwQVsIqzNSBsm1nA2Js8,46608
astropy/io/fits/hdu/nonstandard.py,sha256=WioYIpqSLmT0d4QG6883BRlE_05FIt1ilTaWhZbjOKM,4088
astropy/io/fits/hdu/streaming.py,sha256=eY6i4Hui4xaR43tVhx3r_emVhrEAgUmSwDf0zTVUQVg,7683
astropy/io/fits/hdu/table.py,sha256=CT0RPe1soCJDCVyskDvIm61Xze1XiMR7FUv5HxkvFvA,61961
astropy/io/fits/header.py,sha256=oEz4aF-bb7fhArlJcQI_rblftYD6wI4GhN7nDLHKYcc,81156
astropy/io/fits/scripts/__init__.py,sha256=OR90Qv2NMW3fdn4Kcg9c5NkppPrvFxsq8vCPg-gwW0c,291
astropy/io/fits/scripts/__pycache__/__init__.cpython-39.pyc,,
astropy/io/fits/scripts/__pycache__/fitscheck.cpython-39.pyc,,
astropy/io/fits/scripts/__pycache__/fitsdiff.cpython-39.pyc,,
astropy/io/fits/scripts/__pycache__/fitsheader.cpython-39.pyc,,
astropy/io/fits/scripts/__pycache__/fitsinfo.cpython-39.pyc,,
astropy/io/fits/scripts/fitscheck.py,sha256=ovxnmkTxAci7wtsN0nvv2sVDX8euwVrSPZlso7puKMY,7508
astropy/io/fits/scripts/fitsdiff.py,sha256=H0cvPpSIqu3vE7zLsDFxI6YHJt6k0uz4FEifH_ZOhc4,12904
astropy/io/fits/scripts/fitsheader.py,sha256=-J_EGzWlTx1s60vSze5cZkSlrJZoaoEe27LaqYuzGxE,16990
astropy/io/fits/scripts/fitsinfo.py,sha256=Nz86vTTtdeExnQbDU3vLHFNayWQo1oh1gy4RcIwBzHk,2075
astropy/io/fits/setup_package.py,sha256=qjRw1hL48dJYHjxoNU2lt6QJtIhAFx3vmOWv5IVrjKU,2728
astropy/io/fits/tests/__init__.py,sha256=6OGux-X9A5RzO9H399NCxaFHQga5VlP-1UcRVcET1CQ,2017
astropy/io/fits/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_checksum.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_compression_failures.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_connect.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_convenience.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_core.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_diff.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_division.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_fitscheck.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_fitsdiff.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_fitsheader.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_fitsinfo.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_fitstime.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_groups.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_hdulist.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_header.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_image.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_image_dask.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_nonstandard.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_structured.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_table.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_uint.cpython-39.pyc,,
astropy/io/fits/tests/__pycache__/test_util.cpython-39.pyc,,
astropy/io/fits/tests/data/arange.fits,sha256=Fc2vcpojV6h8bPCslpzvMM6-gw7tf8ZtzmfzYkbK5kg,8640
astropy/io/fits/tests/data/ascii.fits,sha256=s9XnnFz5hNjyIhLatPpe9eRvQ8A5CwCPTYN01_P-OkI,8640
astropy/io/fits/tests/data/ascii_i4-i20.fits,sha256=uXBwU7-iDvLVE90YlMSp-XDkPdfQ4inV_tA34rGl3N0,8640
astropy/io/fits/tests/data/blank.fits,sha256=2XkZNoWQwnBvMzdcCzmzeH0_L7j73p9hOYcH_kp_P-s,5760
astropy/io/fits/tests/data/btable.fits,sha256=UU73L2JZ5W01pTJDvARSPE0nUzpJG9EteyS7aiov_n0,8640
astropy/io/fits/tests/data/chandra_time.fits,sha256=2sB_nAbyS3VULRJ6OmyP1qKBJqT-O3M9s5hdo2UfmNQ,31680
astropy/io/fits/tests/data/checksum.fits,sha256=gKbt25uaC2LryAX15cmdx1GMZuIKUmacve07rbDRMKU,20160
astropy/io/fits/tests/data/checksum_false.fits,sha256=3QWgkZcJ-SMS0XE8hhTFHrTFjD_qpv05afLmFNVZhDY,20160
astropy/io/fits/tests/data/comp.fits,sha256=vGdb13XNUmuknpceUlfDAod-_MOqiamok8EPQ7DOUlo,86400
astropy/io/fits/tests/data/compressed_float_bzero.fits,sha256=PEO0Sz8ag4X3Dj7wWdik-nBJLgNN0M0uVzO-uaNeQnU,8640
astropy/io/fits/tests/data/compressed_image.fits,sha256=3AjX_Sa469pdt9n709yImZn6zpX75Cr2pvQYc10-BP4,8640
astropy/io/fits/tests/data/double_ext.fits,sha256=erEFt2ldvr19oL9DD1hSew2oIj6pkEYNIlk8VD91e_w,11520
astropy/io/fits/tests/data/fixed-1890.fits,sha256=aWQZK71MwVSFxbEyVdWO3iLGFLiZPJm6TNFLCS1Qz4Q,31680
astropy/io/fits/tests/data/group.fits,sha256=OvLlW0kNRCx9cPeMb6J3evx6_bEnyEHYgY1X17GprXI,5760
astropy/io/fits/tests/data/history_header.fits,sha256=0KRt2J7hizrkhsoupkmTI3ZHAMTxcvKMB0YNYJH7Sz8,2880
astropy/io/fits/tests/data/invalid/group_invalid.fits,sha256=bdvLIBrBBnk57GBLxNnR5W_UBCJVidDjo2tS0SVF-ls,2880
astropy/io/fits/tests/data/memtest.fits,sha256=XJgG7O2DFc9-s-jYeDuDHvVk79rWV8feg6ZGvTHYyaQ,31680
astropy/io/fits/tests/data/o4sp040b0_raw.fits,sha256=255ISTsiYnYGT-HTPxxgAl7UZqp0UWVy8gcX0o9wGFs,74880
astropy/io/fits/tests/data/random_groups.fits,sha256=BMdOwhCH033IPhBkEXr2Knn86T1sRQFiM2dtBHm34T8,20160
astropy/io/fits/tests/data/scale.fits,sha256=0V7Q4Vh99b_CeS7tJF6Oe-PpDUArAaZAw3p4-kMDmC0,8640
astropy/io/fits/tests/data/stddata.fits,sha256=2TdoFuJDBUR7eio0Yx8jvkeqAECRUN7ppfUIDsIbGgg,23040
astropy/io/fits/tests/data/table.fits,sha256=6Gvd5QXvwP60b9MuJCG45vpnsd2WObJJ3QCTy1FMXMo,8640
astropy/io/fits/tests/data/tb.fits,sha256=4YkaVFOvLMyhaho9g8lnhepij8ZZpFPfhMkNhTnJ8Kc,8640
astropy/io/fits/tests/data/tdim.fits,sha256=QOhVGXpAUmMk2anTjcucoNz544Y6nYhwxubgvdwgSBA,8640
astropy/io/fits/tests/data/test0.fits,sha256=6gbuMLKPHqLoymLFKJdWdjt_QTVtf6MpHbw0bi7TTpQ,57600
astropy/io/fits/tests/data/test1.fits,sha256=GvJHkaNZ-KfcA_fLTkRaNCmvDPjfw6MTyUT9wa1Ok64,57600
astropy/io/fits/tests/data/theap-gap.fits,sha256=nFzmzHHeyqgStmaZjKHOZqq3OiU3OjZn87MMyha01Nk,20160
astropy/io/fits/tests/data/variable_length_table.fits,sha256=ZanMGZ_2yZGOl-u_5pVKWfcMq0ctLxNJYKiVdQI-lO0,8640
astropy/io/fits/tests/data/verify.fits,sha256=pj42PzPvPHTu74myI4DjzyLynNU9t8aq82l3Rqc9X-U,2880
astropy/io/fits/tests/data/zerowidth.fits,sha256=DV2p9yTiZBvIHM5rTJwcK6oMZP62p0NEsZvhDx3Bj_Q,54720
astropy/io/fits/tests/test_checksum.py,sha256=dctnIolG-Da9dsSPsQM6_7Gesm0bgxfLMaAroBc8Cpw,21270
astropy/io/fits/tests/test_compression_failures.py,sha256=anJrNZ0ce-Dvuv-qY5ywxzqZq9vhJCZ3JduD_-1WaGo,5016
astropy/io/fits/tests/test_connect.py,sha256=LXp15uXr0-mba_2IGPlTlXeggvQnnkEmKJ3rYKyWntM,38941
astropy/io/fits/tests/test_convenience.py,sha256=KnrxL4JO4ZR8phjD5-n-b4a721q3AU2MfWhYynBmp8E,17438
astropy/io/fits/tests/test_core.py,sha256=JiWVQYNHrVZmnpAyj2f9-OLa88lIjKz6x7a3fTLCIiQ,52627
astropy/io/fits/tests/test_diff.py,sha256=uNYXZIBkUvrJR7B6JGa6Ezu_zSGxvF9P-GPEihO-sXU,34614
astropy/io/fits/tests/test_division.py,sha256=3MI9kyl2FOzT7KrDrmiJ_Q-sTezfHdoq8Uzvb7lTq08,1106
astropy/io/fits/tests/test_fitscheck.py,sha256=xH7pw5AiAuKosEvBY2ZsNRo1iCwA5PsOaIDgWBDmGAU,3180
astropy/io/fits/tests/test_fitsdiff.py,sha256=0_NreeaWce1B1xu07u00lT1kK-N3AzoyH3ycpz22rKQ,10317
astropy/io/fits/tests/test_fitsheader.py,sha256=Tn5yCq3uarjshvtl6ZV9fijXis_hMgexhBY5slzDLi0,5967
astropy/io/fits/tests/test_fitsinfo.py,sha256=yHYjO7F7vOg4jhb1Jj2NjX2jb5qEvtZx9J39ZQH1etk,1625
astropy/io/fits/tests/test_fitstime.py,sha256=M7pZbUqhO1-FekoHNtdyRDLCKCyqRHVruBhBZigqhlA,21286
astropy/io/fits/tests/test_groups.py,sha256=y0JrHXRbj_4AXfxUHNQbYeWLlwv9OPD28iG2KTlwsC4,8393
astropy/io/fits/tests/test_hdulist.py,sha256=wdz1L3NDxW0nNeGUz3mzOlq3mJlIrHlGbZ4OD72RTwE,44363
astropy/io/fits/tests/test_header.py,sha256=aWdwOh0wUmMrkdTt7Q4FdMsMuboI_-2GV0Rt_tRjILU,115380
astropy/io/fits/tests/test_image.py,sha256=YsrONzKK8tMzB4wN06-UnNgWdXm9T6KdIf4lV5p9INE,80451
astropy/io/fits/tests/test_image_dask.py,sha256=w2FHvwam1uyAiOoia3P4B7Pv_ags8USQNyxmiabJCZs,5671
astropy/io/fits/tests/test_nonstandard.py,sha256=zmHCfIhW12syhvnoJn7XiylawJfNMmxlcTI1lsKvIAI,2346
astropy/io/fits/tests/test_structured.py,sha256=wSnfW42RtNjcG78f-8A-gSP4BOynQX0qvYrtpqOyAes,3111
astropy/io/fits/tests/test_table.py,sha256=CuuEbfhSt7li-f3a-IHXRJQTOlEmqDeoAbaUJUUJA6o,144674
astropy/io/fits/tests/test_uint.py,sha256=e8cSm2DeXLE62Jl8KtzZZQ6wPIvKcRKhOH3LglEAR-M,5926
astropy/io/fits/tests/test_util.py,sha256=qNhpRmOvO5Ea6NVuCSIwu2kHl7PMfDR4umfxhykQRrY,6952
astropy/io/fits/util.py,sha256=BfyPQWXTPtc1EQw0HrlB_irgnJCCvM37FosPRXuNGMo,30130
astropy/io/fits/verify.py,sha256=vzykZTY2M-5u9lzffxA_3jSnh_FBOTek1DId58llUS0,5576
astropy/io/misc/__init__.py,sha256=uPQSNNeufHvCTjnGbu8JqXOB_onW9zEC1jRdsCc_9bs,193
astropy/io/misc/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/__pycache__/connect.cpython-39.pyc,,
astropy/io/misc/__pycache__/hdf5.cpython-39.pyc,,
astropy/io/misc/__pycache__/parquet.cpython-39.pyc,,
astropy/io/misc/__pycache__/pickle_helpers.cpython-39.pyc,,
astropy/io/misc/__pycache__/yaml.cpython-39.pyc,,
astropy/io/misc/asdf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/io/misc/asdf/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/__pycache__/conftest.cpython-39.pyc,,
astropy/io/misc/asdf/__pycache__/connect.cpython-39.pyc,,
astropy/io/misc/asdf/__pycache__/extension.cpython-39.pyc,,
astropy/io/misc/asdf/__pycache__/types.cpython-39.pyc,,
astropy/io/misc/asdf/conftest.py,sha256=HRAfeRJqIgvUGcZGBLB6nEIeYImhtbX1AwLqzLnHRsA,409
astropy/io/misc/asdf/connect.py,sha256=yGMTqGy_oBL5lI4O2cqEEekGHd9_YsQ4MgFlPG89L6c,4006
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/angle-1.0.0.yaml,sha256=W5vITt7RRI1HPkkX__tLcJly6I110Vqy3GNMeC6gTcI,833
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/earthlocation-1.0.0.yaml,sha256=cVtcE6pwWfX8Hfgu6qWChCWoBgJTdkx6G-gR7JfH5zk,948
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/baseframe-1.0.0.yaml,sha256=X0NfOFlsvpYD1xJfy4UzbK6I1RLQnMtqY8AT7N44teo,655
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/cirs-1.0.0.yaml,sha256=OhAyQtVqHUF7_ZF5OlqsjkQ_4BPGUUbhz6yypofPZPk,1468
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/fk4-1.0.0.yaml,sha256=2U3NBE89R3oNTtSXIlymwE0jqNxL354LH_NLd0hMSOo,1673
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/fk4noeterms-1.0.0.yaml,sha256=yIkP4dk2NLrMdG2d0ra1HwQcePRQkeBIC-4D6cpYtGg,1729
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/fk5-1.0.0.yaml,sha256=mcGiVkf-2YFOXkT4uqmouq8kPnHXoIjLLjRqj2BW-3E,1437
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/galactic-1.0.0.yaml,sha256=QyeLog7T-gjkblUA7PY9yNsYHB9lmmUrdd_NBn4UBW4,1199
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/galactocentric-1.0.0.yaml,sha256=Z4ZcK3lk3wKydwJSceCTjYUjs5Xha-FZlY1TCKlsOrU,2420
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/gcrs-1.0.0.yaml,sha256=KJjgVVimdDivDy7zRI0SXg2yi20JkeDy_RFMpUhPeWY,3137
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/icrs-1.0.0.yaml,sha256=sV6iunKMphvkJIUnIJaqR1GLlM-2htmxzwxIhhcFT0g,1118
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/icrs-1.1.0.yaml,sha256=OEz4w3Twz0xdev0huTnupcUiEvvetv0nnfkfxgvvdrc,1159
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/itrs-1.0.0.yaml,sha256=cwUUylpk4SW_xfrOJ4aGdgW0wbzganv-cz5uQnkuU4A,1468
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/frames/precessedgeocentric-1.0.0.yaml,sha256=iivC8Y4SaJMZRb6LWyEIzkuaqJvNz10_qgynlkhBcnM,3434
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/latitude-1.0.0.yaml,sha256=we1egfhheETjI0GC1QX0q0obHIIEK8qWzUvAUCgos8w,838
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/longitude-1.0.0.yaml,sha256=PBp9_CHtt_kPOo-cvzCsEdgWpdQem-n1GGA9g1k9OlM,1118
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/representation-1.0.0.yaml,sha256=5nIEdGXrQHvC19mLuxN4T6aduuGwU6D8Gey3GxhoWSE,6210
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/skycoord-1.0.0.yaml,sha256=UnEy2pc0ztkaOPxd3OkLXjwoexAlVnl7BRK-MPQrFxg,550
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/coordinates/spectralcoord-1.0.0.yaml,sha256=_Je0U5t_g-Dltrwq6rLzttYr58duF0XqG-GTIn74NmU,922
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/fits/fits-1.0.0.yaml,sha256=lXscJlygGoO2WTqmUAF1gjb8j-24MAVI_T7ugN1BG4Q,3408
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/table/table-1.0.0.yaml,sha256=obT3gFuXe9ZjKhL8hQNsktoNyRGHRuS4H8rOxBEYP4o,3696
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/time/timedelta-1.0.0.yaml,sha256=-idTPJWSRrL0VpOex17k83cRZAnHAXOC6sY_W7I_tYk,928
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/transform/units_mapping-1.0.0.yaml,sha256=YFBulNnw8_W48ftaHsJSo98ocNE669EnP8qHBr7z8tM,2753
astropy/io/misc/asdf/data/schemas/astropy.org/astropy/units/equivalency-1.0.0.yaml,sha256=fOWT22bxdRg3O5i-wFB9qkFRzctEyl71lmR5QiUxV0I,747
astropy/io/misc/asdf/extension.py,sha256=pgNmNsYNOnJ1_h9JvC6dYOIhXZW1AMgXE-8UViLsuvo,2619
astropy/io/misc/asdf/tags/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/__pycache__/helpers.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/coordinates/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__pycache__/angle.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__pycache__/earthlocation.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__pycache__/frames.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__pycache__/representation.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__pycache__/skycoord.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/__pycache__/spectralcoord.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/angle.py,sha256=c7fIa7lYhkJHpmKL6XGZwHF4cDRwwheWHdYGy9_1G8A,1203
astropy/io/misc/asdf/tags/coordinates/earthlocation.py,sha256=n4Iu7cn5lDSuYCRWV9z4pryqNrcPJfF7aB96cZilzko,595
astropy/io/misc/asdf/tags/coordinates/frames.py,sha256=y4-xLJVjzTuJB8YRDtjJKVwr5i4htSGuiE6ryo9oXxo,4652
astropy/io/misc/asdf/tags/coordinates/representation.py,sha256=j_gM85YGdPA4rqzBlkZvDUtaUhMM6MYoZaNypRNLl58,1304
astropy/io/misc/asdf/tags/coordinates/skycoord.py,sha256=g-EnZc2kELsU4lqcwlVl4-pzJPMKaMIcqOegJXZBtDs,637
astropy/io/misc/asdf/tags/coordinates/spectralcoord.py,sha256=EeOvTC8UFjDWY3V98CuDRQD_MhtDMSVrDqOHBSsTmOA,1570
astropy/io/misc/asdf/tags/coordinates/tests/__init__.py,sha256=e6cnT2zvGeJQJssbay3YeUyJuHAmD4qrqE8tJFEbJeM,212
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/test_angle.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/test_earthlocation.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/test_frames.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/test_representation.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/test_skycoord.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/__pycache__/test_spectralcoord.cpython-39.pyc,,
astropy/io/misc/asdf/tags/coordinates/tests/test_angle.py,sha256=CugnPozy3qL69r1o_HCE_6nwDvO8EWqqyeaKaQhved8,695
astropy/io/misc/asdf/tags/coordinates/tests/test_earthlocation.py,sha256=hnLXHXxiTGQ0GuswWV0sJ-32mvrdkO_oxic4qwIldKU,1757
astropy/io/misc/asdf/tags/coordinates/tests/test_frames.py,sha256=gAf_EQy2GF7hbd5UjZnZwOnzjUeKiQLa9Rv5HncL7bI,1204
astropy/io/misc/asdf/tags/coordinates/tests/test_representation.py,sha256=kaLIeRkCCkGGAghOkHYgREZcia9rwIC6hs6gKbb2074,984
astropy/io/misc/asdf/tags/coordinates/tests/test_skycoord.py,sha256=3bVNaKmRs6LXOZpjqQlMV0eaNgEuMfd1JF868vvCq5E,3603
astropy/io/misc/asdf/tags/coordinates/tests/test_spectralcoord.py,sha256=9u3XkOxRuwmehH6mO5XobQXkOA9bbQUOXULJavoZjmk,1851
astropy/io/misc/asdf/tags/fits/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/fits/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/fits/__pycache__/fits.cpython-39.pyc,,
astropy/io/misc/asdf/tags/fits/fits.py,sha256=MjC3q2xvFfyNCSMnHj_LpL9wuKJbAikuDyz6OjJL8o4,3505
astropy/io/misc/asdf/tags/fits/tests/__init__.py,sha256=e6cnT2zvGeJQJssbay3YeUyJuHAmD4qrqE8tJFEbJeM,212
astropy/io/misc/asdf/tags/fits/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/fits/tests/__pycache__/test_fits.cpython-39.pyc,,
astropy/io/misc/asdf/tags/fits/tests/data/complex.fits,sha256=TTxnKeqa3w07N1ZxouIJO8ZkkzfdnRIvnnwM5mhoM8A,34560
astropy/io/misc/asdf/tags/fits/tests/test_fits.py,sha256=Tm5UG9i8PFDI0yeGDAMxnXiJk5mLfXiMHkMb5GtVZPo,1433
astropy/io/misc/asdf/tags/helpers.py,sha256=WJoeloP6hvHtROeL8Ia8q9IaEhma3zNaJdJsbDMixWE,663
astropy/io/misc/asdf/tags/table/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/table/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/table/__pycache__/table.cpython-39.pyc,,
astropy/io/misc/asdf/tags/table/table.py,sha256=hhsypaHxxw24pMyO7CjxSFKIdzvaA1cNBnK8tBXfass,4380
astropy/io/misc/asdf/tags/table/tests/__init__.py,sha256=e6cnT2zvGeJQJssbay3YeUyJuHAmD4qrqE8tJFEbJeM,212
astropy/io/misc/asdf/tags/table/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/table/tests/__pycache__/test_table.cpython-39.pyc,,
astropy/io/misc/asdf/tags/table/tests/test_table.py,sha256=uDYgPfeaW_SLLxnAXG9DO2c8rV4yunTURHC4J1jXun4,7179
astropy/io/misc/asdf/tags/tests/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/tests/__pycache__/helpers.cpython-39.pyc,,
astropy/io/misc/asdf/tags/tests/helpers.py,sha256=l2UA4qHvsdPNbVbK-ROh3GuV_O2epir0pNQXUWq9-pM,1280
astropy/io/misc/asdf/tags/time/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/time/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/time/__pycache__/time.cpython-39.pyc,,
astropy/io/misc/asdf/tags/time/__pycache__/timedelta.cpython-39.pyc,,
astropy/io/misc/asdf/tags/time/tests/__init__.py,sha256=e6cnT2zvGeJQJssbay3YeUyJuHAmD4qrqE8tJFEbJeM,212
astropy/io/misc/asdf/tags/time/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/time/tests/__pycache__/test_time.cpython-39.pyc,,
astropy/io/misc/asdf/tags/time/tests/__pycache__/test_timedelta.cpython-39.pyc,,
astropy/io/misc/asdf/tags/time/tests/test_time.py,sha256=3tka0CU_N4fPiMTV1yK_UCyboY2LJdDbOrWn7uLxenQ,3404
astropy/io/misc/asdf/tags/time/tests/test_timedelta.py,sha256=HNuuFxpRpZPgruL7POI7sEIXVx2kUWwemrEON9a03Mw,862
astropy/io/misc/asdf/tags/time/time.py,sha256=gErQ_GzCyETMA0FKTIQtTlK44CWZS_J2ym4Ixpt9o7E,4296
astropy/io/misc/asdf/tags/time/timedelta.py,sha256=GikhK2hdr4AEYxIlKXSpXXtQ8L7ZdpRpDRt5LCIPt5Q,1061
astropy/io/misc/asdf/tags/transform/__init__.py,sha256=urZ7I70JKo8rArQDWknhOqe8vEy5euysnBURv2-gAjU,250
astropy/io/misc/asdf/tags/transform/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/basic.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/compound.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/functional_models.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/math.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/physical_models.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/polynomial.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/powerlaws.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/projections.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/__pycache__/tabular.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/basic.py,sha256=OXEZd3t0h0Ss3AscYKBWMYhMwnjZejQWGoglm7FHgKQ,9379
astropy/io/misc/asdf/tags/transform/compound.py,sha256=bLaZkWtzYupY2I9_lMtBBSXmvR-EG-HEqGhiLkiCjpc,4124
astropy/io/misc/asdf/tags/transform/functional_models.py,sha256=4ugbx5voca_a8hwWvpMcoUCrORzxcD6mMVbfkslLy-c,30731
astropy/io/misc/asdf/tags/transform/math.py,sha256=znvFWI1KIimzJgjLo2kZN1vPCTYnpKOeZClr1rJR0Xw,877
astropy/io/misc/asdf/tags/transform/physical_models.py,sha256=UHs0pEUtCi0iot0oIiMpZ7SGuC2pCxzc3RySWTjKyHs,3240
astropy/io/misc/asdf/tags/transform/polynomial.py,sha256=gvAccmKFp8g9ntUF6yZc7j6_OQomEV44DACnw_fpEkc,12645
astropy/io/misc/asdf/tags/transform/powerlaws.py,sha256=Yuyg4WoarLsNH745EQ2XBVPenCHLWzHbJwki3y5d7Pw,6998
astropy/io/misc/asdf/tags/transform/projections.py,sha256=Rq8HIBMAx0X2D5KR9gEuX-3X5QRxqohcMC7NkDI09G0,10527
astropy/io/misc/asdf/tags/transform/tabular.py,sha256=IytaYH6cUiY1LgzK-FvkB5iXoBA25uaYIF1P8QITmWU,3524
astropy/io/misc/asdf/tags/transform/tests/__init__.py,sha256=e6cnT2zvGeJQJssbay3YeUyJuHAmD4qrqE8tJFEbJeM,212
astropy/io/misc/asdf/tags/transform/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/tests/__pycache__/test_transform.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/tests/__pycache__/test_units_mapping.cpython-39.pyc,,
astropy/io/misc/asdf/tags/transform/tests/test_transform.py,sha256=PT0nT3ndhe89MfHhph6Ofx8tcIaN8EujsIq4GnUk1fs,13816
astropy/io/misc/asdf/tags/transform/tests/test_units_mapping.py,sha256=ny0qA2Q3nmWpUqZWnRg-b7sr0QZOgIAldJZDK9Lsw4s,1935
astropy/io/misc/asdf/tags/unit/__init__.py,sha256=H3nCwkvz7ZK0G6-qjZm5iM6yfMbetbBi88O-djg8C0w,88
astropy/io/misc/asdf/tags/unit/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/__pycache__/equivalency.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/__pycache__/quantity.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/__pycache__/unit.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/equivalency.py,sha256=mrwtkb3rxhMb98bqJBoT-2WrkSmwoaQ59JjuslcRp-Y,1254
astropy/io/misc/asdf/tags/unit/quantity.py,sha256=cqXAC3XpaVuzbOLicfLEgoKGh1KCz97VF3ilsVWHkN0,963
astropy/io/misc/asdf/tags/unit/tests/__init__.py,sha256=e6cnT2zvGeJQJssbay3YeUyJuHAmD4qrqE8tJFEbJeM,212
astropy/io/misc/asdf/tags/unit/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/tests/__pycache__/test_equivalency.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/tests/__pycache__/test_quantity.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/tests/__pycache__/test_unit.cpython-39.pyc,,
astropy/io/misc/asdf/tags/unit/tests/test_equivalency.py,sha256=HUIdOHvsOhq8yytt6JhIy0xmahp4wWV8nqGV10_gR_0,1723
astropy/io/misc/asdf/tags/unit/tests/test_quantity.py,sha256=pnRbndUwdwhh1Vb5YTJBhsiBj3eSmV1wNAHNWoMZhO8,1827
astropy/io/misc/asdf/tags/unit/tests/test_unit.py,sha256=I12c1H0uykchMtMiKT1lLEVoFWD4rjTkEZwlvcFaZ_0,616
astropy/io/misc/asdf/tags/unit/unit.py,sha256=HUA0U9EOdiUPiqp5Kk-p5IECNdxzdHR-WamPGV_wF2g,728
astropy/io/misc/asdf/tests/__init__.py,sha256=cEFcoTQYbiwvO2hsIY_gIRY83eO0jJy_WjQj1OqWTIE,746
astropy/io/misc/asdf/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/asdf/tests/__pycache__/test_io.cpython-39.pyc,,
astropy/io/misc/asdf/tests/test_io.py,sha256=T6h35ZIoYzhPCgIQH2Y2-ozVxnVm8uI20Oj5k6XzG10,2116
astropy/io/misc/asdf/types.py,sha256=N6lvRElCInrTreqPaw1y5AI3RMDvaa1R10ELICi5smI,1885
astropy/io/misc/connect.py,sha256=DT_SFMJ6B9SLxyhkL5m3bjnncyWJs0Mv1tepuwHKv7I,250
astropy/io/misc/hdf5.py,sha256=F2mx3mglcRsY5D7IYCTAzsGV6Gtdn2ly5UXh814Vib0,14459
astropy/io/misc/pandas/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/io/misc/pandas/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/pandas/__pycache__/connect.cpython-39.pyc,,
astropy/io/misc/pandas/connect.py,sha256=-itxn514Z_GLRQQzk77caT76GLcJZsrRKsQt7KoGYGk,3792
astropy/io/misc/parquet.py,sha256=IDUlg1r3J_q3QmiIS1jTyy443Ggg7I7kYd-wCLnr62E,13685
astropy/io/misc/pickle_helpers.py,sha256=VbQ8LfzEUuvjSpWEEx_mjZ5ZAVmaL1Uv5GgqsEKQePI,3020
astropy/io/misc/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/io/misc/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/misc/tests/__pycache__/test_hdf5.cpython-39.pyc,,
astropy/io/misc/tests/__pycache__/test_pandas.cpython-39.pyc,,
astropy/io/misc/tests/__pycache__/test_parquet.cpython-39.pyc,,
astropy/io/misc/tests/__pycache__/test_pickle_helpers.cpython-39.pyc,,
astropy/io/misc/tests/__pycache__/test_yaml.cpython-39.pyc,,
astropy/io/misc/tests/data/old_meta_example.hdf5,sha256=dtawtTOcxw8moqEa623f0H7SRELEzydB45IbGgkOA78,2072
astropy/io/misc/tests/test_hdf5.py,sha256=dDD5NwPBnp3X_2NZ3q3bLH_04rKYuJyZaiAqo4G6D0E,31295
astropy/io/misc/tests/test_pandas.py,sha256=Qq9T9c2a1L2pW1-XPHUrvqwNCWXG9eRYz5r84elNqWQ,3638
astropy/io/misc/tests/test_parquet.py,sha256=Nd7NjMRwbTUz9zIpDQNiu1RupHcCnYFqOP6z4JqV3Tg,21876
astropy/io/misc/tests/test_pickle_helpers.py,sha256=DYSAPy27K2w7rOaGlDcHCf38NgwVD6rR1IAZFpQ_Ik4,2285
astropy/io/misc/tests/test_yaml.py,sha256=f-Q1wcdragwUHzQ1XxwkF0mKAzTj87kh6JZM20i_1IQ,7593
astropy/io/misc/yaml.py,sha256=ygV4sgwO7A9FpAVABABXTlMQBnUkZh8DYMYEwFfbe1I,12131
astropy/io/registry/__init__.py,sha256=bjpky6zgMlcH5HkEtJh8W69aWiZ_TT41MIY064iWLQE,379
astropy/io/registry/__pycache__/__init__.cpython-39.pyc,,
astropy/io/registry/__pycache__/base.cpython-39.pyc,,
astropy/io/registry/__pycache__/compat.cpython-39.pyc,,
astropy/io/registry/__pycache__/core.cpython-39.pyc,,
astropy/io/registry/__pycache__/interface.cpython-39.pyc,,
astropy/io/registry/base.py,sha256=M2T0suNrqoP6IdaFqcS5ChTAjDNlN4qVr7oWkclE4N0,18046
astropy/io/registry/compat.py,sha256=nAbPBG-gEY7Ai0Y36X5dmkvG6SCWcShFf-4WXKmZia4,1896
astropy/io/registry/core.py,sha256=Kec5ROP-vPXIECeLiPN3tlDFNUDK14Vu8mkX2-LP9Yw,13862
astropy/io/registry/interface.py,sha256=v-Dneu9URKovhV68ymyVJCLJQU6p18ah4kCGciIZgQY,5502
astropy/io/registry/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/io/registry/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/registry/tests/__pycache__/test_registries.cpython-39.pyc,,
astropy/io/registry/tests/__pycache__/test_registry_help.cpython-39.pyc,,
astropy/io/registry/tests/test_registries.py,sha256=2vw8obMP8q8iDPRugZJkKCkRnoq94JWdOTFWUp2qt-Q,42536
astropy/io/registry/tests/test_registry_help.py,sha256=m56lTt00GoKoEFAIX62Gqvq5YZ2td7A9uK44FL6h7-w,4656
astropy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/io/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/tests/__pycache__/safeio.cpython-39.pyc,,
astropy/io/tests/safeio.py,sha256=VQsGL0Vz5NYSJo9uV2JiufaVX0Qnp76OF4xVCHs-wQU,359
astropy/io/votable/__init__.py,sha256=bw616EKEVsPOwesfaVvwtgdwWQTUnd10QUJy6uO_RbA,1218
astropy/io/votable/__pycache__/__init__.cpython-39.pyc,,
astropy/io/votable/__pycache__/connect.cpython-39.pyc,,
astropy/io/votable/__pycache__/converters.cpython-39.pyc,,
astropy/io/votable/__pycache__/exceptions.cpython-39.pyc,,
astropy/io/votable/__pycache__/setup_package.cpython-39.pyc,,
astropy/io/votable/__pycache__/table.cpython-39.pyc,,
astropy/io/votable/__pycache__/tree.cpython-39.pyc,,
astropy/io/votable/__pycache__/ucd.cpython-39.pyc,,
astropy/io/votable/__pycache__/util.cpython-39.pyc,,
astropy/io/votable/__pycache__/volint.cpython-39.pyc,,
astropy/io/votable/__pycache__/xmlutil.cpython-39.pyc,,
astropy/io/votable/connect.py,sha256=dBRDLYzoR3UBnYFAiT5K0MthGYab-niLghEQKgZyVDw,6557
astropy/io/votable/converters.py,sha256=Q9sU_F0_z7zktucBoKWPHBmcqMFs-cyzQbd_Bifq-6k,43159
astropy/io/votable/data/VOTable.dtd,sha256=SUApDh5tTZgPBHblHZ_n-1bu0nbYCk3KKCbeHOWkMxA,4743
astropy/io/votable/data/VOTable.v1.1.xsd,sha256=g8xRsjughA8Z5Su7R3IsluMcXIlpQ_yubHB5NrOaBcU,16813
astropy/io/votable/data/VOTable.v1.2.xsd,sha256=APJZe6szKYKC7oz-JxQoVYHthXZpPdMNl4W3VIlHt6k,21521
astropy/io/votable/data/VOTable.v1.3.xsd,sha256=VEh5KFEyiK9yV7JRF-8XzPvQhUDd2THoyOFGEyeJ37c,21829
astropy/io/votable/data/VOTable.v1.4.xsd,sha256=je6xuXJR7A3QPm7MvGb03-rXuPC_rn2SMVJveqhE9Po,24230
astropy/io/votable/data/ucd1p-words.txt,sha256=12dvZQcew-CJQjoM5FwDrW8gyhYMjzGijsDNRDgf1T4,31252
astropy/io/votable/exceptions.py,sha256=2X1cnWXSbBNAHSgO5XVu3-EnBOuPh5Su1P6-RINa4Jw,49520
astropy/io/votable/setup_package.py,sha256=j66MHVfX8VfkngfcLEKYq7jS68rvRjIiFqk2rqWjUME,344
astropy/io/votable/table.py,sha256=K62Bsv9ImtjBOED0DUtrmuxmPKlkdUMMWr-1w2wRmF4,13743
astropy/io/votable/tablewriter.cp39-win_amd64.pyd,sha256=Ux-Mc9ROCKtMVCqaO2M33FY2iemgL6VH7V-Ob38gak4,14336
astropy/io/votable/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/io/votable/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/converter_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/exception_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/resource_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/table_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/tree_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/ucd_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/util_test.cpython-39.pyc,,
astropy/io/votable/tests/__pycache__/vo_test.cpython-39.pyc,,
astropy/io/votable/tests/converter_test.py,sha256=4_PtwTgRQOvGQU_yICFTLVHQnovn9GcsweJUjUjVCNA,10964
astropy/io/votable/tests/data/binary2_masked_strings.xml,sha256=y9L6CDwquzp399x0qJ_tzwgrPaMkBJx1aAg0vSghfvE,2497
astropy/io/votable/tests/data/custom_datatype.xml,sha256=LxhKZMd_1rUCOgHcZdw4EeI3I_FmnoLpcNfXGUkEcEg,475
astropy/io/votable/tests/data/empty_table.xml,sha256=QzqVRXD8vV6q7lA59lJaOHwzQxfWxrSvP20XPp_WxTs,444
astropy/io/votable/tests/data/gemini.xml,sha256=X26VXOtDMxu8uYyjk2ZgUghGG2SSCO24GbhNWgfKFN4,9465
astropy/io/votable/tests/data/irsa-nph-error.xml,sha256=-7_dbaET5UdlDmCfELBHYy5TUjfQxhe0E7G8PwNmSmE,213
astropy/io/votable/tests/data/irsa-nph-m31.xml,sha256=Pl_PyICrWzVBT2Xy2bfrfMcf51YuFaNb92AGX78B65k,9432
astropy/io/votable/tests/data/names.xml,sha256=lColivRHvR7-Yh3Lv5IcsIW3kybzhFwKT6e7WLX6HfA,10472
astropy/io/votable/tests/data/no_field_not_empty_table.xml,sha256=sPHmAd5Q6_M3WwoNtic16Ow1LxtQLzEo3zd1mv09mqI,539
astropy/io/votable/tests/data/no_resource.txt,sha256=2Vu0cy1K6pQLsc5rTXe4g5QbOIjbOhHG6YHqJoDYF7M,200
astropy/io/votable/tests/data/no_resource.xml,sha256=8ausYMQo9seWGFcKi7cYtvLBth9TTTGnZsqHnMEL07g,259
astropy/io/votable/tests/data/nonstandard_units.xml,sha256=4786FuAOdUgCu-_jLj-uAynlGYEjYP_RPMU4cvEINsM,556
astropy/io/votable/tests/data/regression.bin.tabledata.truth.1.1.xml,sha256=99PamvTw-HjxseJ_wJZgDmYDNUADIMnfd_JJRDI-q_w,9303
astropy/io/votable/tests/data/regression.bin.tabledata.truth.1.3.xml,sha256=SDwgh1GVAFn6uBW5gqMvFPVCFjix9KJw3WwA4rrMfe0,9202
astropy/io/votable/tests/data/regression.xml,sha256=S310XFoRb5Y8hoX6IZ8dAIc-ZWkRoVO49YaaPxpZOFI,9438
astropy/io/votable/tests/data/resource_groups.xml,sha256=1XDJoDzi6Gsv2_3seSiZ0JV5bXGQh7xoQLFdoG1abi8,565
astropy/io/votable/tests/data/tb.fits,sha256=4YkaVFOvLMyhaho9g8lnhepij8ZZpFPfhMkNhTnJ8Kc,8640
astropy/io/votable/tests/data/timesys.xml,sha256=hS5QN5ltn1DWIiQky-tUhNZKm9eiIysUfK-sYRsdnhk,1389
astropy/io/votable/tests/data/timesys_errors.xml,sha256=_qiuTgLxSsGluTzQHxJkbDpgUaf5IG2hlQnN2JgdRww,1316
astropy/io/votable/tests/data/too_many_columns.xml.gz,sha256=IZWk7d7RsbRDtkmGAQlGVTWtIisoyFxirJMEP007OGg,1734
astropy/io/votable/tests/data/valid_votable.xml,sha256=joII2mzxfHdylWUgG37RARWLChF03qCV-36BEKUgMvs,1725
astropy/io/votable/tests/data/validation.txt,sha256=r8wml8HLOsaCG_HlfJ4bwcKAZvGCyY4TEHKwt9DM4GU,5641
astropy/io/votable/tests/exception_test.py,sha256=29LW_RrU9NBRYfQqocvMDEfnM72bSTVWBae_v0dX8BU,1817
astropy/io/votable/tests/resource_test.py,sha256=ZxB_Z2Mabj7ILRd-HQQlOZKTxNcauw1XNp_zdZIIFMw,632
astropy/io/votable/tests/table_test.py,sha256=eZ7-cyCTB1S4sel1-frJ52aYGfjQ23kNCP116GXS-wA,12331
astropy/io/votable/tests/tree_test.py,sha256=MCFB6vdncYG8v2aPOEZtprlW6FDMeXAW0ktTlmQqgX0,6126
astropy/io/votable/tests/ucd_test.py,sha256=7GGsLgUHG5wfxgB7UztdCjS3cjfN2TVRbKe3O-do47A,1859
astropy/io/votable/tests/util_test.py,sha256=ZMvMDbZlnXZZ_fiHDCwABTRYeAUFmFtStk2vM-Fuaso,1697
astropy/io/votable/tests/vo_test.py,sha256=8XGEEmUGDckQJTtFNmVXg7GINq571LgN4OLpa1e_LOw,35397
astropy/io/votable/tree.py,sha256=GZiY4XfuWs4mXn6irt6aR3lcO-SXUs_trWgPQPuVTCY,131635
astropy/io/votable/ucd.py,sha256=rQnn-kZ1cBnlMqeSo-d9VdFTuZYtU6Vlt24-2zlbMPg,5664
astropy/io/votable/util.py,sha256=TTXgQek4psWVMC_WJpS8GUgqa2sFCySTXhZ3-rvBWE8,6145
astropy/io/votable/validator/__init__.py,sha256=WyltlIlT0iOSDieThCAigyNBQj2PpMybCWPicw1ksxM,157
astropy/io/votable/validator/__pycache__/__init__.cpython-39.pyc,,
astropy/io/votable/validator/__pycache__/html.cpython-39.pyc,,
astropy/io/votable/validator/__pycache__/main.cpython-39.pyc,,
astropy/io/votable/validator/__pycache__/result.cpython-39.pyc,,
astropy/io/votable/validator/data/urls/cone.big.dat.gz,sha256=0LS4lHCk_6qCYuQh2Ztt76Bv1hn0vkrm3Mszxi7ietI,168333
astropy/io/votable/validator/data/urls/cone.broken.dat.gz,sha256=bZl8fkJU_PadSrgcLF_S3voNKppeinE9p7HsasuEvDs,350
astropy/io/votable/validator/data/urls/cone.good.dat.gz,sha256=542ebGfr2MNYqh80ZneRYy2OeHZvq6YJ2wKCvaqf4V8,168334
astropy/io/votable/validator/data/urls/cone.incorrect.dat.gz,sha256=HXLwdnB1XImuLG5spuLE85baSIPRBUF8Jsxi4raZyOw,721
astropy/io/votable/validator/html.py,sha256=2s9lMNnERNHt_0ptCBKtLMzZdItedrazfyt19E6A9Xc,9901
astropy/io/votable/validator/main.py,sha256=uEdjzRNy12qGO_aPOJJjv_nsozFZM4okcxoTREguAnw,4779
astropy/io/votable/validator/result.py,sha256=uDBGXQkZGZddjlqLmTYBJmthxwRXiAiGKtr6C7dxfI8,11239
astropy/io/votable/volint.py,sha256=7gZJdPkQzZvlP8hVDt5mm4npSo3GbK_hC7DPClc3qgw,498
astropy/io/votable/xmlutil.py,sha256=dryuO5CP40i3gmVuI4z8m3lgli60LQndQjVzVXN2QU8,3558
astropy/logger.py,sha256=q5TabtUfjiR8tb9PKb1UqoWhLphJSuvxSSmIJvXap2k,19579
astropy/modeling/__init__.py,sha256=sxWrgU2oXpkBesyaVZSlfLgGwdiYgc4IIlFRd5vvMo4,415
astropy/modeling/__pycache__/__init__.cpython-39.pyc,,
astropy/modeling/__pycache__/bounding_box.cpython-39.pyc,,
astropy/modeling/__pycache__/convolution.cpython-39.pyc,,
astropy/modeling/__pycache__/core.cpython-39.pyc,,
astropy/modeling/__pycache__/fitting.cpython-39.pyc,,
astropy/modeling/__pycache__/functional_models.cpython-39.pyc,,
astropy/modeling/__pycache__/mappings.cpython-39.pyc,,
astropy/modeling/__pycache__/math_functions.cpython-39.pyc,,
astropy/modeling/__pycache__/models.cpython-39.pyc,,
astropy/modeling/__pycache__/optimizers.cpython-39.pyc,,
astropy/modeling/__pycache__/parameters.cpython-39.pyc,,
astropy/modeling/__pycache__/physical_models.cpython-39.pyc,,
astropy/modeling/__pycache__/polynomial.cpython-39.pyc,,
astropy/modeling/__pycache__/powerlaws.cpython-39.pyc,,
astropy/modeling/__pycache__/projections.cpython-39.pyc,,
astropy/modeling/__pycache__/rotations.cpython-39.pyc,,
astropy/modeling/__pycache__/separable.cpython-39.pyc,,
astropy/modeling/__pycache__/setup_package.cpython-39.pyc,,
astropy/modeling/__pycache__/spline.cpython-39.pyc,,
astropy/modeling/__pycache__/statistic.cpython-39.pyc,,
astropy/modeling/__pycache__/tabular.cpython-39.pyc,,
astropy/modeling/__pycache__/utils.cpython-39.pyc,,
astropy/modeling/_projections.cp39-win_amd64.pyd,sha256=OZcLBvmV2cDmUG5ACnX2glziSxX9jntKn9AOMlYeByE,113664
astropy/modeling/bounding_box.py,sha256=Dim2MsunezOYQ60Ge_J9i_tsAqfwXLEKjPQs7eHDTgg,50745
astropy/modeling/convolution.py,sha256=PAM-oeoY8Qkyxa_nG1RDsOM3Q6Kbad_TGY8c9F-3QNg,4101
astropy/modeling/core.py,sha256=DCOjVb1wEnZyZ1W9vGIc45MoFYbYZ6N7GJnUNd6Y8pI,175296
astropy/modeling/fitting.py,sha256=mqHNJ6M2qQthXwXUr_AdEGFhIeCvuF_EYxvUVwnUhJs,71722
astropy/modeling/functional_models.py,sha256=yV_Q4JAuTD3G9iLb-rFbfuBm9I3OMbP0-m1sdwI50rU,106011
astropy/modeling/mappings.py,sha256=3EsOY1ppbo2hmClC3uydweR3oDaob4HckixFk-TJlT8,10835
astropy/modeling/math_functions.py,sha256=PISV5Wzf0Kr7Ha1YIT-sXkvMzx2xAGhGJjt3Z4QsJno,2449
astropy/modeling/models.py,sha256=yGJCsGcDkcw0aqOSbcelF6N3YqxzK_K5nTBt_Yotd7A,2697
astropy/modeling/optimizers.py,sha256=fpe8k5b3cLKe2o1yHeEnvyN1s4K3LLbiAQYnC_-7oKM,7257
astropy/modeling/parameters.py,sha256=s65g5zeHl_xU4c7hPyqN8j3Ff5RUl6qEZzKRarYTKV4,24277
astropy/modeling/physical_models.py,sha256=dTSLdaql6WvAPweaGAwO2tR-hAfQpkBGcNeRYRB2dec,26074
astropy/modeling/polynomial.py,sha256=My96xebrH_9qXR9dr6CYWTlD5VefzG_95aI6YvkHPtk,55362
astropy/modeling/powerlaws.py,sha256=k2Ekg2zRnAL-3zOyGZmU489j4SccjDIRi4wIKmAPFew,16704
astropy/modeling/projections.py,sha256=xOGJsZloMzbii1ke29pzY441cci6OeCKa-hmrHlDp9A,57424
astropy/modeling/rotations.py,sha256=cV6OwqbzG5z-s64pov9RjF3RPcqzzC9RQTYJ3kMO5oI,17495
astropy/modeling/separable.py,sha256=_5FKPWH72GuKpeD8h-uWQz0q0dgfEK4Mx3pV6nfg6pM,9882
astropy/modeling/setup_package.py,sha256=k6WVc1RYVDLlf737qvHXepfiEA7URQlcfxLrlVvtfDU,2236
astropy/modeling/spline.py,sha256=V0O5nUrJSIF6W7ImLnr_lKV-EBEOhw5kTdIYeanBkds,21314
astropy/modeling/statistic.py,sha256=XJqJ1mwxwxAY-a9GCidkcfRjK_k-ULrlbtMRaiZ_i2U,5484
astropy/modeling/tabular.py,sha256=-6zKNcbiP81gTCoOffHzvxaTv9Kmod2E2Ed1d1XaGp4,12325
astropy/modeling/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/modeling/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/example_models.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/irafutil.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_bounding_box.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_compound.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_constraints.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_convolution.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_core.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_fitters.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_functional_models.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_input.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_mappings.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_math_func.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_model_sets.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_models.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_models_quantities.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_parameters.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_physical_models.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_polynomial.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_projections.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_quantities_evaluation.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_quantities_fitting.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_quantities_model.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_quantities_parameters.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_quantities_rotations.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_rotations.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_separable.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_spline.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_statistics.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_units_mapping.cpython-39.pyc,,
astropy/modeling/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/modeling/tests/data/1904-66_AZP.fits,sha256=UdlUUNNctsjGClnnLmk7cSeudgfOzlkFIG9kawpMAkY,161280
astropy/modeling/tests/data/__init__.py,sha256=M1f7jkPOdRuYfiO4C4DJZszhgDc51-q6Godu3uAPYQg,128
astropy/modeling/tests/data/__pycache__/__init__.cpython-39.pyc,,
astropy/modeling/tests/data/hst_sip.hdr,sha256=CsWzGHfx5hu827AkZi2g3njjc_Ybqq8qRjYZrpqrza4,3305
astropy/modeling/tests/data/idcompspec.fits,sha256=JGJZ6Pr3VAuNRxBJvfZblFHD22g8O-8ZI2KxLaBtvog,823
astropy/modeling/tests/data/irac_sip.hdr,sha256=nXK74F5W0GD2N2x7vr31fvThf1jwyor5qicAGelfo_A,19520
astropy/modeling/tests/data/spec.txt,sha256=d5SdZ8LWuiaXmZxHkTcEjDl0x5TvH2gWMj_mDMFg_cE,33385
astropy/modeling/tests/example_models.py,sha256=-wDBuZOgX1fgI81LiWcDjNLEcuDdBHfbsAq82LSbeX8,12363
astropy/modeling/tests/irafutil.py,sha256=h9isVTM0z1SL8kkJvZT9sFW4C9yP-3gLv66IiRnUcxE,7024
astropy/modeling/tests/test_bounding_box.py,sha256=BMrlOUktsYWzP0SDkEJAhDm5CnQXBvythjH0p0g9RqM,110761
astropy/modeling/tests/test_compound.py,sha256=nrDMXLVJRjputXpaO2-2P9mjha6WZ2h7EAvnm66A42I,31697
astropy/modeling/tests/test_constraints.py,sha256=LbsNVZQdjwZkdnnM9XCW__2_-T0jPkBK0SMU9U3cxVw,22852
astropy/modeling/tests/test_convolution.py,sha256=pF6AY54Au1aN-TQbj0BEDTHHnJL4A7C25Xs5dQ0Z80E,2627
astropy/modeling/tests/test_core.py,sha256=hWLrBFXJJOnrz7V-Q3ty_3QLI6E4C9RIa6UmVxgGnG8,45501
astropy/modeling/tests/test_fitters.py,sha256=_YV62g_SbpaWbtUXHPheiiylwdyJdsPTqSS_ahXe7bA,49705
astropy/modeling/tests/test_functional_models.py,sha256=cBpE5XTcCJ0lldpmhwCEayyqCZr-mJLViY9UrQISgEM,17809
astropy/modeling/tests/test_input.py,sha256=vU4BnhjZ6VCD4ruB44F0-GP994HA2YT-Km-hrHjvqO8,36684
astropy/modeling/tests/test_mappings.py,sha256=l5intuPVRogopjU6bwImem8_f8cTy2y5F7N3tCQmLhk,5318
astropy/modeling/tests/test_math_func.py,sha256=LlrF4kqCj7ZYYIjIr8Udvx6PaAP4g536VH8AtnPi74c,846
astropy/modeling/tests/test_model_sets.py,sha256=1toC9lV2p8MBYh3By0lorD16tE3aC_jO8qm_wnSdZEU,18698
astropy/modeling/tests/test_models.py,sha256=4yAH6vR3Gd_jdZLyWQlYv_PwTqLbqcUw_VUND2oGKos,38607
astropy/modeling/tests/test_models_quantities.py,sha256=lCCAVefq2KvEIWSUH0FP6YnPT5JjAWGKFYqrv8alcaU,21442
astropy/modeling/tests/test_parameters.py,sha256=A3X4vtq2e5FrD6wBlP8LU5SE5wEUYtPaXSlxBTvMzo8,37518
astropy/modeling/tests/test_physical_models.py,sha256=on9Mb5o6BHuvVc-0RPvvhJDSRM_gVwVkiyYgA4UnRWY,21501
astropy/modeling/tests/test_polynomial.py,sha256=cYsz01eH1gpOQYar6uZGgeBNIXKI3d276rGLJQlql1g,24443
astropy/modeling/tests/test_projections.py,sha256=mXmjArvrJ72ipeH2BOIFkzw4ncD8IY4EEWdlFtR98K4,43641
astropy/modeling/tests/test_quantities_evaluation.py,sha256=x_n4jr3oS_lTx-XO88FtgvO-gCy9VcRH2l6jXlgf2aY,14077
astropy/modeling/tests/test_quantities_fitting.py,sha256=_lT3GndwE37e-o82aBC_tVSEPCokL80KKwkaQQ341cM,9223
astropy/modeling/tests/test_quantities_model.py,sha256=m6zLQjlvTHklwp2zh5nvhOBRrv9ZffMS2_YsXXa9Jts,3597
astropy/modeling/tests/test_quantities_parameters.py,sha256=c3SJIwLz7TB1sIaHxzudpJsPTZqsCLfToWM9nNLJ5qI,11959
astropy/modeling/tests/test_quantities_rotations.py,sha256=rdthvGkYhOOGcaLkcqsOf580MUp-kh5WfZrEwlvcy3k,4047
astropy/modeling/tests/test_rotations.py,sha256=Gx6erdC_Lo4_Gu1Gl0Y-v0rj0UKolBfCfumV9ZyA5Pc,13526
astropy/modeling/tests/test_separable.py,sha256=CucU6xLimso1uuaTs-f1lPR-EhNfyl5rH3SbMxQwx2I,5640
astropy/modeling/tests/test_spline.py,sha256=R0w0BSV8R2R-itKYnAS0KhoDlkRMbqmffKmgBeASrMA,54371
astropy/modeling/tests/test_statistics.py,sha256=4il5RcATh0-SVE9bYKJro81_HXspIOzjA4B07rn0sho,3046
astropy/modeling/tests/test_units_mapping.py,sha256=boN4b4o3L2NRTDnzWEhjRD-BRweNBMTaimaxryvOwSg,1767
astropy/modeling/tests/test_utils.py,sha256=N90DuiE2lnHSdM3Xyn1LR8JUEsy0axhp0g820JLz5uI,1931
astropy/modeling/utils.py,sha256=ykuAtNe-RG_ZOEZ2lW00IaGGLPtATINbDxAK6wTgbaI,13561
astropy/nddata/__init__.py,sha256=SEI4DmMYNtyjHEcAI-It9FE_LqqXXhYI55-n1csIke0,1526
astropy/nddata/__pycache__/__init__.cpython-39.pyc,,
astropy/nddata/__pycache__/_testing.cpython-39.pyc,,
astropy/nddata/__pycache__/bitmask.cpython-39.pyc,,
astropy/nddata/__pycache__/blocks.cpython-39.pyc,,
astropy/nddata/__pycache__/ccddata.cpython-39.pyc,,
astropy/nddata/__pycache__/compat.cpython-39.pyc,,
astropy/nddata/__pycache__/decorators.cpython-39.pyc,,
astropy/nddata/__pycache__/flag_collection.cpython-39.pyc,,
astropy/nddata/__pycache__/nddata.cpython-39.pyc,,
astropy/nddata/__pycache__/nddata_base.cpython-39.pyc,,
astropy/nddata/__pycache__/nddata_withmixins.cpython-39.pyc,,
astropy/nddata/__pycache__/nduncertainty.cpython-39.pyc,,
astropy/nddata/__pycache__/utils.cpython-39.pyc,,
astropy/nddata/_testing.py,sha256=BxFK0cqC_cS0Ul_gZdhdNEp7a34kXY50VFyx4Iy131Q,1605
astropy/nddata/bitmask.py,sha256=kHzeL1w2nlPbhHvEkaiFHINjHU7Xt3JjjJ_6ZwZzaCE,27653
astropy/nddata/blocks.py,sha256=ujNWIFZwIsPgkM_ehMZxxNC3sqhqnxWwa2-4PM7l-W8,6462
astropy/nddata/ccddata.py,sha256=plLBGvjHdjZlXyC5TrPu4vMwd-GZganKIgL7atKz76w,27949
astropy/nddata/compat.py,sha256=KjVQzFKGjJs-lZm7GRAd0Ak1HvaPVE_gnrGc5uw8HEk,9967
astropy/nddata/decorators.py,sha256=fmaLrk8gJYqWmPKznhT1zsCk5iZikLlHrrDAhn3voog,11804
astropy/nddata/flag_collection.py,sha256=_1mIPyKCTDMUILkmTUsSfmGk-Y6IaFYq7zp-7fHARVs,1662
astropy/nddata/mixins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/nddata/mixins/__pycache__/__init__.cpython-39.pyc,,
astropy/nddata/mixins/__pycache__/ndarithmetic.cpython-39.pyc,,
astropy/nddata/mixins/__pycache__/ndio.cpython-39.pyc,,
astropy/nddata/mixins/__pycache__/ndslicing.cpython-39.pyc,,
astropy/nddata/mixins/ndarithmetic.py,sha256=uBeN7gplvbfJc0sYeDSPO6SDAd6xOu-6xrcmIFmrLzc,24195
astropy/nddata/mixins/ndio.py,sha256=0T6VOr2rlF0A60E5DnyajIrJQ62fZu7fCcSqmtg47bI,3656
astropy/nddata/mixins/ndslicing.py,sha256=AtwyS8IK-yIzaG-AyV-aKSrH1TZyzghxmG9rPsMC_co,4359
astropy/nddata/mixins/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/nddata/mixins/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/nddata/mixins/tests/__pycache__/test_ndarithmetic.cpython-39.pyc,,
astropy/nddata/mixins/tests/__pycache__/test_ndio.cpython-39.pyc,,
astropy/nddata/mixins/tests/__pycache__/test_ndslicing.cpython-39.pyc,,
astropy/nddata/mixins/tests/test_ndarithmetic.py,sha256=Mm_5SpT_d-LxAih2kJ7dWeu3e-ibRGMWyZXaRgUfFPw,47355
astropy/nddata/mixins/tests/test_ndio.py,sha256=scmDMueVcldnnjqqZIUadwMQvDtLcUKwiryhb9v7Vv0,274
astropy/nddata/mixins/tests/test_ndslicing.py,sha256=EzRY7nIz9pQRhtj91RL9ldhuiMc7OsAz5CsxjMvazGQ,5997
astropy/nddata/nddata.py,sha256=GIdgdc04BZdsF7LT35XcA_3JGQu4dmn31NMQbSj1XqQ,12658
astropy/nddata/nddata_base.py,sha256=rIzQ8cD_AISTP8ChW5nbBc6RjfZNYxi6nVNpgDLT3Nw,1797
astropy/nddata/nddata_withmixins.py,sha256=HALbi7zkzqTYVHG06xZwuHjFjYntp2V6QdipZWZlR6c,2222
astropy/nddata/nduncertainty.py,sha256=dVr8OsnMO-kXDOnDaCkBLQPb_Bv1AVzW2P25A8hnYHw,36367
astropy/nddata/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/nddata/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_bitmask.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_blocks.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_ccddata.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_compat.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_decorators.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_flag_collection.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_nddata.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_nddata_base.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_nduncertainty.cpython-39.pyc,,
astropy/nddata/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/nddata/tests/data/sip-wcs.fits,sha256=nh6D7nhMRG5OjD_66LcROwrR7V8NeMF5VcQXg3zytMg,23040
astropy/nddata/tests/test_bitmask.py,sha256=gw0iH_IBORtnsQR2ulmN7_4geQfmd64L5Pw13ZbERQ8,10907
astropy/nddata/tests/test_blocks.py,sha256=01JtvIE9JntjD4tItFRhAMkOs12NRNw2mLiQrvqnhYc,5957
astropy/nddata/tests/test_ccddata.py,sha256=grEJemzJOvvkWBSm2h1oTdiXRhT76IZ3906lqavTl20,38332
astropy/nddata/tests/test_compat.py,sha256=NPzeeXetB_cdd4KKJA8fL5_o11dvRywhyf22ALahhHo,4958
astropy/nddata/tests/test_decorators.py,sha256=tKbfxXq2e4FBiWVQ1vTZwB5IvEOzBXnNGEoL1KbKFgw,9416
astropy/nddata/tests/test_flag_collection.py,sha256=FUr0QRYKrdK_wp1TQ06rHR7aq3vXgbWQsXj6fdKUPJA,1485
astropy/nddata/tests/test_nddata.py,sha256=p35me2tfzjRDRPQYwlwRyEtGMDqe3BE6qebFcCnygpQ,16528
astropy/nddata/tests/test_nddata_base.py,sha256=wZlcIdJE8-zK0WlXfB_jG1-7MK_09SgNvhvn-73p730,831
astropy/nddata/tests/test_nduncertainty.py,sha256=wUq_IXr1ykneX4dEFoK74CC_3sp4nhoJqiEfjSFl9fU,13578
astropy/nddata/tests/test_utils.py,sha256=BvIY61XQUsx9-t9udzjXomIs1cvXdGN9Qlb5BBVBrUk,21318
astropy/nddata/utils.py,sha256=Uw56pGtoSpMRN4EkvYUwuTRg81ChWssS3P3XT_2Mexw,31127
astropy/samp/__init__.py,sha256=w7W3iFQ0i8kv6WNpTsX_8ZdYQfb91uajlhFLVNDSdmw,1039
astropy/samp/__pycache__/__init__.cpython-39.pyc,,
astropy/samp/__pycache__/client.cpython-39.pyc,,
astropy/samp/__pycache__/constants.cpython-39.pyc,,
astropy/samp/__pycache__/errors.cpython-39.pyc,,
astropy/samp/__pycache__/hub.cpython-39.pyc,,
astropy/samp/__pycache__/hub_proxy.cpython-39.pyc,,
astropy/samp/__pycache__/hub_script.cpython-39.pyc,,
astropy/samp/__pycache__/integrated_client.cpython-39.pyc,,
astropy/samp/__pycache__/lockfile_helpers.cpython-39.pyc,,
astropy/samp/__pycache__/setup_package.cpython-39.pyc,,
astropy/samp/__pycache__/standard_profile.cpython-39.pyc,,
astropy/samp/__pycache__/utils.cpython-39.pyc,,
astropy/samp/__pycache__/web_profile.cpython-39.pyc,,
astropy/samp/client.py,sha256=UWQQY3pVBBKJRkVjQnoCy6qhe5rE7ue5dp0hZD9oJjw,25448
astropy/samp/constants.py,sha256=iaUyIuAOnTNH2ZyO5cvGXhMMSW78P9d8eKSIUQ9-IaA,793
astropy/samp/data/astropy_icon.png,sha256=anxCPITjfCCiKWJrjEZMYCBVWvKxq2tP-Rlh-W5Pk5s,1434
astropy/samp/data/clientaccesspolicy.xml,sha256=m1iU7PmY6w3H-oTxK1BkrvSAIuhTaJrUOY212bixXZI,284
astropy/samp/data/crossdomain.xml,sha256=AzMTGKyFVzw636h5aNgCn9nG-BwUg6XwVIh0JyXC-GE,311
astropy/samp/errors.py,sha256=46HeFnSerJi9205ikquyPTu5_At62tIbIiz64hnZi38,637
astropy/samp/hub.py,sha256=8Tt0yFmoRWX3bq5yn7kwXgp6OFl8HFlHnq_-NhFzCGU,54980
astropy/samp/hub_proxy.py,sha256=cI0tLImD8c7N6QV7oka_f3zhP1J3Bj5yNw8V7SwJB_g,6098
astropy/samp/hub_script.py,sha256=Xu9xKA80YJGXnhg9g-Ybo4qAlExR_ivA4Wd5IRaaeLM,6571
astropy/samp/integrated_client.py,sha256=vJBe11-cuqDqTYlB1BLlld131HgxHVpxB0qS3wWKxnY,17173
astropy/samp/lockfile_helpers.py,sha256=R6Grg0horsvniosiPnfn__2m_IwbYMDEO9zJwcR5NxE,8148
astropy/samp/setup_package.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/samp/standard_profile.py,sha256=YS2scz7OMf2BrH5lJjN1lmFr2SJ318INCCvwoWhom5U,5580
astropy/samp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/samp/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_client.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_errors.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_helpers.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_hub.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_hub_proxy.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_hub_script.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_standard_profile.cpython-39.pyc,,
astropy/samp/tests/__pycache__/test_web_profile.cpython-39.pyc,,
astropy/samp/tests/__pycache__/web_profile_test_helpers.cpython-39.pyc,,
astropy/samp/tests/test_client.py,sha256=cs5eLeFNN3W4u2yku8GZHnB-SEVKPc9_JIOwSBpLUuc,1267
astropy/samp/tests/test_errors.py,sha256=LNQkkBxp9sOKU2ieGEzKH9EIjS-cfF92IEcmCNJVkXk,618
astropy/samp/tests/test_helpers.py,sha256=ob_8hitf0YxqfkyXpisTHbFKUAOL90Wc7UPgL7msY-E,2206
astropy/samp/tests/test_hub.py,sha256=GIinj5cZ-0OUzGQ6LUHlhsAmrpEw69yra2YLvTeXpOc,909
astropy/samp/tests/test_hub_proxy.py,sha256=uH5cvuNPgnClzwXKtGYNxE6Ct0pUKyBZXfK-H5TtUNk,1202
astropy/samp/tests/test_hub_script.py,sha256=TmS2u7ixrUtfqSRBijUneG2hEZeudB6n1Dzglgmrcmk,469
astropy/samp/tests/test_standard_profile.py,sha256=gP7-lsOHoiGH2Z9gU3KsCtF0KoXJG0uKMKfm40KlN1U,8639
astropy/samp/tests/test_web_profile.py,sha256=MIQmh231oWyDJNvQ3PedFM_7ixOavDtDKL7_qavpnw0,2963
astropy/samp/tests/web_profile_test_helpers.py,sha256=_0NcbZ9Vy7_WvS6s99BvbonRPlFqwH2UT05cG1zlroo,9585
astropy/samp/utils.py,sha256=1LLZCFZPQoNVPgrP4K7kzTU5QpE5FpYs1mgBkBlTMQg,4777
astropy/samp/web_profile.py,sha256=BVPcaSkq1pAIkO-VwHEfHNon7XdM0C0XFzLRrgKtBkA,5583
astropy/stats/__init__.py,sha256=CbvTjkZqmHkNJ0RK_6ecNBimuMjkUXmLQIg6rxs6I4c,1497
astropy/stats/__pycache__/__init__.cpython-39.pyc,,
astropy/stats/__pycache__/bayesian_blocks.cpython-39.pyc,,
astropy/stats/__pycache__/biweight.cpython-39.pyc,,
astropy/stats/__pycache__/circstats.cpython-39.pyc,,
astropy/stats/__pycache__/funcs.cpython-39.pyc,,
astropy/stats/__pycache__/histogram.cpython-39.pyc,,
astropy/stats/__pycache__/info_theory.cpython-39.pyc,,
astropy/stats/__pycache__/jackknife.cpython-39.pyc,,
astropy/stats/__pycache__/setup_package.cpython-39.pyc,,
astropy/stats/__pycache__/sigma_clipping.cpython-39.pyc,,
astropy/stats/__pycache__/spatial.cpython-39.pyc,,
astropy/stats/_fast_sigma_clip.cp39-win_amd64.pyd,sha256=NYKH3dcBo9lHQi1TvJeKFOOmb-N1xqy-UteF1kLOBHk,14848
astropy/stats/_stats.cp39-win_amd64.pyd,sha256=VdGdiU918jgrcwHCFkeOJ07xVYS4n6TUImwZuLvKgz0,164864
astropy/stats/bayesian_blocks.py,sha256=vI06OQV8i4vYcxNLksPUizhmDTMZN-Le8C8tqx1ROjo,18789
astropy/stats/biweight.py,sha256=0qPeZH2Ki9VYChe79nMHQzPnTGdElJy6z2P75qnlM3I,27723
astropy/stats/bls/__init__.py,sha256=UUEC9nrRgOlpmznW-K3VPGDT7kVU9gqyBklmMRls6v0,2112
astropy/stats/bls/__pycache__/__init__.cpython-39.pyc,,
astropy/stats/circstats.py,sha256=-9OIT8O4c6mium5YY1j3bgG_KBmCEv5KiozTa2QSi_U,20143
astropy/stats/funcs.py,sha256=s9YqXhzqaqKH2nbKsDlzm-zxGRT6Ywpjh0ynRaYsA1U,62020
astropy/stats/histogram.py,sha256=bLglgKKIGRWzCsWeJOsILgJA0Y71A65ElfPFaU5iNxk,12621
astropy/stats/info_theory.py,sha256=DC3w5VYKFawifeYgWpdZGDQjnYOaZ99PgY-45nATY6c,15046
astropy/stats/jackknife.py,sha256=gbYy7jBoxXg_1eo8kkbJ3IRP8WYyvZYM7POcOjb2uTY,5913
astropy/stats/lombscargle/__init__.py,sha256=wUNRqfyRF1za1XsHAUEikM-IFuOQ7tvXTt3_uWHrntg,1287
astropy/stats/lombscargle/__pycache__/__init__.cpython-39.pyc,,
astropy/stats/setup_package.py,sha256=QHSPuNUzVFcFTfGihF8jsX4xPlm5rUlaBXj665Zep70,593
astropy/stats/sigma_clipping.py,sha256=38E7rJNTV2KqPWASYQ3PkhIK0tbOe_GkJwDUV7rSFEc,39050
astropy/stats/spatial.py,sha256=Sh0c2EKjy_RbPciO8xo-7NG2da36vBjhCXIYzwCndQ0,12743
astropy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/stats/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_bayesian_blocks.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_biweight.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_circstats.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_funcs.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_histogram.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_info_theory.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_jackknife.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_sigma_clipping.cpython-39.pyc,,
astropy/stats/tests/__pycache__/test_spatial.cpython-39.pyc,,
astropy/stats/tests/test_bayesian_blocks.py,sha256=mTf7wB6qyomjkHiZEAxKGfsSzm-1AhcIeu-UQAr7EqU,5482
astropy/stats/tests/test_biweight.py,sha256=VoSHHU_OfcCwn3Uoyug9-9FWIYslMuHVdg94yrpwSQo,19541
astropy/stats/tests/test_circstats.py,sha256=iwssRAH8T64w7g_Uz3HK9cJVkqxZV_vRAEcqrGL0AMA,4240
astropy/stats/tests/test_funcs.py,sha256=Xf60iH6P1tZL1O26n1igf6ZpXQxAUoysVdH_zmfhE2U,31077
astropy/stats/tests/test_histogram.py,sha256=_LV2Tjo9hSV7GxN_4HfqoPbs81AwakP0rdHqlZUqaaw,5674
astropy/stats/tests/test_info_theory.py,sha256=QSE-nNLyJYfDvye0ktsKvDiVo6VY8ScMyodUAF2wLa4,2755
astropy/stats/tests/test_jackknife.py,sha256=c6WdDtX8KqjUGPnnWsK9OMFl5GZ9E7aR91j78btf5Ac,2021
astropy/stats/tests/test_sigma_clipping.py,sha256=z74mAaHeTRxli5Bz9dAPvi8t7xC1ZpinbgJNonNe37s,19304
astropy/stats/tests/test_spatial.py,sha256=VtHLAqmuKFu_H-zR0XDP7W3q9U5bX5BUaB3pSCIpaO4,5735
astropy/table/__init__.py,sha256=n1X1tsABrHs4dkjQgurwBeKXNQWLdwxcy-oXIPH7VkI,3389
astropy/table/__pycache__/__init__.cpython-39.pyc,,
astropy/table/__pycache__/bst.cpython-39.pyc,,
astropy/table/__pycache__/column.cpython-39.pyc,,
astropy/table/__pycache__/connect.cpython-39.pyc,,
astropy/table/__pycache__/groups.cpython-39.pyc,,
astropy/table/__pycache__/index.cpython-39.pyc,,
astropy/table/__pycache__/info.cpython-39.pyc,,
astropy/table/__pycache__/jsviewer.cpython-39.pyc,,
astropy/table/__pycache__/meta.cpython-39.pyc,,
astropy/table/__pycache__/ndarray_mixin.cpython-39.pyc,,
astropy/table/__pycache__/np_utils.cpython-39.pyc,,
astropy/table/__pycache__/operations.cpython-39.pyc,,
astropy/table/__pycache__/pandas.cpython-39.pyc,,
astropy/table/__pycache__/pprint.cpython-39.pyc,,
astropy/table/__pycache__/row.cpython-39.pyc,,
astropy/table/__pycache__/serialize.cpython-39.pyc,,
astropy/table/__pycache__/setup_package.cpython-39.pyc,,
astropy/table/__pycache__/soco.cpython-39.pyc,,
astropy/table/__pycache__/sorted_array.cpython-39.pyc,,
astropy/table/__pycache__/table.cpython-39.pyc,,
astropy/table/__pycache__/table_helpers.cpython-39.pyc,,
astropy/table/_column_mixins.cp39-win_amd64.pyd,sha256=q3eBMGkoMV6wOedv_SJNzOk61ty4PlGUdlXwTCG2kFw,48128
astropy/table/_np_utils.cp39-win_amd64.pyd,sha256=3b7GrdZQQyN3XQXZaRmRtHxEIfN-LUYEG41uVPrrznw,43008
astropy/table/bst.py,sha256=p9x2BY8oIntxsmH7su155SH0DWj-QYOUysr8Z3QCR9g,13750
astropy/table/column.py,sha256=3deyP6iTpSl87sbcypfKX909hirvrVEpnrPZSt_GuX4,60366
astropy/table/connect.py,sha256=YyF2RGotEm5EU2795BXIr-aB2I7Lmaq2FKIUKl67W2A,4579
astropy/table/groups.py,sha256=Bq0A_sjc0PjP51P2P_Nle_YHTsiRG2B0PqB12LPyoMo,13902
astropy/table/index.py,sha256=BjtAiEhYLqv-RfCah8ZhPtgxnVMlSqvqC2gQvOQmakw,31193
astropy/table/info.py,sha256=S4MlnmIH1S2Nm2k9NoEvjFe1blIK6OACY9Mue6pxKpQ,7408
astropy/table/jsviewer.py,sha256=QMXOlcX7YC5hiUgn6n_EvHMBeBKdqirNpOFW636ZuzM,6543
astropy/table/meta.py,sha256=Dt-En83VolzUcszEcJw9743OT10Wvp4cIOVOwMP9gFo,13890
astropy/table/mixins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/table/mixins/__pycache__/__init__.cpython-39.pyc,,
astropy/table/mixins/__pycache__/dask.cpython-39.pyc,,
astropy/table/mixins/__pycache__/registry.cpython-39.pyc,,
astropy/table/mixins/dask.py,sha256=X5VRLeGLzyI5NS3fd2CiTYtM0SXQ5NRLDbURaLJ4pJc,1039
astropy/table/mixins/registry.py,sha256=a8I0IYHkQkLlULckyv1k4tPetibta52-4vYxfplxgGU,2699
astropy/table/mixins/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/table/mixins/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/table/mixins/tests/__pycache__/test_dask.cpython-39.pyc,,
astropy/table/mixins/tests/__pycache__/test_registry.cpython-39.pyc,,
astropy/table/mixins/tests/test_dask.py,sha256=GIzAPHcT3OdY0f0RUjkrqvexb8q3iDO4z7f-V3-e6e8,1972
astropy/table/mixins/tests/test_registry.py,sha256=HdqVR5Rzpd0pMgPndtpOw2xYl1Wb11UsCylEfBYW0p0,2496
astropy/table/ndarray_mixin.py,sha256=aC49gVa3VkSU2XFq9dqTf9sSB0psGnqRBTIJeI7SuZQ,2170
astropy/table/np_utils.py,sha256=PB9rLDLp3ujxi1jSLFJwxJiZ9qHOl4UT2-QxC7kTfT8,6339
astropy/table/operations.py,sha256=AZPG0mQ_cPe-ChFcFkYDdWEFrl3Bqw9uRe02iIo2yV0,58557
astropy/table/pandas.py,sha256=dNlFuOJa8tWnRRDCWFt62MZ7uICoiBzwnLMF7sfmD_g,1776
astropy/table/pprint.py,sha256=8SuExaPaSMJ9930eDX9y5VsE7Q8rh63Ar0izijzxj28,28684
astropy/table/row.py,sha256=0kiS-j7Yuwzb4kgliUk4V10T6I_4vYkRvKwXWt_NVoE,6663
astropy/table/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/table/scripts/__pycache__/__init__.cpython-39.pyc,,
astropy/table/scripts/__pycache__/showtable.cpython-39.pyc,,
astropy/table/scripts/showtable.py,sha256=HEOQnI5k4-WAzZP7huAdwlEiwhMrMpilDglKRc1ZGAM,5462
astropy/table/serialize.py,sha256=liVkA_o4Hg3PJ5R96G2oWkvZugsJIY9ek1-PMI6MAL0,16316
astropy/table/setup_package.py,sha256=22zeSNAZp1FqCWUn361IgN385zfu0A7UYzcaln0eMpA,531
astropy/table/soco.py,sha256=OAPU8BjJ8SirRKhIAAE1cN1vhkMDV0R8vklUYGT1dm4,5068
astropy/table/sorted_array.py,sha256=KnEQ-AyY9a34K6WmlWzyA5Yh3qKQuEhmEm9-3J81xA4,9285
astropy/table/table.py,sha256=kzeQrCY4fywOKsuIfFfXtxBLOhsTmvmBo_oYRBCdk1E,150618
astropy/table/table_helpers.py,sha256=x6Q8VQygk8KJgl4JlHUFsIhR6O1Z8z1eS0uqJDQzrMc,6319
astropy/table/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/table/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/table/tests/__pycache__/conftest.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_array.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_bst.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_column.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_groups.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_index.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_info.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_init_table.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_item_access.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_jsviewer.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_masked.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_mixin.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_np_utils.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_operations.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_pprint.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_row.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_showtable.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_subclass.cpython-39.pyc,,
astropy/table/tests/__pycache__/test_table.cpython-39.pyc,,
astropy/table/tests/conftest.py,sha256=lFO-YIvNDbiwNBntDnEZ38_mpHCYJqqBntRrdRVJRDg,6788
astropy/table/tests/test_array.py,sha256=lp0NoEGT2cMsKbKbzek3Kbtu5pggXIrmR6vZXv4xaN4,1338
astropy/table/tests/test_bst.py,sha256=eKdkX9ymefdyEuQFfsbAKbm2AX0F6UmHkMzpy-qjhvY,2633
astropy/table/tests/test_column.py,sha256=-QhfVnS5W0TXTkGa3eHxbnx_OZDhO-P71HvSvB2jtJU,34084
astropy/table/tests/test_groups.py,sha256=GfF27yML3um_97um2mzXU2VXri8jLU7vt6wkJVaISdQ,22347
astropy/table/tests/test_index.py,sha256=-8A8mNz3moH6U4aMGJ2txs1eo7udrTO2UtjTUvfyMaE,21556
astropy/table/tests/test_info.py,sha256=xIO88nljfPjVIeZeL02rWK30eXJBvulfpcLU5Q6wZQ0,12315
astropy/table/tests/test_init_table.py,sha256=dIqt25-v8sumPHkjvDNOm728xUKRNX8xtCEiuqGmJ8Y,22576
astropy/table/tests/test_item_access.py,sha256=6h2_0KrPJUQxaHqbDmPDS7Fel13eJ_uB10s7aKiVHTI,9266
astropy/table/tests/test_jsviewer.py,sha256=OZSBTPRAO5fWe4PE8dy6X3a7sUbSTQg4iGZxdVcNvBc,7250
astropy/table/tests/test_masked.py,sha256=5_QeTrZN8yVZHoN8qBhmfF0vV71UV9rL3NMN2lyEKEA,25370
astropy/table/tests/test_mixin.py,sha256=zmq4pq1Q_pac1KowNiU0-69VaLe6tM8-jyIBbMllkmU,30600
astropy/table/tests/test_np_utils.py,sha256=KOUBkaScGENNCmbjbd5TZYY3jAS4wbq_EbtbYu7dTNI,1995
astropy/table/tests/test_operations.py,sha256=FRURfF9yUlwtORNjMrMI9z8B6Cj5z7DRO8Pd3JOvUYo,81484
astropy/table/tests/test_pickle.py,sha256=xXPOC023gzcPzsKfK8v1thkKVJarLtq-IpHaz38eBbY,3835
astropy/table/tests/test_pprint.py,sha256=YajJoGpb_QD7QxGXKkccSHVFhJD2IKdpPsyRFMXOdPc,34752
astropy/table/tests/test_row.py,sha256=9f6pA46JFYHtix7Va9xuVGv0Slo0id5L23RRHZ_r2hU,12241
astropy/table/tests/test_showtable.py,sha256=b8MPs4mcNP8wgIlD5Cc6pm9jl-xpIcvH-lDW7F7HNXM,6143
astropy/table/tests/test_subclass.py,sha256=534legazPqAxj_7p7EhdMml6uNgFy3lB5Kwqcnz8_xc,2449
astropy/table/tests/test_table.py,sha256=tYOztJ_1pwyh4Pw0FygdTiSnceNd_BEAGZo7fg-iOfc,107027
astropy/tests/__init__.py,sha256=K1ngEZ-4-K39knp8Qeuwh72DwrFglM-R278x8SrSkLc,229
astropy/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/tests/__pycache__/command.cpython-39.pyc,,
astropy/tests/__pycache__/disable_internet.cpython-39.pyc,,
astropy/tests/__pycache__/helper.cpython-39.pyc,,
astropy/tests/__pycache__/image_tests.cpython-39.pyc,,
astropy/tests/__pycache__/runner.cpython-39.pyc,,
astropy/tests/__pycache__/test_logger.cpython-39.pyc,,
astropy/tests/command.py,sha256=a9HgmxVKYONCyI5Nn9bF9I_2kMyNGq9BDYtzbQpWuzA,13848
astropy/tests/disable_internet.py,sha256=4SYYqnrDNzeJ_oao-QMgs3-dnXhJK6pgRH3bTu1POPc,1514
astropy/tests/helper.py,sha256=qY3JTilvQ_ddDnsusFj60UFUWQJXQoE-E8IXn6fXSXA,16458
astropy/tests/image_tests.py,sha256=YayGGKFfPcROjUH2tFActUKhr-IwR8NlQJyOrFOLG50,741
astropy/tests/plugins/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/tests/plugins/__pycache__/__init__.cpython-39.pyc,,
astropy/tests/plugins/__pycache__/display.cpython-39.pyc,,
astropy/tests/plugins/display.py,sha256=ERuMRX8gR4gvs76JJL8mDDZ4dEOIAiZjw3DDBhynk_A,755
astropy/tests/runner.py,sha256=N6kEeFxh8--fSYORLp-N20ppdDxOJVS9bdiLbY7d644,22474
astropy/tests/test_logger.py,sha256=kb4LUO8BsrH1DfhvV8nLOpTtCQsG6VdUvFHVyP_rk50,15942
astropy/tests/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/tests/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/tests/tests/__pycache__/test_imports.cpython-39.pyc,,
astropy/tests/tests/__pycache__/test_quantity_helpers.cpython-39.pyc,,
astropy/tests/tests/__pycache__/test_run_tests.cpython-39.pyc,,
astropy/tests/tests/__pycache__/test_runner.cpython-39.pyc,,
astropy/tests/tests/test_imports.py,sha256=ADCT3b5Rb6sjU0r2krAMsca5to-P-nRclUpaukvSBLs,856
astropy/tests/tests/test_quantity_helpers.py,sha256=T9BtYEtLgfQqmxVbCD5d5rvx-wTVdhl-M8N6YepIwwM,1626
astropy/tests/tests/test_run_tests.py,sha256=KORjC8-nxOp8mu7B4RC_lAXrKFByjeUU6QmWxyOYy9w,632
astropy/tests/tests/test_runner.py,sha256=gHlq8L_5cy1xUEELj-w7C1zGxWtA-hZEDaVWzaBp9sQ,2026
astropy/time/__init__.py,sha256=d4U6KHZI4IOM55ZufW2IBCo1qRHO4wpRxrLW7prNGjc,738
astropy/time/__pycache__/__init__.cpython-39.pyc,,
astropy/time/__pycache__/core.cpython-39.pyc,,
astropy/time/__pycache__/formats.cpython-39.pyc,,
astropy/time/__pycache__/setup_package.cpython-39.pyc,,
astropy/time/__pycache__/utils.cpython-39.pyc,,
astropy/time/_parse_times.cp39-win_amd64.pyd,sha256=fHPZGLW8EA3-YZZEX6UHlVBdbRci9X44bGk7PKs0sKU,16384
astropy/time/core.py,sha256=H8a2R6E3I84ZrBFfkMFj3pMgEM8bgxkTpXvyDnRf7II,114429
astropy/time/formats.py,sha256=fpDG96SABCwwoSd0EE_9Y81NnsQHh9NiLCHRf2NkqWc,74868
astropy/time/setup_package.py,sha256=sCQ3d9i7d9dKtDoOkJ7P82cin21Xk3TjFpElWcdbL3o,711
astropy/time/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/time/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_basic.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_comparisons.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_corrs.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_custom_formats.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_delta.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_fast_parser.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_functions.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_guess.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_mask.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_methods.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_precision.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_quantity_interaction.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_sidereal.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_update_leap_seconds.cpython-39.pyc,,
astropy/time/tests/__pycache__/test_ut1.cpython-39.pyc,,
astropy/time/tests/test_basic.py,sha256=UkY0gYllHtP-lG8ieAij7vaHYKHUgLc0kZtOkKnuDZ0,89642
astropy/time/tests/test_comparisons.py,sha256=TX2N8ycKCij8UFQQf2Yb9yhUQYRL9XLGfHdQAad4_S8,7107
astropy/time/tests/test_corrs.py,sha256=1K9xUTwGc4G6ckQGidKLraSv-z5x7IO0sVWTe9LN79g,3065
astropy/time/tests/test_custom_formats.py,sha256=n5kQdaqNBva-8-TEpLlrsnPBPAA00pdpIuVCHKoxECk,7556
astropy/time/tests/test_delta.py,sha256=ENUlSrfKQlOokAEoG92Rbhbr_VarN4V8g6Dgr8b8RPw,20997
astropy/time/tests/test_fast_parser.py,sha256=BUoyJ56uvECn8NiqDC-vRS1YYAbdRCLTpz78ei-xQW8,5562
astropy/time/tests/test_functions.py,sha256=3Antr0OHPYgg3B6ElQ7sfAynMwo95InbK8C9eXhxMs4,2092
astropy/time/tests/test_guess.py,sha256=N3H8n1PXmF5FOkvYpGJ0yKOCxl-Jymy9x0IRxjdrROo,1039
astropy/time/tests/test_mask.py,sha256=DcxbOdVwqNm_r41_sIUSgn-ZR4ndHkYoUqCXfy1vLD8,6442
astropy/time/tests/test_methods.py,sha256=KAGG0jpd4Omm-cBKpLD81YiBKyqbA8WONCtkYYaKkHw,27962
astropy/time/tests/test_pickle.py,sha256=sTdvkXGTUjHgDQXYD6b8toAYaUkjHAAZhcOCEKMxPKY,706
astropy/time/tests/test_precision.py,sha256=U4ddutuSk7r407QI9gY5_x3mbF71U6xyGYR-E9PS5qU,26206
astropy/time/tests/test_quantity_interaction.py,sha256=0iSjh8Za7qlTw-uMvky74r2rEkmt30vN5vWg-Rw-gLQ,13261
astropy/time/tests/test_sidereal.py,sha256=_r6Qw7cLa7H6-JoGpNj2IQbNLi0yXTggBt5Zr0_FMXo,11395
astropy/time/tests/test_update_leap_seconds.py,sha256=omq93lrVQmtS7pDKWkBqLDYhi6PllRcRxc4voh3wGNw,4051
astropy/time/tests/test_ut1.py,sha256=eIzqZVQw1rkSgOQ4KVzRB4vchb0kgjae4trtdAAHTsk,4866
astropy/time/utils.py,sha256=0kHZK8EwseM1QNUhL4DHk52Gpj0r6g3qIUkCRL5uQ24,8028
astropy/timeseries/__init__.py,sha256=Err--pDHlHQkjdmqRrPxCjIkmajYfZQ4JRFa8VldQ8Y,439
astropy/timeseries/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/__pycache__/binned.cpython-39.pyc,,
astropy/timeseries/__pycache__/core.cpython-39.pyc,,
astropy/timeseries/__pycache__/downsample.cpython-39.pyc,,
astropy/timeseries/__pycache__/sampled.cpython-39.pyc,,
astropy/timeseries/binned.py,sha256=oBAXqmAayNfSl6srPxYYup0BwHzzIts_355t3syqpxE,15677
astropy/timeseries/core.py,sha256=E96H13wqY-8Mdxyq1zpQHCThbQg-fQKQtpkdzhY0spE,3280
astropy/timeseries/downsample.py,sha256=v4Q4h8oMGT5n_9q9Lxn05cTQhezc0IhPyCw9bjryyvk,9480
astropy/timeseries/io/__init__.py,sha256=R9JkBYYxy2lrQiPNELV65o48FTEu4vXxrvejQKQ-fVw,87
astropy/timeseries/io/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/io/__pycache__/kepler.cpython-39.pyc,,
astropy/timeseries/io/kepler.py,sha256=RaSXtBeqIPlGNqStb2LPauEvA0GDs7uqq4rE_sUaIYI,3372
astropy/timeseries/io/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/timeseries/io/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/io/tests/__pycache__/test_kepler.cpython-39.pyc,,
astropy/timeseries/io/tests/test_kepler.py,sha256=D6SsdE7v7gZTd0lF6wgAFO2aCmt3JhuBbXLRpbpJaM0,3595
astropy/timeseries/periodograms/__init__.py,sha256=LB5WUx_wklVClPV7MqDOizVKy_WiGZla4qB_UwIc264,183
astropy/timeseries/periodograms/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/__pycache__/base.cpython-39.pyc,,
astropy/timeseries/periodograms/base.py,sha256=OYLSK9b-iJgP_rj7nqSstw_4-qm9Gr6SbyMU7wKIQcA,1889
astropy/timeseries/periodograms/bls/__init__.py,sha256=pnoSjxVk0J2Ab6abg444SoCFG3ejNss8-5Z0B9Hy4QY,338
astropy/timeseries/periodograms/bls/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/bls/__pycache__/core.cpython-39.pyc,,
astropy/timeseries/periodograms/bls/__pycache__/methods.cpython-39.pyc,,
astropy/timeseries/periodograms/bls/__pycache__/setup_package.cpython-39.pyc,,
astropy/timeseries/periodograms/bls/_impl.cp39-win_amd64.pyd,sha256=onew-amoCXf6jeonxPamayjGTQSL3dWjMeoEut8okJs,46080
astropy/timeseries/periodograms/bls/core.py,sha256=EwSS3UgPr0X40aNPXRJ9jOEMyJw-SvSqzchvinGsklk,33798
astropy/timeseries/periodograms/bls/methods.py,sha256=df5HocibQfrEOWbGOLxsaz66TsCt5_FlsYCFegrE90Q,5174
astropy/timeseries/periodograms/bls/setup_package.py,sha256=fjC0KnJrPkB8ZEowLadqEeoqfAGbA5IAefheP0TMHvg,475
astropy/timeseries/periodograms/bls/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/timeseries/periodograms/bls/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/bls/tests/__pycache__/test_bls.cpython-39.pyc,,
astropy/timeseries/periodograms/bls/tests/test_bls.py,sha256=LlvcyalqmjqhDVZiOpTeaWzkTOOK0DINbGNYwRIUVn4,42048
astropy/timeseries/periodograms/lombscargle/__init__.py,sha256=bOfKB5Q_Vgv2T03tM1aR5Hl_0V47YSPTx13nr4wtOP0,194
astropy/timeseries/periodograms/lombscargle/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/__pycache__/_statistics.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/__pycache__/core.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/__pycache__/utils.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/_statistics.py,sha256=buSQyZpKTQUoLHw2tOXZKsL0iKYUskjyPqjkEzDcWvQ,16405
astropy/timeseries/periodograms/lombscargle/core.py,sha256=WU53zBZ4JtAh-wTHJjEkFz7wGAUhAO7J6Hx4-MjDj4Y,29161
astropy/timeseries/periodograms/lombscargle/implementations/__init__.py,sha256=YoSLtFWsMova_XKbc1cNxWG-qPeUs_nynBoITWO4foc,322
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/chi2_impl.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/fast_impl.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/fastchi2_impl.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/main.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/mle.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/scipy_impl.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/slow_impl.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/__pycache__/utils.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/chi2_impl.py,sha256=qexMh80EHkPhuMeavb5Cy6CAyHTKmJxL1LHorqZGMPg,2882
astropy/timeseries/periodograms/lombscargle/implementations/cython_impl.cp39-win_amd64.pyd,sha256=Lo4LTl9TuhEFfUQMXDOC_00nM34I0lYa2wTsLv3eRwQ,139776
astropy/timeseries/periodograms/lombscargle/implementations/fast_impl.py,sha256=pco7MDJ4LlwMsw8CtNxjftmBnCh5NGVeLKF54552kcA,4879
astropy/timeseries/periodograms/lombscargle/implementations/fastchi2_impl.py,sha256=MDeB7xx-CcVSxmpuf7YZA1k1XJrSF4WP5R30r7BTHPY,4906
astropy/timeseries/periodograms/lombscargle/implementations/main.py,sha256=SeuXenwkswpYriucQCaGvS0lvA7PoKiPA8d9eQsbtik,7741
astropy/timeseries/periodograms/lombscargle/implementations/mle.py,sha256=5vnW6Lj_R1fyDKa9s5tvlUR_U07iw4TKgBKVRernALc,3227
astropy/timeseries/periodograms/lombscargle/implementations/scipy_impl.py,sha256=yLxt7t69OO9YVj1nMkVBvT9i2dWv_qJjf4E9be3JdWQ,2415
astropy/timeseries/periodograms/lombscargle/implementations/slow_impl.py,sha256=90G7kPvu_Jl-ia-5vYNp8E5L_F_EepWh_tYR2nHlKDk,3753
astropy/timeseries/periodograms/lombscargle/implementations/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/timeseries/periodograms/lombscargle/implementations/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/tests/__pycache__/test_mle.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/implementations/tests/test_mle.py,sha256=3CbJbLlTxT6hIcRdsn68A4OOtWaW_kNgXMqJxFCruxg,1946
astropy/timeseries/periodograms/lombscargle/implementations/tests/test_utils.py,sha256=DcJcNOXLeQnqHDtryJZNsBnkNw1TFnGkKIbxJoiPQlM,2223
astropy/timeseries/periodograms/lombscargle/implementations/utils.py,sha256=fWfRzLnA3J6voE6jgfHsfGv3sxsp5AO0f7AaQSp4ZQY,5014
astropy/timeseries/periodograms/lombscargle/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/timeseries/periodograms/lombscargle/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/tests/__pycache__/test_lombscargle.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/tests/__pycache__/test_statistics.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/timeseries/periodograms/lombscargle/tests/test_lombscargle.py,sha256=VPtBuWtpdZHHm7Srr1Fp-zImoTp9QSL3UHiGZt7PXs8,17023
astropy/timeseries/periodograms/lombscargle/tests/test_statistics.py,sha256=RCzviEHU1ImP_AfdltAqKZzuqMim1_uS-5p2RNlIAmk,7537
astropy/timeseries/periodograms/lombscargle/tests/test_utils.py,sha256=cWg-oxqoKTbqRfQBs9Z_F3WRCQSjQQRKwZsxT5HrVag,1529
astropy/timeseries/periodograms/lombscargle/utils.py,sha256=TqG7h58a5l_Ogeh6U7itCUurgVirrl2uTd1H7aOWi5M,3265
astropy/timeseries/sampled.py,sha256=zJqPKcgSdcDNztaiQOObpW03IVWN4z8o7CUXj4ajDg8,16159
astropy/timeseries/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/timeseries/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/timeseries/tests/__pycache__/test_binned.cpython-39.pyc,,
astropy/timeseries/tests/__pycache__/test_common.cpython-39.pyc,,
astropy/timeseries/tests/__pycache__/test_downsample.cpython-39.pyc,,
astropy/timeseries/tests/__pycache__/test_sampled.cpython-39.pyc,,
astropy/timeseries/tests/data/binned.csv,sha256=bqtwcAzKX5-XDs95xsf1TwrDdtf-jDxgGbV7h9AqIC0,919
astropy/timeseries/tests/data/sampled.csv,sha256=cdyOIw56GwxjVrGggZp9Ua8UpuVzp1r2Ggn7WH-s2Wo,622
astropy/timeseries/tests/test_binned.py,sha256=V73OHYH8q9OxymSKSnK1YE4YodBLZAr5iHoDmQIOPQw,13686
astropy/timeseries/tests/test_common.py,sha256=tf8s2agLzU-xez1BEnspaC0d-H5n3Kj6vVbzNAFZSoQ,3325
astropy/timeseries/tests/test_downsample.py,sha256=LgAZ8Z4daYkgHsuzowSDdmvUrf_8-tavBfE0Prn0f1E,10182
astropy/timeseries/tests/test_sampled.py,sha256=S_16pWazsPIiPdOnYti4d0lp0e8hXPn4PUqSTMJZMcY,16315
astropy/uncertainty/__init__.py,sha256=MmosQcbaIeHQNSzvSoifMywkIV6bzINV2E_FI0uXM-E,321
astropy/uncertainty/__pycache__/__init__.cpython-39.pyc,,
astropy/uncertainty/__pycache__/core.cpython-39.pyc,,
astropy/uncertainty/__pycache__/distributions.cpython-39.pyc,,
astropy/uncertainty/core.py,sha256=AAI3CmKjRMecg1-6pfA_21Y699Vxvpp0o1yHNws64jg,12779
astropy/uncertainty/distributions.py,sha256=xC3SFuGgxp5JDWH7GwkHaxlATJyAn48llKBTdTKNYBM,6654
astropy/uncertainty/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/uncertainty/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/uncertainty/tests/__pycache__/test_distribution.cpython-39.pyc,,
astropy/uncertainty/tests/test_distribution.py,sha256=Qw7V2kKafotAgzYWU8MvmoSxC1HZBoVXRA4Ti9gBP4k,17072
astropy/units/__init__.py,sha256=OZJdczUsaPclXB0NkkKA72OYpWtZtcyzwDR-PitT8J8,1725
astropy/units/__pycache__/__init__.cpython-39.pyc,,
astropy/units/__pycache__/_typing.cpython-39.pyc,,
astropy/units/__pycache__/astrophys.cpython-39.pyc,,
astropy/units/__pycache__/cds.cpython-39.pyc,,
astropy/units/__pycache__/cgs.cpython-39.pyc,,
astropy/units/__pycache__/core.cpython-39.pyc,,
astropy/units/__pycache__/decorators.cpython-39.pyc,,
astropy/units/__pycache__/deprecated.cpython-39.pyc,,
astropy/units/__pycache__/equivalencies.cpython-39.pyc,,
astropy/units/__pycache__/imperial.cpython-39.pyc,,
astropy/units/__pycache__/misc.cpython-39.pyc,,
astropy/units/__pycache__/photometric.cpython-39.pyc,,
astropy/units/__pycache__/physical.cpython-39.pyc,,
astropy/units/__pycache__/quantity.cpython-39.pyc,,
astropy/units/__pycache__/required_by_vounit.cpython-39.pyc,,
astropy/units/__pycache__/si.cpython-39.pyc,,
astropy/units/__pycache__/structured.cpython-39.pyc,,
astropy/units/__pycache__/utils.cpython-39.pyc,,
astropy/units/_typing.py,sha256=CyeB-qymtpM6uqE_KCTqFSMLvRD965-VdEMxWgVfHZE,536
astropy/units/astrophys.py,sha256=pnFoIEbwqH-0_uCPmdw53AMiXMi8FwM4QedQSLI9vp0,6099
astropy/units/cds.py,sha256=lmX1TbuK8oi9iO5vLCMg5paq999Jgmv71FslWkR4JzI,7111
astropy/units/cgs.py,sha256=mNkLEnOYIsZYI2Aj2_pLoecLzs_itsjOR1jZ2sKND_Y,3692
astropy/units/core.py,sha256=OZwJH-z7iVR0J-N8blacytFsJdOmS_tXfnoZmTkTWJI,87550
astropy/units/decorators.py,sha256=mmFGDc5F9RjdRne0XRDiLCJv2zHbGIKZiOSVjm1qalQ,12062
astropy/units/deprecated.py,sha256=gD-DjY63yKDHBchYAaXTp5ENJhzT7GsMk_gg16IOn1Y,2231
astropy/units/equivalencies.py,sha256=maOJ9DwvdQ9pACJW-KuygDnHHvkprc2fAKPbXYofR_M,30590
astropy/units/format/__init__.py,sha256=TIu10EbGxnH8oUXmrzlCt-na5LIEKDp5szZjjuMq8Ok,2187
astropy/units/format/__pycache__/__init__.cpython-39.pyc,,
astropy/units/format/__pycache__/base.cpython-39.pyc,,
astropy/units/format/__pycache__/cds.cpython-39.pyc,,
astropy/units/format/__pycache__/cds_lextab.cpython-39.pyc,,
astropy/units/format/__pycache__/cds_parsetab.cpython-39.pyc,,
astropy/units/format/__pycache__/console.cpython-39.pyc,,
astropy/units/format/__pycache__/fits.cpython-39.pyc,,
astropy/units/format/__pycache__/generic.cpython-39.pyc,,
astropy/units/format/__pycache__/generic_lextab.cpython-39.pyc,,
astropy/units/format/__pycache__/generic_parsetab.cpython-39.pyc,,
astropy/units/format/__pycache__/latex.cpython-39.pyc,,
astropy/units/format/__pycache__/ogip.cpython-39.pyc,,
astropy/units/format/__pycache__/ogip_lextab.cpython-39.pyc,,
astropy/units/format/__pycache__/ogip_parsetab.cpython-39.pyc,,
astropy/units/format/__pycache__/unicode_format.cpython-39.pyc,,
astropy/units/format/__pycache__/utils.cpython-39.pyc,,
astropy/units/format/__pycache__/vounit.cpython-39.pyc,,
astropy/units/format/base.py,sha256=_Ki3kSMiu4L1IEI1vkuswhj_pxG64b36QdmjrTfZuI4,1147
astropy/units/format/cds.py,sha256=mOSjWXO-bUBP9HubdJztglrZd62Kugjd5IfWGklsgv0,10484
astropy/units/format/cds_lextab.py,sha256=4GlM68srC3KeyCZHwIlibS2NbSG6tkdtMZ49B5MzNMg,1455
astropy/units/format/cds_parsetab.py,sha256=gBdsmRlsZHBU38REu71ip7cNKbWEfuY061HsD-uH2Ns,6202
astropy/units/format/console.py,sha256=2pj5A1GiD09ngw_LjgtrjXzGx_iBkvFszq6j-vN9iWc,2717
astropy/units/format/fits.py,sha256=x_UsddgPlvXRJhD-bEiL7oBHkWKVHSR8ITOwkl60R_A,5227
astropy/units/format/generic.py,sha256=bN7WE7Gws4jD-9qmLtlKyVdoNXwV4blOIwT-SZotyA0,20429
astropy/units/format/generic_lextab.py,sha256=lAYIAho2Gtd-TfzDkaSyM-ZOGsbl5uDaDyg3_m3RfkU,1598
astropy/units/format/generic_parsetab.py,sha256=Dj8b16TBiE9c_2ZdrFovTOBwqyF4VGfnFyFYBzIIE-c,15242
astropy/units/format/latex.py,sha256=c5FFM61wIXmtNxQ9IxlrCeurhiMoKzMI03p0KYxqj5g,4345
astropy/units/format/ogip.py,sha256=ekyS5pcWqHxU5RyBOBktFVvWdnqSon90x5o-2sO7ewE,14109
astropy/units/format/ogip_lextab.py,sha256=M2v7az6v2OO1ft-rMyifgGK7bTLkKFFwE073eKDU4xA,1540
astropy/units/format/ogip_parsetab.py,sha256=7vQlbQ6JE6dI3v_5VbddjWz4iK7AtdEmZy0VBcmcjMg,9473
astropy/units/format/unicode_format.py,sha256=ImR7OuH0yoRiZULcE-l4jVdFnIc7POrjo_UZC_iS4OM,1594
astropy/units/format/utils.py,sha256=YhvJc2rh9s8gA3v1hyssPmJbk08EDmqEtdLOAI60Jc8,6079
astropy/units/format/vounit.py,sha256=OE51hrOUt9kzC6aGMxlDEhGkrNxsAgPNZbPJv0PEjSU,8612
astropy/units/function/__init__.py,sha256=7qN6CizT3qCgOcBooF40LPhoOzI41WUsFpFoC-FaI3c,339
astropy/units/function/__pycache__/__init__.cpython-39.pyc,,
astropy/units/function/__pycache__/core.cpython-39.pyc,,
astropy/units/function/__pycache__/logarithmic.cpython-39.pyc,,
astropy/units/function/__pycache__/mixin.cpython-39.pyc,,
astropy/units/function/__pycache__/units.cpython-39.pyc,,
astropy/units/function/core.py,sha256=NmvI0i4uuMgq4zsXdhxtbLFBNt5gNOQ_w-fCEAm0zUI,27582
astropy/units/function/logarithmic.py,sha256=TBNxOtii6048XALkBX-pY_onQIFkhr3WtXQ2TqiUYWg,13790
astropy/units/function/mixin.py,sha256=kwHIW9PXNGotTdAngv66QS0uxrDzQGEuJNaaSwuydCU,749
astropy/units/function/units.py,sha256=zI7yd54dtlGKKb3qFEn3pbCQ_26IndwoM9kYX9CfsDA,1777
astropy/units/imperial.py,sha256=biK28pYIT8uBzyoeSs0bu0qmlSOM96nO5ADaokZTCjk,5447
astropy/units/misc.py,sha256=mTxxTyAIKIpxRgECetAF3k0jJF4-RgkYxmBp63lmo_E,3393
astropy/units/photometric.py,sha256=U4pQfIHdSEUSAVFM-QtEfmcSymO2qk2WLAi89SZCuJg,2421
astropy/units/physical.py,sha256=RtIdmZpgpDiGxVlu3FlYpmKZtREkrnr0PIOX46sayAU,22480
astropy/units/quantity.py,sha256=YFcMpkpyPgT8GE62GbgeT4lNhbg2gtPBrAgMuNLFC4M,81285
astropy/units/quantity_helper/__init__.py,sha256=nCdOYMKHoCoBXFS2Ejmae7EmbHOINzDnbc90Qqgvl5c,584
astropy/units/quantity_helper/__pycache__/__init__.cpython-39.pyc,,
astropy/units/quantity_helper/__pycache__/converters.cpython-39.pyc,,
astropy/units/quantity_helper/__pycache__/erfa.cpython-39.pyc,,
astropy/units/quantity_helper/__pycache__/function_helpers.cpython-39.pyc,,
astropy/units/quantity_helper/__pycache__/helpers.cpython-39.pyc,,
astropy/units/quantity_helper/__pycache__/scipy_special.cpython-39.pyc,,
astropy/units/quantity_helper/converters.py,sha256=Vt-M6O06ZEkKhyDvtOqMWWdnWbf5loqnkhoDahAWURI,15712
astropy/units/quantity_helper/erfa.py,sha256=PI6K60SCurHgTm59OyRwyk3PPUyB2RaOpQiuBwcRPqg,13387
astropy/units/quantity_helper/function_helpers.py,sha256=LAZUKhUSgIFl6w5fTFfiQ4d45fItkVhWmQVo9JaRGJw,35231
astropy/units/quantity_helper/helpers.py,sha256=BbMPnn4QcQeOwYpM4_z1lhVqWUSlKhkMmTcinxRy_34,15676
astropy/units/quantity_helper/scipy_special.py,sha256=dcUV3XWIKuIEgh1B58CtZAzmn48TihoqOw63iK3Kk4A,3614
astropy/units/required_by_vounit.py,sha256=AQrLhzkmivs5tX9oog8m0A8EH8Re5vBZHaNf1IBXFvU,2171
astropy/units/si.py,sha256=Nx-vpIiDQqNRzwO6E2SvQItDHDFvqCbvxmq56tD1pzg,8664
astropy/units/structured.py,sha256=2iwMZ6oO8kz2aKcfHhk9dyhPF9syjK_haKlHfOwLBJQ,19169
astropy/units/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/units/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_aliases.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_deprecated.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_equivalencies.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_format.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_logarithmic.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_photometric.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_physical.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_annotations.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_array_methods.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_decorator.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_helpers.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_non_ufuncs.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_typing.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_quantity_ufuncs.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_structured.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_structured_erfa_ufuncs.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_units.cpython-39.pyc,,
astropy/units/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/units/tests/test_aliases.py,sha256=rVqBfIWDKBePl5WLSlEnvB8Aa6bZixzHbbO007Dcw8U,3068
astropy/units/tests/test_deprecated.py,sha256=WWLjs-DQyb194zqHMPULVoN0VG5VtaBfNzLDI2Flx1g,2162
astropy/units/tests/test_equivalencies.py,sha256=NgEVwO-kSg-xx_KDE9XKf2i8UsMotm8S30a-WfJhmsc,33090
astropy/units/tests/test_format.py,sha256=TcH64NxMHfhdYM76iwdrbx_j6EvNPNSFOgVZnPAjmkc,24033
astropy/units/tests/test_logarithmic.py,sha256=7YbAatoHxeJhHw8HSesVRMt4xnzikGhaL4kjEfCq1ek,33191
astropy/units/tests/test_photometric.py,sha256=SR52ck0j--eHzJv72Y_s9BsUUgrVj583xr7H-nkVkFM,1115
astropy/units/tests/test_physical.py,sha256=******************************-z3LgB91vXvSw,19909
astropy/units/tests/test_quantity.py,sha256=khmy3lWAxI1m-6O_Xr2jAeIhA-lWqYC_toFMRjJDokg,55119
astropy/units/tests/test_quantity_annotations.py,sha256=OHVw-Yq9Cc23CtO5e-HHXnzDx-wvURW4yxg8IZWRatg,10873
astropy/units/tests/test_quantity_array_methods.py,sha256=dp_Hj22wg5xCqRvgz-4DzeAdSbbENOQtTBVM6c9PRSA,19227
astropy/units/tests/test_quantity_decorator.py,sha256=67kDZaCxWlXJxwbwWRiLluAElJcE1HpGGfWLF3PX7RM,12626
astropy/units/tests/test_quantity_helpers.py,sha256=ReetKsyfCNOLOC64RNGJajFNAfUXTIaFXDSu_UFEsHc,700
astropy/units/tests/test_quantity_non_ufuncs.py,sha256=GqRGFdZWZOyWFFkbTlW7laIwl8ouF9KHCV_w6pnPcTA,70483
astropy/units/tests/test_quantity_typing.py,sha256=egyk5J46uhv_PsMZYFUw7xAbrKhACT0pIwxPde80Byw,2740
astropy/units/tests/test_quantity_ufuncs.py,sha256=lTOSKY4TE9LBKFxnf4m8gVjpI1FkT1e2SRv5O0lDpxw,51554
astropy/units/tests/test_structured.py,sha256=tS0tRboCoLUqrrmMrYFiFYxAtvgmopH3PEieXdHBv5A,28477
astropy/units/tests/test_structured_erfa_ufuncs.py,sha256=cJfb2vbYb_oynBFwBD64U-5Mq98_mHdCypezPlQOb54,19959
astropy/units/tests/test_units.py,sha256=PQtkjmcbpbEYz72Dt_64H2J2azj58dvaDq6PG8N9HB0,24065
astropy/units/tests/test_utils.py,sha256=MG3_Y6MNj2z4Km8o0_h8cse1YOHUPJbPIc4I-mt-Z8c,838
astropy/units/utils.py,sha256=eY4EFh1231BaITTSoGOcF47rZoRFo50JxiMW8_YSLWg,8672
astropy/utils/__init__.py,sha256=ohb4apQQbQyi1gsCpY_321yWbcnWrDuWMQTYXwIth68,853
astropy/utils/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/__pycache__/argparse.cpython-39.pyc,,
astropy/utils/__pycache__/codegen.cpython-39.pyc,,
astropy/utils/__pycache__/collections.cpython-39.pyc,,
astropy/utils/__pycache__/console.cpython-39.pyc,,
astropy/utils/__pycache__/data.cpython-39.pyc,,
astropy/utils/__pycache__/data_info.cpython-39.pyc,,
astropy/utils/__pycache__/decorators.cpython-39.pyc,,
astropy/utils/__pycache__/diff.cpython-39.pyc,,
astropy/utils/__pycache__/exceptions.cpython-39.pyc,,
astropy/utils/__pycache__/introspection.cpython-39.pyc,,
astropy/utils/__pycache__/metadata.cpython-39.pyc,,
astropy/utils/__pycache__/misc.cpython-39.pyc,,
astropy/utils/__pycache__/parsing.cpython-39.pyc,,
astropy/utils/__pycache__/setup_package.cpython-39.pyc,,
astropy/utils/__pycache__/shapes.cpython-39.pyc,,
astropy/utils/__pycache__/state.cpython-39.pyc,,
astropy/utils/_compiler.cp39-win_amd64.pyd,sha256=SxgCyzXc3XBLBgL1jmyWBnLM_6plwVn97-hv2X9KtsE,10240
astropy/utils/argparse.py,sha256=CLKfFxDRFTmpmNeWsRQt7jUWwwZWCT89sAglLCE-Q5U,1503
astropy/utils/codegen.py,sha256=DMNOiXHRZC5NvZkkyWAPkMNb1RreBkLlGJVIigNZZOs,4341
astropy/utils/collections.py,sha256=y9csPTWZtK8U0YNtdt4kvvFNqc9PUAqr-_-7QLDlqNs,1494
astropy/utils/compat/__init__.py,sha256=IJIlLvUAFACi_G-5kLl70lzLpAHLj8hT1zB4DTyBzwU,657
astropy/utils/compat/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/compat/__pycache__/misc.cpython-39.pyc,,
astropy/utils/compat/__pycache__/numpycompat.cpython-39.pyc,,
astropy/utils/compat/__pycache__/optional_deps.cpython-39.pyc,,
astropy/utils/compat/misc.py,sha256=RmWJtBque8a5RVC7-bB1t-iWXYtK7PkhM7t4qUc2ELc,2085
astropy/utils/compat/numpycompat.py,sha256=3hGKPKfLPYE88CAw9YWmLaZBSlDmiEnY0YDAWIvuy7A,864
astropy/utils/compat/optional_deps.py,sha256=h234u-4jY-_124KDghXbfyDMN7DLgs-K3TrxntcfFSo,1567
astropy/utils/console.py,sha256=okyH_E1wl7Jyi1m6nTqSwmo_jaqILFBL8ULYvJX2jhk,36270
astropy/utils/data.py,sha256=oxjh6mqTt4MPppICY2g1-T4RCwTI3k6XpmESAlyKxqk,81726
astropy/utils/data_info.py,sha256=xcY8NHoiYZMZkU5VsBGur3iJXe1zofzD8_mfWQEyWAU,27116
astropy/utils/decorators.py,sha256=FrLpovOFMJucvbDSS9Oyru6FLUJ3q4eSYxGZMpyHSYM,39666
astropy/utils/diff.py,sha256=E2kBc4ldQuIhweTZljtYGXQAAawFyNsopy_7l5cUhg4,4974
astropy/utils/exceptions.py,sha256=j9YrPG9H0Kz72hQWlHrJzMsmZhXvd_nFoGauqaCsQsI,2174
astropy/utils/iers/__init__.py,sha256=bJIAnOcqYDMLuCkSM-FDa3Z070jLmS9vVK42Ug9zblg,21
astropy/utils/iers/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/iers/__pycache__/iers.cpython-39.pyc,,
astropy/utils/iers/data/Leap_Second.dat,sha256=I3OPHsFPfRKzsLDzaU3TSNID419loYq4s10qvuUMX64,1359
astropy/utils/iers/data/ReadMe.eopc04_IAU2000,sha256=odVASGVkgVh801OWaz7Jq7rULvQEEBBgHb_3XZ0R1kQ,2170
astropy/utils/iers/data/ReadMe.finals2000A,sha256=fGGCzA_Qy-zjlxH2SNFeSLSRaJJWAuNgpXCcXMyNWhI,3429
astropy/utils/iers/data/eopc04_IAU2000.62-now,sha256=AuS8t9aGrFdrC3mg1ovCTfzL9xIZs3cRTcv-WSOLhOM,3425412
astropy/utils/iers/data/update_builtin_iers.sh,sha256=xwHBwUB-KiBHG_meMs3kEzpJpKd0a2Tae5aLEmNZWqo,388
astropy/utils/iers/iers.py,sha256=_fQjYAW9GnhL_VSIRZyU2PSHuzYTot337ojwRGFVb4A,46881
astropy/utils/iers/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/iers/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/iers/tests/__pycache__/test_iers.cpython-39.pyc,,
astropy/utils/iers/tests/__pycache__/test_leap_second.cpython-39.pyc,,
astropy/utils/iers/tests/data/finals2000A-2016-02-30-test,sha256=Tr4de3PTwjVpsAum7rrKvPF5OrQwNeW0wGFaUdAbLFg,34026
astropy/utils/iers/tests/data/finals2000A-2016-04-30-test,sha256=b1d5gp7Ds1k5qF7ZcNYitcCHpipwkVgf8V0-ZEoFIpo,33917
astropy/utils/iers/tests/data/iers_a_excerpt,sha256=9locNMrUY5FMLu7zRsa5dUeEFdjsgPBh74M2-0Meknc,11280
astropy/utils/iers/tests/data/leap-seconds.list,sha256=PhQeB6g74EfeUBDiJ8zl24KJFAOkwuqED-4Htd6pUGw,10662
astropy/utils/iers/tests/test_iers.py,sha256=zglA6bxccrzuMgiU7o9EErcskYoMWNX0QmENcUOUR50,16678
astropy/utils/iers/tests/test_leap_second.py,sha256=NH6pe55h89PT65DhW4Re9ZeJlzKcH35IaH0hULGh-OA,21306
astropy/utils/introspection.py,sha256=y2DCIgiD4ls0HAZWcrDXZpMjlSvvwe30j6F-gLlbziQ,15308
astropy/utils/masked/__init__.py,sha256=Gvx_Vq5DY_5LwGuoAOervhxv_quLhUcKkAulqfHjDJU,369
astropy/utils/masked/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/masked/__pycache__/core.cpython-39.pyc,,
astropy/utils/masked/__pycache__/function_helpers.cpython-39.pyc,,
astropy/utils/masked/core.py,sha256=ZXqoLdgnaNxhsAdviBaAJqooXPNAxJ7sE9EvuL0w-do,45844
astropy/utils/masked/function_helpers.py,sha256=5FGoZZ05S6RwIJGsWyMHfbyN9hCLJ8Mr_UZ0i_D6Ylk,34509
astropy/utils/masked/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/masked/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/masked/tests/__pycache__/test_containers.cpython-39.pyc,,
astropy/utils/masked/tests/__pycache__/test_function_helpers.cpython-39.pyc,,
astropy/utils/masked/tests/__pycache__/test_functions.cpython-39.pyc,,
astropy/utils/masked/tests/__pycache__/test_masked.cpython-39.pyc,,
astropy/utils/masked/tests/__pycache__/test_table.cpython-39.pyc,,
astropy/utils/masked/tests/test_containers.py,sha256=bKTiH10MLGeH0lULUSZRuCgpPOvy4oSz6kB5xMHfL4c,4959
astropy/utils/masked/tests/test_function_helpers.py,sha256=lSWYjnwsNSOVwfetWVLD32C542uClMG_AYudZTdGPV0,49228
astropy/utils/masked/tests/test_functions.py,sha256=WkGCrSna8zSXUOsQ4ylBKCo23dqZ6wztmArCTI2RvNU,13632
astropy/utils/masked/tests/test_masked.py,sha256=8fgbyiIzh-pcVcZIMbtK0u8lRF51zSTfvJWKd5iCy_E,46057
astropy/utils/masked/tests/test_table.py,sha256=W0GhLOnfjorbG7xe42oiTx9NwERyhpU7CHShNCJqKWU,6626
astropy/utils/metadata.py,sha256=GuhgXsXzDpCoIAYZE7FpLCWUIAN45rCZZrvp0Awwg7U,18215
astropy/utils/misc.py,sha256=xkEBPaPdzxp-h7kCAJJtIp-T4Ko29uFrqMuo0JbJOkM,30392
astropy/utils/parsing.py,sha256=-GwV1Pq6b1jRI-4fLOERa-jqvBJwMQy8IfsmBGhzMXQ,5020
astropy/utils/setup_package.py,sha256=wzauWpVbSqk2wYyUzzt5B5nHZJCmfumgeJKI2bp3dCE,345
astropy/utils/shapes.py,sha256=cPdP-3Z2vvYa8Kf1ClB64bvkwhv3SU67sdLIgC_gmt0,14300
astropy/utils/state.py,sha256=LIJZZHmY3VeStCKariNwJ2bVH9TqGh4ZJ1Q-_BIWNN0,2154
astropy/utils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_codegen.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_collections.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_console.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_data.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_data_info.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_decorators.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_diff.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_introspection.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_metadata.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_misc.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_parsing.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_progress_bar_func.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_shapes.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_state.cpython-39.pyc,,
astropy/utils/tests/__pycache__/test_xml.cpython-39.pyc,,
astropy/utils/tests/data/.hidden_file.txt,sha256=zN30ZDXvfZ8y95PuAkGxfL1MvqXYxJh4aY2yTPtQZJE,36
astropy/utils/tests/data/alias.cfg,sha256=zJojR6EGTRE-BX34gy-HZp18T1pzGBy3hilbPn9WoWM,42
astropy/utils/tests/data/dataurl/index.html,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/tests/data/dataurl_mirror/index.html,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/tests/data/local.dat,sha256=JGgy4ZeKZPN0uaw9mtMaoYw9CHYlFEDQYfBxoUMHiZM,69
astropy/utils/tests/data/local.dat.bz2,sha256=F8Kt61rOaPSqSvAphYS1srdeS6jmMHAzZ5qVr1JmIIc,96
astropy/utils/tests/data/local.dat.gz,sha256=EjQQulF2cfojD46FnXoTK1MwzlJ0R615yUoGdNLXjQM,94
astropy/utils/tests/data/local.dat.xz,sha256=8qI-OYMGn8BHQJ3HNf0hX7Gz-D0Zne254vljliNKTqU,128
astropy/utils/tests/data/test_package/__init__.py,sha256=BP4HcZ6lZh6fIkY3T267fKqhdqbd9qXjgcDqe8OrsNI,129
astropy/utils/tests/data/test_package/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/tests/data/test_package/data/foo.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/tests/data/unicode.txt,sha256=TYSzbz7mzL_3wd0qpH-e1j8ciJBZHg06Jx1nP0APhNk,58
astropy/utils/tests/data/unicode.txt.bz2,sha256=pgDzi5fWIN_84O5gwEdTVFEyQVHcl8TFbC9atbkC8sQ,89
astropy/utils/tests/data/unicode.txt.gz,sha256=3cAWZpk5aFMCQ3h9CL6DuC4Kqk4hzXS_7CJ_YSsnVkI,86
astropy/utils/tests/data/unicode.txt.xz,sha256=pwAYJ-esz_KcFrcK820DvW0MaZfkGANWBeJJvu_n3PA,116
astropy/utils/tests/test_codegen.py,sha256=Jnm-es_ZOuAba3nSUO4Gj9DQjrx9pj-EYMn2Eq89plQ,1321
astropy/utils/tests/test_collections.py,sha256=Vo25uXu3KyqwaedZWZhknJadfyLPQ255j7hgHpHegQE,1736
astropy/utils/tests/test_console.py,sha256=5PitGcwdB4icNP9ltNj1fgRuNBsiKPkEhXdOKC1JGYM,5308
astropy/utils/tests/test_data.py,sha256=yL8kxWcy_hRLpZ1KCXrF0CDEYlbW70Qwt1hE-5aps10,72191
astropy/utils/tests/test_data_info.py,sha256=EwdxmH-Rv9jyGNE2FcXVXzUn2dRbQAHb4NG8bfrqG3M,3023
astropy/utils/tests/test_decorators.py,sha256=yhyP7dVI9TvyCAKeNimEYngYWqY1s8OsNQaX9hM1ChA,22242
astropy/utils/tests/test_diff.py,sha256=kTt-DNXdyurThX4b1kSk6JCCHAG0ZV4MgoBC3wonfOU,4354
astropy/utils/tests/test_introspection.py,sha256=Xd4xy_Jry_impdIv6kTES5RIWcE5TlNbTajTUrOsEw4,3422
astropy/utils/tests/test_metadata.py,sha256=a9zmk9p_LBVGKUYOoNJqmjUVg1oS5ydLobaJy86-PNY,6553
astropy/utils/tests/test_misc.py,sha256=NK3Qzp1bwl_tGRENIafN55SHaVTMrEneVDTxgOME0_Q,5185
astropy/utils/tests/test_parsing.py,sha256=C0BV_caXRyMA37xbSZyASBz-L5vMWr1bX1fh4ehAA8c,2401
astropy/utils/tests/test_progress_bar_func.py,sha256=SdPZaSfsl2XKpZeauEvgDCxznznTE8ysuUFFOmeU4VQ,585
astropy/utils/tests/test_shapes.py,sha256=VqtQtvfSSBAIrOeKzgT66QPIHbyh4XXOCYDE3i_d-5Y,774
astropy/utils/tests/test_state.py,sha256=zdiaEmiQ2vdYaBYpQ-Ee4yW9ac080ls5BsQojOyg9Gc,601
astropy/utils/tests/test_xml.py,sha256=oLQLGGnzFnt3hLAcF6dZ9drSEyldg5W6STLFU_EDd6c,3170
astropy/utils/xml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/xml/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/xml/__pycache__/check.cpython-39.pyc,,
astropy/utils/xml/__pycache__/iterparser.cpython-39.pyc,,
astropy/utils/xml/__pycache__/setup_package.cpython-39.pyc,,
astropy/utils/xml/__pycache__/unescaper.cpython-39.pyc,,
astropy/utils/xml/__pycache__/validate.cpython-39.pyc,,
astropy/utils/xml/__pycache__/writer.cpython-39.pyc,,
astropy/utils/xml/_iterparser.cp39-win_amd64.pyd,sha256=J47_fYOlUxnNhIxMpjsD1mEAplBfTf0ePEO3LHVRHXw,138240
astropy/utils/xml/check.py,sha256=zpZEf6pE6iwZOpdhtdfydotvW4tcm35eYoYziixb5T0,2124
astropy/utils/xml/iterparser.py,sha256=vWlIlR-IgQYcfC6v8YUArmaat1-QSl6jbDud91QLFMM,6051
astropy/utils/xml/setup_package.py,sha256=YdksI9a-XREKj-POn36-Qsvs3_qUdxFn6nFH5YhWz0Y,1613
astropy/utils/xml/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/utils/xml/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/utils/xml/tests/__pycache__/test_iterparse.cpython-39.pyc,,
astropy/utils/xml/tests/test_iterparse.py,sha256=81lFjrEnYFS3_5aIUi4T5yHwh5DwE3wxjUTFbrh_Jk4,4830
astropy/utils/xml/unescaper.py,sha256=A1c-IoBV-HNlbWdUyFEK5jMU3HmmMwc-8HMokQS178M,1413
astropy/utils/xml/validate.py,sha256=_gvhVifqPLlEcPOfRrAKrP2pBwi7Jy19TcNs1NM_aVc,1578
astropy/utils/xml/writer.py,sha256=4GvxKmubvVeNG6sIv3t5CjE8_4x9Cf42jAX6hh8Xw0Q,10389
astropy/version.py,sha256=hJ0miY_UaaSUiQpGTEfhB29tVd8otUwmGcyJWrVzQNE,979
astropy/visualization/__init__.py,sha256=Rl8qGKQFj3Foh0SLKZBTAXgtExZewoh88nhV9J0tnsA,278
astropy/visualization/__pycache__/__init__.cpython-39.pyc,,
astropy/visualization/__pycache__/hist.cpython-39.pyc,,
astropy/visualization/__pycache__/interval.cpython-39.pyc,,
astropy/visualization/__pycache__/lupton_rgb.cpython-39.pyc,,
astropy/visualization/__pycache__/mpl_normalize.cpython-39.pyc,,
astropy/visualization/__pycache__/mpl_style.cpython-39.pyc,,
astropy/visualization/__pycache__/stretch.cpython-39.pyc,,
astropy/visualization/__pycache__/time.cpython-39.pyc,,
astropy/visualization/__pycache__/transform.cpython-39.pyc,,
astropy/visualization/__pycache__/units.cpython-39.pyc,,
astropy/visualization/hist.py,sha256=_8vVJiwthxv23m9_o7lgHVEPJdcGxceUoNDbngDqw1A,2441
astropy/visualization/interval.py,sha256=dSgKNMctfQrsxJ-tDLt_c6OyvyZgIAi4ruq_w19KpCs,9466
astropy/visualization/lupton_rgb.py,sha256=hqLai8iPP5STiupXLRg-iES1JdWtP12iN8zz4SuJj_g,12524
astropy/visualization/mpl_normalize.py,sha256=qPRVTGuCypGGIbY-u625omAodeSsQfY3AIKpMiPd6uM,13747
astropy/visualization/mpl_style.py,sha256=BB-eip-aLxaaaSTeQSOcaGBcgCU5L4Udr_TsVe06LpM,2195
astropy/visualization/scripts/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/visualization/scripts/__pycache__/__init__.cpython-39.pyc,,
astropy/visualization/scripts/__pycache__/fits2bitmap.cpython-39.pyc,,
astropy/visualization/scripts/fits2bitmap.py,sha256=HBPFC20Lo6jVLZFM3hZfDvjQR4-rmflFe2oJg2Rhhp4,7364
astropy/visualization/scripts/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/visualization/scripts/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/visualization/scripts/tests/__pycache__/test_fits2bitmap.cpython-39.pyc,,
astropy/visualization/scripts/tests/test_fits2bitmap.py,sha256=lzYeYsk2ZXzwtC9L_Agy0gsSOsy3w_leSKgpr9Bxu-M,2299
astropy/visualization/stretch.py,sha256=fZLckimbBkUq9uJhhIywuQD-JFUKYTFioIg6cr3xtPk,21132
astropy/visualization/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/visualization/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_histogram.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_interval.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_lupton_rgb.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_norm.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_stretch.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_time.cpython-39.pyc,,
astropy/visualization/tests/__pycache__/test_units.cpython-39.pyc,,
astropy/visualization/tests/test_histogram.py,sha256=M6eBNtChECtXj8uyR8iWhK29BatPMV8jeXmu179ysek,2299
astropy/visualization/tests/test_interval.py,sha256=RGiEZR3YsfRGJ00WXLkpxto1Cm9ZDTGbHLJhB70cpZE,5249
astropy/visualization/tests/test_lupton_rgb.py,sha256=qC47Ht3bZUEqItrZDriIhFtf20DEaepY_ey7h8dnilQ,9224
astropy/visualization/tests/test_norm.py,sha256=4QvMLRGgg-Ut51h4gg0We3tXYbmZF_y7AnxsBWm9_kQ,11306
astropy/visualization/tests/test_stretch.py,sha256=M1Pd64XNUwNyZovad69EkTf812Xf9kFdqh5e8v4APjU,6067
astropy/visualization/tests/test_time.py,sha256=X7RasOqOuuaRBe0LUt1ok042m1GLndUPq0kTRPpDlXQ,8246
astropy/visualization/tests/test_units.py,sha256=HPPt6TcudRNXgo3h4WtXK9hcrRtX36NlkoJKl7FaPTU,3906
astropy/visualization/time.py,sha256=jKElwKgb6uD4FiOwvxWoKXQmvZ6eC0B62gb917FUtk0,9610
astropy/visualization/transform.py,sha256=cajJiaPju388kybCH3tek_FLaoxSvr5GUIbgenZIK0w,1113
astropy/visualization/units.py,sha256=0noljEfMJuvaXwiZrYk9JlXUUyRF2_kKLjMZfaXDLo4,3946
astropy/visualization/wcsaxes/__init__.py,sha256=9QWnfphT1mRJXias9x_LERtfMvSNel9ywmpAzSBZn_Y,1159
astropy/visualization/wcsaxes/__pycache__/__init__.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/axislabels.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/coordinate_helpers.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/coordinate_range.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/coordinates_map.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/core.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/formatter_locator.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/frame.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/grid_paths.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/patches.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/ticklabels.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/ticks.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/transforms.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/utils.cpython-39.pyc,,
astropy/visualization/wcsaxes/__pycache__/wcsapi.cpython-39.pyc,,
astropy/visualization/wcsaxes/axislabels.py,sha256=wf5-KNTGNVjB-8EZbzB4-J7wi_i5KISOfiL4bLJNFMk,4732
astropy/visualization/wcsaxes/coordinate_helpers.py,sha256=ug9Qs1CewjoT8XheYu3YRTPEbjfYlPa0Wo56e2U82Ck,43951
astropy/visualization/wcsaxes/coordinate_range.py,sha256=bOzjIprXuaUnTOM70tA5WtzVzCvkKoIFDfsQQY3zBes,4818
astropy/visualization/wcsaxes/coordinates_map.py,sha256=N5wvpienXq_5_AVqW3BZpDiGelmmZNa5JRQDly4CY30,7817
astropy/visualization/wcsaxes/core.py,sha256=PonTRGv8Z-jY23uGdYfRRmhpx5T757eJrFpUODwhvXk,31615
astropy/visualization/wcsaxes/formatter_locator.py,sha256=G2DTj05e-Xk10k7-4PM-ybPEHb7xriOGIVpHKSfFCGk,21248
astropy/visualization/wcsaxes/frame.py,sha256=E0s7N_PktT3Jmd32G9nd033FJSmlDaqT7e-kG6ejvzQ,10649
astropy/visualization/wcsaxes/grid_paths.py,sha256=jTcIClZWGKy_z3WuyzwuCVbGeBvyJEyjqElbIyBLcjc,4066
astropy/visualization/wcsaxes/patches.py,sha256=W172KEV2MnKZSGMNjpnvPd_fvwufbH9S-8b-Ob5Fh8M,7667
astropy/visualization/wcsaxes/tests/__init__.py,sha256=FAV_YDCqelhxCfopJ4FhQaJY57I32k4jgkp2HjLrX_0,300
astropy/visualization/wcsaxes/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_coordinate_helpers.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_display_world_coordinates.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_formatter_locator.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_frame.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_grid_paths.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_images.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_misc.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_transform_coord_meta.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_transforms.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/__pycache__/test_wcsapi.cpython-39.pyc,,
astropy/visualization/wcsaxes/tests/data/2MASS_k_header,sha256=M2DC6I5L8enE69knIBF4PqnCw05NmlKh97uyfO_PkZc,1133
astropy/visualization/wcsaxes/tests/data/cube_header,sha256=o11AIq-WGRXvIOnuMwwMlxOAsbJEsK1AErQD0UtpQwI,1619
astropy/visualization/wcsaxes/tests/data/msx_header,sha256=qczPbXvyNl4kWDv28Qt_fnX0pzHTW5ut57prZlFEW0w,1052
astropy/visualization/wcsaxes/tests/data/rosat_header,sha256=S2k_t9Q9x-549QMb2pJd4hlxWzwAvC_xswDEAzxpdsE,1052
astropy/visualization/wcsaxes/tests/data/slice_header,sha256=axmEHC5sxgJhIJkJyd02mOJaXAwFjuzUCPLEploNOCE,1123
astropy/visualization/wcsaxes/tests/test_coordinate_helpers.py,sha256=3VMLb5NRlnT44wsQdVxlOhi_HhnhB-GGcDD8-yKP9Ms,3303
astropy/visualization/wcsaxes/tests/test_display_world_coordinates.py,sha256=P6bAAEq7pPXtLt8eeZSWMXIBjqQa5RvvyuVH2-bPSnw,6507
astropy/visualization/wcsaxes/tests/test_formatter_locator.py,sha256=yrJWHnMNGx1QcAvXSctYez28Vnm2CZ1RbVVS_S1FbfU,22477
astropy/visualization/wcsaxes/tests/test_frame.py,sha256=W1DEw2IHJAJ2EAUdStj-CLU4kDoyL9sYSNEn3En277w,5290
astropy/visualization/wcsaxes/tests/test_grid_paths.py,sha256=8OogE5yMqW1jA7i9kU21uJxAYO8AgP9ZwOzJyxXK9Lo,1050
astropy/visualization/wcsaxes/tests/test_images.py,sha256=HbadZtOu2GFOndFCljhuzn-RzDz3cpZVNcMxByc7_Q4,40453
astropy/visualization/wcsaxes/tests/test_misc.py,sha256=qluly6H6rMWmP1003wBTPFRJZ4dhueBfMqlopKxOpxA,17796
astropy/visualization/wcsaxes/tests/test_transform_coord_meta.py,sha256=LJMSBJyAZe5RxoYFhGvddpSNFhVMK5d6g8ohfNmuKNU,5203
astropy/visualization/wcsaxes/tests/test_transforms.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/visualization/wcsaxes/tests/test_utils.py,sha256=272prwKNGCyWhPjk2J5P5Nh56jDFRnehwNTvQ9oxXNI,3585
astropy/visualization/wcsaxes/tests/test_wcsapi.py,sha256=ekgvZ0Fb_opu6QHUq5dVgNE3mpOBeQUUAryNZEr76OE,17905
astropy/visualization/wcsaxes/ticklabels.py,sha256=v2SPM8jJDir8NYUZTBRmf_wc0Eup6ZE_STrIu8G-I1A,9824
astropy/visualization/wcsaxes/ticks.py,sha256=v6JOEZseZCPC3tvvdZtPxkrrdO_mu7OoP7pIGw6m3Hg,6785
astropy/visualization/wcsaxes/transforms.py,sha256=36LZei2GNqtPZHwyrnbEKY8nUn6RiQK9TGLhjmqqpgA,5761
astropy/visualization/wcsaxes/utils.py,sha256=FPPvkkEv519hMzafgi-Aq9bu1kZtirTHDwFLukRR7RA,5676
astropy/visualization/wcsaxes/wcsapi.py,sha256=TJmIHoKhPCABecNJ-LXUyBFi9eHElNh0Rrm-QmBcOJs,13534
astropy/wcs/__init__.py,sha256=dGmUPySEgQjVkI1nceHgHWkWGewj8G7jbI129O8QcDg,1347
astropy/wcs/__pycache__/__init__.cpython-39.pyc,,
astropy/wcs/__pycache__/docstrings.cpython-39.pyc,,
astropy/wcs/__pycache__/setup_package.cpython-39.pyc,,
astropy/wcs/__pycache__/utils.cpython-39.pyc,,
astropy/wcs/__pycache__/wcs.cpython-39.pyc,,
astropy/wcs/__pycache__/wcslint.cpython-39.pyc,,
astropy/wcs/_wcs.cp39-win_amd64.pyd,sha256=vDT4boO-pQmTKeWtukNHUXyOATLWzMsUgKRkCyolX78,1440256
astropy/wcs/docstrings.py,sha256=1TPSbmlCKsoSEK3BxblYb1P-etaUW0xTN_w7zHak4Dg,68913
astropy/wcs/include/astropy_wcs/astropy_wcs.h,sha256=ailzl2068NgA_dc3cvuW4qnogXVFhvZrLLUCaLTHq4w,482
astropy/wcs/include/astropy_wcs/astropy_wcs_api.h,sha256=dKXYdsLmHs5Spmr5oIJ4ZuLj9YQGi9BZ207k2dMGjVA,4788
astropy/wcs/include/astropy_wcs/distortion.h,sha256=w3yaPcOG0v1z75hWNLC4HKLM0jJMxidq_tApSSnlkYI,2656
astropy/wcs/include/astropy_wcs/distortion_wrap.h,sha256=3V3wQ4FI1uxu8d-aL4WtQdtbTp9bAFEh-ayHRiyO0NE,403
astropy/wcs/include/astropy_wcs/docstrings.h,sha256=Yn5sn7L6GNS1s7YPzfzE42-l4m_1qQSVDK3IHGkWQpc,5439
astropy/wcs/include/astropy_wcs/isnan.h,sha256=bkK9oBU-4WzupOgzl7EeQ0Eo0GPfaTFdBiNwhqQ9g_A,1182
astropy/wcs/include/astropy_wcs/pipeline.h,sha256=wrODGeRXwF1B2XP6Z6qyfv0D4mQO_6cdMzW6j2Lwe30,2327
astropy/wcs/include/astropy_wcs/pyutil.h,sha256=8RS3rrm5xJK4Afz0v0g0-zHk7TH--9x-VeK9Uzas1aE,6562
astropy/wcs/include/astropy_wcs/sip.h,sha256=gfjMI3J0VSQG9ab4G5OJzoM0lpmOy9hHJxjCZRnKwQ0,4208
astropy/wcs/include/astropy_wcs/sip_wrap.h,sha256=P_vIika7JspANnG4ftOZHH8yr8Yob9dDKV0mO4ZOHrw,278
astropy/wcs/include/astropy_wcs/str_list_proxy.h,sha256=PGW_50OD_ZJU-8f-F-wc4h7bibZnnUiZRqKfuxV-pJ4,804
astropy/wcs/include/astropy_wcs/unit_list_proxy.h,sha256=KspoxcPrr0t2kMvpYgvwnSaqlrGQgLcXkHJpXcJ04Gk,952
astropy/wcs/include/astropy_wcs/util.h,sha256=Km3k90qZiVdnU9rlzcGDrehQAY_776q1Ky9dYt623NM,538
astropy/wcs/include/astropy_wcs/wcsconfig.h,sha256=-GbCxwwIZqU3rCu19oUmsWKPq9EAkmQCfrT_vTEyBDM,899
astropy/wcs/include/astropy_wcs/wcslib_auxprm_wrap.h,sha256=Fxl9r_4h4Xo4kP5xVpZ6qYoSLpeG1MfqOXrdQioqONQ,332
astropy/wcs/include/astropy_wcs/wcslib_tabprm_wrap.h,sha256=BYgR6icetQH9U7IGpCxA1a5sbYQuooWQNJnCTE-Z2Ok,392
astropy/wcs/include/astropy_wcs/wcslib_units_wrap.h,sha256=va_gtHekHiCp63Ta1OSM8hDIWlXXUCqVe5ow2SwOJqs,528
astropy/wcs/include/astropy_wcs/wcslib_wrap.h,sha256=8yCSb1XkuvQKiQ8ofkOOeEAF1eTNXPpyTSmjNRY4_kY,470
astropy/wcs/include/astropy_wcs/wcslib_wtbarr_wrap.h,sha256=wR5Mau0xvFJxGOI0BVbkNuPoCltpI9cO-iGKJhlvbDE,392
astropy/wcs/include/wcslib/cel.h,sha256=EguK9BlKmRMMpQcPiIMcVo3_R9hg6_-vWCPWRbkr0N0,19459
astropy/wcs/include/wcslib/lin.h,sha256=rISnTbAVcOEPx-hG-8l6as2aqVGuPOPxgIb0T2vBWos,29003
astropy/wcs/include/wcslib/prj.h,sha256=9OxAsAFyQjeI3a5mn2Mv7l9VwKqlS9EoVe44UffKuHc,32349
astropy/wcs/include/wcslib/spc.h,sha256=iZX1VQTwFwrQ2pZHfZ958K-9hK1s7Gidc9lWiiYGkdo,40223
astropy/wcs/include/wcslib/spx.h,sha256=DCjQi6DzEmzQ5y1gDyJ3UFPoACOpDREGbwO9YzgqdSU,21956
astropy/wcs/include/wcslib/tab.h,sha256=dQWh0kYvZskNa13eugNcjw_Jlzx06nN-KAEM6hD5uuU,26333
astropy/wcs/include/wcslib/wcs.h,sha256=4c-wJIP1HAqJKxrCLMrQl5jhvTbLbVWZmciMRwdaUJo,94022
astropy/wcs/include/wcslib/wcserr.h,sha256=pKjjHDRYsezr6VrgtcnqwCpEAoKmGRwrue7gX4X4kSg,9811
astropy/wcs/include/wcslib/wcsmath.h,sha256=qfUUI_uBSb4xVhQI3t1nMOdvnEcRrNPlO2JsksziM7w,1969
astropy/wcs/include/wcslib/wcsprintf.h,sha256=hX6P-4k4Ub9Z5q9sFkUOdSAph2jurGJamvXU9wgBNo0,6054
astropy/wcs/setup_package.py,sha256=i82xe0HLY4mXn3TXqYuYRTqHnSKX7V0Eg2-Uo-gqV-s,9633
astropy/wcs/tests/__init__.py,sha256=ykqVHge2EmIDTMOd96h2DyGHaM_gpp_wKz3K1MlZZic,64
astropy/wcs/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/conftest.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/helper.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_auxprm.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_pickle.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_profiling.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_tab.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_tabprm.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_wcs.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_wcsprm.cpython-39.pyc,,
astropy/wcs/tests/__pycache__/test_wtbarr.cpython-39.pyc,,
astropy/wcs/tests/conftest.py,sha256=Dk8aEflK39jaRGg_T7OnVBiL4vxcJ3e9T5QpvLSRSTI,880
astropy/wcs/tests/data/2wcses.hdr,sha256=wvk-MIKIF9d7x4RZizvENIo4ZKfJ90CW3qP04S9uHNQ,8640
astropy/wcs/tests/data/3d_cd.hdr,sha256=mSkLOeMAJLL-j69_bgd_juFj2X9BJ0HJVM0bRxf8fKY,1280
astropy/wcs/tests/data/chandra-pixlist-wcs.hdr,sha256=7mxrC3_95Klk4vGvDt7A-NmKgxgR258dvKvvIDR3uUo,6560
astropy/wcs/tests/data/defunct_keywords.hdr,sha256=MNEZerZed6KEbxEXzwMUPapjRIgpGhTnymo8nkNcrys,3123
astropy/wcs/tests/data/dist.fits,sha256=AXIOKR2I_pFzFOHdDCKOHWZMp9mSwAE5k-IBhJUl1bM,23040
astropy/wcs/tests/data/dist_lookup.fits.gz,sha256=Us3GgwAb5XurTMOOS02YzTgpg9YlVw7--ebeHij3pVU,68483
astropy/wcs/tests/data/dss.14.29.56-62.41.05.fits.gz,sha256=2eiprc7VUgC0GFF9X7pfYaQDhCql61ffkoHrOL641oo,13277
astropy/wcs/tests/data/header_newlines.fits,sha256=E1B8WLLO2cj28lHd9D3074isp5W3gnWGIs4lPqaUUwA,37440
astropy/wcs/tests/data/header_with_time.fits,sha256=QcE1qgqka4KAmv0CRlOUhEYodUtIEtRLhq5BghnG3n0,8640
astropy/wcs/tests/data/header_with_time_wcslib71.fits,sha256=rJcNWmeqdm0RHLE41gLcJT_kQvwiMtH6JW_9oeq7hXE,8880
astropy/wcs/tests/data/ie6d07ujq_wcs.fits,sha256=djYuqGpa9xz9vnYooVO0TLnWGumB653zr59tPir4nD8,28800
astropy/wcs/tests/data/invalid_header.hdr,sha256=XvZ7IdK9GqYeaiHCIOmcHSD0xyWe48MCFv7wnvlLzmY,28964
astropy/wcs/tests/data/irac_sip.hdr,sha256=5c7u4wRh3VCy1nTIySO_LiKtPLPzZbXP7nioKvx93S8,19360
astropy/wcs/tests/data/j94f05bgq_flt.fits,sha256=kAA44NhTgoFAp1fiZWk0yyaP-fMVxcb2F96FpjKtUms,83520
astropy/wcs/tests/data/locale.hdr,sha256=0TYfA4-IJd6mtZGSHJFUXSN08TAs8hDZ_BJft-jPCM0,2880
astropy/wcs/tests/data/maps/1904-66_AIR.hdr,sha256=wVTlRZxBg7f6S8nP45PtyXuIvVWkBZ1pUdl8rjOP3sk,9280
astropy/wcs/tests/data/maps/1904-66_AIT.hdr,sha256=4-hP422SgFfFOnF_PfVdKF30TgwbcUQG7gfzP6SJqJc,9200
astropy/wcs/tests/data/maps/1904-66_ARC.hdr,sha256=UtXrKukkKuFz_zdU9p3Dx-SjkhcXV_6TEA1q2PmKJP8,9200
astropy/wcs/tests/data/maps/1904-66_AZP.hdr,sha256=JZp8wSZJAl3WUZB4Nus5LAbxEFbjaVF5mTL9V2-ZrOQ,9360
astropy/wcs/tests/data/maps/1904-66_BON.hdr,sha256=CxktHp7y6f5hidS01ojQu5eVAgIYMppKkMagwsQWgsw,9280
astropy/wcs/tests/data/maps/1904-66_CAR.hdr,sha256=DPtguXGTmKjfe-9tgunr94jk0FhQklIUpYFtr73gUDY,9200
astropy/wcs/tests/data/maps/1904-66_CEA.hdr,sha256=VbB6Z9MOTbuHlgTJu5TDqS-nFTExgSEf8udUKedRS2o,9280
astropy/wcs/tests/data/maps/1904-66_COD.hdr,sha256=FvRTYQOS2bdnT05sniiKGusbRMLrcxlX36gCUKMOF2g,9360
astropy/wcs/tests/data/maps/1904-66_COE.hdr,sha256=sNl9XKNzd2b1qDWw_iYR-cfHkaiL4ZGuGYv1YUd2wq8,9360
astropy/wcs/tests/data/maps/1904-66_COO.hdr,sha256=PqZ4Ycqjb7KADa93McFgt6dju-9eB3cCL5A1l4hto4M,9360
astropy/wcs/tests/data/maps/1904-66_COP.hdr,sha256=dtnyecQipwDx9uNwVXfrg2C0CCPBeS8-No1bMlh-os4,9360
astropy/wcs/tests/data/maps/1904-66_CSC.hdr,sha256=f0X1nvcsbBFryC_Ns2ia79k-NgOMTBr-HeQmPukWKR8,9200
astropy/wcs/tests/data/maps/1904-66_CYP.hdr,sha256=f8LkYfN87pSd3d4JgxVy6uC6El06vPIPLZOOtNfCZWk,9360
astropy/wcs/tests/data/maps/1904-66_HPX.hdr,sha256=UCGYQMNhU4t-Q-Fw7141JHPnAtr1Z7CbWhVMgQl-1oQ,9440
astropy/wcs/tests/data/maps/1904-66_MER.hdr,sha256=ZqLQhgcnnOSVxWjewTDxbQVuzbUmJ8MdP-uARoeDkRI,9200
astropy/wcs/tests/data/maps/1904-66_MOL.hdr,sha256=U2w6_vbtIzCB7JceAVtobhLKvNf75nRWPwCL2PhZzis,9200
astropy/wcs/tests/data/maps/1904-66_NCP.hdr,sha256=mytR_YjrxCHHwyekpXp9zcjfal6d9OdcUcsygXL9zoA,9360
astropy/wcs/tests/data/maps/1904-66_PAR.hdr,sha256=jpvyG0q3C6NfVAjLHWkMz4xu7F37oB4LiYznTDjuz1o,9200
astropy/wcs/tests/data/maps/1904-66_PCO.hdr,sha256=Ae_Jp7YfO34MiUxNIHRve0pQZlg6bF8dITlsIbIw89A,9200
astropy/wcs/tests/data/maps/1904-66_QSC.hdr,sha256=fa4hDxsJUgtEBdrMtxwt7uFUbiVnlK_S1zkjx8PtuME,9200
astropy/wcs/tests/data/maps/1904-66_SFL.hdr,sha256=deGT0yl_XNjm-A0sAXrm57eBDvi_4oghHiJR2BlARoY,9200
astropy/wcs/tests/data/maps/1904-66_SIN.hdr,sha256=eLQUFg3opJoZb6h8EUXG63IY50-AlKLpGMlLJf3kkWw,9360
astropy/wcs/tests/data/maps/1904-66_STG.hdr,sha256=l1Sx5QS82e0MjsDmH2IXO7UV__TduHOJTf7DZ2jstvc,9200
astropy/wcs/tests/data/maps/1904-66_SZP.hdr,sha256=_TY5m-c9UdezfJj5nwP1QWV8Q_5AHfmW6r6mgNoeIsQ,9440
astropy/wcs/tests/data/maps/1904-66_TAN.hdr,sha256=RA-RxnldIz_t3zXJ9Rkv7pvp0NcWJ-EwvmdRX_4mPms,9200
astropy/wcs/tests/data/maps/1904-66_TSC.hdr,sha256=Zxi148pBbp6bMayrJrtDCEGxH6NYNmli6nrDkgfDzXk,9200
astropy/wcs/tests/data/maps/1904-66_ZEA.hdr,sha256=_VAlXDZae5oTGGeq8WJRF8C6yAIimz6msjg6SnG3cuw,9200
astropy/wcs/tests/data/maps/1904-66_ZPN.hdr,sha256=M07CWMPW_wWZxjwbwa6KwUi2rvyZac5MLNR_YN9uJ-4,10800
astropy/wcs/tests/data/nonstandard_units.hdr,sha256=_U78tjNeshdfjfEc4PaMjUbneo7QRpEgm7f2ahNo8DQ,1283
astropy/wcs/tests/data/outside_sky.hdr,sha256=4QVktMeEy_9Jh7xSvgWNbCoOhnwTr6TOnfXpjOVXUbI,1471
astropy/wcs/tests/data/sip-broken.hdr,sha256=8CjPzJ7FpE2Q-vsgb-1aNY3CVZ-BQNyxnojq18XMhsE,25920
astropy/wcs/tests/data/sip.fits,sha256=6va-fWqHSABBBoKDJrX-tiBD9hSabTpssgwjKcnzkdM,5760
astropy/wcs/tests/data/sip2.fits,sha256=0Sk_2QLWFZlI3d7PlCV1GUj1wH9HLx0_sb7hSO6jiX8,5760
astropy/wcs/tests/data/siponly.hdr,sha256=rHhqEkk0o80A8RkhxvJke0cPBe8EovQDOWC0CGNaNKk,28800
astropy/wcs/tests/data/spectra/orion-freq-1.hdr,sha256=dpLVpCS9rKTO1Rek30EGTseiMNf9tOTt5sMcmI4yYCA,30400
astropy/wcs/tests/data/spectra/orion-freq-4.hdr,sha256=xolKD3Rr8jkBJSpcNS9rZ7Cs8TGgoESKV2rAjp8ThkI,30640
astropy/wcs/tests/data/spectra/orion-velo-1.hdr,sha256=aG6xVghXd_ts1cVtT5ep9HUSJfqCM2SqAcUKn18NbuA,29680
astropy/wcs/tests/data/spectra/orion-velo-4.hdr,sha256=y9rb2HwCLtTRJ5BRj30WC2yKnOvquvvPl7grsaduU_s,29920
astropy/wcs/tests/data/spectra/orion-wave-1.hdr,sha256=lK1uIc8gYL3uP-CNMhM0NXoHZa18pWYCaNcUTKAYmZc,29600
astropy/wcs/tests/data/spectra/orion-wave-4.hdr,sha256=beF-BphLGxC69fv_UfBQwdmeRxxtjH4Lk-tVwNTXD_8,29840
astropy/wcs/tests/data/sub-segfault.hdr,sha256=BmklcvQT4PR13x__gCwMWa4BKkKt0j9tXl2tjZipFr8,1986
astropy/wcs/tests/data/tab-time-last-axis.fits,sha256=0pE7PMkX3OyPlWbCCwas36YQrMogh1eNIJf2n21xBWE,14400
astropy/wcs/tests/data/too_many_pv.hdr,sha256=zWNL8mgcgsJeIQpvX7awnLaGdpcKiiHL6EwjBeD2-VA,34560
astropy/wcs/tests/data/tpvonly.hdr,sha256=yj91PHBPrcevt0ssCFr9RTnElCca-9ISyTR2qSAliZ0,25920
astropy/wcs/tests/data/unit.hdr,sha256=pnaByaDErnfZt1VCPBIO-d0sHLiiz-9lgPDJ8kIhuYI,2880
astropy/wcs/tests/data/validate.5.0.txt,sha256=WkeknwqmYxZTPxRjjrqdCbR4prv3QtSFA5Ny3Gmv4RQ,605
astropy/wcs/tests/data/validate.5.13.txt,sha256=gPVYDz8kG-StlRsnhkDkMff_AKUobxisoQUd7m-EAwI,599
astropy/wcs/tests/data/validate.6.txt,sha256=Z_ELGDtBeNk59Dx8cTFT01_T1QhwpVXM26L4rMBPjUw,606
astropy/wcs/tests/data/validate.7.4.txt,sha256=-6XZZX1VgLdOAX9mcJpcIw1-k4k_fTSMTkhGim_OaAw,731
astropy/wcs/tests/data/validate.7.6.txt,sha256=iH4Kc-US56OyfH-1N2pPobVN5w28uBYUShPuSd70qVg,688
astropy/wcs/tests/data/validate.fits,sha256=3X-AOOpJ3tonKVBU-j2Q7efiWSRieVU9_nT3tXfPyxg,40320
astropy/wcs/tests/data/validate.txt,sha256=nTBzSluFaoSz7U0ZyHLSB1W_cicwFKEEFzpHzv2dIqg,595
astropy/wcs/tests/data/zpn-hole.hdr,sha256=dwOWuhTm8iSrVuie_qlHOO-EiPAwfT4dkzOi0ovjI6w,2880
astropy/wcs/tests/helper.py,sha256=Bsw-VxLnOob_zivjDtZXNWJZeYm0KGxmo-uTfb8903o,3793
astropy/wcs/tests/test_auxprm.py,sha256=r4mAN6n3t4FR07SAqRPLYdGDAgqJD-LrldXS0djYDZM,5596
astropy/wcs/tests/test_pickle.py,sha256=NlbZkvjARzhbsTjcD7UWnatx66QnkLBxKrFBNqHdT-c,4848
astropy/wcs/tests/test_profiling.py,sha256=iIgNi5z25Jef_pWtGA3PDqq4R_0dNOxG3BwRQ0aEWuo,2582
astropy/wcs/tests/test_tab.py,sha256=1FmJXb7qy0ocrcAtGqps_lKMR9Vj_FVLQQ2ugAoxUUY,1904
astropy/wcs/tests/test_tabprm.py,sha256=GVs1WRTzLaRN7kzoBv9OQzZrfv3QT80EZGwGdlWK_KU,3113
astropy/wcs/tests/test_utils.py,sha256=Mf0-RSgYAEd5qjYiqEgm6RjTTiXRDFO4z6su1gOvHyk,48257
astropy/wcs/tests/test_wcs.py,sha256=AavYm6CSoKvihA1TixRvMAq1kQjiEE01WWSukLOc1zI,52954
astropy/wcs/tests/test_wcsprm.py,sha256=HRENNgfllZHbCESrZdUPTe6ucOzC-BsIaRNnja5Fr60,27697
astropy/wcs/tests/test_wtbarr.py,sha256=2wperDfwTCrNzNizGu088h6-y8oBU6wAkwfzVDOeb6Q,1588
astropy/wcs/utils.py,sha256=5BPbZaZgbdOUQA9zR0AHul2GkKF6RHnIOIK_MPK6CSQ,42794
astropy/wcs/wcs.py,sha256=CpW-yjXD2MT35l5HNgTrgBmEpqWgWeQ0tXK_uEkkR_s,133221
astropy/wcs/wcsapi/__init__.py,sha256=3pSx24CthCn920Zy3whVJyu164DaALja-_DKY5aVlc8,182
astropy/wcs/wcsapi/__pycache__/__init__.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/conftest.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/fitswcs.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/high_level_api.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/high_level_wcs_wrapper.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/low_level_api.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/sliced_low_level_wcs.cpython-39.pyc,,
astropy/wcs/wcsapi/__pycache__/utils.cpython-39.pyc,,
astropy/wcs/wcsapi/conftest.py,sha256=povWUMO_ULYKZggbHuGaM_rFAkXbGSXdLSYIo9pk2J0,4462
astropy/wcs/wcsapi/data/ucds.txt,sha256=3uKrwHXEdw_p-mYCUYdaJWrXPX0cjm0hqACwOp4V650,7048
astropy/wcs/wcsapi/fitswcs.py,sha256=RB2TaCm3gXjc6UP11xhw8S0ZWTetPOtrr18ce0yXiJ0,26660
astropy/wcs/wcsapi/high_level_api.py,sha256=7UmyQnEnktrhAi4wDjP5HSRAnH0FXHeEO89C6-Ykxdg,11373
astropy/wcs/wcsapi/high_level_wcs_wrapper.py,sha256=kT0JbLlKNdAbTD0WzHMO-lecDT0e988k_Wssa9Nid0k,2294
astropy/wcs/wcsapi/low_level_api.py,sha256=7f704MB9S0Q2YMrlvFa0RzlaAMhmw875ZtRpfOpzq90,15734
astropy/wcs/wcsapi/sliced_low_level_wcs.py,sha256=W0Hy-6ufFAhsYgRvbhO2e7qq417pG8Ve0OwqypOcfa8,354
astropy/wcs/wcsapi/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/wcs/wcsapi/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/wcs/wcsapi/tests/__pycache__/test_fitswcs.cpython-39.pyc,,
astropy/wcs/wcsapi/tests/__pycache__/test_high_level_api.cpython-39.pyc,,
astropy/wcs/wcsapi/tests/__pycache__/test_high_level_wcs_wrapper.cpython-39.pyc,,
astropy/wcs/wcsapi/tests/__pycache__/test_low_level_api.cpython-39.pyc,,
astropy/wcs/wcsapi/tests/__pycache__/test_utils.cpython-39.pyc,,
astropy/wcs/wcsapi/tests/test_fitswcs.py,sha256=pgZeP5u5DSDSq5e73cR3iAAhBixYU4acM_HIa5QeKts,36635
astropy/wcs/wcsapi/tests/test_high_level_api.py,sha256=otMpxtnwi-XGH1IG9iWQ_80T2gXT3CLRLorFIGE2394,4974
astropy/wcs/wcsapi/tests/test_high_level_wcs_wrapper.py,sha256=CytiLNzQ4HuAGMeoSKlsS_lZqrERjlUxOGaMRv9Lw0E,2091
astropy/wcs/wcsapi/tests/test_low_level_api.py,sha256=oMyrvw4mD4yJ4ZQCqODXF82QCiyqaD6CEYCpzwXkB9o,709
astropy/wcs/wcsapi/tests/test_utils.py,sha256=kSTlHun3qZeA08XP5yT1nMQlObRYhZbCZGTBxCfizO4,1547
astropy/wcs/wcsapi/utils.py,sha256=7tiVV3ylsggZW2xuZO91BzF6jYjf4uBDolz4tMX6H2A,4446
astropy/wcs/wcsapi/wrappers/__init__.py,sha256=_cXHv952WU8InWmyHcOJgUGFnnJauyFWO1K5QZQWnD8,67
astropy/wcs/wcsapi/wrappers/__pycache__/__init__.cpython-39.pyc,,
astropy/wcs/wcsapi/wrappers/__pycache__/base.cpython-39.pyc,,
astropy/wcs/wcsapi/wrappers/__pycache__/sliced_wcs.cpython-39.pyc,,
astropy/wcs/wcsapi/wrappers/base.py,sha256=JZjTdF4gW-l5dJzPg7io_2NUhcZoYjrP8oo_RHUduTg,2013
astropy/wcs/wcsapi/wrappers/sliced_wcs.py,sha256=n7rHCRs1N1ovRhpRRDjOMm44C415M5oItu0jOS0RBPA,11483
astropy/wcs/wcsapi/wrappers/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
astropy/wcs/wcsapi/wrappers/tests/__pycache__/__init__.cpython-39.pyc,,
astropy/wcs/wcsapi/wrappers/tests/__pycache__/test_sliced_wcs.cpython-39.pyc,,
astropy/wcs/wcsapi/wrappers/tests/test_sliced_wcs.py,sha256=qHDvz96n_hTmHCr_rFIZOI0Tg8qbpcs5FOh_YCTCaAI,32387
astropy/wcs/wcslint.py,sha256=xKKdSUe5es2WWCqDMDRLgODCsUyNRAcvUKUG9_myyN0,530
