Metadata-Version: 2.1
Name: wheel
Version: 0.37.1
Summary: A built-package format for Python
Home-page: https://github.com/pypa/wheel
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON>
Maintainer-email: alex.g<PERSON><PERSON>@nextday.fi
License: MIT
Project-URL: Documentation, https://wheel.readthedocs.io/
Project-URL: Changelog, https://wheel.readthedocs.io/en/stable/news.html
Project-URL: Issue Tracker, https://github.com/pypa/wheel/issues
Keywords: wheel,packaging
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: System :: Archiving :: Packaging
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Requires-Python: !=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,>=2.7
Provides-Extra: test
License-File: LICENSE.txt

wheel
=====

This library is the reference implementation of the Python wheel packaging
standard, as defined in `PEP 427`_.

It has two different roles:

#. A setuptools_ extension for building wheels that provides the
   ``bdist_wheel`` setuptools command
#. A command line tool for working with wheel files

It should be noted that wheel is **not** intended to be used as a library, and
as such there is no stable, public API.

.. _PEP 427: https://www.python.org/dev/peps/pep-0427/
.. _setuptools: https://pypi.org/project/setuptools/

Documentation
-------------

The documentation_ can be found on Read The Docs.

.. _documentation: https://wheel.readthedocs.io/

Code of Conduct
---------------

Everyone interacting in the wheel project's codebases, issue trackers, chat
rooms, and mailing lists is expected to follow the `PSF Code of Conduct`_.

.. _PSF Code of Conduct: https://github.com/pypa/.github/blob/main/CODE_OF_CONDUCT.md



