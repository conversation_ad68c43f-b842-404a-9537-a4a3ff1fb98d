#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import logging
import hashlib
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import json
import threading
import traceback
from pathlib import Path

class DatabaseLogManager:
    """数据库日志管理器 - 加密存储日志并自动清理过期数据"""
    
    def __init__(self, db_path=None, log_dir=None, enable_file_logging=False):
        # 设置日志目录
        if log_dir is None:
            self.log_dir = Path(__file__).parent / "logs"
        else:
            self.log_dir = Path(log_dir)
        
        # 确保日志目录存在
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置数据库路径
        if db_path is None:
            self.db_path = self.log_dir / "app_logs.db"
        else:
            self.db_path = Path(db_path)
        
        # 生成或加载加密密钥
        self.key_file = self.log_dir / ".log_key"
        self.cipher_suite = self._get_or_create_cipher()
        
        # 是否启用文件日志记录
        self.enable_file_logging = enable_file_logging
        
        # 初始化数据库
        self._init_database()
        
        # 设置日志记录器
        self._setup_logger()
        
        # 启动清理线程
        self._start_cleanup_thread()
        
        # 记录日志管理器启动
        self.log_info("日志管理器启动", {"version": "1.0", "db_path": str(self.db_path), "file_logging": enable_file_logging})
    
    def _get_or_create_cipher(self):
        """获取或创建加密密钥"""
        try:
            if self.key_file.exists():
                # 读取现有密钥
                with open(self.key_file, 'rb') as f:
                    key = f.read()
            else:
                # 生成新密钥
                key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(key)
                # 隐藏密钥文件
                if sys.platform == "win32":
                    import ctypes
                    ctypes.windll.kernel32.SetFileAttributesW(str(self.key_file), 2)  # FILE_ATTRIBUTE_HIDDEN
            
            return Fernet(key)
        except Exception as e:
            print(f"加密密钥初始化失败: {e}")
            # 如果加密失败，使用简单的备用方案
            return None
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建日志表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        level TEXT NOT NULL,
                        category TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        encrypted_data TEXT,
                        hash_value TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON logs(timestamp)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_level ON logs(level)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_category ON logs(category)")
                
                conn.commit()
        except Exception as e:
            print(f"数据库初始化失败: {e}")
    
    def _setup_logger(self):
        """设置文件日志记录器（作为备份）"""
        self.logger = logging.getLogger('AppLogger')
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            # 控制台处理器（始终启用）
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            
            # 文件处理器（仅在启用文件日志时添加）
            if self.enable_file_logging:
                log_file = self.log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
    
    def _encrypt_data(self, data):
        """加密数据"""
        try:
            if self.cipher_suite and data:
                # 预处理数据，确保所有对象都是JSON可序列化的
                processed_data = self._prepare_data_for_json(data)
                json_data = json.dumps(processed_data, ensure_ascii=False)
                encrypted_data = self.cipher_suite.encrypt(json_data.encode('utf-8'))
                return encrypted_data.decode('utf-8')
            return None
        except Exception as e:
            print(f"数据加密失败: {e}")
            return None
    
    def _prepare_data_for_json(self, data):
        """预处理数据，确保JSON可序列化"""
        if isinstance(data, dict):
            return {key: self._prepare_data_for_json(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._prepare_data_for_json(item) for item in data]
        elif hasattr(data, 'toString'):  # QDate, QDateTime等Qt对象
            return data.toString()
        elif hasattr(data, 'name'):  # QColor等Qt对象
            return data.name()
        elif hasattr(data, '__dict__'):  # 其他对象
            return str(data)
        else:
            return data
    
    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            if self.cipher_suite and encrypted_data:
                decrypted_data = self.cipher_suite.decrypt(encrypted_data.encode('utf-8'))
                return json.loads(decrypted_data.decode('utf-8'))
            return None
        except Exception as e:
            print(f"数据解密失败: {e}")
            return None
    
    def _generate_hash(self, message, details=None):
        """生成消息哈希值用于完整性验证"""
        try:
            content = f"{message}|{details or ''}"
            return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]
        except:
            return ""
    
    def _log_to_database(self, level, category, message, details=None):
        """将日志写入数据库"""
        try:
            timestamp = datetime.now().isoformat()
            encrypted_data = self._encrypt_data(details) if details else None
            hash_value = self._generate_hash(message, str(details))
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO logs (timestamp, level, category, message, details, encrypted_data, hash_value)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (timestamp, level, category, message, str(details) if details else None, 
                     encrypted_data, hash_value))
                conn.commit()
        except Exception as e:
            # 数据库写入失败时，至少记录到文件
            self.logger.error(f"数据库日志写入失败: {e}")
    
    def log_info(self, message, details=None, category="INFO"):
        """记录信息级别日志"""
        self._log_to_database("INFO", category, message, details)
        if self.enable_file_logging:
            self.logger.info(f"[{category}] {message}")
    
    def log_warning(self, message, details=None, category="WARNING"):
        """记录警告级别日志"""
        self._log_to_database("WARNING", category, message, details)
        if self.enable_file_logging:
            self.logger.warning(f"[{category}] {message}")
    
    def log_error(self, message, details=None, category="ERROR"):
        """记录错误级别日志"""
        self._log_to_database("ERROR", category, message, details)
        if self.enable_file_logging:
            self.logger.error(f"[{category}] {message}")
    
    def log_critical(self, message, details=None, category="CRITICAL"):
        """记录严重错误级别日志"""
        self._log_to_database("CRITICAL", category, message, details)
        if self.enable_file_logging:
            self.logger.critical(f"[{category}] {message}")
    
    def log_user_action(self, action, username=None, details=None):
        """记录用户操作"""
        category = "USER_ACTION"
        message = f"用户操作: {action}"
        if username:
            message += f" (用户: {username})"
        
        log_details = {
            "action": action,
            "username": username,
            "timestamp": datetime.now().isoformat()
        }
        if details:
            log_details.update(details)
        
        self._log_to_database("INFO", category, message, log_details)
        if self.enable_file_logging:
            self.logger.info(f"[{category}] {message}")
    
    def log_system_event(self, event, details=None):
        """记录系统事件"""
        category = "SYSTEM"
        message = f"系统事件: {event}"
        
        log_details = {
            "event": event,
            "timestamp": datetime.now().isoformat()
        }
        if details:
            log_details.update(details)
        
        self._log_to_database("INFO", category, message, log_details)
        if self.enable_file_logging:
            self.logger.info(f"[{category}] {message}")
    
    def log_exception(self, exception, context=None):
        """记录异常信息"""
        category = "EXCEPTION"
        message = f"异常: {type(exception).__name__}: {str(exception)}"
        
        details = {
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "traceback": traceback.format_exc(),
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
        
        self._log_to_database("ERROR", category, message, details)
        if self.enable_file_logging:
            self.logger.error(f"[{category}] {message}")
    
    def cleanup_old_logs(self, days=15):
        """清理指定天数之前的日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_timestamp = cutoff_date.isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询要删除的记录数
                cursor.execute("SELECT COUNT(*) FROM logs WHERE timestamp < ?", (cutoff_timestamp,))
                count = cursor.fetchone()[0]
                
                # 删除过期记录
                cursor.execute("DELETE FROM logs WHERE timestamp < ?", (cutoff_timestamp,))
                conn.commit()
                
                if count > 0:
                    self.log_info(f"清理了 {count} 条过期日志记录", 
                                {"days": days, "cutoff_date": cutoff_timestamp}, "CLEANUP")
            
            # 清理过期的日志文件
            self._cleanup_log_files(days)
            
        except Exception as e:
            self.log_error(f"日志清理失败: {e}", {"days": days}, "CLEANUP")
    
    def _cleanup_log_files(self, days=15):
        """清理过期的日志文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for log_file in self.log_dir.glob("app_*.log"):
                try:
                    # 从文件名提取日期
                    file_date_str = log_file.stem.replace("app_", "")
                    file_date = datetime.strptime(file_date_str, "%Y%m%d")
                    
                    if file_date < cutoff_date:
                        log_file.unlink()
                        print(f"删除过期日志文件: {log_file}")
                except Exception as e:
                    print(f"删除日志文件失败 {log_file}: {e}")
        except Exception as e:
            print(f"日志文件清理失败: {e}")
    
    def _start_cleanup_thread(self):
        """启动后台清理线程"""
        def cleanup_worker():
            import time
            while True:
                try:
                    # 每24小时执行一次清理
                    time.sleep(24 * 60 * 60)  # 24小时
                    self.cleanup_old_logs()
                except Exception as e:
                    print(f"后台清理线程错误: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def get_logs(self, level=None, category=None, days=7, limit=1000):
        """获取日志记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_timestamp = cutoff_date.isoformat()
            
            query = "SELECT * FROM logs WHERE timestamp >= ?"
            params = [cutoff_timestamp]
            
            if level:
                query += " AND level = ?"
                params.append(level)
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            self.log_error(f"获取日志失败: {e}")
            return []
    
    def export_logs(self, output_path, days=7):
        """导出日志到文件（仅管理员可用）"""
        try:
            logs = self.get_logs(days=days, limit=10000)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("时间戳,级别,类别,消息,详情\n")
                for log in logs:
                    timestamp, level, category, message, details = log[1:6]
                    details_str = details.replace('\n', '\\n').replace(',', '，') if details else ""
                    f.write(f"{timestamp},{level},{category},{message},{details_str}\n")
            
            self.log_info(f"日志导出成功", {"output_path": output_path, "count": len(logs)}, "EXPORT")
            return True
        except Exception as e:
            self.log_error(f"日志导出失败: {e}", {"output_path": output_path}, "EXPORT")
            return False
    
    def close(self):
        """关闭日志管理器"""
        try:
            # 执行最后一次清理
            self.cleanup_old_logs()
            self.log_info("日志管理器关闭", category="SYSTEM")
        except Exception as e:
            print(f"日志管理器关闭时出错: {e}")


# 全局日志管理器实例
_global_log_manager = None

def get_log_manager():
    """获取全局日志管理器实例"""
    global _global_log_manager
    if _global_log_manager is None:
        _global_log_manager = DatabaseLogManager()
    return _global_log_manager

def init_log_manager(log_dir=None, enable_file_logging=False):
    """初始化全局日志管理器"""
    global _global_log_manager
    if _global_log_manager is None:
        _global_log_manager = DatabaseLogManager(log_dir=log_dir, enable_file_logging=enable_file_logging)
    return _global_log_manager

def close_log_manager():
    """关闭全局日志管理器"""
    global _global_log_manager
    if _global_log_manager:
        _global_log_manager.close()
        _global_log_manager = None


# 便捷函数
def log_info(message, details=None, category="INFO"):
    get_log_manager().log_info(message, details, category)

def log_warning(message, details=None, category="WARNING"):
    get_log_manager().log_warning(message, details, category)

def log_error(message, details=None, category="ERROR"):
    get_log_manager().log_error(message, details, category)

def log_critical(message, details=None, category="CRITICAL"):
    get_log_manager().log_critical(message, details, category)

def log_user_action(action, username=None, details=None):
    get_log_manager().log_user_action(action, username, details)

def log_system_event(event, details=None):
    get_log_manager().log_system_event(event, details)

def log_exception(exception, context=None):
    get_log_manager().log_exception(exception, context)


if __name__ == "__main__":
    # 测试代码
    log_manager = DatabaseLogManager()
    
    # 测试各种日志类型
    log_manager.log_info("应用程序启动", {"version": "1.0"})
    log_manager.log_user_action("登录", "admin", {"ip": "*************"})
    log_manager.log_warning("内存使用率较高", {"usage": "85%"})
    log_manager.log_error("文件读取失败", {"file": "test.txt", "error": "文件不存在"})
    
    try:
        1/0
    except Exception as e:
        log_manager.log_exception(e, "测试异常记录")
    
    # 获取日志
    logs = log_manager.get_logs(days=1)
    print(f"获取到 {len(logs)} 条日志记录")
    
    # 清理测试
    log_manager.cleanup_old_logs(days=0)  # 清理所有日志用于测试
    
    log_manager.close()
