WCSAXES =                    2 / Number of coordinate axes                      CRPIX1  =               1920.5 / Pixel coordinate of reference point            CRPIX2  =               1920.5 / Pixel coordinate of reference point            CDELT1  =   -0.000416666666667 / [deg] Coordinate increment at reference point  CDELT2  =    0.000416666666667 / [deg] Coordinate increment at reference point  CUNIT1  = 'deg'                / Units of coordinate increment and value        CUNIT2  = 'deg'                / Units of coordinate increment and value        CTYPE1  = 'RA---TAN'           / Right ascension, gnomonic projection           CTYPE2  = 'DEC--TAN'           / Declination, gnomonic projection               CRVAL1  =               36.661 / [deg] Coordinate value at reference point      CRVAL2  =                -4.48 / [deg] Coordinate value at reference point      LONPOLE =                  180 / [deg] Native longitude of celestial pole       LATPOLE =                -4.48 / [deg] Native latitude of celestial pole        RESTFRQ =                    0 / [Hz] Line rest frequency                       RESTWAV =                    0 / [Hz] Line rest wavelength                      EQUINOX =                 2000 / [yr] Equinox of equatorial coordinates         END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             