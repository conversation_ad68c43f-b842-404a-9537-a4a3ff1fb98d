#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import json
import os
import re
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QMessageBox, QComboBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from db_user_manager import DatabaseUserManager
from language_manager import get_text

class PasswordDialog(QDialog):
    """密码管理对话框"""
    password_changed = pyqtSignal(str, str)  # 用户名, 新密码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(get_text("password_management"))
        self.setFixedSize(500, 500)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        
        self.user_manager = DatabaseUserManager()
        self.setup_ui()
        self.setup_connections()
        self.load_users()
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(40)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel(get_text("password_management"))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        line.setStyleSheet("background-color: #bdc3c7;")
        layout.addWidget(line)
        
        # 用户名选择
        username_layout = QHBoxLayout()
        username_label = QLabel(get_text("username_colon"))
        username_label.setFixedWidth(80)
        username_label.setStyleSheet("font-size: 14px; color: #34495e;")
        
        self.username_combo = QComboBox()
        self.username_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
                background: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_combo)
        layout.addLayout(username_layout)
        
        # 新密码输入
        new_password_layout = QHBoxLayout()
        new_password_label = QLabel(get_text("new_password"))
        new_password_label.setFixedWidth(80)
        new_password_label.setStyleSheet("font-size: 14px; color: #34495e;")
        
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setPlaceholderText(get_text("enter_new_password"))
        self.new_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.new_password_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # 密码显示切换按钮
        self.password_toggle_btn = QPushButton()
        self.password_toggle_btn.setFixedSize(30, 30)
        self.password_toggle_btn.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
                padding: 0;
            }
            QPushButton:hover {
                background-color: #ecf0f1;
                border-radius: 15px;
            }
        """)
        self.update_password_toggle_icon()
        
        new_password_layout.addWidget(new_password_label)
        new_password_layout.addWidget(self.new_password_edit)
        new_password_layout.addWidget(self.password_toggle_btn)
        layout.addLayout(new_password_layout)
        
        # 新增：在新密码输入框下方添加注释
        self.password_hint = QLabel(f"<font color='gray'>{get_text('password_requirements')}</font>")
        self.password_hint.setStyleSheet("font-size:10px;")
        # 假设你用QFormLayout
        # self.form_layout.addRow("新密码：", self.password_edit)
        # self.form_layout.addRow("", self.password_hint)
        layout.addWidget(self.password_hint)
        
        # 确认密码输入
        confirm_password_layout = QHBoxLayout()
        confirm_password_label = QLabel(get_text("confirm_password"))
        confirm_password_label.setFixedWidth(80)
        confirm_password_label.setStyleSheet("font-size: 14px; color: #34495e;")
        
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText(get_text("enter_confirm_password"))
        self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_password_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        confirm_password_layout.addWidget(confirm_password_label)
        confirm_password_layout.addWidget(self.confirm_password_edit)
        layout.addLayout(confirm_password_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.change_btn = QPushButton(get_text("change_password"))
        self.change_btn.setFixedHeight(35)
        self.change_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        self.cancel_btn = QPushButton(get_text("cancel"))
        self.cancel_btn.setFixedHeight(35)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(self.change_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 设置回车键确认
        self.confirm_password_edit.returnPressed.connect(self.change_btn.click)
    
    def setup_connections(self):
        """设置信号连接"""
        self.password_toggle_btn.clicked.connect(self.toggle_password_visibility)
        self.change_btn.clicked.connect(self.handle_change_password)
        self.cancel_btn.clicked.connect(self.reject)
    
    def toggle_password_visibility(self):
        """切换密码显示/隐藏"""
        if self.new_password_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.new_password_edit.setEchoMode(QLineEdit.EchoMode.Normal)
            self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.new_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.update_password_toggle_icon()
    
    def update_password_toggle_icon(self):
        """更新密码切换按钮图标"""
        if self.new_password_edit.echoMode() == QLineEdit.EchoMode.Password:
            # 显示眼睛图标（表示可以显示密码）
            self.password_toggle_btn.setText("👁")
        else:
            # 显示斜杠眼睛图标（表示可以隐藏密码）
            self.password_toggle_btn.setText("👁‍🗨")
    
    def load_users(self):
        """加载用户列表"""
        try:
            users = self.user_manager.get_all_users()
            self.username_combo.clear()
            for username in users:
                self.username_combo.addItem(username)
        except Exception as e:
            print(f"加载用户列表失败: {e}")
    
    def hash_password(self, password):
        """对密码进行哈希加密"""
        return self.user_manager.hash_password(password)
    
    def handle_change_password(self):
        """处理密码更改"""
        username = self.username_combo.currentText()
        new_password = self.new_password_edit.text().strip()
        confirm_password = self.confirm_password_edit.text().strip()
        
        if not username:
            QMessageBox.warning(self, get_text("error"), "请选择用户名")
            return
        
        if not new_password:
            QMessageBox.warning(self, get_text("error"), "请输入新密码")
            return
        
        if not confirm_password:
            QMessageBox.warning(self, get_text("error"), "请确认新密码")
            return
        
        if new_password != confirm_password:
            QMessageBox.warning(self, get_text("error"), get_text("password_mismatch"))
            return
        
        if len(new_password) < 4:
            QMessageBox.warning(self, get_text("error"), "密码长度不能少于4位")
            return
        
        try:
            # 使用用户管理器更新密码
            if self.user_manager.update_user_password(username, new_password):
                QMessageBox.information(self, get_text("success"), get_text("password_changed_successfully"))
                self.password_changed.emit(username, new_password)
                self.accept()
            else:
                QMessageBox.warning(self, get_text("error"), "用户不存在或更新失败")
                
        except Exception as e:
            QMessageBox.critical(self, get_text("error"), get_text("password_change_failed").format(str(e))) 

    def is_valid_password(self, pwd):
        return (
            len(pwd) >= 6 and
            re.search(r'[A-Z]', pwd) and
            re.search(r'[a-z]', pwd) and
            re.search(r'[0-9]', pwd)
        )

    def accept(self):
        pwd = self.new_password_edit.text()
        if not self.is_valid_password(pwd):
            QMessageBox.warning(self, get_text("error"), get_text("invalid_password"))
            return
        super().accept() 