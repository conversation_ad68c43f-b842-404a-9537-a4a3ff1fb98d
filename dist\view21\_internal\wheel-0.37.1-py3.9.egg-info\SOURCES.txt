LICENSE.txt
MANIFEST.in
README.rst
setup.cfg
setup.py
tox.ini
docs/Makefile
docs/conf.py
docs/development.rst
docs/index.rst
docs/installing.rst
docs/make.bat
docs/news.rst
docs/quickstart.rst
docs/story.rst
docs/user_guide.rst
docs/reference/index.rst
docs/reference/wheel_convert.rst
docs/reference/wheel_pack.rst
docs/reference/wheel_unpack.rst
manpages/wheel.rst
src/wheel/__init__.py
src/wheel/__main__.py
src/wheel/bdist_wheel.py
src/wheel/macosx_libfile.py
src/wheel/metadata.py
src/wheel/pkginfo.py
src/wheel/util.py
src/wheel/wheelfile.py
src/wheel.egg-info/PKG-INFO
src/wheel.egg-info/SOURCES.txt
src/wheel.egg-info/dependency_links.txt
src/wheel.egg-info/entry_points.txt
src/wheel.egg-info/not-zip-safe
src/wheel.egg-info/requires.txt
src/wheel.egg-info/top_level.txt
src/wheel/cli/__init__.py
src/wheel/cli/convert.py
src/wheel/cli/pack.py
src/wheel/cli/unpack.py
src/wheel/vendored/__init__.py
src/wheel/vendored/vendor.txt
src/wheel/vendored/packaging/__init__.py
src/wheel/vendored/packaging/_typing.py
src/wheel/vendored/packaging/tags.py
tests/conftest.py
tests/test_bdist_wheel.py
tests/test_macosx_libfile.py
tests/test_metadata.py
tests/test_pkginfo.py
tests/test_tagopt.py
tests/test_wheelfile.py
tests/cli/eggnames.txt
tests/cli/test_convert.py
tests/cli/test_pack.py
tests/cli/test_unpack.py
tests/testdata/test-1.0-py2.py3-none-any.whl
tests/testdata/abi3extension.dist/extension.c
tests/testdata/abi3extension.dist/setup.py
tests/testdata/commasinfilenames.dist/setup.py
tests/testdata/commasinfilenames.dist/mypackage/__init__.py
tests/testdata/commasinfilenames.dist/mypackage/data/1,2,3.txt
tests/testdata/commasinfilenames.dist/mypackage/data/__init__.py
tests/testdata/commasinfilenames.dist/testrepo-0.1.0/mypackage/__init__.py
tests/testdata/complex-dist/setup.py
tests/testdata/complex-dist/complexdist/__init__.py
tests/testdata/extension.dist/extension.c
tests/testdata/extension.dist/setup.py
tests/testdata/headers.dist/header.h
tests/testdata/headers.dist/headersdist.py
tests/testdata/headers.dist/setup.py
tests/testdata/macosx_minimal_system_version/test_lib.c
tests/testdata/simple.dist/setup.py
tests/testdata/simple.dist/simpledist/__init__.py
tests/testdata/unicode.dist/setup.py
tests/testdata/unicode.dist/unicodedist/__init__.py
tests/testdata/unicode.dist/unicodedist/åäö_日本語.py