#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import pandas as pd
import atexit
import traceback
import glob
from datetime import datetime, timedelta
from language_manager import get_text, set_language

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QFrame, QPushButton, QSpacerItem, QSizePolicy, QMessageBox
)
from PyQt6.QtCore import Qt, QPoint
from menu_bar import MenuBar
from sidebar import Sidebar
from editor import Editor
from status_bar import StatusBar
from login_dialog import LoginDialog
from password_dialog import PasswordDialog

# 导入日志管理器
from log_manager import init_log_manager, close_log_manager, log_info, log_error, log_user_action, log_system_event, log_exception, log_critical
from language_manager import set_language

import numpy as np
import matplotlib
from matplotlib import font_manager
import matplotlib.pyplot as plt


matplotlib.use('TkAgg')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'Noto Sans CJK', 'Noto Sans CJK JP', 'Noto Sans CJK TC', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False




class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 初始化日志管理器
        try:
            log_dir = os.path.join(os.path.dirname(__file__), "logs")
            self.log_manager = init_log_manager(log_dir)
            log_system_event("应用程序启动", {"version": "1.0", "pid": os.getpid()})
        except Exception as e:
            print(f"日志管理器初始化失败: {e}")
            self.log_manager = None
        
        # 注册程序退出时的清理函数
        atexit.register(self._cleanup_on_exit)
        
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Window)
        self.setMinimumSize(1200, 800)

        self.drag_pos = None  # 拖动位置初始化
        
        # 用户登录相关
        from db_user_manager import DatabaseUserManager
        self.user_manager = DatabaseUserManager()
        self.login_dialog = None
        
        # 设置全局样式（略，保留你原来的样式不变）

        self.setStyleSheet("""
    /* 全局样式设置 */
    QWidget {
        font-family: 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', sans-serif;
    }
    QMenuBar {
    background-color: transparent;
    border: none;
    padding: 0;
    height: 20px;  /* 和你的title_bar一致 */
}

    QMenuBar::item {
        background-color: transparent;
        border: none;
        padding: 4px 4px;
        color: #4a5568;
}

    QMenuBar::item:selected {
        background-color: #e2e8f0; /* 选中时背景色 */
}

    /* 工具提示样式设置 */
    QToolTip {
        background-color: #2d3748;
        color: #ffffff;
        border: 1px solid #4a5568;
        border-radius: 4px;
        padding: 6px 8px;
        font-size: 12px;
        font-family: 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', sans-serif;
    }

        """)

        # 创建主窗口中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # === 顶部自定义标题栏 ===
        title_bar = QWidget()
        title_bar.setFixedHeight(36)
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0)

        # 菜单栏（自定义）
        self.menu_bar = MenuBar(self)
        #self.menu_bar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        title_layout.addWidget(self.menu_bar)

        # 右侧窗口控制按钮
        #title_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        title_layout.insertStretch(1)
        
        minimize_btn = QPushButton("—")
        maximize_btn = QPushButton("□")
        close_btn = QPushButton("×")

        for btn in [minimize_btn, maximize_btn, close_btn]:
            btn.setFixedSize(36, 28)
            btn.setStyleSheet("""
                QPushButton {
                    border: none;
                    background: transparent;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #e2e8f0;
                }
            """)

        minimize_btn.clicked.connect(self.showMinimized)
        maximize_btn.clicked.connect(lambda: self.showNormal() if self.isMaximized() else self.showMaximized())
        close_btn.clicked.connect(self.close)

        title_layout.addWidget(minimize_btn)
        title_layout.addWidget(maximize_btn)
        title_layout.addWidget(close_btn)

        # 添加标题栏到主布局顶部
        main_layout.addWidget(title_bar)

        # === 主内容区（侧边栏+分割线+编辑器） ===
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        self.editor = Editor()
        self.sidebar = Sidebar()

        content_layout.addWidget(self.sidebar)
        line = QFrame()
        line.setFrameShape(QFrame.Shape.VLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        content_layout.addWidget(line)
        content_layout.addWidget(self.editor)
        content_layout.setStretch(0, 0)
        content_layout.setStretch(2, 1)

        main_layout.addWidget(content_widget)

        # === 底部状态栏 ===
        self.status_bar = StatusBar()
        main_layout.addWidget(self.status_bar)

        # 让编辑器能够访问状态栏
        self.editor.status_bar = self.status_bar

        self.setup_connections()

        # 初始化软件设置
        self.initialize_software_settings()

        # 示例初始化参数
        params = {
            "filename": "未加载",
            "laser_power": 0,
            "integration_time": 0,
            "current_measurement": 1,
            "total_measurements": 1
        }
        self.editor.update_parameter_display(params)

        # 测点规划回调已删除
        
        # 先显示登录对话框
        self.show_login_dialog()
    
    def cleanup_old_files(self, days_to_keep=7):
        """清理指定天数之前的save和config文件"""
        try:
            log_system_event("开始清理旧文件", {"days_to_keep": days_to_keep})
            
            # 计算截止时间
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            
            # 获取项目根目录（UI2的上级目录）
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            
            # 需要清理的目录
            directories_to_clean = [
                os.path.join(project_root, "save"),
                os.path.join(project_root, "config"),
                os.path.join(current_dir, "logs")  # 也清理日志文件
            ]
            
            total_cleaned = 0
            total_size_cleaned = 0
            
            for directory in directories_to_clean:
                if not os.path.exists(directory):
                    continue
                    
                log_info(f"正在清理目录: {directory}")
                cleaned_count, cleaned_size = self._cleanup_directory(directory, cutoff_time)
                total_cleaned += cleaned_count
                total_size_cleaned += cleaned_size
                
            if total_cleaned > 0:
                log_system_event("文件清理完成", {
                    "cleaned_files": total_cleaned,
                    "cleaned_size_mb": round(total_size_cleaned / (1024 * 1024), 2),
                    "cutoff_date": cutoff_time.strftime("%Y-%m-%d %H:%M:%S")
                })
                print(f"已清理 {total_cleaned} 个旧文件，释放空间 {total_size_cleaned / (1024 * 1024):.2f} MB")
            else:
                log_info("没有需要清理的旧文件")
                
        except Exception as e:
            log_exception(e, "清理旧文件时出错")
            print(f"清理旧文件时出错: {e}")
    
    def _cleanup_directory(self, directory, cutoff_time):
        """清理指定目录中的旧文件"""
        cleaned_count = 0
        cleaned_size = 0
        
        try:
            # 定义需要清理的文件模式
            file_patterns = []
            
            if directory.endswith("save"):
                file_patterns = ["raman_save_*.csv"]
            elif directory.endswith("config"):
                file_patterns = ["plate_config_*.csv"]
            elif directory.endswith("logs"):
                file_patterns = ["*.log", "*.log.*"]
            else:
                # 其他目录，清理所有文件
                file_patterns = ["*"]
            
            for pattern in file_patterns:
                file_pattern = os.path.join(directory, pattern)
                files = glob.glob(file_pattern)
                
                for file_path in files:
                    try:
                        # 获取文件修改时间
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        
                        # 如果文件比截止时间旧，删除它
                        if file_mtime < cutoff_time:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_count += 1
                            cleaned_size += file_size
                            log_info(f"已删除旧文件: {file_path}")
                            
                    except Exception as e:
                        log_error(f"删除文件失败: {file_path}, 错误: {e}")
                        
        except Exception as e:
            log_error(f"清理目录失败: {directory}, 错误: {e}")
            
        return cleaned_count, cleaned_size
    
    def initialize_software_settings(self):
        """初始化软件设置"""
        try:
            # 从菜单栏加载软件设置
            settings = self.menu_bar.load_software_settings()
            
            # 设置编辑器的工作目录（只在没有设置或为默认值时设置）
            current_work_dir = getattr(self.editor, 'work_directory', None)
            if hasattr(self.menu_bar, 'work_dir') and self.menu_bar.work_dir:
                # 如果菜单栏有设置工作目录，使用菜单栏的设置
                self.editor.work_directory = self.menu_bar.work_dir
                print(f"[调试] 使用菜单栏工作目录: {self.menu_bar.work_dir}")
            elif not current_work_dir or current_work_dir == './exported_data':
                # 只在没有设置或为默认值时才使用默认工作目录
                self.editor.work_directory = './exported_data'
                print(f"[调试] 使用默认工作目录: ./exported_data")
            else:
                # 保持当前已设置的工作目录
                print(f"[调试] 保持当前工作目录: {current_work_dir}")
            
            # 设置编辑器的输出格式
            self.editor.output_format = settings.get("output_format", "txt")
            
            # 设置其他软件设置
            self.editor.max_curves = settings.get("max_curves", 100)
            self.editor.default_x_axis = settings.get("x_axis", "拉曼位移")
            
            # 应用坐标系设置到编辑器
            if hasattr(self.editor, 'update_coordinate_system_from_settings'):
                self.editor.update_coordinate_system_from_settings(settings)
            
            print(f"软件设置已初始化: 输出格式={self.editor.output_format}, 工作目录={self.editor.work_directory},最大曲线数={self.editor.max_curves},默认坐标={self.editor.default_x_axis}")
            
        except Exception as e:
            print(f"初始化软件设置失败: {e}")
            # 设置默认值（只在没有设置时设置）
            if not hasattr(self.editor, 'work_directory'):
                self.editor.work_directory = './exported_data'
            if not hasattr(self.editor, 'output_format'):
                self.editor.output_format = 'txt'
            if not hasattr(self.editor, 'max_curves'):
                self.editor.max_curves = 100
            if not hasattr(self.editor, 'default_x_axis'):
                self.editor.default_x_axis = "拉曼位移"

    def show_login_dialog(self):
        """显示登录对话框"""
        try:
            log_system_event("显示登录对话框")
            self.login_dialog = LoginDialog(self)
            self.login_dialog.login_successful.connect(self.on_login_successful)
            result = self.login_dialog.exec()
            
            # 如果登录对话框被关闭且未登录成功，则退出程序
            if result != self.login_dialog.DialogCode.Accepted:
                log_system_event("用户取消登录，程序退出")
                import sys
                sys.exit(0)
        except Exception as e:
            log_exception(e, "显示登录对话框时出错")
    
    def on_login_successful(self, username, role):
        """登录成功处理"""
        try:
            # 使用用户管理器登录
            self.user_manager.login(username, role)
            
            # 记录登录日志
            log_user_action("用户登录成功", username, {
                "role": role,
                "login_time": str(datetime.now()),
                "ip": "localhost"  # 可以添加实际IP获取
            })
            
            # 执行文件清理（在后台进行）
            self.cleanup_old_files(days_to_keep=7)
            
            # 更新菜单栏和侧边栏的权限
            self.update_permissions()
            
            # 重新初始化软件设置（确保设置正确应用）
            self.initialize_software_settings()
            
            # 显示主窗口
            self.show()
            
            # 更新状态栏显示用户信息
            self.status_bar.set_user_info(username, role)
            
            log_system_event("主窗口显示完成", {"username": username, "role": role})
            print(f"用户 {username} ({role}) 登录成功")
        except Exception as e:
            log_exception(e, f"用户登录处理时出错: {username}")

    def update_permissions(self):
        """根据用户角色更新权限"""
        is_admin = self.user_manager.is_admin()
        
        # 更新菜单栏权限
        if hasattr(self.menu_bar, 'update_service_action'):
            self.menu_bar.update_service_action.setVisible(is_admin)
        if hasattr(self.menu_bar, 'software_management_action'):
            self.menu_bar.software_management_action.setVisible(is_admin)
        if hasattr(self.menu_bar, 'import_database_action'):
            self.menu_bar.import_database_action.setVisible(is_admin)
        if hasattr(self.menu_bar, 'auxiliary_test_action'):
            self.menu_bar.auxiliary_test_action.setVisible(is_admin)
        # if hasattr(self.menu_bar, 'password_management_action'):
        #     self.menu_bar.password_management_action.setVisible(is_admin)
        if hasattr(self.menu_bar, 'log_viewer_action'):
            self.menu_bar.log_viewer_action.setVisible(is_admin)

        # 更新侧边栏权限
        if hasattr(self.sidebar, 'update_service_action'):
            self.sidebar.update_service_action.setVisible(is_admin)
        if hasattr(self.sidebar, 'software_management_action'):
            self.sidebar.software_management_action.setVisible(is_admin)
        if hasattr(self.sidebar, 'log_viewer_action'):
            self.sidebar.log_viewer_action.setVisible(is_admin)

    def setup_connections(self):
        self.sidebar.data_management_signal.connect(self.editor.handle_data_management)
        self.sidebar.data_management_file_signal.connect(self.editor.load_data_files)
        self.sidebar.baseline_calibration_signal.connect(self.editor.handle_baseline_calibration)
        self.sidebar.data_smoothing_signal.connect(self.editor.handle_data_smoothing)
        self.sidebar.normalization_signal.connect(self.editor.handle_normalization)
        self.sidebar.peak_finding_signal.connect(self.editor.handle_peak_finding)
        self.sidebar.restoration_signal.connect(self.editor.handle_restoration)
        self.sidebar.clear_spectrum_signal.connect(self.editor.handle_clear_spectrum)
        

        self.menu_bar.language_changed_signal.connect(self.handle_language_change)
        self.menu_bar.title_changed.connect(self.setWindowTitle)
        self.menu_bar.open_files_signal.connect(self.editor.load_data_files)
        self.menu_bar.save_files_signal.connect(self.handle_save_files)
        self.menu_bar.language_changed_signal.connect(self.status_bar.update_language)
        self.menu_bar.print_report_signal.connect(self.handle_print_report)
        
        # 侧边栏语言切换连接到菜单栏语言切换
        self.sidebar.language_changed.connect(self.menu_bar.handle_language_change)
        # 测点规划功能已删除

        # 通讯状态联动
        self.sidebar.communication_status_changed.connect(self.handle_communication_status)



    def handle_language_change(self, language):
        set_language(language)
        self.menu_bar.update_language(language)
        self.sidebar.update_language(language)
        self.editor.update_language(language)
        self.status_bar.update_language(language)

    def handle_communication_status(self, laser, spectrometer):
        if laser and spectrometer:
            self.status_bar.set_online()
        else:
            self.status_bar.set_offline()

    # === 拖动窗口 ===
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_pos = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_pos:
            self.move(self.pos() + event.globalPosition().toPoint() - self.drag_pos)
            self.drag_pos = event.globalPosition().toPoint()

    # def load_data_from_file(self, file_path):
    #     """从文件加载数据"""
    #     try:
    #         log_user_action("开始加载文件", self.user_manager.current_user if self.user_manager else None, 
    #                       {"file_path": file_path})
            
    #         # 使用新的文件读取器
    #         try:
    #             from spectrum_file_reader import SpectrumFileReader
    #         except ImportError:
    #             import sys
    #             import os
    #             sys.path.append(os.path.dirname(__file__))
    #             from spectrum_file_reader import SpectrumFileReader
            
    #         reader = SpectrumFileReader()
    #         file_info, spectrum_data = reader.read_file(file_path)
            
    #         # 保存文件信息
    #         self.file_info = file_info
            
    #         # 提取光谱数据
    #         self.wavelength = spectrum_data[:, 0]  # 拉曼位移
    #         self.intensity = spectrum_data[:, 1]   # 强度
    #         self.intensity_original = self.intensity.copy()
    #         self.intensity_raw = self.intensity.copy()
            
    #         # 初始化历史数据
    #         self.intensity_history = [self.intensity.copy()]
    #         self.timestamp_history = [0]
            
    #         # 记录成功日志
    #         log_user_action("文件加载成功", self.user_manager.current_user if self.user_manager else None, {
    #             "file_path": file_path,
    #             "file_size": os.path.getsize(file_path),
    #             "data_points": len(spectrum_data),
    #             "wavelength_range": f"{np.min(self.wavelength):.2f} - {np.max(self.wavelength):.2f}",
    #             "intensity_range": f"{np.min(self.intensity):.2f} - {np.max(self.intensity):.2f}"
    #         })
            
    #         # 显示文件信息
    #         print("文件信息:")
    #         print(reader.get_file_info_display())
    #         print(f"光谱数据形状: {spectrum_data.shape}")
    #         print(f"拉曼位移范围: {np.min(self.wavelength):.2f} - {np.max(self.wavelength):.2f}")
    #         print(f"强度范围: {np.min(self.intensity):.2f} - {np.max(self.intensity):.2f}")
            
    #         # 更新显示
    #         self.update_plot()
    #         print('拉曼光谱数据加载完成')
    #         return True
            
    #     except Exception as e:
    #         log_error(f'文件读取失败: {e}', {"file_path": file_path})
    #         print(f'文件读取失败: {e}')
    #         QMessageBox.critical(self, "读取失败", f"文件读取失败: {e}")
    #         return False



    def handle_save_files(self):
        """处理保存文件"""
        try:
            log_user_action("打开保存文件对话框", self.user_manager.current_user if self.user_manager else None)
            
            # 导入保存文件对话框
            from save_files_dialog import SaveFilesDialog
            
            # 获取当前加载的文件信息
            available_files = self.editor.get_checked_files_info()
            
            if not available_files:
                QMessageBox.warning(self, get_text("warning"), get_text("no_files_to_save"))
                return
            
            # 创建并显示保存对话框
            dialog = SaveFilesDialog(available_files, self)
            result = dialog.exec()
            
            if result:
                log_user_action("文件保存完成", self.user_manager.current_user if self.user_manager else None, {
                    "files_count": len(available_files)
                })
            
        except ImportError as e:
            QMessageBox.critical(self, get_text("error"), f"无法导入保存文件对话框模块: {str(e)}")
            log_error(f"无法导入保存文件对话框模块: {str(e)}")
        except Exception as e:
            log_exception(e, "打开保存文件对话框时出错")
            QMessageBox.critical(self, get_text("error"), f"打开保存文件对话框时出错：{str(e)}")

    def handle_print_report(self):
        """处理打印报告"""
        try:
            log_user_action("开始生成报告", self.user_manager.current_user if self.user_manager else None)
            
            # import os
            try:
            #     # 导入报告对话框 - 使用相对导入
            #     current_dir = os.path.dirname(os.path.abspath(__file__))
            #     if current_dir not in sys.path:
            #         sys.path.insert(0, current_dir)
                
                from report_dialog import ReportDialog
                
                # 获取勾选的文件信息
                files_info = self.editor.get_checked_files_info()
                
                # 捕获光谱图像
                import tempfile
                temp_dir = tempfile.gettempdir()
                spectrum_image_path = os.path.join(temp_dir, "spectrum_report.png")
                
                if self.editor.capture_spectrum_image(spectrum_image_path):
                    # 创建并显示报告对话框
                    dialog = ReportDialog(self, files_info, spectrum_image_path)
                    result = dialog.exec()
                    
                    # 记录报告生成结果
                    log_user_action("报告生成完成", self.user_manager.current_user if self.user_manager else None, {
                        "files_count": len(files_info),
                        "dialog_result": "accepted" if result else "rejected"
                    })
                    
                    # 清理临时文件
                    try:
                        if os.path.exists(spectrum_image_path):
                            os.remove(spectrum_image_path)
                    except:
                        pass
                else:
                    QMessageBox.critical(self, get_text("error"), "无法捕获光谱图像：无数据显示")
                    log_error("无法捕获光谱图像", {"reason": "无数据显示"})
                    
            except ImportError as e:
                QMessageBox.critical(self, get_text("error"), f"无法导入报告对话框模块: {str(e)}")
                log_error(f"无法导入报告对话框模块: {str(e)}")
        except Exception as e:
            log_exception(e, "生成报告时出错")
            QMessageBox.critical(self, get_text("error"), f"生成报告时出错：{str(e)}")

# def read_csv_auto_encoding(file_path):
#     for enc in ['utf-8', 'gbk', 'gb2312', 'latin1']:
#         try:
#             return pd.read_csv(file_path, encoding=enc)
#         except Exception:
#             continue
#     raise ValueError(f"无法识别文件编码: {file_path}")

        # 添加清理函数和关闭事件处理到MainWindow类
    def _cleanup_on_exit(self):
        """程序退出时的清理工作"""
        try:
            if self.user_manager and hasattr(self.user_manager, 'current_user'):
                log_user_action("用户退出登录", self.user_manager.current_user)
            
            log_system_event("应用程序关闭", {"exit_time": str(datetime.now())})
            close_log_manager()
        except Exception as e:
            print(f"程序退出清理时出错: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            log_system_event("主窗口关闭事件触发")
            
            # 完全关闭光谱仪（如果存在）
            if hasattr(self, 'editor') and self.editor:
                if hasattr(self.editor, 'integrated_measurement_logic') and self.editor.integrated_measurement_logic:
                    try:
                        self.editor.integrated_measurement_logic.close_spectrometer_completely()
                    except Exception as e:
                        print(f"关闭光谱仪时出错: {e}")
            
            # 确保清理工作完成
            self._cleanup_on_exit()
            
            # 接受关闭事件
            event.accept()
        except Exception as e:
            print(f"窗口关闭事件处理出错: {e}")
            event.accept()


def main():
    app = QApplication(sys.argv)
    
    try:
        window = MainWindow()
        
        # 设置异常处理
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            # 记录未处理的异常
            log_critical(f"未处理的异常: {exc_type.__name__}", {
                "exception_type": exc_type.__name__,
                "exception_message": str(exc_value),
                "traceback": ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            })
            
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
        
        sys.excepthook = handle_exception
        
        exit_code = app.exec()
        log_system_event("应用程序正常退出", {"exit_code": exit_code})
        sys.exit(exit_code)
        
    except Exception as e:
        log_exception(e, "应用程序启动时出错")
        sys.exit(1)



# # 将方法添加到MainWindow类
# MainWindow._cleanup_on_exit = _cleanup_on_exit
# MainWindow.closeEvent = closeEvent

if __name__ == '__main__':
    main()


