#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备监控配置对话框
允许用户配置设备监控的各种参数
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, 
    QDoubleSpinBox, QCheckBox, QPushButton, QGroupBox, QFormLayout,
    QMessageBox, QSlider
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

class DeviceMonitorDialog(QDialog):
    """设备监控配置对话框"""
    
    # 定义信号
    settings_changed = pyqtSignal(dict)  # 设置变化信号
    
    def __init__(self, parent=None, current_settings=None):
        super().__init__(parent)
        self.current_settings = current_settings or {}
        self.init_ui()
        self.load_current_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("设备监控配置")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 监控设置组
        monitoring_group = QGroupBox("监控设置")
        monitoring_layout = QFormLayout(monitoring_group)
        
        # 检查间隔设置
        self.check_interval_spin = QDoubleSpinBox()
        self.check_interval_spin.setRange(0.5, 60.0)
        self.check_interval_spin.setValue(2.0)
        self.check_interval_spin.setSuffix(" 秒")
        self.check_interval_spin.setToolTip("设备状态检查的时间间隔")
        monitoring_layout.addRow("检查间隔:", self.check_interval_spin)
        
        # 自动重连开关
        self.auto_reconnect_check = QCheckBox("启用自动重连")
        self.auto_reconnect_check.setChecked(True)
        self.auto_reconnect_check.setToolTip("当设备断开时自动尝试重连")
        monitoring_layout.addRow("", self.auto_reconnect_check)
        
        # 重连设置组
        reconnect_group = QGroupBox("重连设置")
        reconnect_layout = QFormLayout(reconnect_group)
        
        # 最大重连次数
        self.max_attempts_spin = QSpinBox()
        self.max_attempts_spin.setRange(1, 10)
        self.max_attempts_spin.setValue(3)
        self.max_attempts_spin.setToolTip("设备断开后的最大重连尝试次数")
        reconnect_layout.addRow("最大重连次数:", self.max_attempts_spin)
        
        # 重连延迟
        self.reconnect_delay_spin = QDoubleSpinBox()
        self.reconnect_delay_spin.setRange(1.0, 30.0)
        self.reconnect_delay_spin.setValue(5.0)
        self.reconnect_delay_spin.setSuffix(" 秒")
        self.reconnect_delay_spin.setToolTip("重连尝试之间的延迟时间")
        reconnect_layout.addRow("重连延迟:", self.reconnect_delay_spin)
        
        # 设备特定设置组
        device_group = QGroupBox("设备特定设置")
        device_layout = QFormLayout(device_group)
        
        # 光谱仪设置
        self.spectrometer_check = QCheckBox("监控光谱仪")
        self.spectrometer_check.setChecked(True)
        device_layout.addRow("", self.spectrometer_check)
        
        # 激光器设置
        self.laser_check = QCheckBox("监控激光器")
        self.laser_check.setChecked(True)
        device_layout.addRow("", self.laser_check)
        
        # 电源设置
        self.power_check = QCheckBox("监控电源")
        self.power_check.setChecked(True)
        device_layout.addRow("", self.power_check)
        
        # 通知设置组
        notification_group = QGroupBox("通知设置")
        notification_layout = QFormLayout(notification_group)
        
        # 断开通知
        self.disconnect_notify_check = QCheckBox("设备断开时显示通知")
        self.disconnect_notify_check.setChecked(True)
        notification_layout.addRow("", self.disconnect_notify_check)
        
        # 重连通知
        self.reconnect_notify_check = QCheckBox("重连成功时显示通知")
        self.reconnect_notify_check.setChecked(True)
        notification_layout.addRow("", self.reconnect_notify_check)
        
        # 日志记录
        self.log_events_check = QCheckBox("记录设备事件到日志")
        self.log_events_check.setChecked(True)
        notification_layout.addRow("", self.log_events_check)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置为默认值")
        reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 应用按钮
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(self.apply_settings)
        apply_btn.setDefault(True)
        button_layout.addWidget(apply_btn)
        
        # 添加所有组件到主布局
        layout.addWidget(monitoring_group)
        layout.addWidget(reconnect_group)
        layout.addWidget(device_group)
        layout.addWidget(notification_group)
        layout.addLayout(button_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: #f8f9fa;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
            QPushButton:default {
                background-color: #007bff;
                color: white;
                border-color: #0056b3;
            }
            QPushButton:default:hover {
                background-color: #0056b3;
            }
        """)
    
    def load_current_settings(self):
        """加载当前设置"""
        if not self.current_settings:
            return
            
        # 加载监控设置
        if 'check_interval' in self.current_settings:
            self.check_interval_spin.setValue(self.current_settings['check_interval'])
        
        if 'auto_reconnect_enabled' in self.current_settings:
            self.auto_reconnect_check.setChecked(self.current_settings['auto_reconnect_enabled'])
        
        # 加载重连设置
        if 'max_reconnect_attempts' in self.current_settings:
            self.max_attempts_spin.setValue(self.current_settings['max_reconnect_attempts'])
        
        if 'reconnect_delay' in self.current_settings:
            self.reconnect_delay_spin.setValue(self.current_settings['reconnect_delay'])
        
        # 加载设备设置
        if 'monitor_spectrometer' in self.current_settings:
            self.spectrometer_check.setChecked(self.current_settings['monitor_spectrometer'])
        
        if 'monitor_laser' in self.current_settings:
            self.laser_check.setChecked(self.current_settings['monitor_laser'])
        
        if 'monitor_power' in self.current_settings:
            self.power_check.setChecked(self.current_settings['monitor_power'])
        
        # 加载通知设置
        if 'notify_disconnect' in self.current_settings:
            self.disconnect_notify_check.setChecked(self.current_settings['notify_disconnect'])
        
        if 'notify_reconnect' in self.current_settings:
            self.reconnect_notify_check.setChecked(self.current_settings['notify_reconnect'])
        
        if 'log_events' in self.current_settings:
            self.log_events_check.setChecked(self.current_settings['log_events'])
    
    def get_settings(self):
        """获取当前设置"""
        return {
            'check_interval': self.check_interval_spin.value(),
            'auto_reconnect_enabled': self.auto_reconnect_check.isChecked(),
            'max_reconnect_attempts': self.max_attempts_spin.value(),
            'reconnect_delay': self.reconnect_delay_spin.value(),
            'monitor_spectrometer': self.spectrometer_check.isChecked(),
            'monitor_laser': self.laser_check.isChecked(),
            'monitor_power': self.power_check.isChecked(),
            'notify_disconnect': self.disconnect_notify_check.isChecked(),
            'notify_reconnect': self.reconnect_notify_check.isChecked(),
            'log_events': self.log_events_check.isChecked()
        }
    
    def reset_to_defaults(self):
        """重置为默认值"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要将所有设置重置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 重置为默认值
            self.check_interval_spin.setValue(2.0)
            self.auto_reconnect_check.setChecked(True)
            self.max_attempts_spin.setValue(3)
            self.reconnect_delay_spin.setValue(5.0)
            self.spectrometer_check.setChecked(True)
            self.laser_check.setChecked(True)
            self.power_check.setChecked(True)
            self.disconnect_notify_check.setChecked(True)
            self.reconnect_notify_check.setChecked(True)
            self.log_events_check.setChecked(True)
            
            QMessageBox.information(self, "重置完成", "设置已重置为默认值")
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 获取当前设置
            settings = self.get_settings()
            
            # 验证设置
            if not self._validate_settings(settings):
                return
            
            # 发送设置变化信号
            self.settings_changed.emit(settings)
            
            # 关闭对话框
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用设置时出错: {str(e)}")
    
    def _validate_settings(self, settings):
        """验证设置的有效性"""
        # 检查间隔不能太短
        if settings['check_interval'] < 0.5:
            QMessageBox.warning(self, "设置验证", "检查间隔不能少于0.5秒")
            return False
        
        # 重连延迟不能太短
        if settings['reconnect_delay'] < 1.0:
            QMessageBox.warning(self, "设置验证", "重连延迟不能少于1秒")
            return False
        
        # 至少需要监控一个设备
        if not any([settings['monitor_spectrometer'], 
                   settings['monitor_laser'], 
                   settings['monitor_power']]):
            QMessageBox.warning(self, "设置验证", "至少需要监控一个设备")
            return False
        
        return True
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 检查是否有未保存的更改
        current_settings = self.get_settings()
        if current_settings != self.current_settings:
            reply = QMessageBox.question(
                self,
                "确认关闭",
                "设置已更改但未保存，确定要关闭吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
