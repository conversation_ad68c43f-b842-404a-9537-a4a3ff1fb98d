WCSAXES =                    2 / Number of coordinate axes
CRPIX1  =                  1.0 / Pixel coordinate of reference point
CRPIX2  =                 99.0 / Pixel coordinate of reference point
CDELT1  =     0.00416666666667 / [deg] Coordinate increment at reference point
CDELT2  =               1000.0 / [m s-1] Coordinate increment at reference point
CUNIT1  = 'deg'                / Units of coordinate increment and value
CUNIT2  = 'm s-1'              / Units of coordinate increment and value
CTYPE1  = 'OFFSET'             / Coordinate type code
CTYPE2  = 'VRAD'               / Radio velocity (linear)
CRVAL1  =                  0.0 / [deg] Coordinate value at reference point
CRVAL2  =              50000.0 / [m s-1] Coordinate value at reference point
LONPOLE =                  0.0 / [deg] Native longitude of celestial pole
LATPOLE =                 90.0 / [deg] Native latitude of celestial pole
RESTFRQ =         4829659400.0 / [Hz] Line rest frequency
EQUINOX =               2000.0 / [yr] Equinox of equatorial coordinates
SPECSYS = 'LSRK'               / Reference frame of spectral coordinates        