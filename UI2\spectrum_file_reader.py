#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, List

# 导入NOD文件读取相关模块
try:
    from nod_Spectrum_read import NodFileReader, Spectrum
    from nod_raman_read import parse_nod_file
    from nod_file_serializer import NodFileSerializer
    NOD_MODULES_AVAILABLE = True
except ImportError:
    NOD_MODULES_AVAILABLE = False


class SpectrumFileReader:
    """光谱文件读取器，支持CSV、TXT和NOD格式"""
    
    def __init__(self):
        self.file_info = {}
        self.spectrum_data = None
        
    def read_file(self, file_path: str) -> Tuple[Dict, np.ndarray]:
        """
        读取光谱文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[Dict, np.ndarray]: (文件信息字典, 光谱数据数组)
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.csv':
            file_info, spectrum_data = self._read_csv_file(file_path)
        elif file_ext == '.txt':
            file_info, spectrum_data = self._read_txt_file(file_path)
        elif file_ext == '.nod':
            file_info, spectrum_data = self._read_nod_file(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        # 保存文件信息到实例变量
        self.file_info = file_info
        self.spectrum_data = spectrum_data
        
        return file_info, spectrum_data
    
    def _read_csv_file(self, file_path: str) -> Tuple[Dict, np.ndarray]:
        """读取CSV格式文件"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            content = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
                    
            if content is None:
                raise ValueError("无法识别文件编码")
            
            lines = content.split('\n')
            
            # 解析文件头信息
            file_info = self._parse_csv_header(lines)
            
            # 查找数据开始位置
            data_start_line = self._find_data_start_line(lines)
            
            if data_start_line == -1:
                raise ValueError("未找到光谱数据")
            
            # 读取光谱数据
            spectrum_data = self._read_spectrum_data(lines[data_start_line:])
            
            return file_info, spectrum_data
            
        except Exception as e:
            raise Exception(f"读取CSV文件失败: {e}")
    
    def _read_txt_file(self, file_path: str) -> Tuple[Dict, np.ndarray]:
        """读取TXT格式文件"""
        try:
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            content = None
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            if content is None:
                raise ValueError("无法识别文件编码")
            lines = content.split('\n')
            file_info = self._parse_txt_header(lines)
            data_start_line = self._find_data_start_line(lines)
            if data_start_line == -1:
                raise ValueError("未找到光谱数据")
            # 使用专用方法读取光谱数据，从表头行开始处理
            # data_start_line 返回的是数据行索引，所以我们需要从前一行（表头行）开始
            header_line = max(0, data_start_line - 1)
            spectrum_data = self._read_txt_spectrum_data(lines[header_line:])
            return file_info, spectrum_data
        except Exception as e:
            raise Exception(f"读取TXT文件失败: {e}")
    
    def _read_nod_file(self, file_path: str) -> Tuple[Dict, np.ndarray]:
        """读取NOD格式文件 - 自动识别新旧格式"""
        if not NOD_MODULES_AVAILABLE:
            raise Exception("NOD文件读取模块不可用，请确保相关模块文件存在")
        
        try:
            # 首先识别NOD文件格式
            nod_format = self._detect_nod_format(file_path)
            
            if nod_format == 'new':
                return self._read_new_nod_file(file_path)
            else:
                return self._read_old_nod_file(file_path)
                
        except Exception as e:
            raise Exception(f"读取NOD文件失败: {e}")
    
    def _detect_nod_format(self, file_path: str) -> str:
        """
        检测NOD文件格式
        返回 'new' 表示Python保存的新格式，'old' 表示C#保存的旧格式
        """
        try:
            # 尝试使用pickle加载（新格式特征）
            import pickle
            with open(file_path, 'rb') as f:
                # 尝试读取前几个字节，看是否是pickle格式
                f.seek(0)
                first_bytes = f.read(10)
                
                # pickle文件通常以特定字节开头
                if first_bytes.startswith(b'\x80\x03') or first_bytes.startswith(b'\x80\x04') or first_bytes.startswith(b'\x80\x02'):
                    # 尝试完整pickle反序列化
                    f.seek(0)
                    try:
                        data = pickle.load(f)
                        # 检查是否是预期的列表格式
                        if isinstance(data, list) and len(data) > 0:
                            # 检查是否包含新格式的标识
                            if isinstance(data[0], bytes):
                                try:
                                    first_line = data[0].decode('utf-16le')
                                    if "Data Generated by Neodots" in first_line:
                                        return 'new'
                                except UnicodeDecodeError:
                                    pass
                    except:
                        pass
            
            # 如果不是pickle格式，判断为旧格式
            return 'old'
            
        except Exception:
            # 默认返回旧格式
            return 'old'
    
    def _read_new_nod_file(self, file_path: str) -> Tuple[Dict, np.ndarray]:
        """读取新格式NOD文件（Python保存）"""
        try:
            serializer = NodFileSerializer()
            nod_data = serializer.deserialize_from_nod(file_path)
            
            if not nod_data:
                raise ValueError("无法解析新格式NOD文件")
            
            # 构建文件信息字典
            file_info = {
                '积分时间': '0',
                '激光功率': '0',
                '平均次数': '0',
                '采集模式': '0',
                '采集间隔': '0',
                '像素': '0'
            }
            
            # 从反序列化数据中提取信息
            if 'gather_parameters' in nod_data:
                params = nod_data['gather_parameters']
                file_info.update({
                    '积分时间': params.get('integration_time', '0'),
                    '激光功率': params.get('laser_power', '0'),
                    '平均次数': params.get('scan_count', '0'),
                    '像素': params.get('pixel_number', '0')
                })
            
            # 提取光谱数据
            raman_shift = nod_data.get('raman_shift', [])
            intensity = nod_data.get('intensity', [])
            
            if not raman_shift or not intensity:
                raise ValueError("新格式NOD文件中未找到有效的光谱数据")
            
            # 确保数据长度一致
            min_length = min(len(raman_shift), len(intensity))
            spectrum_data = np.column_stack((raman_shift[:min_length], intensity[:min_length]))
            
            return file_info, spectrum_data
            
        except Exception as e:
            raise Exception(f"读取新格式NOD文件失败: {e}")
    
    def _read_old_nod_file(self, file_path: str) -> Tuple[Dict, np.ndarray]:
        """读取旧格式NOD文件（C#保存）- 原有的读取方法"""
        try:
            # 方法1: 使用NodFileReader类读取（获取详细元数据）
            reader = NodFileReader()
            spectrum = reader.read_file(file_path)
            
            # 方法2: 使用parse_nod_file函数读取（获取光谱数据）
            metadata_simple, raman_data = parse_nod_file(file_path)
            
            # 构建文件信息字典
            file_info = {
                '积分时间': '0',
                '激光功率': '0',
                '平均次数': '0',
                '采集模式': '0',
                '采集间隔': '0',
                '像素': '0'
            }
            
            # 如果NodFileReader成功读取，提取元数据
            if spectrum:
                try:
                    formatted_metadata = reader.get_formatted_metadata(spectrum)
                    file_info.update({
                        '积分时间': formatted_metadata.get('积分时间', '0'),
                        '激光功率': formatted_metadata.get('激光功率', '0'),
                        '平均次数': formatted_metadata.get('扫描次数', '0'),
                        '采集模式': formatted_metadata.get('采集模式', '0'),
                        '采集间隔': formatted_metadata.get('采样间隔', '0'),
                        '像素': formatted_metadata.get('像素数', '0')
                    })
                except Exception as e:
                    print(f"提取NOD元数据时出错: {e}")
            
            # 准备光谱数据
            spectrum_data = None
            
            # 优先使用parse_nod_file的数据（通常更完整）
            if raman_data and len(raman_data) > 0:
                spectrum_data = np.array(raman_data)
            
            # 如果parse_nod_file失败，尝试使用NodFileReader的数据
            elif (spectrum and spectrum.spectrum_data and 
                  spectrum.spectrum_data.raman_shift is not None and 
                  spectrum.spectrum_data.intensity is not None):
                
                raman_shift = spectrum.spectrum_data.raman_shift
                intensity = spectrum.spectrum_data.intensity
                spectrum_data = np.column_stack((raman_shift, intensity))
            
            # 如果都没有数据，抛出异常
            if spectrum_data is None or len(spectrum_data) == 0:
                raise ValueError("未找到有效的光谱数据")
            
            return file_info, spectrum_data
            
        except Exception as e:
            raise Exception(f"读取旧格式NOD文件失败: {e}")
    
    def _parse_csv_header(self, lines: List[str]) -> Dict:
        """解析CSV文件头信息，增强分隔符兼容性"""
        file_info = {
            '积分时间': '0',
            '激光功率': '0',
            '平均次数': '0',
            '采集模式': '0',
            '采集间隔': '0',
            '像素': '0'
        }
        for line in lines:
            line = line.strip()
            if not line or line.startswith('Raman Shift') or line.startswith('拉曼位移'):
                break
            # 支持多种分隔符
            for sep in ['\t', ':', '：', ';', ',', ' ']:
                if sep in line:
                    parts = line.split(sep, 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        # 映射到标准字段名
                        if '积分时间' in key:
                            file_info['积分时间'] = value
                        elif '激光功率' in key:
                            file_info['激光功率'] = value
                        elif '平均次数' in key:
                            file_info['平均次数'] = value
                        elif '采集模式' in key:
                            file_info['采集模式'] = value
                        elif '采集间隔' in key or '采样间隔' in key:
                            file_info['采集间隔'] = value
                        elif '像素' in key:
                            file_info['像素'] = value
                    break  # 找到分隔符就不再尝试其它分隔符
        return file_info
    
    def _parse_txt_header(self, lines: List[str]) -> Dict:
        """解析TXT文件头信息"""
        file_info = {
            '积分时间': '0',
            '激光功率': '0',
            '平均次数': '0',
            '采集模式': '0',
            '采集间隔': '0',
            '像素': '0'
        }
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('Raman Shift') or line.startswith('拉曼位移'):
                break
                
            # 解析键值对，支持多种分隔符
            parts = None
            if '\t' in line:
                parts = line.split('\t', 1)
            elif ';' in line:
                parts = line.split(';', 1)
            elif ':' in line:
                parts = line.split(':', 1)
            else:
                continue
                
            if len(parts) == 2:
                key = parts[0].strip()
                value = parts[1].strip()
                
                # 映射到标准字段名
                if '积分时间' in key or 'integration_time' in key.lower():
                    file_info['积分时间'] = value
                elif '激光功率' in key or 'laser_power' in key.lower():
                    file_info['激光功率'] = value
                elif '平均次数' in key or 'average' in key.lower():
                    file_info['平均次数'] = value
                elif '采集模式' in key or 'acquisition_mode' in key.lower():
                    file_info['采集模式'] = value
                elif '采集间隔' in key or '采样间隔' in key or 'sampling_interval' in key.lower():
                    file_info['采集间隔'] = value
                elif '像素' in key or 'pixel' in key or '像素个数' in key.lower():
                    file_info['像素'] = value
        
        return file_info

    
    def _parse_nod_header(self, lines: List[str]) -> Dict:
        """解析NOD文件头信息"""
        # NOD格式与TXT格式类似，使用相同的解析逻辑
        return self._parse_txt_header(lines)
    
    def _find_data_start_line(self, lines: List[str]) -> int:
        """查找数据开始的行号"""
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 检查是否包含光谱数据的列标题（支持中英文）
            if any(keyword in line for keyword in ['Raman Shift', 'Dark Subtracted', '拉曼位移', '强度', 'Intensity']):
                # 如果是表头行，下一行就是数据开始
                return i + 1  # 返回下一行（数据行）
            
            # 检查是否是纯数字行（可能是数据）
            if self._is_data_line(line):
                return i
                
        return -1
    
    def _is_data_line(self, line: str) -> bool:
        """判断是否是数据行"""
        # 尝试不同的分隔符
        for sep in ['\t', ';', ',', ' ']:
            if sep in line:
                parts = line.split(sep)
                # 检查前两个部分是否都是数字
                if len(parts) >= 2:
                    try:
                        float(parts[0].strip())
                        float(parts[1].strip())
                        return True
                    except ValueError:
                        continue
        return False
    
    def _read_spectrum_data(self, data_lines: List[str]) -> np.ndarray:
        """读取光谱数据"""
        data = []
        
        for line in data_lines:
            line = line.strip()
            if not line:
                continue
                
            # 尝试不同的分隔符
            if '\t' in line:
                parts = line.split('\t')
            elif ';' in line:
                parts = line.split(';')
            elif ',' in line:
                parts = line.split(',')
            else:
                continue
                
            if len(parts) >= 2:
                try:
                    # 查找拉曼位移和强度列
                    raman_shift = None
                    intensity = None
                    
                    for i, part in enumerate(parts):
                        part = part.strip()
                        if not part:
                            continue
                            
                        try:
                            value = float(part)
                            if raman_shift is None:
                                raman_shift = value
                            elif intensity is None:
                                intensity = value
                                break
                        except ValueError:
                            continue
                    
                    if raman_shift is not None and intensity is not None:
                        data.append([raman_shift, intensity])
                        
                except (ValueError, IndexError):
                    continue
        
        if not data:
            raise ValueError("未找到有效的光谱数据")
            
        return np.array(data)
    
    def _read_txt_spectrum_data(self, data_lines: List[str]) -> np.ndarray:
        """专门用于TXT/NOD格式，按表头自动提取Raman Shift和Dark Subtracted列，支持中英文列标题"""
        data = []
        header = None
        sep_used = None
        idx_raman = -1
        idx_intensity = -1
        
        for line in data_lines:
            line = line.strip()
            if not line:
                continue
                
            # 先找到表头
            if header is None:
                for sep in ['\t', ';', ',', ' ']:
                    if sep in line:
                        # 检查是否包含光谱数据的列标题（中英文支持）
                        if any(keyword in line for keyword in ['Raman Shift', 'Dark Subtracted', '拉曼位移', '强度', 'Intensity']):
                            header = [h.strip() for h in line.split(sep)]
                            sep_used = sep
                            
                            # 查找拉曼位移列的索引
                            for i, col in enumerate(header):
                                if 'Raman Shift' in col or '拉曼位移' in col:
                                    idx_raman = i
                                elif 'Dark Subtracted' in col or '强度' in col or 'Intensity' in col:
                                    idx_intensity = i
                            
                            # 如果找到了有效的列，则确认表头
                            if idx_raman >= 0 and idx_intensity >= 0:
                                print(f"找到表头: 拉曼位移列 {idx_raman}, 强度列 {idx_intensity}")
                                break
                            else:
                                # 重置，继续查找
                                header = None
                                idx_raman = -1
                                idx_intensity = -1
                        
                if header is not None:
                    continue  # 表头行不处理，继续到数据行
            else:
                # 处理数据行
                parts = line.split(sep_used)
                if len(parts) <= max(idx_raman, idx_intensity):
                    continue
                    
                try:
                    raman = float(parts[idx_raman])
                    intensity = float(parts[idx_intensity])
                    data.append([raman, intensity])
                except (ValueError, IndexError):
                    continue
                    
        if not data:
            raise ValueError("未找到有效的光谱数据")
            
        return np.array(data)
    
    def get_file_info_display(self) -> str:
        """获取文件信息的显示字符串"""
        info = self.file_info
        display_lines = [
            f"积分时间: {info.get('积分时间', '0')}",
            f"激光功率: {info.get('激光功率', '0')}",
            f"平均次数: {info.get('平均次数', '0')}",
            f"采集模式: {info.get('采集模式', '0')}",
            f"采集间隔: {info.get('采集间隔', '0')}",
            f"像素: {info.get('像素', '0')}"
        ]
        return '\n'.join(display_lines)


def read_spectrum_file(file_path: str) -> Tuple[Dict, np.ndarray]:
    """
    便捷函数：读取光谱文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        Tuple[Dict, np.ndarray]: (文件信息字典, 光谱数据数组)
    """
    reader = SpectrumFileReader()
    return reader.read_file(file_path)

def test_read_generated_file():
    """测试读取生成的文件功能"""
    try:
        # 创建一个测试文件内容，模拟我们生成的格式
        test_content = """Data Generated by	Neolily
File Version	4
文件名称	测试文件_1_A01
操作员	测试操作员
描述	无
扫描时间	2025-01-08 14:30:00
积分时间(ms)	1000
激光功率(mW)	80
平均次数	3
采集模式	精确采集
采样间隔	0
设备型号	测试设备
像素个数	5
设备序列号	TEST001

拉曼位移	强度(a.u.)
200.00	100.50
400.00	200.75
600.00	300.25
800.00	150.80
1000.00	180.90
"""
        
        # 保存测试文件
        test_file_path = './test_generated_file.txt'
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 读取测试文件
        reader = SpectrumFileReader()
        file_info, spectrum_data = reader.read_file(test_file_path)
        
        print("测试成功!")
        print("文件信息:", file_info)
        print("光谱数据形状:", spectrum_data.shape)
        print("前3个数据点:", spectrum_data[:3])
        
        # 清理测试文件
        import os
        os.remove(test_file_path)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    test_read_generated_file()