#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备连接监控器
监控光谱仪、激光器和电源的连接状态，并在状态栏显示，同时实现自动重连功能
"""

import time
import threading
from typing import Dict, Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from PyQt6.QtWidgets import QMessageBox
import sys
import os

# 添加spectrumeter目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'spectrumeter'))

try:
    from raman_spectrometer import RamanSpectrometer
    from laser_controller_usb import LaserController
    DEVICE_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"设备模块导入失败: {e}")
    DEVICE_MODULES_AVAILABLE = False

class DeviceMonitor(QObject):
    """设备连接监控器"""
    
    # 定义信号
    device_status_changed = pyqtSignal(str, str, str)  # device_type, status, message
    connection_attempt = pyqtSignal(str, str)  # device_type, message
    auto_reconnect_started = pyqtSignal(str)  # device_type
    auto_reconnect_completed = pyqtSignal(str, bool)  # device_type, success
    
    def __init__(self, status_bar=None):
        super().__init__()
        self.status_bar = status_bar
        
        # 设备状态
        self.device_status = {
            'spectrometer': {'connected': False, 'last_check': 0, 'error_count': 0},
            'laser': {'connected': False, 'last_check': 0, 'error_count': 0},
            'power': {'connected': False, 'last_check': 0, 'error_count': 0}
        }
        
        # 设备实例
        self.spectrometer = None
        self.laser_controller = None
        
        # 监控设置
        self.monitoring_active = False
        self.monitoring_thread = None
        self.check_interval = 2.0  # 检查间隔（秒）
        self.auto_reconnect_enabled = True
        self.max_reconnect_attempts = 3
        self.reconnect_delay = 5.0  # 重连延迟（秒）
        
        # 通知设置
        self.notify_disconnect = True
        self.notify_reconnect = True
        self.log_events = True
        
        # 设备监控开关
        self.monitor_spectrometer = True
        self.monitor_laser = True
        self.monitor_power = True
        
        # 定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_all_devices)
        
        # 初始化设备
        self._init_devices()
        
    def _init_devices(self):
        """初始化设备实例"""
        if not DEVICE_MODULES_AVAILABLE:
            print("设备模块不可用，无法初始化设备")
            return
            
        try:
            # 初始化光谱仪
            self.spectrometer = RamanSpectrometer()
            print("光谱仪实例初始化成功")
        except Exception as e:
            print(f"光谱仪初始化失败: {e}")
            self.spectrometer = None
            
        try:
            # 初始化激光控制器
            self.laser_controller = LaserController()
            print("激光控制器实例初始化成功")
        except Exception as e:
            print(f"激光控制器初始化失败: {e}")
            self.laser_controller = None
    
    def start_monitoring(self):
        """启动设备监控"""
        if self.monitoring_active:
            return
            
        self.monitoring_active = True
        
        # 启动状态检查定时器
        self.status_timer.start(int(self.check_interval * 1000))
        
        # 启动监控线程
        if not self.monitoring_thread or not self.monitoring_thread.is_alive():
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
        print("🔄 设备监控已启动")
        
    def stop_monitoring(self):
        """停止设备监控"""
        self.monitoring_active = False
        
        # 停止定时器
        self.status_timer.stop()
        
        # 等待监控线程结束
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=2.0)
            
        print("⏹️ 设备监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 检查所有设备状态
                self.check_all_devices()
                time.sleep(self.check_interval)
            except Exception as e:
                print(f"设备监控循环异常: {e}")
                time.sleep(1.0)
    
    def check_all_devices(self):
        """检查所有设备状态"""
        if self.monitor_spectrometer:
            self._check_spectrometer()
        if self.monitor_laser:
            self._check_laser()
        if self.monitor_power:
            self._check_power()
        
        # 更新状态栏显示
        self._update_status_bar()
    
    def _check_spectrometer(self):
        """检查光谱仪连接状态"""
        try:
            if not self.spectrometer:
                self._update_device_status('spectrometer', False, "设备未初始化")
                return
                
            # 尝试获取设备信息来检查连接状态
            if hasattr(self.spectrometer, 'hand') and self.spectrometer.hand:
                # 检查设备句柄是否有效
                self._update_device_status('spectrometer', True, "已连接")
            else:
                self._update_device_status('spectrometer', False, "未连接")
                
        except Exception as e:
            self._update_device_status('spectrometer', False, f"连接异常: {str(e)}")
    
    def _check_laser(self):
        """检查激光器连接状态"""
        try:
            if not self.laser_controller:
                self._update_device_status('laser', False, "设备未初始化")
                return
                
            # 检查串口连接状态
            if hasattr(self.laser_controller, 'ser') and self.laser_controller.ser:
                if self.laser_controller.ser.is_open:
                    # 尝试发送状态查询命令
                    try:
                        # 这里可以添加实际的激光器状态查询命令
                        self._update_device_status('laser', True, "已连接")
                    except Exception as e:
                        self._update_device_status('laser', False, f"通信异常: {str(e)}")
                else:
                    self._update_device_status('laser', False, "串口未打开")
            else:
                self._update_device_status('laser', False, "串口未初始化")
                
        except Exception as e:
            self._update_device_status('laser', False, f"连接异常: {str(e)}")
    
    def _check_power(self):
        """检查电源状态"""
        try:
            if not self.laser_controller:
                self._update_device_status('power', False, "设备未初始化")
                return
                
            # 检查激光器电源状态
            if hasattr(self.laser_controller, 'power_on_status'):
                if self.laser_controller.power_on_status is True:
                    self._update_device_status('power', True, "电源正常")
                elif self.laser_controller.power_on_status is False:
                    self._update_device_status('power', False, "电源关闭")
                else:
                    self._update_device_status('power', False, "电源状态未知")
            else:
                self._update_device_status('power', False, "无法检测电源状态")
                
        except Exception as e:
            self._update_device_status('power', False, f"电源检查异常: {str(e)}")
    
    def _update_device_status(self, device_type: str, connected: bool, message: str):
        """更新设备状态"""
        current_time = time.time()
        old_status = self.device_status[device_type]['connected']
        
        # 更新状态
        self.device_status[device_type].update({
            'connected': connected,
            'last_check': current_time,
            'message': message
        })
        
        # 如果状态发生变化，发送信号
        if old_status != connected:
            self.device_status_changed.emit(device_type, 
                                          'connected' if connected else 'disconnected', 
                                          message)
            
            # 如果设备断开且启用了自动重连，尝试重连
            if not connected and self.auto_reconnect_enabled:
                self._schedule_reconnect(device_type)
            
            # 记录日志（如果启用）
            if self.log_events:
                if connected:
                    print(f"✅ 设备 {device_type} 已连接: {message}")
                else:
                    print(f"❌ 设备 {device_type} 断开连接: {message}")
    
    def _schedule_reconnect(self, device_type: str):
        """安排设备重连"""
        if self.device_status[device_type]['error_count'] >= self.max_reconnect_attempts:
            print(f"设备 {device_type} 重连次数已达上限，停止自动重连")
            return
            
        # 延迟重连
        threading.Timer(self.reconnect_delay, self._attempt_reconnect, args=[device_type]).start()
        self.auto_reconnect_started.emit(device_type)
    
    def _attempt_reconnect(self, device_type: str) -> bool:
        """尝试重连设备"""
        if not self.monitoring_active:
            return False
            
        print(f"🔄 尝试重连设备: {device_type}")
        self.connection_attempt.emit(device_type, "正在尝试重连...")
        
        success = False
        try:
            if device_type == 'spectrometer':
                success = self._reconnect_spectrometer()
            elif device_type == 'laser':
                success = self._reconnect_laser()
            elif device_type == 'power':
                success = self._reconnect_power()
        except Exception as e:
            print(f"重连设备 {device_type} 失败: {e}")
            success = False
        
        # 更新重连结果
        if success:
            self.device_status[device_type]['error_count'] = 0
            print(f"✅ 设备 {device_type} 重连成功")
        else:
            self.device_status[device_type]['error_count'] += 1
            print(f"❌ 设备 {device_type} 重连失败，错误计数: {self.device_status[device_type]['error_count']}")
            
            # 如果还有重连机会，继续尝试
            if self.device_status[device_type]['error_count'] < self.max_reconnect_attempts:
                self._schedule_reconnect(device_type)
        
        self.auto_reconnect_completed.emit(device_type, success)
        return success
    
    def _reconnect_spectrometer(self) -> bool:
        """重连光谱仪"""
        try:
            if not self.spectrometer:
                return False
                
            # 尝试重新连接
            result = self.spectrometer.connect_device()
            return result == "yes"
        except Exception as e:
            print(f"光谱仪重连异常: {e}")
            return False
    
    def _reconnect_laser(self) -> bool:
        """重连激光器"""
        try:
            if not self.laser_controller:
                return False
                
            # 重新打开串口
            return self.laser_controller.open_port()
        except Exception as e:
            print(f"激光器重连异常: {e}")
            return False
    
    def _reconnect_power(self) -> bool:
        """重连电源（检查电源状态）"""
        try:
            if not self.laser_controller:
                return False
                
            # 重新检查电源状态
            self.laser_controller.check_power_status()
            return self.laser_controller.power_on_status is True
        except Exception as e:
            print(f"电源重连异常: {e}")
            return False
    
    def _update_status_bar(self):
        """更新状态栏显示"""
        if not self.status_bar:
            return
            
        # 检查是否有设备断开
        disconnected_devices = []
        for device_type, status in self.device_status.items():
            if not status['connected']:
                disconnected_devices.append(f"{device_type}: {status['message']}")
        
        if disconnected_devices:
            # 显示断开状态
            error_text = " | ".join(disconnected_devices)
            self.status_bar.set_error(error_text)
            self.status_bar.set_offline()
        else:
            # 所有设备正常
            self.status_bar.set_online()
            self.status_bar.set_error("")
    
    def get_device_status_summary(self) -> Dict:
        """获取设备状态摘要"""
        summary = {}
        for device_type, status in self.device_status.items():
            summary[device_type] = {
                'connected': status['connected'],
                'message': status.get('message', ''),
                'error_count': status['error_count'],
                'last_check': status['last_check']
            }
        return summary
    
    def force_reconnect_device(self, device_type: str) -> bool:
        """强制重连指定设备"""
        if device_type not in self.device_status:
            return False
            
        # 重置错误计数
        self.device_status[device_type]['error_count'] = 0
        
        # 立即尝试重连
        return self._attempt_reconnect(device_type)
    
    def set_auto_reconnect(self, enabled: bool):
        """设置是否启用自动重连"""
        self.auto_reconnect_enabled = enabled
        print(f"自动重连已{'启用' if enabled else '禁用'}")
    
    def set_check_interval(self, interval: float):
        """设置检查间隔"""
        self.check_interval = interval
        if self.status_timer.isActive():
            self.status_timer.setInterval(int(interval * 1000))
        print(f"设备检查间隔已设置为 {interval} 秒")

    def apply_config(self, config: dict):
        """应用配置设置"""
        try:
            # 更新监控设置
            if 'check_interval' in config:
                self.set_check_interval(config['check_interval'])
            
            if 'auto_reconnect_enabled' in config:
                self.set_auto_reconnect(config['auto_reconnect_enabled'])
            
            if 'max_reconnect_attempts' in config:
                self.max_reconnect_attempts = config['max_reconnect_attempts']
            
            if 'reconnect_delay' in config:
                self.reconnect_delay = config['reconnect_delay']
            
            # 更新通知设置
            if 'notify_disconnect' in config:
                self.notify_disconnect = config['notify_disconnect']
            
            if 'notify_reconnect' in config:
                self.notify_reconnect = config['notify_reconnect']
            
            if 'log_events' in config:
                self.log_events = config['log_events']
            
            # 更新设备监控开关
            if 'monitor_spectrometer' in config:
                self.monitor_spectrometer = config['monitor_spectrometer']
            
            if 'monitor_laser' in config:
                self.monitor_laser = config['monitor_laser']
            
            if 'monitor_power' in config:
                self.monitor_power = config['monitor_power']
            
            print("设备监控配置已应用")
            
        except Exception as e:
            print(f"应用设备监控配置失败: {e}")
    
    def get_config(self) -> dict:
        """获取当前配置"""
        return {
            'check_interval': self.check_interval,
            'auto_reconnect_enabled': self.auto_reconnect_enabled,
            'max_reconnect_attempts': self.max_reconnect_attempts,
            'reconnect_delay': self.reconnect_delay,
            'notify_disconnect': self.notify_disconnect,
            'notify_reconnect': self.notify_reconnect,
            'log_events': self.log_events,
            'monitor_spectrometer': self.monitor_spectrometer,
            'monitor_laser': self.monitor_laser,
            'monitor_power': self.monitor_power
        }
