# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 获取项目根目录
project_root = os.path.abspath('.')
ui2_dir = os.path.join(project_root, 'UI2')
spectrumeter_dir = os.path.join(project_root, 'spectrumeter')

# 数据文件列表 - 只包含必要的文件
datas = [
    # UI2目录下的必要资源文件
    (os.path.join(ui2_dir, 'logo48.ico'), '.'),  # 应用图标
    (os.path.join(ui2_dir, 'software_settings.json'), '.'),  # 软件设置
    (os.path.join(ui2_dir, 'users.db'), '.'),  # 用户数据库
    
    # spectrumeter目录下的DLL文件
    (os.path.join(spectrumeter_dir, 'UserApplication.dll'), '.'),
    (os.path.join(spectrumeter_dir, 'SiUSBXp.dll'), '.'),
]

# 隐藏导入模块 - 只包含必要的模块
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'matplotlib.backends.backend_qt5agg',
    'numpy',
    'scipy.signal',
    'scipy.sparse',
    'scipy.sparse.linalg',
    'pandas',
    'cryptography',
    'requests',
    'sqlite3',
    'pickle',
    'json',
    'csv',
    'ctypes',
    # 项目内部模块
    'language_manager',
    'log_manager', 
    'menu_bar',
    'sidebar',
    'editor',
    'status_bar',
    'login_dialog',
    'password_dialog',
    'db_user_manager',
    'integrated_measurement_logic',
    'report_dialog',
    'save_files_dialog',
    'spectrum_file_reader',
    'nod_Spectrum_read',
    'nod_file_serializer',
    'nod_raman_read',
    # spectrumeter模块
    'laser_controller_usb',
    'raman_spectrometer',
]

# 排除的模块 - 排除大型不必要的库
excludes = [
    'tkinter',
    'test',
    'unittest',
    'pdb',
    'doctest',
    'PyQt5',
    'tensorflow',
    'torch',
    'torchvision',
    'sklearn',
    'IPython',
    'jupyter',
    'notebook',
    'sphinx',
    'bokeh',
    'plotly',
    'dask',
    'distributed',
    'numba',
    'llvmlite',
    'h5py',
    'tables',
    'openpyxl',
    'lxml',
    'boto3',
    'botocore',
    'google',
    'grpc',
    'zmq',
    'win32com',
    'pythoncom',
    'pywintypes',
]

a = Analysis(
    [os.path.join(ui2_dir, 'main.py')],
    pathex=[ui2_dir, spectrumeter_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='view21',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(ui2_dir, 'logo48.ico'),  # 设置应用图标
)
