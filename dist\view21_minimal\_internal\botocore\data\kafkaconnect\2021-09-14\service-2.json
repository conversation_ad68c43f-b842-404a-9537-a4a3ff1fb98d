{"version": "2.0", "metadata": {"apiVersion": "2021-09-14", "endpointPrefix": "kafkaconnect", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "Kafka Connect", "serviceFullName": "Managed Streaming for Kafka Connect", "serviceId": "KafkaConnect", "signatureVersion": "v4", "signingName": "kafkaconnect", "uid": "kafkaconnect-2021-09-14"}, "operations": {"CreateConnector": {"name": "CreateConnector", "http": {"method": "POST", "requestUri": "/v1/connectors", "responseCode": 200}, "input": {"shape": "CreateConnectorRequest"}, "output": {"shape": "CreateConnectorResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a connector using the specified properties.</p>"}, "CreateCustomPlugin": {"name": "CreateCustomPlugin", "http": {"method": "POST", "requestUri": "/v1/custom-plugins", "responseCode": 200}, "input": {"shape": "CreateCustomPluginRequest"}, "output": {"shape": "CreateCustomPluginResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a custom plugin using the specified properties.</p>"}, "CreateWorkerConfiguration": {"name": "CreateWorkerConfiguration", "http": {"method": "POST", "requestUri": "/v1/worker-configurations", "responseCode": 200}, "input": {"shape": "CreateWorkerConfigurationRequest"}, "output": {"shape": "CreateWorkerConfigurationResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a worker configuration using the specified properties.</p>"}, "DeleteConnector": {"name": "DeleteConnector", "http": {"method": "DELETE", "requestUri": "/v1/connectors/{connectorArn}", "responseCode": 200}, "input": {"shape": "DeleteConnectorRequest"}, "output": {"shape": "DeleteConnectorResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes the specified connector.</p>", "idempotent": true}, "DeleteCustomPlugin": {"name": "DeleteCustomPlugin", "http": {"method": "DELETE", "requestUri": "/v1/custom-plugins/{customPluginArn}", "responseCode": 200}, "input": {"shape": "DeleteCustomPluginRequest"}, "output": {"shape": "DeleteCustomPluginResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes a custom plugin.</p>", "idempotent": true}, "DescribeConnector": {"name": "DescribeConnector", "http": {"method": "GET", "requestUri": "/v1/connectors/{connectorArn}", "responseCode": 200}, "input": {"shape": "DescribeConnectorRequest"}, "output": {"shape": "DescribeConnectorResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns summary information about the connector.</p>"}, "DescribeCustomPlugin": {"name": "DescribeCustomPlugin", "http": {"method": "GET", "requestUri": "/v1/custom-plugins/{customPluginArn}", "responseCode": 200}, "input": {"shape": "DescribeCustomPluginRequest"}, "output": {"shape": "DescribeCustomPluginResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>A summary description of the custom plugin.</p>"}, "DescribeWorkerConfiguration": {"name": "DescribeWorkerConfiguration", "http": {"method": "GET", "requestUri": "/v1/worker-configurations/{workerConfigurationArn}", "responseCode": 200}, "input": {"shape": "DescribeWorkerConfigurationRequest"}, "output": {"shape": "DescribeWorkerConfigurationResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns information about a worker configuration.</p>"}, "ListConnectors": {"name": "ListConnectors", "http": {"method": "GET", "requestUri": "/v1/connectors", "responseCode": 200}, "input": {"shape": "ListConnectorsRequest"}, "output": {"shape": "ListConnectorsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns a list of all the connectors in this account and Region. The list is limited to connectors whose name starts with the specified prefix. The response also includes a description of each of the listed connectors.</p>"}, "ListCustomPlugins": {"name": "ListCustomPlugins", "http": {"method": "GET", "requestUri": "/v1/custom-plugins", "responseCode": 200}, "input": {"shape": "ListCustomPluginsRequest"}, "output": {"shape": "ListCustomPluginsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns a list of all of the custom plugins in this account and Region.</p>"}, "ListWorkerConfigurations": {"name": "ListWorkerConfigurations", "http": {"method": "GET", "requestUri": "/v1/worker-configurations", "responseCode": 200}, "input": {"shape": "ListWorkerConfigurationsRequest"}, "output": {"shape": "ListWorkerConfigurationsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns a list of all of the worker configurations in this account and Region.</p>"}, "UpdateConnector": {"name": "UpdateConnector", "http": {"method": "PUT", "requestUri": "/v1/connectors/{connectorArn}", "responseCode": 200}, "input": {"shape": "UpdateConnectorRequest"}, "output": {"shape": "UpdateConnectorResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates the specified connector.</p>", "idempotent": true}}, "shapes": {"ApacheKafkaCluster": {"type": "structure", "required": ["bootstrapServers", "vpc"], "members": {"bootstrapServers": {"shape": "__string", "documentation": "<p>The bootstrap servers of the cluster.</p>"}, "vpc": {"shape": "Vpc", "documentation": "<p>Details of an Amazon VPC which has network connectivity to the Apache Kafka cluster.</p>"}}, "documentation": "<p>The details of the Apache Kafka cluster to which the connector is connected.</p>"}, "ApacheKafkaClusterDescription": {"type": "structure", "members": {"bootstrapServers": {"shape": "__string", "documentation": "<p>The bootstrap servers of the cluster.</p>"}, "vpc": {"shape": "VpcDescription", "documentation": "<p>Details of an Amazon VPC which has network connectivity to the Apache Kafka cluster.</p>"}}, "documentation": "<p>The description of the Apache Kafka cluster to which the connector is connected.</p>"}, "AutoScaling": {"type": "structure", "required": ["max<PERSON><PERSON>ker<PERSON>ount", "mcuCount", "minWorkerCount"], "members": {"maxWorkerCount": {"shape": "__integerMin1Max10", "documentation": "<p>The maximum number of workers allocated to the connector.</p>"}, "mcuCount": {"shape": "__integerMin1Max8", "documentation": "<p>The number of microcontroller units (MCUs) allocated to each connector worker. The valid values are 1,2,4,8.</p>"}, "minWorkerCount": {"shape": "__integerMin1Max10", "documentation": "<p>The minimum number of workers allocated to the connector.</p>"}, "scaleInPolicy": {"shape": "ScaleInPolicy", "documentation": "<p>The sacle-in policy for the connector.</p>"}, "scaleOutPolicy": {"shape": "ScaleOutPolicy", "documentation": "<p>The sacle-out policy for the connector.</p>"}}, "documentation": "<p>Specifies how the connector scales.</p>"}, "AutoScalingDescription": {"type": "structure", "members": {"maxWorkerCount": {"shape": "__integer", "documentation": "<p>The maximum number of workers allocated to the connector.</p>"}, "mcuCount": {"shape": "__integer", "documentation": "<p>The number of microcontroller units (MCUs) allocated to each connector worker. The valid values are 1,2,4,8.</p>"}, "minWorkerCount": {"shape": "__integer", "documentation": "<p>The minimum number of workers allocated to the connector.</p>"}, "scaleInPolicy": {"shape": "ScaleInPolicyDescription", "documentation": "<p>The sacle-in policy for the connector.</p>"}, "scaleOutPolicy": {"shape": "ScaleOutPolicyDescription", "documentation": "<p>The sacle-out policy for the connector.&gt;</p>"}}, "documentation": "<p>Information about the auto scaling parameters for the connector.</p>"}, "AutoScalingUpdate": {"type": "structure", "required": ["max<PERSON><PERSON>ker<PERSON>ount", "mcuCount", "minWorkerCount", "scaleInPolicy", "scaleOutPolicy"], "members": {"maxWorkerCount": {"shape": "__integerMin1Max10", "documentation": "<p>The target maximum number of workers allocated to the connector.</p>"}, "mcuCount": {"shape": "__integerMin1Max8", "documentation": "<p>The target number of microcontroller units (MCUs) allocated to each connector worker. The valid values are 1,2,4,8.</p>"}, "minWorkerCount": {"shape": "__integerMin1Max10", "documentation": "<p>The target minimum number of workers allocated to the connector.</p>"}, "scaleInPolicy": {"shape": "ScaleInPolicyUpdate", "documentation": "<p>The target sacle-in policy for the connector.</p>"}, "scaleOutPolicy": {"shape": "ScaleOutPolicyUpdate", "documentation": "<p>The target sacle-out policy for the connector.</p>"}}, "documentation": "<p>The updates to the auto scaling parameters for the connector.</p>"}, "BadRequestException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Capacity": {"type": "structure", "members": {"autoScaling": {"shape": "AutoScaling", "documentation": "<p>Information about the auto scaling parameters for the connector.</p>"}, "provisionedCapacity": {"shape": "ProvisionedCapacity", "documentation": "<p>Details about a fixed capacity allocated to a connector.</p>"}}, "documentation": "<p>Information about the capacity of the connector, whether it is auto scaled or provisioned.</p>"}, "CapacityDescription": {"type": "structure", "members": {"autoScaling": {"shape": "AutoScalingDescription", "documentation": "<p>Describes the connector's auto scaling capacity.</p>"}, "provisionedCapacity": {"shape": "ProvisionedCapacityDescription", "documentation": "<p>Describes a connector's provisioned capacity.</p>"}}, "documentation": "<p>A description of the connector's capacity.</p>"}, "CapacityUpdate": {"type": "structure", "members": {"autoScaling": {"shape": "AutoScalingUpdate", "documentation": "<p>The target auto scaling setting.</p>"}, "provisionedCapacity": {"shape": "ProvisionedCapacityUpdate", "documentation": "<p>The target settings for provisioned capacity.</p>"}}, "documentation": "<p>The target capacity for the connector. The capacity can be auto scaled or provisioned.</p>"}, "CloudWatchLogsLogDelivery": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"shape": "__boolean", "documentation": "<p>Whether log delivery to Amazon CloudWatch Logs is enabled.</p>"}, "logGroup": {"shape": "__string", "documentation": "<p>The name of the CloudWatch log group that is the destination for log delivery.</p>"}}, "documentation": "<p>The settings for delivering connector logs to Amazon CloudWatch Logs.</p>"}, "CloudWatchLogsLogDeliveryDescription": {"type": "structure", "members": {"enabled": {"shape": "__boolean", "documentation": "<p>Whether log delivery to Amazon CloudWatch Logs is enabled.</p>"}, "logGroup": {"shape": "__string", "documentation": "<p>The name of the CloudWatch log group that is the destination for log delivery.</p>"}}, "documentation": "<p>A description of the log delivery settings.</p>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 409: Conflict. A resource with this name already exists. Retry your request with another name.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ConnectorState": {"type": "string", "enum": ["RUNNING", "CREATING", "UPDATING", "DELETING", "FAILED"]}, "ConnectorSummary": {"type": "structure", "members": {"capacity": {"shape": "CapacityDescription", "documentation": "<p>The connector's compute capacity settings.</p>"}, "connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector.</p>"}, "connectorDescription": {"shape": "__string", "documentation": "<p>The description of the connector.</p>"}, "connectorName": {"shape": "__string", "documentation": "<p>The name of the connector.</p>"}, "connectorState": {"shape": "ConnectorState", "documentation": "<p>The state of the connector.</p>"}, "creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the connector was created.</p>"}, "currentVersion": {"shape": "__string", "documentation": "<p>The current version of the connector.</p>"}, "kafkaCluster": {"shape": "KafkaClusterDescription", "documentation": "<p>The details of the Apache Kafka cluster to which the connector is connected.</p>"}, "kafkaClusterClientAuthentication": {"shape": "KafkaClusterClientAuthenticationDescription", "documentation": "<p>The type of client authentication used to connect to the Apache Kafka cluster. The value is NONE when no client authentication is used.</p>"}, "kafkaClusterEncryptionInTransit": {"shape": "KafkaClusterEncryptionInTransitDescription", "documentation": "<p>Details of encryption in transit to the Apache Kafka cluster.</p>"}, "kafkaConnectVersion": {"shape": "__string", "documentation": "<p>The version of Kafka Connect. It has to be compatible with both the Apache Kafka cluster's version and the plugins.</p>"}, "logDelivery": {"shape": "LogDeliveryDescription", "documentation": "<p>The settings for delivering connector logs to Amazon CloudWatch Logs.</p>"}, "plugins": {"shape": "__listOfPluginDescription", "documentation": "<p>Specifies which plugins were used for this connector.</p>"}, "serviceExecutionRoleArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role used by the connector to access Amazon Web Services resources.</p>"}, "workerConfiguration": {"shape": "WorkerConfigurationDescription", "documentation": "<p>The worker configurations that are in use with the connector.</p>"}}, "documentation": "<p>Summary of a connector.</p>"}, "CreateConnectorRequest": {"type": "structure", "required": ["capacity", "connectorConfiguration", "connectorName", "kafkaCluster", "kafkaClusterClientAuthentication", "kafkaClusterEncryptionInTransit", "kafkaConnectVersion", "plugins", "serviceExecutionRoleArn"], "members": {"capacity": {"shape": "Capacity", "documentation": "<p>Information about the capacity allocated to the connector. Exactly one of the two properties must be specified.</p>"}, "connectorConfiguration": {"shape": "__sensitive__mapOf__string", "documentation": "<p>A map of keys to values that represent the configuration for the connector.</p>"}, "connectorDescription": {"shape": "__stringMax1024", "documentation": "<p>A summary description of the connector.</p>"}, "connectorName": {"shape": "__stringMin1Max128", "documentation": "<p>The name of the connector.</p>"}, "kafkaCluster": {"shape": "KafkaCluster", "documentation": "<p>Specifies which Apache Kafka cluster to connect to.</p>"}, "kafkaClusterClientAuthentication": {"shape": "KafkaClusterClientAuthentication", "documentation": "<p>Details of the client authentication used by the Apache Kafka cluster.</p>"}, "kafkaClusterEncryptionInTransit": {"shape": "KafkaClusterEncryptionInTransit", "documentation": "<p>Details of encryption in transit to the Apache Kafka cluster.</p>"}, "kafkaConnectVersion": {"shape": "__string", "documentation": "<p>The version of Kafka Connect. It has to be compatible with both the Apache Kafka cluster's version and the plugins.</p>"}, "logDelivery": {"shape": "LogDelivery", "documentation": "<p>Details about log delivery.</p>"}, "plugins": {"shape": "__listOfPlugin", "documentation": "<p>Specifies which plugins to use for the connector.</p>"}, "serviceExecutionRoleArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role used by the connector to access the Amazon Web Services resources that it needs. The types of resources depends on the logic of the connector. For example, a connector that has Amazon S3 as a destination must have permissions that allow it to write to the S3 destination bucket.</p>"}, "workerConfiguration": {"shape": "WorkerConfiguration", "documentation": "<p>Specifies which worker configuration to use with the connector.</p>"}}}, "CreateConnectorResponse": {"type": "structure", "members": {"connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) that Amazon assigned to the connector.</p>"}, "connectorName": {"shape": "__string", "documentation": "<p>The name of the connector.</p>"}, "connectorState": {"shape": "ConnectorState", "documentation": "<p>The state of the connector.</p>"}}}, "CreateCustomPluginRequest": {"type": "structure", "required": ["contentType", "location", "name"], "members": {"contentType": {"shape": "CustomPluginContentType", "documentation": "<p>The type of the plugin file.</p>"}, "description": {"shape": "__stringMax1024", "documentation": "<p>A summary description of the custom plugin.</p>"}, "location": {"shape": "CustomPluginLocation", "documentation": "<p>Information about the location of a custom plugin.</p>"}, "name": {"shape": "__stringMin1Max128", "documentation": "<p>The name of the custom plugin.</p>"}}}, "CreateCustomPluginResponse": {"type": "structure", "members": {"customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) that Amazon assigned to the custom plugin.</p>"}, "customPluginState": {"shape": "CustomPluginState", "documentation": "<p>The state of the custom plugin.</p>"}, "name": {"shape": "__string", "documentation": "<p>The name of the custom plugin.</p>"}, "revision": {"shape": "__long", "documentation": "<p>The revision of the custom plugin.</p>"}}}, "CreateWorkerConfigurationRequest": {"type": "structure", "required": ["name", "propertiesFileContent"], "members": {"description": {"shape": "__stringMax1024", "documentation": "<p>A summary description of the worker configuration.</p>"}, "name": {"shape": "__stringMin1Max128", "documentation": "<p>The name of the worker configuration.</p>"}, "propertiesFileContent": {"shape": "__sensitiveString", "documentation": "<p>Base64 encoded contents of connect-distributed.properties file.</p>"}}}, "CreateWorkerConfigurationResponse": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the worker configuration was created.</p>"}, "latestRevision": {"shape": "WorkerConfigurationRevisionSummary", "documentation": "<p>The latest revision of the worker configuration.</p>"}, "name": {"shape": "__string", "documentation": "<p>The name of the worker configuration.</p>"}, "workerConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) that Amazon assigned to the worker configuration.</p>"}}}, "CustomPlugin": {"type": "structure", "required": ["customPluginArn", "revision"], "members": {"customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom plugin.</p>"}, "revision": {"shape": "__longMin1", "documentation": "<p>The revision of the custom plugin.</p>"}}, "documentation": "<p>A plugin is an AWS resource that contains the code that defines a connector's logic.</p>"}, "CustomPluginContentType": {"type": "string", "enum": ["JAR", "ZIP"]}, "CustomPluginDescription": {"type": "structure", "members": {"customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom plugin.</p>"}, "revision": {"shape": "__long", "documentation": "<p>The revision of the custom plugin.</p>"}}, "documentation": "<p>Details about a custom plugin.</p>"}, "CustomPluginFileDescription": {"type": "structure", "members": {"fileMd5": {"shape": "__string", "documentation": "<p>The hex-encoded MD5 checksum of the custom plugin file. You can use it to validate the file.</p>"}, "fileSize": {"shape": "__long", "documentation": "<p>The size in bytes of the custom plugin file. You can use it to validate the file.</p>"}}, "documentation": "<p>Details about a custom plugin file.</p>"}, "CustomPluginLocation": {"type": "structure", "required": ["s3Location"], "members": {"s3Location": {"shape": "S3Location", "documentation": "<p>The S3 bucket Amazon Resource Name (ARN), file key, and object version of the plugin file stored in Amazon S3.</p>"}}, "documentation": "<p>Information about the location of a custom plugin.</p>"}, "CustomPluginLocationDescription": {"type": "structure", "members": {"s3Location": {"shape": "S3LocationDescription", "documentation": "<p>The S3 bucket Amazon Resource Name (ARN), file key, and object version of the plugin file stored in Amazon S3.</p>"}}, "documentation": "<p>Information about the location of a custom plugin.</p>"}, "CustomPluginRevisionSummary": {"type": "structure", "members": {"contentType": {"shape": "CustomPluginContentType", "documentation": "<p>The format of the plugin file.</p>"}, "creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the custom plugin was created.</p>"}, "description": {"shape": "__string", "documentation": "<p>The description of the custom plugin.</p>"}, "fileDescription": {"shape": "CustomPluginFileDescription", "documentation": "<p>Details about the custom plugin file.</p>"}, "location": {"shape": "CustomPluginLocationDescription", "documentation": "<p>Information about the location of the custom plugin.</p>"}, "revision": {"shape": "__long", "documentation": "<p>The revision of the custom plugin.</p>"}}, "documentation": "<p>Details about the revision of a custom plugin.</p>"}, "CustomPluginState": {"type": "string", "enum": ["CREATING", "CREATE_FAILED", "ACTIVE", "UPDATING", "UPDATE_FAILED", "DELETING"]}, "CustomPluginSummary": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the custom plugin was created.</p>"}, "customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom plugin.</p>"}, "customPluginState": {"shape": "CustomPluginState", "documentation": "<p>The state of the custom plugin.</p>"}, "description": {"shape": "__string", "documentation": "<p>A description of the custom plugin.</p>"}, "latestRevision": {"shape": "CustomPluginRevisionSummary", "documentation": "<p>The latest revision of the custom plugin.</p>"}, "name": {"shape": "__string", "documentation": "<p>The name of the custom plugin.</p>"}}, "documentation": "<p>A summary of the custom plugin.</p>"}, "DeleteConnectorRequest": {"type": "structure", "required": ["connectorArn"], "members": {"connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector that you want to delete.</p>", "location": "uri", "locationName": "connectorArn"}, "currentVersion": {"shape": "__string", "documentation": "<p>The current version of the connector that you want to delete.</p>", "location": "querystring", "locationName": "currentVersion"}}}, "DeleteConnectorResponse": {"type": "structure", "members": {"connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector that you requested to delete.</p>"}, "connectorState": {"shape": "ConnectorState", "documentation": "<p>The state of the connector that you requested to delete.</p>"}}}, "DeleteCustomPluginRequest": {"type": "structure", "required": ["customPluginArn"], "members": {"customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom plugin that you want to delete.</p>", "location": "uri", "locationName": "customPluginArn"}}}, "DeleteCustomPluginResponse": {"type": "structure", "members": {"customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom plugin that you requested to delete.</p>"}, "customPluginState": {"shape": "CustomPluginState", "documentation": "<p>The state of the custom plugin.</p>"}}}, "DescribeConnectorRequest": {"type": "structure", "required": ["connectorArn"], "members": {"connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector that you want to describe.</p>", "location": "uri", "locationName": "connectorArn"}}}, "DescribeConnectorResponse": {"type": "structure", "members": {"capacity": {"shape": "CapacityDescription", "documentation": "<p>Information about the capacity of the connector, whether it is auto scaled or provisioned.</p>"}, "connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector.</p>"}, "connectorConfiguration": {"shape": "__sensitive__mapOf__string", "documentation": "<p>A map of keys to values that represent the configuration for the connector.</p>"}, "connectorDescription": {"shape": "__string", "documentation": "<p>A summary description of the connector.</p>"}, "connectorName": {"shape": "__string", "documentation": "<p>The name of the connector.</p>"}, "connectorState": {"shape": "ConnectorState", "documentation": "<p>The state of the connector.</p>"}, "creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time the connector was created.</p>"}, "currentVersion": {"shape": "__string", "documentation": "<p>The current version of the connector.</p>"}, "kafkaCluster": {"shape": "KafkaClusterDescription", "documentation": "<p>The Apache Kafka cluster that the connector is connected to.</p>"}, "kafkaClusterClientAuthentication": {"shape": "KafkaClusterClientAuthenticationDescription", "documentation": "<p>The type of client authentication used to connect to the Apache Kafka cluster. The value is NONE when no client authentication is used.</p>"}, "kafkaClusterEncryptionInTransit": {"shape": "KafkaClusterEncryptionInTransitDescription", "documentation": "<p>Details of encryption in transit to the Apache Kafka cluster.</p>"}, "kafkaConnectVersion": {"shape": "__string", "documentation": "<p>The version of Kafka Connect. It has to be compatible with both the Apache Kafka cluster's version and the plugins.</p>"}, "logDelivery": {"shape": "LogDeliveryDescription", "documentation": "<p>Details about delivering logs to Amazon CloudWatch Logs.</p>"}, "plugins": {"shape": "__listOfPluginDescription", "documentation": "<p>Specifies which plugins were used for this connector.</p>"}, "serviceExecutionRoleArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role used by the connector to access Amazon Web Services resources.</p>"}, "stateDescription": {"shape": "StateDescription", "documentation": "<p>Details about the state of a connector.</p>"}, "workerConfiguration": {"shape": "WorkerConfigurationDescription", "documentation": "<p>Specifies which worker configuration was used for the connector.</p>"}}}, "DescribeCustomPluginRequest": {"type": "structure", "required": ["customPluginArn"], "members": {"customPluginArn": {"shape": "__string", "documentation": "<p>Returns information about a custom plugin.</p>", "location": "uri", "locationName": "customPluginArn"}}}, "DescribeCustomPluginResponse": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the custom plugin was created.</p>"}, "customPluginArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom plugin.</p>"}, "customPluginState": {"shape": "CustomPluginState", "documentation": "<p>The state of the custom plugin.</p>"}, "description": {"shape": "__string", "documentation": "<p>The description of the custom plugin.</p>"}, "latestRevision": {"shape": "CustomPluginRevisionSummary", "documentation": "<p>The latest successfully created revision of the custom plugin. If there are no successfully created revisions, this field will be absent.</p>"}, "name": {"shape": "__string", "documentation": "<p>The name of the custom plugin.</p>"}, "stateDescription": {"shape": "StateDescription", "documentation": "<p>Details about the state of a custom plugin.</p>"}}}, "DescribeWorkerConfigurationRequest": {"type": "structure", "required": ["workerConfigurationArn"], "members": {"workerConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the worker configuration that you want to get information about.</p>", "location": "uri", "locationName": "workerConfigurationArn"}}}, "DescribeWorkerConfigurationResponse": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the worker configuration was created.</p>"}, "description": {"shape": "__string", "documentation": "<p>The description of the worker configuration.</p>"}, "latestRevision": {"shape": "WorkerConfigurationRevisionDescription", "documentation": "<p>The latest revision of the custom configuration.</p>"}, "name": {"shape": "__string", "documentation": "<p>The name of the worker configuration.</p>"}, "workerConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the custom configuration.</p>"}}}, "FirehoseLogDelivery": {"type": "structure", "required": ["enabled"], "members": {"deliveryStream": {"shape": "__string", "documentation": "<p>The name of the Kinesis Data Firehose delivery stream that is the destination for log delivery.</p>"}, "enabled": {"shape": "__boolean", "documentation": "<p>Specifies whether connector logs get delivered to Amazon Kinesis Data Firehose.</p>"}}, "documentation": "<p>The settings for delivering logs to Amazon Kinesis Data Firehose.</p>"}, "FirehoseLogDeliveryDescription": {"type": "structure", "members": {"deliveryStream": {"shape": "__string", "documentation": "<p>The name of the Kinesis Data Firehose delivery stream that is the destination for log delivery.</p>"}, "enabled": {"shape": "__boolean", "documentation": "<p>Specifies whether connector logs get delivered to Amazon Kinesis Data Firehose.</p>"}}, "documentation": "<p>A description of the settings for delivering logs to Amazon Kinesis Data Firehose.</p>"}, "ForbiddenException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "InternalServerErrorException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "KafkaCluster": {"type": "structure", "required": ["apacheKafkaCluster"], "members": {"apacheKafkaCluster": {"shape": "ApacheKafkaCluster", "documentation": "<p>The Apache Kafka cluster to which the connector is connected.</p>"}}, "documentation": "<p>The details of the Apache Kafka cluster to which the connector is connected.</p>"}, "KafkaClusterClientAuthentication": {"type": "structure", "required": ["authenticationType"], "members": {"authenticationType": {"shape": "KafkaClusterClientAuthenticationType", "documentation": "<p>The type of client authentication used to connect to the Apache Kafka cluster. Value NONE means that no client authentication is used.</p>"}}, "documentation": "<p>The client authentication information used in order to authenticate with the Apache Kafka cluster.</p>"}, "KafkaClusterClientAuthenticationDescription": {"type": "structure", "members": {"authenticationType": {"shape": "KafkaClusterClientAuthenticationType", "documentation": "<p>The type of client authentication used to connect to the Apache Kafka cluster. Value NONE means that no client authentication is used.</p>"}}, "documentation": "<p>The client authentication information used in order to authenticate with the Apache Kafka cluster.</p>"}, "KafkaClusterClientAuthenticationType": {"type": "string", "enum": ["NONE", "IAM"]}, "KafkaClusterDescription": {"type": "structure", "members": {"apacheKafkaCluster": {"shape": "ApacheKafkaClusterDescription", "documentation": "<p>The Apache Kafka cluster to which the connector is connected.</p>"}}, "documentation": "<p>Details of how to connect to the Apache Kafka cluster.</p>"}, "KafkaClusterEncryptionInTransit": {"type": "structure", "required": ["encryptionType"], "members": {"encryptionType": {"shape": "KafkaClusterEncryptionInTransitType", "documentation": "<p>The type of encryption in transit to the Apache Kafka cluster.</p>"}}, "documentation": "<p>Details of encryption in transit to the Apache Kafka cluster.</p>"}, "KafkaClusterEncryptionInTransitDescription": {"type": "structure", "members": {"encryptionType": {"shape": "KafkaClusterEncryptionInTransitType", "documentation": "<p>The type of encryption in transit to the Apache Kafka cluster.</p>"}}, "documentation": "<p>The description of the encryption in transit to the Apache Kafka cluster.</p>"}, "KafkaClusterEncryptionInTransitType": {"type": "string", "enum": ["PLAINTEXT", "TLS"]}, "ListConnectorsRequest": {"type": "structure", "members": {"connectorNamePrefix": {"shape": "__string", "documentation": "<p>The name prefix that you want to use to search for and list connectors.</p>", "location": "querystring", "locationName": "connectorNamePrefix"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of connectors to list in one response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "__string", "documentation": "<p>If the response of a ListConnectors operation is truncated, it will include a NextToken. Send this NextToken in a subsequent request to continue listing from where the previous operation left off.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListConnectorsResponse": {"type": "structure", "members": {"connectors": {"shape": "__listOfConnectorSummary", "documentation": "<p>An array of connector descriptions.</p>"}, "nextToken": {"shape": "__string", "documentation": "<p>If the response of a ListConnectors operation is truncated, it will include a NextToken. Send this NextToken in a subsequent request to continue listing from where it left off.</p>"}}}, "ListCustomPluginsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of custom plugins to list in one response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "__string", "documentation": "<p>If the response of a ListCustomPlugins operation is truncated, it will include a NextToken. Send this NextToken in a subsequent request to continue listing from where the previous operation left off.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCustomPluginsResponse": {"type": "structure", "members": {"customPlugins": {"shape": "__listOfCustomPluginSummary", "documentation": "<p>An array of custom plugin descriptions.</p>"}, "nextToken": {"shape": "__string", "documentation": "<p>If the response of a ListCustomPlugins operation is truncated, it will include a NextToken. Send this NextToken in a subsequent request to continue listing from where the previous operation left off.</p>"}}}, "ListWorkerConfigurationsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of worker configurations to list in one response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "__string", "documentation": "<p>If the response of a ListWorkerConfigurations operation is truncated, it will include a NextToken. Send this NextToken in a subsequent request to continue listing from where the previous operation left off.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListWorkerConfigurationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "__string", "documentation": "<p>If the response of a ListWorkerConfigurations operation is truncated, it will include a NextToken. Send this NextToken in a subsequent request to continue listing from where the previous operation left off.</p>"}, "workerConfigurations": {"shape": "__listOfWorkerConfigurationSummary", "documentation": "<p>An array of worker configuration descriptions.</p>"}}}, "LogDelivery": {"type": "structure", "required": ["workerLogDelivery"], "members": {"workerLogDelivery": {"shape": "WorkerLogDelivery", "documentation": "<p>The workers can send worker logs to different destination types. This configuration specifies the details of these destinations.</p>"}}, "documentation": "<p>Details about log delivery.</p>"}, "LogDeliveryDescription": {"type": "structure", "members": {"workerLogDelivery": {"shape": "WorkerLogDeliveryDescription", "documentation": "<p>The workers can send worker logs to different destination types. This configuration specifies the details of these destinations.</p>"}}, "documentation": "<p>The description of the log delivery settings.</p>"}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "NotFoundException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "Plugin": {"type": "structure", "required": ["customPlugin"], "members": {"customPlugin": {"shape": "CustomPlugin", "documentation": "<p>Details about a custom plugin.</p>"}}, "documentation": "<p>A plugin is an AWS resource that contains the code that defines your connector logic. </p>"}, "PluginDescription": {"type": "structure", "members": {"customPlugin": {"shape": "CustomPluginDescription", "documentation": "<p>Details about a custom plugin.</p>"}}, "documentation": "<p>The description of the plugin.</p>"}, "ProvisionedCapacity": {"type": "structure", "required": ["mcuCount", "workerCount"], "members": {"mcuCount": {"shape": "__integerMin1Max8", "documentation": "<p>The number of microcontroller units (MCUs) allocated to each connector worker. The valid values are 1,2,4,8.</p>"}, "workerCount": {"shape": "__integerMin1Max10", "documentation": "<p>The number of workers that are allocated to the connector.</p>"}}, "documentation": "<p>Details about a connector's provisioned capacity.</p>"}, "ProvisionedCapacityDescription": {"type": "structure", "members": {"mcuCount": {"shape": "__integer", "documentation": "<p>The number of microcontroller units (MCUs) allocated to each connector worker. The valid values are 1,2,4,8.</p>"}, "workerCount": {"shape": "__integer", "documentation": "<p>The number of workers that are allocated to the connector.</p>"}}, "documentation": "<p>The description of a connector's provisioned capacity.</p>"}, "ProvisionedCapacityUpdate": {"type": "structure", "required": ["mcuCount", "workerCount"], "members": {"mcuCount": {"shape": "__integerMin1Max8", "documentation": "<p>The number of microcontroller units (MCUs) allocated to each connector worker. The valid values are 1,2,4,8.</p>"}, "workerCount": {"shape": "__integerMin1Max10", "documentation": "<p>The number of workers that are allocated to the connector.</p>"}}, "documentation": "<p>An update to a connector's fixed capacity.</p>"}, "S3Location": {"type": "structure", "required": ["bucketArn", "fileKey"], "members": {"bucketArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of an S3 bucket.</p>"}, "fileKey": {"shape": "__string", "documentation": "<p>The file key for an object in an S3 bucket.</p>"}, "objectVersion": {"shape": "__string", "documentation": "<p>The version of an object in an S3 bucket.</p>"}}, "documentation": "<p>The location of an object in Amazon S3.</p>"}, "S3LocationDescription": {"type": "structure", "members": {"bucketArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of an S3 bucket.</p>"}, "fileKey": {"shape": "__string", "documentation": "<p>The file key for an object in an S3 bucket.</p>"}, "objectVersion": {"shape": "__string", "documentation": "<p>The version of an object in an S3 bucket.</p>"}}, "documentation": "<p>The description of the location of an object in Amazon S3.</p>"}, "S3LogDelivery": {"type": "structure", "required": ["enabled"], "members": {"bucket": {"shape": "__string", "documentation": "<p>The name of the S3 bucket that is the destination for log delivery.</p>"}, "enabled": {"shape": "__boolean", "documentation": "<p>Specifies whether connector logs get sent to the specified Amazon S3 destination.</p>"}, "prefix": {"shape": "__string", "documentation": "<p>The S3 prefix that is the destination for log delivery.</p>"}}, "documentation": "<p>Details about delivering logs to Amazon S3.</p>"}, "S3LogDeliveryDescription": {"type": "structure", "members": {"bucket": {"shape": "__string", "documentation": "<p>The name of the S3 bucket that is the destination for log delivery.</p>"}, "enabled": {"shape": "__boolean", "documentation": "<p>Specifies whether connector logs get sent to the specified Amazon S3 destination.</p>"}, "prefix": {"shape": "__string", "documentation": "<p>The S3 prefix that is the destination for log delivery.</p>"}}, "documentation": "<p>The description of the details about delivering logs to Amazon S3.</p>"}, "ScaleInPolicy": {"type": "structure", "required": ["cpuUtilizationPercentage"], "members": {"cpuUtilizationPercentage": {"shape": "__integerMin1Max100", "documentation": "<p>Specifies the CPU utilization percentage threshold at which you want connector scale in to be triggered.</p>"}}, "documentation": "<p>The scale-in policy for the connector.</p>"}, "ScaleInPolicyDescription": {"type": "structure", "members": {"cpuUtilizationPercentage": {"shape": "__integer", "documentation": "<p>Specifies the CPU utilization percentage threshold at which you want connector scale in to be triggered.</p>"}}, "documentation": "<p>The description of the scale-in policy for the connector.</p>"}, "ScaleInPolicyUpdate": {"type": "structure", "required": ["cpuUtilizationPercentage"], "members": {"cpuUtilizationPercentage": {"shape": "__integerMin1Max100", "documentation": "<p>The target CPU utilization percentage threshold at which you want connector scale in to be triggered.</p>"}}, "documentation": "<p>An update to the connector's scale-in policy.</p>"}, "ScaleOutPolicy": {"type": "structure", "required": ["cpuUtilizationPercentage"], "members": {"cpuUtilizationPercentage": {"shape": "__integerMin1Max100", "documentation": "<p>The CPU utilization percentage threshold at which you want connector scale out to be triggered.</p>"}}, "documentation": "<p>The scale-out policy for the connector.</p>"}, "ScaleOutPolicyDescription": {"type": "structure", "members": {"cpuUtilizationPercentage": {"shape": "__integer", "documentation": "<p>The CPU utilization percentage threshold at which you want connector scale out to be triggered.</p>"}}, "documentation": "<p>The description of the scale-out policy for the connector.</p>"}, "ScaleOutPolicyUpdate": {"type": "structure", "required": ["cpuUtilizationPercentage"], "members": {"cpuUtilizationPercentage": {"shape": "__integerMin1Max100", "documentation": "<p>The target CPU utilization percentage threshold at which you want connector scale out to be triggered.</p>"}}, "documentation": "<p>An update to the connector's scale-out policy.</p>"}, "ServiceUnavailableException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "StateDescription": {"type": "structure", "members": {"code": {"shape": "__string", "documentation": "<p>A code that describes the state of a resource.</p>"}, "message": {"shape": "__string", "documentation": "<p>A message that describes the state of a resource.</p>"}}, "documentation": "<p>Details about the state of a resource.</p>"}, "TooManyRequestsException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "UnauthorizedException": {"type": "structure", "members": {"message": {"shape": "__string"}}, "documentation": "<p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>", "error": {"httpStatusCode": 401, "senderFault": true}, "exception": true}, "UpdateConnectorRequest": {"type": "structure", "required": ["capacity", "connectorArn", "currentVersion"], "members": {"capacity": {"shape": "CapacityUpdate", "documentation": "<p>The target capacity.</p>"}, "connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector that you want to update.</p>", "location": "uri", "locationName": "connectorArn"}, "currentVersion": {"shape": "__string", "documentation": "<p>The current version of the connector that you want to update.</p>", "location": "querystring", "locationName": "currentVersion"}}}, "UpdateConnectorResponse": {"type": "structure", "members": {"connectorArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the connector.</p>"}, "connectorState": {"shape": "ConnectorState", "documentation": "<p>The state of the connector.</p>"}}}, "Vpc": {"type": "structure", "required": ["subnets"], "members": {"securityGroups": {"shape": "__listOf__string", "documentation": "<p>The security groups for the connector.</p>"}, "subnets": {"shape": "__listOf__string", "documentation": "<p>The subnets for the connector.</p>"}}, "documentation": "<p>Information about the VPC in which the connector resides.</p>"}, "VpcDescription": {"type": "structure", "members": {"securityGroups": {"shape": "__listOf__string", "documentation": "<p>The security groups for the connector.</p>"}, "subnets": {"shape": "__listOf__string", "documentation": "<p>The subnets for the connector.</p>"}}, "documentation": "<p>The description of the VPC in which the connector resides.</p>"}, "WorkerConfiguration": {"type": "structure", "required": ["revision", "workerConfigurationArn"], "members": {"revision": {"shape": "__longMin1", "documentation": "<p>The revision of the worker configuration.</p>"}, "workerConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the worker configuration.</p>"}}, "documentation": "<p>The configuration of the workers, which are the processes that run the connector logic.</p>"}, "WorkerConfigurationDescription": {"type": "structure", "members": {"revision": {"shape": "__long", "documentation": "<p>The revision of the worker configuration.</p>"}, "workerConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the worker configuration.</p>"}}, "documentation": "<p>The description of the worker configuration.</p>"}, "WorkerConfigurationRevisionDescription": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that the worker configuration was created.</p>"}, "description": {"shape": "__string", "documentation": "<p>The description of the worker configuration revision.</p>"}, "propertiesFileContent": {"shape": "__sensitiveString", "documentation": "<p>Base64 encoded contents of the connect-distributed.properties file.</p>"}, "revision": {"shape": "__long", "documentation": "<p>The description of a revision of the worker configuration.</p>"}}, "documentation": "<p>The description of the worker configuration revision.</p>"}, "WorkerConfigurationRevisionSummary": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that a worker configuration revision was created.</p>"}, "description": {"shape": "__string", "documentation": "<p>The description of a worker configuration revision.</p>"}, "revision": {"shape": "__long", "documentation": "<p>The revision of a worker configuration.</p>"}}, "documentation": "<p>The summary of a worker configuration revision.</p>"}, "WorkerConfigurationSummary": {"type": "structure", "members": {"creationTime": {"shape": "__timestampIso8601", "documentation": "<p>The time that a worker configuration was created.</p>"}, "description": {"shape": "__string", "documentation": "<p>The description of a worker configuration.</p>"}, "latestRevision": {"shape": "WorkerConfigurationRevisionSummary", "documentation": "<p>The latest revision of a worker configuration.</p>"}, "name": {"shape": "__string", "documentation": "<p>The name of the worker configuration.</p>"}, "workerConfigurationArn": {"shape": "__string", "documentation": "<p>The Amazon Resource Name (ARN) of the worker configuration.</p>"}}, "documentation": "<p>The summary of a worker configuration.</p>"}, "WorkerLogDelivery": {"type": "structure", "members": {"cloudWatchLogs": {"shape": "CloudWatchLogsLogDelivery", "documentation": "<p>Details about delivering logs to Amazon CloudWatch Logs.</p>"}, "firehose": {"shape": "FirehoseLogDelivery", "documentation": "<p>Details about delivering logs to Amazon Kinesis Data Firehose.</p>"}, "s3": {"shape": "S3LogDelivery", "documentation": "<p>Details about delivering logs to Amazon S3.</p>"}}, "documentation": "<p>Workers can send worker logs to different destination types. This configuration specifies the details of these destinations.</p>"}, "WorkerLogDeliveryDescription": {"type": "structure", "members": {"cloudWatchLogs": {"shape": "CloudWatchLogsLogDeliveryDescription", "documentation": "<p>Details about delivering logs to Amazon CloudWatch Logs.</p>"}, "firehose": {"shape": "FirehoseLogDeliveryDescription", "documentation": "<p>Details about delivering logs to Amazon Kinesis Data Firehose.</p>"}, "s3": {"shape": "S3LogDeliveryDescription", "documentation": "<p>Details about delivering logs to Amazon S3.</p>"}}, "documentation": "<p>Workers can send worker logs to different destination types. This configuration specifies the details of these destinations.</p>"}, "__boolean": {"type": "boolean"}, "__integer": {"type": "integer"}, "__integerMin1Max10": {"type": "integer", "max": 10, "min": 1}, "__integerMin1Max100": {"type": "integer", "max": 100, "min": 1}, "__integerMin1Max8": {"type": "integer", "max": 8, "min": 1}, "__listOfConnectorSummary": {"type": "list", "member": {"shape": "ConnectorSummary"}}, "__listOfCustomPluginSummary": {"type": "list", "member": {"shape": "CustomPluginSummary"}}, "__listOfPlugin": {"type": "list", "member": {"shape": "Plugin"}}, "__listOfPluginDescription": {"type": "list", "member": {"shape": "PluginDescription"}}, "__listOfWorkerConfigurationSummary": {"type": "list", "member": {"shape": "WorkerConfigurationSummary"}}, "__listOf__string": {"type": "list", "member": {"shape": "__string"}}, "__long": {"type": "long"}, "__longMin1": {"type": "long", "max": 9223372036854775807, "min": 1}, "__sensitiveString": {"type": "string", "sensitive": true}, "__sensitive__mapOf__string": {"type": "map", "key": {"shape": "__string"}, "value": {"shape": "__string"}, "sensitive": true}, "__string": {"type": "string"}, "__stringMax1024": {"type": "string", "max": 1024, "min": 0}, "__stringMin1Max128": {"type": "string", "max": 128, "min": 1}, "__timestampIso8601": {"type": "timestamp", "timestampFormat": "iso8601"}}, "documentation": "<p/>"}