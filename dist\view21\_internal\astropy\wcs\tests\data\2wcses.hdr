SIMPLE  =                    T / conforms to FITS standard                      BITPIX  =                  -32 / array data type                                NAXIS   =                    2 / number of array dimensions                     NAXIS1  =                 2048                                                  NAXIS2  =                 4096                                                  EXTEND  =                    T                                                  RUN     =               418552 / Run number                                     OBSERVAT= 'LAPALMA '           / Name of observatory (IRAF style)               OBSERVER= 'Drew    '           / Name of principal investigator                 OBJECT  = 'intphas_4970 Ha'    / Title of observation                           LATITUDE=            28.761929 / Telescope latitude  (degrees), +28:45:42.9     LONGITUD=           -17.877577 / Telescope longitude (degrees), -17:52:39.3     HEIGHT  =                 2348 / [m] Height above sea level.                    SLATEL  = 'LPO2.5  '           / Telescope name known to SLALIB                 TELESCOP= 'INT     '           / 2.5m Isaac Newton Telescope                    MJD-OBS =        53240.9816151 / Modified Julian Date of midtime of observation JD      =      2453241.4816151 / Julian Date of midtime of observation          PLATESCA=             6.856013 / [d/m] Platescale ( 24.68arcsec/mm)             TELFOCUS=             0.043969 / Telescope focus (metres)                       AIRMASS =             1.048846 / Effective mean airmass                         DATE-OBS= '2004-08-23T23:32:32.4' / UTC date start of observation               UTSTART = '23:32:32.4'         / UTC of start of observation                    TEMPTUBE=             10.15365 / Truss Temperature (degrees Celsius)            INSTRUME= 'WFC     '           / INT wide-field camera is in use.               WFFPOS  =                    1 / Position-number of deployed filter             WFFBAND = 'Halpha  '           / Waveband of filter                             WFFID   = '197     '           / Unique identifier of filter                    SECPPIX =                0.333 / Arcseconds per pixel                           DETECTOR= 'WFC     '           / Formal name of camera                          CCDSPEED= 'FAST    '           / Readout speed                                  CCDXBIN =                    1 / Binning factor in x axis                       CCDYBIN =                    1 / Binning factor in y axis                       CCDSUM  = '1 1     '           / Binning factors (IRAF style)                   CCDTEMP =              156.114 / [K] Cryostat temperature                       NWINDOWS=                    0 / Number of readout windows                      CCDNAME = 'A5506-4 '           / Name of detector chip.                         CCDXPIXE=             1.35E-05 / [m] Size of pixels in x.                       CCDYPIXE=             1.35E-05 / [m] Size of pixels in y.                       AMPNAME = 'LH      '           / Name of output amplifier.                      GAIN    =                  2.8 / Nominal Photo-electrons per ADU.               READNOIS=                  6.4 / Nominal Readout noise in electrons.            NUMBRMS =                  257 / Number of standards used                       STDCRMS =                0.067 / Astrometric fit error (arcsec)                 PERCORR =                  0.0 / Sky calibration correction (mags)              EXTINCT =                 0.09 / Extinction coefficient (mags)                  RADESYSA= 'ICRS    '                                                            EQUINOX =               2000.0                                                  CTYPE1  = 'RA---ZPN'           / Algorithm type for axis 1                      CTYPE2  = 'DEC--ZPN'           / Algorithm type for axis 2                      CRPIX1  =           -337.20001 / [pixel] Reference pixel along axis 1           CRPIX2  =               3040.5 / [pixel] Reference pixel along axis 2           CRVAL1  =            292.20508 / [deg] Right ascension at the reference pixel   CRVAL2  =            18.582556 / [deg] Declination at the reference pixel       CRUNIT1 = 'deg     '           / Unit of right ascension coordinates            CRUNIT2 = 'deg     '           / Unit of declination coordinates                CD1_1   =       -1.3007094E-06 / Transformation matrix element                  CD1_2   =       -9.2396054E-05 / Transformation matrix element                  CD2_1   =       -9.2389091E-05 / Transformation matrix element                  CD2_2   =        1.3203634E-06 / Transformation matrix element                  PV2_1   =                  1.0 / Coefficient for r term                         PV2_2   =                  0.0 / Coefficient for r**2 term                      PV2_3   =                220.0 / Coefficient for r**3 term                      ORIGZPT =                21.53 / Original nightly ZP; uncorrected for extinctionMAGZPT  =    21.40896253966641 / Re-calibrated DR2 zeropoint                    EXPTIME =               120.02 / [sec] Exposure time assumed by the pipeline    CHECKSUM= '7RREBPRB9PRBAPRB'   / HDU checksum updated 2014-02-06T12:02:07       DATASUM = '1660673036'         / data unit checksum updated 2014-02-06T12:02:07 HISTORY 20041004 14:45:42                                                       HISTORY    $Id: cir_create_file.c,v 1.10 2004/09/03 10:48:45 jim Exp $          HISTORY 20041004 14:45:43                                                       HISTORY    $Id: cir_ccdproc.c,v 1.9 2004/09/07 14:18:51 jim Exp $               HISTORY 20041004 22:52:54                                                       HISTORY    $Id: cir_imcore.c,v 1.11 2004/09/07 14:18:52 jim Exp $               HISTORY 20041004 22:52:56                                                       HISTORY    $Id: cir_platesol.c,v 1.9 2004/09/07 14:18:54 jim Exp $              HISTORY 20041005 16:05:06                                                       HISTORY    $Id: cir_imcore.c,v 1.11 2004/09/07 14:18:52 jim Exp $               HISTORY 20041006 07:31:07                                                       HISTORY    $Id: cir_platesol.c,v 1.9 2004/09/07 14:18:54 jim Exp $              HISTORY 20131220 22:36:15                                                       HISTORY     Headers updated by Geert Barentsen as part of DR2.                  HISTORY     This included changes to MAGZPT, EXPTIME and the WCS.               COMMENT Calibration info                                                        COMMENT ================                                                        COMMENT The MAGZPT keyword in this header has been corrected for atmospheric    COMMENT extinction and gain (PERCORR) and has been re-calibrated as part of DR2.COMMENT                                                                         COMMENT Hence to obtain calibrated magnitudes relative to Vega, use:            COMMENT     mag(Vega) = MAGZPT - 2.5*log(pixel value / EXPTIME)                 END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             