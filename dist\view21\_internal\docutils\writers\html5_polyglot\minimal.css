/* Minimal style sheet for the HTML output of Docutils.                    */
/*                                                                         */
/* :Author: <PERSON><PERSON><PERSON>, based on html4css1.css by <PERSON>          */
/* :Id: $Id: minimal.css 8642 2021-03-26 13:51:14Z milde $               */
/* :Copyright: © 2015 Günter Milde.                                        */
/* :License: Released under the terms of the `2-Clause BSD license`_,      */
/*    in short:                                                            */
/*                                                                         */
/*    Copying and distribution of this file, with or without modification, */
/*    are permitted in any medium without royalty provided the copyright   */
/*    notice and this notice are preserved.                                */
/*                                                                         */
/*    This file is offered as-is, without any warranty.                    */
/*                                                                         */
/* .. _2-Clause BSD license: http://www.spdx.org/licenses/BSD-2-Clause     */

/* This CSS2.1_ stylesheet defines rules for Docutils elements without    */
/* HTML equivalent. It is required to make the document semantic visible. */
/*                                                                        */
/* .. _CSS2.1: http://www.w3.org/TR/CSS2                                  */
/* .. _validates: http://jigsaw.w3.org/css-validator/validator$link       */

/* titles */
p.topic-title,
p.admonition-title,
p.system-message-title {
  font-weight: bold;
}
p.sidebar-title,
p.rubric {
  font-weight: bold;
  font-size: larger;
}
p.rubric {
  color: maroon;
}
p.subtitle,
p.section-subtitle,
p.sidebar-subtitle {
  font-weight: bold;
  margin-top: -0.5em;
}
h1 + p.subtitle {
  font-size: 1.6em;
}
h2 + p.section-subtitle, a.toc-backref {
  color: black;
  text-decoration: none;
}

/* Warnings, Errors */
.system-messages h2,
.system-message-title,
span.problematic {
  color: red;
}

/* Inline Literals */
.docutils.literal {
  font-family: monospace;
  white-space: pre-wrap;
}
/* do not wrap at hyphens and similar: */
.literal > span.pre { white-space: nowrap; }

/* Lists */

/* compact and simple lists: no margin between items */
.simple  li, .simple  ul, .simple  ol,
.compact li, .compact ul, .compact ol,
.simple  > li p, dl.simple  > dd,
.compact > li p, dl.compact > dd {
  margin-top: 0;
  margin-bottom: 0;
}
/* Nested Paragraphs */
p:first-child { margin-top: 0; }
p:last-child { margin-bottom: 0; }
td > p, th > p { margin-bottom: 0; }

/* Table of Contents */
.topic.contents { margin: 0.5em 0; }
.topic.contents ul.auto-toc {
  list-style-type: none;
  padding-left: 1.5em;
}

/* Enumerated Lists */
ol.arabic     { list-style: decimal }
ol.loweralpha { list-style: lower-alpha }
ol.upperalpha { list-style: upper-alpha }
ol.lowerroman { list-style: lower-roman }
ol.upperroman { list-style: upper-roman }

/* Definition Lists and Derivatives */
dt .classifier { font-style: italic }
dt .classifier:before {
  font-style: normal;
  margin: 0.5em;
  content: ":";
}
/* Field Lists and similar */
/* bold field name, content starts on the same line */
dl.field-list > dt,
dl.option-list > dt,
dl.docinfo > dt,
dl.footnote > dt,
dl.citation > dt {
  font-weight: bold;
  clear: left;
  float: left;
  margin: 0;
  padding: 0;
  padding-right: 0.5em;
}
/* Offset for field content (corresponds to the --field-name-limit option) */
dl.field-list > dd,
dl.option-list > dd,
dl.docinfo > dd {
  margin-left:  9em; /* ca. 14 chars in the test examples, fit all Docinfo fields */
}
/* start field-body on a new line after long field names */
dl.field-list > dd > *:first-child,
dl.option-list > dd > *:first-child
{
  display: inline-block;
  width: 100%;
  margin: 0;
}
/* field names followed by a colon */
dl.field-list > dt:after,
dl.docinfo > dt:after {
  content: ":";
}

/* Bibliographic Fields (docinfo) */
dl.docinfo pre.address {
  font: inherit;
  margin: 0.5em 0;
}
dl.docinfo > dd.authors > p { margin: 0; }

/* Option Lists */
dl.option-list > dt { font-weight: normal; }
span.option { white-space: nowrap; }

/* Footnotes and Citations  */
dl.footnote.superscript > dd { margin-left: 1em; }
dl.footnote.brackets > dd { margin-left: 2em; }
dl.footnote > dt { font-weight: normal; }
a.footnote-reference.brackets:before,
dt.label > span.brackets:before { content: "["; }
a.footnote-reference.brackets:after,
dt.label > span.brackets:after { content: "]"; }
a.footnote-reference.superscript,
dl.footnote.superscript > dt.label {
  vertical-align: super;
  font-size: small;
}
dt.label > span.fn-backref {
  margin-left: 0.2em;
  font-weight: normal;
}
dt.label > span.fn-backref > a { font-style: italic; }

/* Alignment */
.align-left   {
  text-align: left;
  margin-right: auto;
}
.align-center {
  clear: both;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}
.align-right  {
  text-align: right;
  margin-left: auto;
}
.align-top    { vertical-align: top; }
.align-middle { vertical-align: middle; }
.align-bottom { vertical-align: bottom; }

/* reset inner alignment in figures and tables */
figure.align-left, figure.align-right,
table.align-left, table.align-center, table.align-right {
  text-align: inherit;
}

/* Text Blocks */
blockquote,
div.topic,
aside.topic {
  margin: 1em 2em;
}
.sidebar,
.admonition,
.system-message {
  border: thin solid;
  margin: 1em 2em;
  padding: 0.5em 1em;
}
.sidebar {
  width: 30%;
  max-width: 26em;
  float: right;
  clear: right;
}
div.line-block { display: block; }
div.line-block div.line-block, pre { margin-left: 2em; }

/* Code line numbers: dropped when copying text from the page */
pre.code .ln { display: none; }
pre.code code:before {
  content: attr(data-lineno); /* …, none) fallback not supported by any browser */
  color: gray;
}

/* Tables */
table {
  border-collapse: collapse;
}
td, th {
  border: thin solid silver;
  padding: 0 1ex;
}
.borderless td, .borderless th {
  border: 0;
  padding: 0;
  padding-right: 0.5em /* separate table cells */
}

table > caption {
  text-align: left;
  margin-top: 0.2em;
  margin-bottom: 0.2em;
}
table.captionbelow {
  caption-side: bottom;
}

/* Document Header and Footer */
header { border-bottom: 1px solid black; }
footer { border-top: 1px solid black; }

/* Images are block-level by default in Docutils */
/* New HTML5 block elements: set display for older browsers */
img, header, section, footer, aside, nav, main, article, figure, video {
  display: block;
}
/* inline images */
p img, p video, figure img, figure video {
  display: inline;
}
