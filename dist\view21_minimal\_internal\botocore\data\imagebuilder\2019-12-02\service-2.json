{"version": "2.0", "metadata": {"apiVersion": "2019-12-02", "endpointPrefix": "imagebuilder", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "imagebuilder", "serviceFullName": "EC2 Image Builder", "serviceId": "imagebuilder", "signatureVersion": "v4", "signingName": "imagebuilder", "uid": "imagebuilder-2019-12-02"}, "operations": {"CancelImageCreation": {"name": "CancelImageCreation", "http": {"method": "PUT", "requestUri": "/CancelImageCreation"}, "input": {"shape": "CancelImageCreationRequest"}, "output": {"shape": "CancelImageCreationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>CancelImageCreation cancels the creation of Image. This operation can only be used on images in a non-terminal state.</p>"}, "CreateComponent": {"name": "CreateComponent", "http": {"method": "PUT", "requestUri": "/CreateComponent"}, "input": {"shape": "CreateComponentRequest"}, "output": {"shape": "CreateComponentResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "InvalidVersionNumberException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new component that can be used to build, validate, test, and assess your image. The component is based on a YAML document that you specify using exactly one of the following methods:</p> <ul> <li> <p>Inline, using the <code>data</code> property in the request body.</p> </li> <li> <p>A URL that points to a YAML document file stored in Amazon S3, using the <code>uri</code> property in the request body.</p> </li> </ul>"}, "CreateContainerRecipe": {"name": "CreateContainerRecipe", "http": {"method": "PUT", "requestUri": "/CreateContainerRecipe"}, "input": {"shape": "CreateContainerRecipeRequest"}, "output": {"shape": "CreateContainerRecipeResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "InvalidVersionNumberException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new container recipe. Container recipes define how images are configured, tested, and assessed.</p>"}, "CreateDistributionConfiguration": {"name": "CreateDistributionConfiguration", "http": {"method": "PUT", "requestUri": "/CreateDistributionConfiguration"}, "input": {"shape": "CreateDistributionConfigurationRequest"}, "output": {"shape": "CreateDistributionConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new distribution configuration. Distribution configurations define and configure the outputs of your pipeline.</p>"}, "CreateImage": {"name": "CreateImage", "http": {"method": "PUT", "requestUri": "/CreateImage"}, "input": {"shape": "CreateImageRequest"}, "output": {"shape": "CreateImageResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new image. This request will create a new image along with all of the configured output resources defined in the distribution configuration. You must specify exactly one recipe for your image, using either a ContainerRecipeArn or an ImageRecipeArn.</p>"}, "CreateImagePipeline": {"name": "CreateImagePipeline", "http": {"method": "PUT", "requestUri": "/CreateImagePipeline"}, "input": {"shape": "CreateImagePipelineRequest"}, "output": {"shape": "CreateImagePipelineResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new image pipeline. Image pipelines enable you to automate the creation and distribution of images.</p>"}, "CreateImageRecipe": {"name": "CreateImageRecipe", "http": {"method": "PUT", "requestUri": "/CreateImageRecipe"}, "input": {"shape": "CreateImageRecipeRequest"}, "output": {"shape": "CreateImageRecipeResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "InvalidVersionNumberException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new image recipe. Image recipes define how images are configured, tested, and assessed.</p>"}, "CreateInfrastructureConfiguration": {"name": "CreateInfrastructureConfiguration", "http": {"method": "PUT", "requestUri": "/CreateInfrastructureConfiguration"}, "input": {"shape": "CreateInfrastructureConfigurationRequest"}, "output": {"shape": "CreateInfrastructureConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new infrastructure configuration. An infrastructure configuration defines the environment in which your image will be built and tested.</p>"}, "DeleteComponent": {"name": "DeleteComponent", "http": {"method": "DELETE", "requestUri": "/DeleteComponent"}, "input": {"shape": "DeleteComponentRequest"}, "output": {"shape": "DeleteComponentResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes a component build version.</p>"}, "DeleteContainerRecipe": {"name": "DeleteContainerRecipe", "http": {"method": "DELETE", "requestUri": "/DeleteContainerRecipe"}, "input": {"shape": "DeleteContainerRecipeRequest"}, "output": {"shape": "DeleteContainerRecipeResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes a container recipe.</p>"}, "DeleteDistributionConfiguration": {"name": "DeleteDistributionConfiguration", "http": {"method": "DELETE", "requestUri": "/DeleteDistributionConfiguration"}, "input": {"shape": "DeleteDistributionConfigurationRequest"}, "output": {"shape": "DeleteDistributionConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes a distribution configuration.</p>"}, "DeleteImage": {"name": "DeleteImage", "http": {"method": "DELETE", "requestUri": "/DeleteImage"}, "input": {"shape": "DeleteImageRequest"}, "output": {"shape": "DeleteImageResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes an Image Builder image resource. This does not delete any EC2 AMIs or ECR container images that are created during the image build process. You must clean those up separately, using the appropriate Amazon EC2 or Amazon ECR console actions, or API or CLI commands.</p> <ul> <li> <p>To deregister an EC2 Linux AMI, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/deregister-ami.html\">Deregister your Linux AMI</a> in the <i> <i>Amazon EC2 User Guide</i> </i>.</p> </li> <li> <p>To deregister an EC2 Windows AMI, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/deregister-ami.html\">Deregister your Windows AMI</a> in the <i> <i>Amazon EC2 Windows Guide</i> </i>.</p> </li> <li> <p>To delete a container image from Amazon ECR, see <a href=\"https://docs.aws.amazon.com/AmazonECR/latest/userguide/delete_image.html\">Deleting an image</a> in the <i>Amazon ECR User Guide</i>.</p> </li> </ul>"}, "DeleteImagePipeline": {"name": "DeleteImagePipeline", "http": {"method": "DELETE", "requestUri": "/DeleteImagePipeline"}, "input": {"shape": "DeleteImagePipelineRequest"}, "output": {"shape": "DeleteImagePipelineResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes an image pipeline.</p>"}, "DeleteImageRecipe": {"name": "DeleteImageRecipe", "http": {"method": "DELETE", "requestUri": "/DeleteImageRecipe"}, "input": {"shape": "DeleteImageRecipeRequest"}, "output": {"shape": "DeleteImageRecipeResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes an image recipe.</p>"}, "DeleteInfrastructureConfiguration": {"name": "DeleteInfrastructureConfiguration", "http": {"method": "DELETE", "requestUri": "/DeleteInfrastructureConfiguration"}, "input": {"shape": "DeleteInfrastructureConfigurationRequest"}, "output": {"shape": "DeleteInfrastructureConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceDependencyException"}], "documentation": "<p>Deletes an infrastructure configuration.</p>"}, "GetComponent": {"name": "GetComponent", "http": {"method": "GET", "requestUri": "/GetComponent"}, "input": {"shape": "GetComponentRequest"}, "output": {"shape": "GetComponentResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets a component object.</p>"}, "GetComponentPolicy": {"name": "GetComponentPolicy", "http": {"method": "GET", "requestUri": "/GetComponentPolicy"}, "input": {"shape": "GetComponentPolicyRequest"}, "output": {"shape": "GetComponentPolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets a component policy.</p>"}, "GetContainerRecipe": {"name": "GetContainerRecipe", "http": {"method": "GET", "requestUri": "/GetContainerRecipe"}, "input": {"shape": "GetContainerRecipeRequest"}, "output": {"shape": "GetContainerRecipeResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Retrieves a container recipe.</p>"}, "GetContainerRecipePolicy": {"name": "GetContainerRecipePolicy", "http": {"method": "GET", "requestUri": "/GetContainerRecipePolicy"}, "input": {"shape": "GetContainerRecipePolicyRequest"}, "output": {"shape": "GetContainerRecipePolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Retrieves the policy for a container recipe.</p>"}, "GetDistributionConfiguration": {"name": "GetDistributionConfiguration", "http": {"method": "GET", "requestUri": "/GetDistributionConfiguration"}, "input": {"shape": "GetDistributionConfigurationRequest"}, "output": {"shape": "GetDistributionConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets a distribution configuration.</p>"}, "GetImage": {"name": "GetImage", "http": {"method": "GET", "requestUri": "/GetImage"}, "input": {"shape": "GetImageRequest"}, "output": {"shape": "GetImageResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets an image.</p>"}, "GetImagePipeline": {"name": "GetImagePipeline", "http": {"method": "GET", "requestUri": "/GetImagePipeline"}, "input": {"shape": "GetImagePipelineRequest"}, "output": {"shape": "GetImagePipelineResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets an image pipeline.</p>"}, "GetImagePolicy": {"name": "GetImagePolicy", "http": {"method": "GET", "requestUri": "/GetImagePolicy"}, "input": {"shape": "GetImagePolicyRequest"}, "output": {"shape": "GetImagePolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets an image policy.</p>"}, "GetImageRecipe": {"name": "GetImageRecipe", "http": {"method": "GET", "requestUri": "/GetImageRecipe"}, "input": {"shape": "GetImageRecipeRequest"}, "output": {"shape": "GetImageRecipeResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets an image recipe.</p>"}, "GetImageRecipePolicy": {"name": "GetImageRecipePolicy", "http": {"method": "GET", "requestUri": "/GetImageRecipePolicy"}, "input": {"shape": "GetImageRecipePolicyRequest"}, "output": {"shape": "GetImageRecipePolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets an image recipe policy.</p>"}, "GetInfrastructureConfiguration": {"name": "GetInfrastructureConfiguration", "http": {"method": "GET", "requestUri": "/GetInfrastructureConfiguration"}, "input": {"shape": "GetInfrastructureConfigurationRequest"}, "output": {"shape": "GetInfrastructureConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Gets an infrastructure configuration.</p>"}, "GetWorkflowExecution": {"name": "GetWorkflowExecution", "http": {"method": "GET", "requestUri": "/GetWorkflowExecution"}, "input": {"shape": "GetWorkflowExecutionRequest"}, "output": {"shape": "GetWorkflowExecutionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Get the runtime information that was logged for a specific runtime instance of the workflow.</p>"}, "GetWorkflowStepExecution": {"name": "GetWorkflowStepExecution", "http": {"method": "GET", "requestUri": "/GetWorkflowStepExecution"}, "input": {"shape": "GetWorkflowStepExecutionRequest"}, "output": {"shape": "GetWorkflowStepExecutionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Get the runtime information that was logged for a specific runtime instance of the workflow step.</p>"}, "ImportComponent": {"name": "ImportComponent", "http": {"method": "PUT", "requestUri": "/ImportComponent"}, "input": {"shape": "ImportComponentRequest"}, "output": {"shape": "ImportComponentResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "InvalidVersionNumberException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Imports a component and transforms its data into a component document.</p>"}, "ImportVmImage": {"name": "ImportVmImage", "http": {"method": "PUT", "requestUri": "/ImportVmImage"}, "input": {"shape": "ImportVmImageRequest"}, "output": {"shape": "ImportVmImageResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>When you export your virtual machine (VM) from its virtualization environment, that process creates a set of one or more disk container files that act as snapshots of your VM’s environment, settings, and data. The Amazon EC2 API <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_ImportImage.html\">ImportImage</a> action uses those files to import your VM and create an AMI. To import using the CLI command, see <a href=\"https://docs.aws.amazon.com/cli/latest/reference/ec2/import-image.html\">import-image</a> </p> <p>You can reference the task ID from the VM import to pull in the AMI that the import created as the base image for your Image Builder recipe.</p>"}, "ListComponentBuildVersions": {"name": "ListComponentBuildVersions", "http": {"method": "POST", "requestUri": "/ListComponentBuildVersions"}, "input": {"shape": "ListComponentBuildVersionsRequest"}, "output": {"shape": "ListComponentBuildVersionsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns the list of component build versions for the specified semantic version.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "ListComponents": {"name": "ListComponents", "http": {"method": "POST", "requestUri": "/ListComponents"}, "input": {"shape": "ListComponentsRequest"}, "output": {"shape": "ListComponentsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns the list of components that can be filtered by name, or by using the listed <code>filters</code> to streamline results. Newly created components can take up to two minutes to appear in the ListComponents API Results.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "ListContainerRecipes": {"name": "ListContainerRecipes", "http": {"method": "POST", "requestUri": "/ListContainerRecipes"}, "input": {"shape": "ListContainerRecipesRequest"}, "output": {"shape": "ListContainerRecipesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of container recipes.</p>"}, "ListDistributionConfigurations": {"name": "ListDistributionConfigurations", "http": {"method": "POST", "requestUri": "/ListDistributionConfigurations"}, "input": {"shape": "ListDistributionConfigurationsRequest"}, "output": {"shape": "ListDistributionConfigurationsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of distribution configurations.</p>"}, "ListImageBuildVersions": {"name": "ListImageBuildVersions", "http": {"method": "POST", "requestUri": "/ListImageBuildVersions"}, "input": {"shape": "ListImageBuildVersionsRequest"}, "output": {"shape": "ListImageBuildVersionsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of image build versions.</p>"}, "ListImagePackages": {"name": "ListImagePackages", "http": {"method": "POST", "requestUri": "/ListImagePackages"}, "input": {"shape": "ListImagePackagesRequest"}, "output": {"shape": "ListImagePackagesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>List the Packages that are associated with an Image Build Version, as determined by Amazon Web Services Systems Manager Inventory at build time.</p>"}, "ListImagePipelineImages": {"name": "ListImagePipelineImages", "http": {"method": "POST", "requestUri": "/ListImagePipelineImages"}, "input": {"shape": "ListImagePipelineImagesRequest"}, "output": {"shape": "ListImagePipelineImagesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of images created by the specified pipeline.</p>"}, "ListImagePipelines": {"name": "ListImagePipelines", "http": {"method": "POST", "requestUri": "/ListImagePipelines"}, "input": {"shape": "ListImagePipelinesRequest"}, "output": {"shape": "ListImagePipelinesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of image pipelines.</p>"}, "ListImageRecipes": {"name": "ListImageRecipes", "http": {"method": "POST", "requestUri": "/ListImageRecipes"}, "input": {"shape": "ListImageRecipesRequest"}, "output": {"shape": "ListImageRecipesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of image recipes.</p>"}, "ListImageScanFindingAggregations": {"name": "ListImageScanFindingAggregations", "http": {"method": "POST", "requestUri": "/ListImageScanFindingAggregations"}, "input": {"shape": "ListImageScanFindingAggregationsRequest"}, "output": {"shape": "ListImageScanFindingAggregationsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of image scan aggregations for your account. You can filter by the type of key that Image Builder uses to group results. For example, if you want to get a list of findings by severity level for one of your pipelines, you might specify your pipeline with the <code>imagePipelineArn</code> filter. If you don't specify a filter, Image Builder returns an aggregation for your account.</p> <p>To streamline results, you can use the following filters in your request:</p> <ul> <li> <p> <code>accountId</code> </p> </li> <li> <p> <code>imageBuildVersionArn</code> </p> </li> <li> <p> <code>imagePipelineArn</code> </p> </li> <li> <p> <code>vulnerabilityId</code> </p> </li> </ul>"}, "ListImageScanFindings": {"name": "ListImageScanFindings", "http": {"method": "POST", "requestUri": "/ListImageScanFindings"}, "input": {"shape": "ListImageScanFindingsRequest"}, "output": {"shape": "ListImageScanFindingsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of image scan findings for your account.</p>"}, "ListImages": {"name": "ListImages", "http": {"method": "POST", "requestUri": "/ListImages"}, "input": {"shape": "ListImagesRequest"}, "output": {"shape": "ListImagesResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns the list of images that you have access to. Newly created images can take up to two minutes to appear in the ListImages API Results.</p>"}, "ListInfrastructureConfigurations": {"name": "ListInfrastructureConfigurations", "http": {"method": "POST", "requestUri": "/ListInfrastructureConfigurations"}, "input": {"shape": "ListInfrastructureConfigurationsRequest"}, "output": {"shape": "ListInfrastructureConfigurationsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of infrastructure configurations.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the list of tags for the specified resource.</p>"}, "ListWorkflowExecutions": {"name": "ListWorkflowExecutions", "http": {"method": "POST", "requestUri": "/ListWorkflowExecutions"}, "input": {"shape": "ListWorkflowExecutionsRequest"}, "output": {"shape": "ListWorkflowExecutionsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Returns a list of workflow runtime instance metadata objects for a specific image build version.</p>"}, "ListWorkflowStepExecutions": {"name": "ListWorkflowStepExecutions", "http": {"method": "POST", "requestUri": "/ListWorkflowStepExecutions"}, "input": {"shape": "ListWorkflowStepExecutionsRequest"}, "output": {"shape": "ListWorkflowStepExecutionsResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidPaginationTokenException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Shows runtime data for each step in a runtime instance of the workflow that you specify in the request.</p>"}, "PutComponentPolicy": {"name": "PutComponentPolicy", "http": {"method": "PUT", "requestUri": "/PutComponentPolicy"}, "input": {"shape": "PutComponentPolicyRequest"}, "output": {"shape": "PutComponentPolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Applies a policy to a component. We recommend that you call the RAM API <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_CreateResourceShare.html\">CreateResourceShare</a> to share resources. If you call the Image Builder API <code>PutComponentPolicy</code>, you must also call the RAM API <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_PromoteResourceShareCreatedFromPolicy.html\">PromoteResourceShareCreatedFromPolicy</a> in order for the resource to be visible to all principals with whom the resource is shared.</p>"}, "PutContainerRecipePolicy": {"name": "PutContainerRecipePolicy", "http": {"method": "PUT", "requestUri": "/PutContainerRecipePolicy"}, "input": {"shape": "PutContainerRecipePolicyRequest"}, "output": {"shape": "PutContainerRecipePolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Applies a policy to a container image. We recommend that you call the RAM API CreateResourceShare (https://docs.aws.amazon.com//ram/latest/APIReference/API_CreateResourceShare.html) to share resources. If you call the Image Builder API <code>PutContainerImagePolicy</code>, you must also call the RAM API PromoteResourceShareCreatedFromPolicy (https://docs.aws.amazon.com//ram/latest/APIReference/API_PromoteResourceShareCreatedFromPolicy.html) in order for the resource to be visible to all principals with whom the resource is shared.</p>"}, "PutImagePolicy": {"name": "PutImagePolicy", "http": {"method": "PUT", "requestUri": "/PutImagePolicy"}, "input": {"shape": "PutImagePolicyRequest"}, "output": {"shape": "PutImagePolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Applies a policy to an image. We recommend that you call the RAM API <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_CreateResourceShare.html\">CreateResourceShare</a> to share resources. If you call the Image Builder API <code>PutImagePolicy</code>, you must also call the RAM API <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_PromoteResourceShareCreatedFromPolicy.html\">PromoteResourceShareCreatedFromPolicy</a> in order for the resource to be visible to all principals with whom the resource is shared.</p>"}, "PutImageRecipePolicy": {"name": "PutImageRecipePolicy", "http": {"method": "PUT", "requestUri": "/PutImageRecipePolicy"}, "input": {"shape": "PutImageRecipePolicyRequest"}, "output": {"shape": "PutImageRecipePolicyResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}], "documentation": "<p>Applies a policy to an image recipe. We recommend that you call the RAM API <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_CreateResourceShare.html\">CreateResourceShare</a> to share resources. If you call the Image Builder API <code>PutImageRecipePolicy</code>, you must also call the RAM API <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_PromoteResourceShareCreatedFromPolicy.html\">PromoteResourceShareCreatedFromPolicy</a> in order for the resource to be visible to all principals with whom the resource is shared.</p>"}, "StartImagePipelineExecution": {"name": "StartImagePipelineExecution", "http": {"method": "PUT", "requestUri": "/StartImagePipelineExecution"}, "input": {"shape": "StartImagePipelineExecutionRequest"}, "output": {"shape": "StartImagePipelineExecutionResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Manually triggers a pipeline to create an image.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds a tag to a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes a tag from a resource.</p>"}, "UpdateDistributionConfiguration": {"name": "UpdateDistributionConfiguration", "http": {"method": "PUT", "requestUri": "/UpdateDistributionConfiguration"}, "input": {"shape": "UpdateDistributionConfigurationRequest"}, "output": {"shape": "UpdateDistributionConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Updates a new distribution configuration. Distribution configurations define and configure the outputs of your pipeline.</p>"}, "UpdateImagePipeline": {"name": "UpdateImagePipeline", "http": {"method": "PUT", "requestUri": "/UpdateImagePipeline"}, "input": {"shape": "UpdateImagePipelineRequest"}, "output": {"shape": "UpdateImagePipelineResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Updates an image pipeline. Image pipelines enable you to automate the creation and distribution of images.</p> <note> <p>UpdateImagePipeline does not support selective updates for the pipeline. You must specify all of the required properties in the update request, not just the properties that have changed.</p> </note>"}, "UpdateInfrastructureConfiguration": {"name": "UpdateInfrastructureConfiguration", "http": {"method": "PUT", "requestUri": "/UpdateInfrastructureConfiguration"}, "input": {"shape": "UpdateInfrastructureConfigurationRequest"}, "output": {"shape": "UpdateInfrastructureConfigurationResponse"}, "errors": [{"shape": "ServiceException"}, {"shape": "ClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "ForbiddenException"}, {"shape": "CallRateLimitExceededException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Updates a new infrastructure configuration. An infrastructure configuration defines the environment in which your image will be built and tested.</p>"}}, "shapes": {"AccountAggregation": {"type": "structure", "members": {"accountId": {"shape": "NonEmptyString", "documentation": "<p>Identifies the account that owns the aggregated resource findings.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>Counts by severity level for medium severity and higher level findings, plus a total for all of the findings.</p>"}}, "documentation": "<p>Contains counts of vulnerability findings from image scans that run when you create new Image Builder images, or build new versions of existing images. The vulnerability counts are grouped by severity level. The counts are aggregated across resources to create the final tally for the account that owns them.</p>"}, "AccountId": {"type": "string", "pattern": "^[0-9]{12}$"}, "AccountList": {"type": "list", "member": {"shape": "AccountId"}, "max": 1536, "min": 1}, "AdditionalInstanceConfiguration": {"type": "structure", "members": {"systemsManagerAgent": {"shape": "SystemsManagerAgent", "documentation": "<p>Contains settings for the Systems Manager agent on your build instance.</p>"}, "userDataOverride": {"shape": "UserDataOverride", "documentation": "<p>Use this property to provide commands or a command script to run when you launch your build instance.</p> <p>The userDataOverride property replaces any commands that Image Builder might have added to ensure that Systems Manager is installed on your Linux build instance. If you override the user data, make sure that you add commands to install Systems Manager, if it is not pre-installed on your base image.</p> <note> <p>The user data is always base 64 encoded. For example, the following commands are encoded as <code>IyEvYmluL2Jhc2gKbWtkaXIgLXAgL3Zhci9iYi8KdG91Y2ggL3Zhci$</code>:</p> <p> <i>#!/bin/bash</i> </p> <p>mkdir -p /var/bb/</p> <p>touch /var</p> </note>"}}, "documentation": "<p>In addition to your infrastructure configuration, these settings provide an extra layer of control over your build instances. You can also specify commands to run on launch for all of your build instances.</p> <p>Image Builder does not automatically install the Systems Manager agent on Windows instances. If your base image includes the Systems Manager agent, then the AMI that you create will also include the agent. For Linux instances, if the base image does not already include the Systems Manager agent, Image Builder installs it. For Linux instances where Image Builder installs the Systems Manager agent, you can choose whether to keep it for the AMI that you create.</p>"}, "Ami": {"type": "structure", "members": {"region": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Web Services Region of the Amazon EC2 AMI.</p>"}, "image": {"shape": "NonEmptyString", "documentation": "<p>The AMI ID of the Amazon EC2 AMI.</p>"}, "name": {"shape": "NonEmptyString", "documentation": "<p>The name of the Amazon EC2 AMI.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the Amazon EC2 AMI. Minimum and maximum length are in characters.</p>"}, "state": {"shape": "ImageState"}, "accountId": {"shape": "NonEmptyString", "documentation": "<p>The account ID of the owner of the AMI.</p>"}}, "documentation": "<p>Details of an Amazon EC2 AMI.</p>"}, "AmiDistributionConfiguration": {"type": "structure", "members": {"name": {"shape": "AmiNameString", "documentation": "<p>The name of the output AMI.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the AMI distribution configuration. Minimum and maximum length are in characters.</p>"}, "targetAccountIds": {"shape": "AccountList", "documentation": "<p>The ID of an account to which you want to distribute an image.</p>"}, "amiTags": {"shape": "TagMap", "documentation": "<p>The tags to apply to AMIs distributed to this Region.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>The KMS key identifier used to encrypt the distributed image.</p>"}, "launchPermission": {"shape": "LaunchPermissionConfiguration", "documentation": "<p>Launch permissions can be used to configure which Amazon Web Services accounts can use the AMI to launch instances.</p>"}}, "documentation": "<p>Define and configure the output AMIs of the pipeline.</p>"}, "AmiList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "AmiNameString": {"type": "string", "max": 127, "min": 1, "pattern": "^[-_A-Za-z0-9{][-_A-Za-z0-9\\s:{}\\.]+[-_A-Za-z0-9}]$"}, "Arn": {"type": "string"}, "Boolean": {"type": "boolean"}, "BuildType": {"type": "string", "enum": ["USER_INITIATED", "SCHEDULED", "IMPORT"]}, "CallRateLimitExceededException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have exceeded the permitted request rate for the specific operation.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "CancelImageCreationRequest": {"type": "structure", "required": ["imageBuildVersionArn", "clientToken"], "members": {"imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image that you want to cancel creation for.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier you provide to ensure idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a> in the <i>Amazon EC2 API Reference</i>.</p>", "idempotencyToken": true}}}, "CancelImageCreationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token that was used for this request.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The ARN of the image whose creation this request canceled.</p>"}}}, "ClientException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>These errors are usually caused by a client action, such as using an action or resource on behalf of a user that doesn't have permissions to use the action or resource, or specifying an invalid resource identifier.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ClientToken": {"type": "string", "max": 36, "min": 1}, "Component": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The version of the component.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the component.</p>"}, "changeDescription": {"shape": "NonEmptyString", "documentation": "<p>The change description of the component.</p>"}, "type": {"shape": "ComponentType", "documentation": "<p>The component type specifies whether Image Builder uses the component to build the image or only to test it.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The operating system platform of the component.</p>"}, "supportedOsVersions": {"shape": "OsVersionList", "documentation": "<p>The operating system (OS) version supported by the component. If the OS information is available, Image Builder performs a prefix match against the base image OS version during image recipe creation.</p>"}, "state": {"shape": "ComponentState", "documentation": "<p>Describes the current status of the component. This is used for components that are no longer active.</p>"}, "parameters": {"shape": "ComponentParameterDetailList", "documentation": "<p>Contains parameter details for each of the parameters that the component document defined for the component.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the component.</p>"}, "data": {"shape": "ComponentData", "documentation": "<p>Component data contains the YAML document content for the component.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>The KMS key identifier used to encrypt the component.</p>"}, "encrypted": {"shape": "NullableBoolean", "documentation": "<p>The encryption status of the component.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date that Image Builder created the component.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that apply to the component.</p>"}, "publisher": {"shape": "NonEmptyString", "documentation": "<p>Contains the name of the publisher if this is a third-party component. Otherwise, this property is empty.</p>"}, "obfuscate": {"shape": "Boolean", "documentation": "<p>Indicates whether component source is hidden from view in the console, and from component detail results for API, CLI, or SDK operations.</p>"}}, "documentation": "<p>A detailed view of a component.</p>"}, "ComponentBuildVersionArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):component/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$"}, "ComponentConfiguration": {"type": "structure", "required": ["componentArn"], "members": {"componentArn": {"shape": "ComponentVersionArnOrBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p>"}, "parameters": {"shape": "ComponentParameterList", "documentation": "<p>A group of parameter settings that Image Builder uses to configure the component for a specific recipe.</p>"}}, "documentation": "<p>Configuration details of the component.</p>"}, "ComponentConfigurationList": {"type": "list", "member": {"shape": "ComponentConfiguration"}, "min": 1}, "ComponentData": {"type": "string"}, "ComponentFormat": {"type": "string", "enum": ["SHELL"]}, "ComponentParameter": {"type": "structure", "required": ["name", "value"], "members": {"name": {"shape": "ComponentParameterName", "documentation": "<p>The name of the component parameter to set.</p>"}, "value": {"shape": "ComponentParameterValueList", "documentation": "<p>Sets the value for the named component parameter.</p>"}}, "documentation": "<p>Contains a key/value pair that sets the named component parameter.</p>"}, "ComponentParameterDescription": {"type": "string", "max": 1024, "min": 1, "pattern": "[^\\x00]+"}, "ComponentParameterDetail": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "ComponentParameterName", "documentation": "<p>The name of this input parameter.</p>"}, "type": {"shape": "ComponentParameterType", "documentation": "<p>The type of input this parameter provides. The currently supported value is \"string\".</p>"}, "defaultValue": {"shape": "ComponentParameterValueList", "documentation": "<p>The default value of this parameter if no input is provided.</p>"}, "description": {"shape": "ComponentParameterDescription", "documentation": "<p>Describes this parameter.</p>"}}, "documentation": "<p>Defines a parameter that is used to provide configuration details for the component.</p>"}, "ComponentParameterDetailList": {"type": "list", "member": {"shape": "ComponentParameterDetail"}}, "ComponentParameterList": {"type": "list", "member": {"shape": "ComponentParameter"}, "min": 1}, "ComponentParameterName": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\x00]+"}, "ComponentParameterType": {"type": "string", "max": 20, "min": 1, "pattern": "^String|Integer|Boolean|StringList$"}, "ComponentParameterValue": {"type": "string", "min": 0, "pattern": "[^\\x00]*"}, "ComponentParameterValueList": {"type": "list", "member": {"shape": "ComponentParameterValue"}}, "ComponentState": {"type": "structure", "members": {"status": {"shape": "ComponentStatus", "documentation": "<p>The current state of the component.</p>"}, "reason": {"shape": "NonEmptyString", "documentation": "<p>Describes how or why the component changed state.</p>"}}, "documentation": "<p>A group of fields that describe the current status of components that are no longer active.</p>"}, "ComponentStatus": {"type": "string", "enum": ["DEPRECATED"]}, "ComponentSummary": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The version of the component.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The operating system platform of the component.</p>"}, "supportedOsVersions": {"shape": "OsVersionList", "documentation": "<p>The operating system (OS) version that the component supports. If the OS information is available, Image Builder performs a prefix match against the base image OS version during image recipe creation.</p>"}, "state": {"shape": "ComponentState", "documentation": "<p>Describes the current status of the component.</p>"}, "type": {"shape": "ComponentType", "documentation": "<p>The component type specifies whether Image Builder uses the component to build the image or only to test it.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the component.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the component.</p>"}, "changeDescription": {"shape": "NonEmptyString", "documentation": "<p>The change description for the current version of the component.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The original creation date of the component.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that apply to the component.</p>"}, "publisher": {"shape": "NonEmptyString", "documentation": "<p>Contains the name of the publisher if this is a third-party component. Otherwise, this property is empty.</p>"}, "obfuscate": {"shape": "Boolean", "documentation": "<p>Indicates whether component source is hidden from view in the console, and from component detail results for API, CLI, or SDK operations.</p>"}}, "documentation": "<p>A high-level summary of a component.</p>"}, "ComponentSummaryList": {"type": "list", "member": {"shape": "ComponentSummary"}}, "ComponentType": {"type": "string", "enum": ["BUILD", "TEST"]}, "ComponentVersion": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component.</p> <note> <p>Semantic versioning is included in each object's Amazon Resource Name (ARN), at the level that applies to that object as follows:</p> <ol> <li> <p>Versionless ARNs and Name ARNs do not include specific values in any of the nodes. The nodes are either left off entirely, or they are specified as wildcards, for example: x.x.x.</p> </li> <li> <p>Version ARNs have only the first three nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;</p> </li> <li> <p>Build version ARNs have all four nodes, and point to a specific build for a specific version of an object.</p> </li> </ol> </note>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the component.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the component.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the component.</p>"}, "supportedOsVersions": {"shape": "OsVersionList", "documentation": "<p>he operating system (OS) version supported by the component. If the OS information is available, a prefix match is performed against the base image OS version during image recipe creation.</p>"}, "type": {"shape": "ComponentType", "documentation": "<p>The type of the component denotes whether the component is used to build the image or only to test it.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the component.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date that the component was created.</p>"}}, "documentation": "<p>The defining characteristics of a specific version of an Amazon Web Services TOE component.</p>"}, "ComponentVersionArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):component/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$"}, "ComponentVersionArnOrBuildVersionArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):component/[a-z0-9-_]+/(?:(?:([0-9]+|x)\\.([0-9]+|x)\\.([0-9]+|x))|(?:[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+))$"}, "ComponentVersionList": {"type": "list", "member": {"shape": "ComponentVersion"}}, "Container": {"type": "structure", "members": {"region": {"shape": "NonEmptyString", "documentation": "<p>Containers and container images are Region-specific. This is the Region context for the container.</p>"}, "imageUris": {"shape": "StringList", "documentation": "<p>A list of URIs for containers created in the context Region.</p>"}}, "documentation": "<p>A container encapsulates the runtime environment for an application.</p>"}, "ContainerDistributionConfiguration": {"type": "structure", "required": ["targetRepository"], "members": {"description": {"shape": "NonEmptyString", "documentation": "<p>The description of the container distribution configuration.</p>"}, "containerTags": {"shape": "StringList", "documentation": "<p>Tags that are attached to the container distribution configuration.</p>"}, "targetRepository": {"shape": "TargetContainerRepository", "documentation": "<p>The destination repository for the container distribution configuration.</p>"}}, "documentation": "<p>Container distribution settings for encryption, licensing, and sharing in a specific Region.</p>"}, "ContainerList": {"type": "list", "member": {"shape": "Container"}}, "ContainerRecipe": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe.</p> <note> <p>Semantic versioning is included in each object's Amazon Resource Name (ARN), at the level that applies to that object as follows:</p> <ol> <li> <p>Versionless ARNs and Name ARNs do not include specific values in any of the nodes. The nodes are either left off entirely, or they are specified as wildcards, for example: x.x.x.</p> </li> <li> <p>Version ARNs have only the first three nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;</p> </li> <li> <p>Build version ARNs have all four nodes, and point to a specific build for a specific version of an object.</p> </li> </ol> </note>"}, "containerType": {"shape": "ContainerType", "documentation": "<p>Specifies the type of container, such as Docker.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the container recipe.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the container recipe.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The system platform for the container, such as Windows or Linux.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the container recipe.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the container recipe.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "components": {"shape": "ComponentConfigurationList", "documentation": "<p>Build and test components that are included in the container recipe. Recipes require a minimum of one build component, and can have a maximum of 20 build and test components in any combination.</p>"}, "instanceConfiguration": {"shape": "InstanceConfiguration", "documentation": "<p>A group of options that can be used to configure an instance for building and testing container images.</p>"}, "dockerfileTemplateData": {"shape": "DockerFileTemplate", "documentation": "<p>Dockerfiles are text documents that are used to build Docker containers, and ensure that they contain all of the elements required by the application running inside. The template data consists of contextual variables where Image Builder places build information or scripts, based on your container image recipe.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>Identifies which KMS key is used to encrypt the container image for distribution to the target Region.</p>"}, "encrypted": {"shape": "NullableBoolean", "documentation": "<p>A flag that indicates if the target container is encrypted.</p>"}, "parentImage": {"shape": "NonEmptyString", "documentation": "<p>The base image for the container recipe.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date when this container recipe was created.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags that are attached to the container recipe.</p>"}, "workingDirectory": {"shape": "NonEmptyString", "documentation": "<p>The working directory for use during build and test workflows.</p>"}, "targetRepository": {"shape": "TargetContainerRepository", "documentation": "<p>The destination repository for the container image.</p>"}}, "documentation": "<p>A container recipe.</p>"}, "ContainerRecipeArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):container-recipe/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$"}, "ContainerRecipeSummary": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe.</p>"}, "containerType": {"shape": "ContainerType", "documentation": "<p>Specifies the type of container, such as \"Docker\".</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the container recipe.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The system platform for the container, such as Windows or Linux.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the container recipe.</p>"}, "parentImage": {"shape": "NonEmptyString", "documentation": "<p>The base image for the container recipe.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date when this container recipe was created.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags that are attached to the container recipe.</p>"}}, "documentation": "<p>A summary of a container recipe</p>"}, "ContainerRecipeSummaryList": {"type": "list", "member": {"shape": "ContainerRecipeSummary"}}, "ContainerRepositoryService": {"type": "string", "enum": ["ECR"]}, "ContainerType": {"type": "string", "enum": ["DOCKER"]}, "CreateComponentRequest": {"type": "structure", "required": ["name", "semanticVersion", "platform", "clientToken"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "semanticVersion": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the component. This version follows the semantic version syntax.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> </note>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>Describes the contents of the component.</p>"}, "changeDescription": {"shape": "NonEmptyString", "documentation": "<p>The change description of the component. Describes what change has been made in this version, or what makes this version different from other versions of this component.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The operating system platform of the component.</p>"}, "supportedOsVersions": {"shape": "OsVersionList", "documentation": "<p>The operating system (OS) version supported by the component. If the OS information is available, a prefix match is performed against the base image OS version during image recipe creation.</p>"}, "data": {"shape": "InlineComponentData", "documentation": "<p>Component <code>data</code> contains inline YAML document content for the component. Alternatively, you can specify the <code>uri</code> of a YAML document file stored in Amazon S3. However, you cannot specify both properties.</p>"}, "uri": {"shape": "<PERSON><PERSON>", "documentation": "<p>The <code>uri</code> of a YAML component document file. This must be an S3 URL (<code>s3://bucket/key</code>), and the requester must have permission to access the S3 bucket it points to. If you use Amazon S3, you can specify component content up to your service quota.</p> <p>Alternatively, you can specify the YAML document inline, using the component <code>data</code> property. You cannot specify both properties.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the KMS key that is used to encrypt this component.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that apply to the component.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token of the component.</p>", "idempotencyToken": true}}}, "CreateComponentResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "componentBuildVersionArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component that this request created.</p>"}}}, "CreateContainerRecipeRequest": {"type": "structure", "required": ["containerType", "name", "semanticVersion", "components", "parentImage", "targetRepository", "clientToken"], "members": {"containerType": {"shape": "ContainerType", "documentation": "<p>The type of container to create.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the container recipe.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the container recipe.</p>"}, "semanticVersion": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the container recipe. This version follows the semantic version syntax.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> </note>"}, "components": {"shape": "ComponentConfigurationList", "documentation": "<p>Components for build and test that are included in the container recipe. Recipes require a minimum of one build component, and can have a maximum of 20 build and test components in any combination.</p>"}, "instanceConfiguration": {"shape": "InstanceConfiguration", "documentation": "<p>A group of options that can be used to configure an instance for building and testing container images.</p>"}, "dockerfileTemplateData": {"shape": "InlineDockerFileTemplate", "documentation": "<p>The Dockerfile template used to build your image as an inline data blob.</p>"}, "dockerfileTemplateUri": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon S3 URI for the Dockerfile that will be used to build your container image.</p>"}, "platformOverride": {"shape": "Platform", "documentation": "<p>Specifies the operating system platform when you use a custom base image.</p>"}, "imageOsVersionOverride": {"shape": "NonEmptyString", "documentation": "<p>Specifies the operating system version for the base image.</p>"}, "parentImage": {"shape": "NonEmptyString", "documentation": "<p>The base image for the container recipe.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags that are attached to the container recipe.</p>"}, "workingDirectory": {"shape": "NonEmptyString", "documentation": "<p>The working directory for use during build and test workflows.</p>"}, "targetRepository": {"shape": "TargetContainerRepository", "documentation": "<p>The destination repository for the container image.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>Identifies which KMS key is used to encrypt the container image.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client token used to make this request idempotent.</p>", "idempotencyToken": true}}}, "CreateContainerRecipeResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The client token used to make this request idempotent.</p>"}, "containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>Returns the Amazon Resource Name (ARN) of the container recipe that the request created.</p>"}}}, "CreateDistributionConfigurationRequest": {"type": "structure", "required": ["name", "distributions", "clientToken"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the distribution configuration.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the distribution configuration.</p>"}, "distributions": {"shape": "DistributionList", "documentation": "<p>The distributions of the distribution configuration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the distribution configuration.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token of the distribution configuration.</p>", "idempotencyToken": true}}}, "CreateDistributionConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that was created by this request.</p>"}}}, "CreateImagePipelineRequest": {"type": "structure", "required": ["name", "infrastructureConfigurationArn", "clientToken"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the image pipeline.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the image pipeline.</p>"}, "imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that will be used to configure images created by this image pipeline.</p>"}, "containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe that is used to configure images created by this container pipeline.</p>"}, "infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that will be used to build images created by this image pipeline.</p>"}, "distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that will be used to configure and distribute images created by this image pipeline.</p>"}, "imageTestsConfiguration": {"shape": "ImageTestsConfiguration", "documentation": "<p>The image test configuration of the image pipeline.</p>"}, "enhancedImageMetadataEnabled": {"shape": "NullableBoolean", "documentation": "<p>Collects additional information about the image being created, including the operating system (OS) version and package list. This information is used to enhance the overall experience of using EC2 Image Builder. Enabled by default.</p>"}, "schedule": {"shape": "Schedule", "documentation": "<p>The schedule of the image pipeline.</p>"}, "status": {"shape": "PipelineStatus", "documentation": "<p>The status of the image pipeline.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the image pipeline.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}, "imageScanningConfiguration": {"shape": "ImageScanningConfiguration", "documentation": "<p>Contains settings for vulnerability scans.</p>"}}}, "CreateImagePipelineResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that was created by this request.</p>"}}}, "CreateImageRecipeRequest": {"type": "structure", "required": ["name", "semanticVersion", "components", "parentImage", "clientToken"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the image recipe.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the image recipe.</p>"}, "semanticVersion": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the image recipe. This version follows the semantic version syntax.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> </note>"}, "components": {"shape": "ComponentConfigurationList", "documentation": "<p>The components included in the image recipe.</p>"}, "parentImage": {"shape": "NonEmptyString", "documentation": "<p>The base image of the image recipe. The value of the string can be the ARN of the base image or an AMI ID. The format for the ARN follows this example: <code>arn:aws:imagebuilder:us-west-2:aws:image/windows-server-2016-english-full-base-x86/x.x.x</code>. You can provide the specific version that you want to use, or you can use a wildcard in all of the fields. If you enter an AMI ID for the string value, you must have access to the AMI, and the AMI must be in the same Region in which you are using Image Builder.</p>"}, "blockDeviceMappings": {"shape": "InstanceBlockDeviceMappings", "documentation": "<p>The block device mappings of the image recipe.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the image recipe.</p>"}, "workingDirectory": {"shape": "NonEmptyString", "documentation": "<p>The working directory used during build and test workflows.</p>"}, "additionalInstanceConfiguration": {"shape": "AdditionalInstanceConfiguration", "documentation": "<p>Specify additional settings and launch scripts for your build instances.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}}}, "CreateImageRecipeResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that was created by this request.</p>"}}}, "CreateImageRequest": {"type": "structure", "required": ["infrastructureConfigurationArn", "clientToken"], "members": {"imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that defines how images are configured, tested, and assessed.</p>"}, "containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe that defines how images are configured and tested.</p>"}, "distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that defines and configures the outputs of your pipeline.</p>"}, "infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that defines the environment in which your image will be built and tested.</p>"}, "imageTestsConfiguration": {"shape": "ImageTestsConfiguration", "documentation": "<p>The image tests configuration of the image.</p>"}, "enhancedImageMetadataEnabled": {"shape": "NullableBoolean", "documentation": "<p>Collects additional information about the image being created, including the operating system (OS) version and package list. This information is used to enhance the overall experience of using EC2 Image Builder. Enabled by default.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the image.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}, "imageScanningConfiguration": {"shape": "ImageScanningConfiguration", "documentation": "<p>Contains settings for vulnerability scans.</p>"}}}, "CreateImageResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image that this request created.</p>"}}}, "CreateInfrastructureConfigurationRequest": {"type": "structure", "required": ["name", "instanceProfileName", "clientToken"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the infrastructure configuration.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the infrastructure configuration.</p>"}, "instanceTypes": {"shape": "InstanceTypeList", "documentation": "<p>The instance types of the infrastructure configuration. You can specify one or more instance types to use for this build. The service will pick one of these instance types based on availability.</p>"}, "instanceProfileName": {"shape": "InstanceProfileNameType", "documentation": "<p>The instance profile to associate with the instance used to customize your Amazon EC2 AMI.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>The security group IDs to associate with the instance used to customize your Amazon EC2 AMI.</p>"}, "subnetId": {"shape": "NonEmptyString", "documentation": "<p>The subnet ID in which to place the instance used to customize your Amazon EC2 AMI.</p>"}, "logging": {"shape": "Logging", "documentation": "<p>The logging configuration of the infrastructure configuration.</p>"}, "keyPair": {"shape": "NonEmptyString", "documentation": "<p>The key pair of the infrastructure configuration. You can use this to log on to and debug the instance used to create your image.</p>"}, "terminateInstanceOnFailure": {"shape": "NullableBoolean", "documentation": "<p>The terminate instance on failure setting of the infrastructure configuration. Set to false if you want Image Builder to retain the instance used to configure your AMI if the build or test phase of your workflow fails.</p>"}, "snsTopicArn": {"shape": "SnsTopicArn", "documentation": "<p>The Amazon Resource Name (ARN) for the SNS topic to which we send image build event notifications.</p> <note> <p>EC2 Image Builder is unable to send notifications to SNS topics that are encrypted using keys from other accounts. The key that is used to encrypt the SNS topic must reside in the account that the Image Builder service runs under.</p> </note>"}, "resourceTags": {"shape": "ResourceTagMap", "documentation": "<p>The tags attached to the resource created by Image Builder.</p>"}, "instanceMetadataOptions": {"shape": "InstanceMetadataOptions", "documentation": "<p>The instance metadata options that you can set for the HTTP requests that pipeline builds use to launch EC2 build and test instances.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the infrastructure configuration.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}}}, "CreateInfrastructureConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that was created by this request.</p>"}}}, "CvssScore": {"type": "structure", "members": {"baseScore": {"shape": "NonNegativeDouble", "documentation": "<p>The CVSS base score.</p>"}, "scoringVector": {"shape": "NonEmptyString", "documentation": "<p>The vector string of the CVSS score.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The CVSS version that generated the score.</p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p>The source of the CVSS score.</p>"}}, "documentation": "<p>Amazon Inspector generates a risk score for each finding. This score helps you to prioritize findings, to focus on the most critical findings and the most vulnerable resources. The score uses the Common Vulnerability Scoring System (CVSS) format. This format is a modification of the base CVSS score that the National Vulnerability Database (NVD) provides. For more information about severity levels, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-understanding-severity.html\">Severity levels for Amazon Inspector findings</a> in the <i>Amazon Inspector User Guide</i>.</p>"}, "CvssScoreAdjustment": {"type": "structure", "members": {"metric": {"shape": "NonEmptyString", "documentation": "<p>The metric that Amazon Inspector used to adjust the CVSS score.</p>"}, "reason": {"shape": "NonEmptyString", "documentation": "<p>The reason for the CVSS score adjustment.</p>"}}, "documentation": "<p>Details about an adjustment that Amazon Inspector made to the CVSS score for a finding.</p>"}, "CvssScoreAdjustmentList": {"type": "list", "member": {"shape": "CvssScoreAdjustment"}}, "CvssScoreDetails": {"type": "structure", "members": {"scoreSource": {"shape": "NonEmptyString", "documentation": "<p>The source for the CVSS score.</p>"}, "cvssSource": {"shape": "NonEmptyString", "documentation": "<p>The source of the finding.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The CVSS version that generated the score.</p>"}, "score": {"shape": "NonNegativeDouble", "documentation": "<p>The CVSS score.</p>"}, "scoringVector": {"shape": "NonEmptyString", "documentation": "<p>A vector that measures the severity of the vulnerability.</p>"}, "adjustments": {"shape": "CvssScoreAdjustmentList", "documentation": "<p>An object that contains details about an adjustment that Amazon Inspector made to the CVSS score for the finding.</p>"}}, "documentation": "<p>Details about the source of the score, and the factors that determined the adjustments to create the final score.</p>"}, "CvssScoreList": {"type": "list", "member": {"shape": "CvssScore"}}, "DateTime": {"type": "string"}, "DateTimeTimestamp": {"type": "timestamp"}, "DeleteComponentRequest": {"type": "structure", "required": ["componentBuildVersionArn"], "members": {"componentBuildVersionArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component build version to delete.</p>", "location": "querystring", "locationName": "componentBuildVersionArn"}}}, "DeleteComponentResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "componentBuildVersionArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The ARN of the component build version that this request deleted.</p>"}}}, "DeleteContainerRecipeRequest": {"type": "structure", "required": ["containerRecipeArn"], "members": {"containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe to delete.</p>", "location": "querystring", "locationName": "containerRecipeArn"}}}, "DeleteContainerRecipeResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe that was deleted.</p>"}}}, "DeleteDistributionConfigurationRequest": {"type": "structure", "required": ["distributionConfigurationArn"], "members": {"distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration to delete.</p>", "location": "querystring", "locationName": "distributionConfigurationArn"}}}, "DeleteDistributionConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that was deleted.</p>"}}}, "DeleteImagePipelineRequest": {"type": "structure", "required": ["imagePipelineArn"], "members": {"imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline to delete.</p>", "location": "querystring", "locationName": "imagePipelineArn"}}}, "DeleteImagePipelineResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that was deleted.</p>"}}}, "DeleteImageRecipeRequest": {"type": "structure", "required": ["imageRecipeArn"], "members": {"imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe to delete.</p>", "location": "querystring", "locationName": "imageRecipeArn"}}}, "DeleteImageRecipeResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that was deleted.</p>"}}}, "DeleteImageRequest": {"type": "structure", "required": ["imageBuildVersionArn"], "members": {"imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Image Builder image resource to delete.</p>", "location": "querystring", "locationName": "imageBuildVersionArn"}}}, "DeleteImageResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The ARN of the Image Builder image resource that this request deleted.</p>"}}}, "DeleteInfrastructureConfigurationRequest": {"type": "structure", "required": ["infrastructureConfigurationArn"], "members": {"infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration to delete.</p>", "location": "querystring", "locationName": "infrastructureConfigurationArn"}}}, "DeleteInfrastructureConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that was deleted.</p>"}}}, "DiskImageFormat": {"type": "string", "enum": ["VMDK", "RAW", "VHD"]}, "Distribution": {"type": "structure", "required": ["region"], "members": {"region": {"shape": "NonEmptyString", "documentation": "<p>The target Region.</p>"}, "amiDistributionConfiguration": {"shape": "AmiDistributionConfiguration", "documentation": "<p>The specific AMI settings; for example, launch permissions or AMI tags.</p>"}, "containerDistributionConfiguration": {"shape": "ContainerDistributionConfiguration", "documentation": "<p>Container distribution settings for encryption, licensing, and sharing in a specific Region.</p>"}, "licenseConfigurationArns": {"shape": "LicenseConfigurationArnList", "documentation": "<p>The License Manager Configuration to associate with the AMI in the specified Region.</p>"}, "launchTemplateConfigurations": {"shape": "LaunchTemplateConfigurationList", "documentation": "<p>A group of launchTemplateConfiguration settings that apply to image distribution for specified accounts.</p>"}, "s3ExportConfiguration": {"shape": "S3ExportConfiguration", "documentation": "<p>Configure export settings to deliver disk images created from your image build, using a file format that is compatible with your VMs in that Region.</p>"}, "fastLaunchConfigurations": {"shape": "FastLaunchConfigurationList", "documentation": "<p>The Windows faster-launching configurations to use for AMI distribution.</p>"}}, "documentation": "<p>Defines the settings for a specific Region.</p>"}, "DistributionConfiguration": {"type": "structure", "required": ["timeoutMinutes"], "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the distribution configuration.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the distribution configuration.</p>"}, "distributions": {"shape": "DistributionList", "documentation": "<p>The distribution objects that apply Region-specific settings for the deployment of the image to targeted Regions.</p>"}, "timeoutMinutes": {"shape": "DistributionTimeoutMinutes", "documentation": "<p>The maximum duration in minutes for this distribution configuration.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which this distribution configuration was created.</p>"}, "dateUpdated": {"shape": "DateTime", "documentation": "<p>The date on which this distribution configuration was last updated.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the distribution configuration.</p>"}}, "documentation": "<p>A distribution configuration.</p>"}, "DistributionConfigurationArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):distribution-configuration/[a-z0-9-_]+$"}, "DistributionConfigurationSummary": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the distribution configuration.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the distribution configuration.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which the distribution configuration was created.</p>"}, "dateUpdated": {"shape": "DateTime", "documentation": "<p>The date on which the distribution configuration was updated.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the distribution configuration.</p>"}, "regions": {"shape": "RegionList", "documentation": "<p>A list of Regions where the container image is distributed to.</p>"}}, "documentation": "<p>A high-level overview of a distribution configuration.</p>"}, "DistributionConfigurationSummaryList": {"type": "list", "member": {"shape": "DistributionConfigurationSummary"}}, "DistributionList": {"type": "list", "member": {"shape": "Distribution"}}, "DistributionTimeoutMinutes": {"type": "integer", "max": 720, "min": 30}, "DockerFileTemplate": {"type": "string"}, "EbsInstanceBlockDeviceSpecification": {"type": "structure", "members": {"encrypted": {"shape": "NullableBoolean", "documentation": "<p>Use to configure device encryption.</p>"}, "deleteOnTermination": {"shape": "NullableBoolean", "documentation": "<p>Use to configure delete on termination of the associated device.</p>"}, "iops": {"shape": "EbsIopsInteger", "documentation": "<p>Use to configure device IOPS.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>Use to configure the KMS key to use when encrypting the device.</p>"}, "snapshotId": {"shape": "NonEmptyString", "documentation": "<p>The snapshot that defines the device contents.</p>"}, "volumeSize": {"shape": "EbsVolumeSizeInteger", "documentation": "<p>Use to override the device's volume size.</p>"}, "volumeType": {"shape": "EbsVolumeType", "documentation": "<p>Use to override the device's volume type.</p>"}, "throughput": {"shape": "EbsVolumeThroughput", "documentation": "<p> <b>For GP3 volumes only</b> – The throughput in MiB/s that the volume supports.</p>"}}, "documentation": "<p>Amazon EBS-specific block device mapping specifications.</p>"}, "EbsIopsInteger": {"type": "integer", "max": 64000, "min": 100}, "EbsVolumeSizeInteger": {"type": "integer", "max": 16000, "min": 1}, "EbsVolumeThroughput": {"type": "integer", "max": 1000, "min": 125}, "EbsVolumeType": {"type": "string", "enum": ["standard", "io1", "io2", "gp2", "gp3", "sc1", "st1"]}, "EcrConfiguration": {"type": "structure", "members": {"repositoryName": {"shape": "NonEmptyString", "documentation": "<p>The name of the container repository that Amazon Inspector scans to identify findings for your container images. The name includes the path for the repository location. If you don’t provide this information, Image Builder creates a repository in your account named <code>image-builder-image-scanning-repository</code> for vulnerability scans of your output container images.</p>"}, "containerTags": {"shape": "StringList", "documentation": "<p>Tags for Image Builder to apply to the output container image that &amp;INS; scans. Tags can help you identify and manage your scanned images.</p>"}}, "documentation": "<p>Settings that Image Builder uses to configure the ECR repository and the output container images that Amazon Inspector scans.</p>"}, "EmptyString": {"type": "string", "max": 0, "min": 0}, "ErrorMessage": {"type": "string"}, "FastLaunchConfiguration": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"shape": "Boolean", "documentation": "<p>A Boolean that represents the current state of faster launching for the Windows AMI. Set to <code>true</code> to start using Windows faster launching, or <code>false</code> to stop using it.</p>"}, "snapshotConfiguration": {"shape": "FastLaunchSnapshotConfiguration", "documentation": "<p>Configuration settings for managing the number of snapshots that are created from pre-provisioned instances for the Windows AMI when faster launching is enabled.</p>"}, "maxParallelLaunches": {"shape": "MaxParallelLaunches", "documentation": "<p>The maximum number of parallel instances that are launched for creating resources.</p>"}, "launchTemplate": {"shape": "FastLaunchLaunchTemplateSpecification", "documentation": "<p>The launch template that the fast-launch enabled Windows AMI uses when it launches Windows instances to create pre-provisioned snapshots.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The owner account ID for the fast-launch enabled Windows AMI.</p>"}}, "documentation": "<p>Define and configure faster launching for output Windows AMIs.</p>"}, "FastLaunchConfigurationList": {"type": "list", "member": {"shape": "FastLaunchConfiguration"}, "max": 1000, "min": 1}, "FastLaunchLaunchTemplateSpecification": {"type": "structure", "members": {"launchTemplateId": {"shape": "LaunchTemplateId", "documentation": "<p>The ID of the launch template to use for faster launching for a Windows AMI.</p>"}, "launchTemplateName": {"shape": "NonEmptyString", "documentation": "<p>The name of the launch template to use for faster launching for a Windows AMI.</p>"}, "launchTemplateVersion": {"shape": "NonEmptyString", "documentation": "<p>The version of the launch template to use for faster launching for a Windows AMI.</p>"}}, "documentation": "<p>Identifies the launch template that the associated Windows AMI uses for launching an instance when faster launching is enabled.</p> <note> <p>You can specify either the <code>launchTemplateName</code> or the <code>launchTemplateId</code>, but not both.</p> </note>"}, "FastLaunchSnapshotConfiguration": {"type": "structure", "members": {"targetResourceCount": {"shape": "TargetResourceCount", "documentation": "<p>The number of pre-provisioned snapshots to keep on hand for a fast-launch enabled Windows AMI.</p>"}}, "documentation": "<p>Configuration settings for creating and managing pre-provisioned snapshots for a fast-launch enabled Windows AMI.</p>"}, "Filter": {"type": "structure", "members": {"name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter. Filter names are case-sensitive.</p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The filter values. Filter values are case-sensitive.</p>"}}, "documentation": "<p>A filter name and value pair that is used to return a more specific list of results from a list operation. Filters can be used to match a set of resources by specific criteria, such as tags, attributes, or IDs.</p>"}, "FilterList": {"type": "list", "member": {"shape": "Filter"}, "max": 10, "min": 1}, "FilterName": {"type": "string", "pattern": "^[a-zA-Z]{1,1024}$"}, "FilterValue": {"type": "string", "pattern": "^[0-9a-zA-Z./_ :-]{1,1024}$"}, "FilterValues": {"type": "list", "member": {"shape": "FilterValue"}, "max": 10, "min": 1}, "ForbiddenException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You are not authorized to perform the requested operation.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "GetComponentPolicyRequest": {"type": "structure", "required": ["componentArn"], "members": {"componentArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component whose policy you want to retrieve.</p>", "location": "querystring", "locationName": "componentArn"}}}, "GetComponentPolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The component policy.</p>"}}}, "GetComponentRequest": {"type": "structure", "required": ["componentBuildVersionArn"], "members": {"componentBuildVersionArn": {"shape": "ComponentVersionArnOrBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component that you want to get. Regex requires the suffix <code>/\\d+$</code>.</p>", "location": "querystring", "locationName": "componentBuildVersionArn"}}}, "GetComponentResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "component": {"shape": "Component", "documentation": "<p>The component object associated with the specified ARN.</p>"}}}, "GetContainerRecipePolicyRequest": {"type": "structure", "required": ["containerRecipeArn"], "members": {"containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe for the policy being requested.</p>", "location": "querystring", "locationName": "containerRecipeArn"}}}, "GetContainerRecipePolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The container recipe policy object that is returned.</p>"}}}, "GetContainerRecipeRequest": {"type": "structure", "required": ["containerRecipeArn"], "members": {"containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe to retrieve.</p>", "location": "querystring", "locationName": "containerRecipeArn"}}}, "GetContainerRecipeResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "containerRecipe": {"shape": "ContainerRecipe", "documentation": "<p>The container recipe object that is returned.</p>"}}}, "GetDistributionConfigurationRequest": {"type": "structure", "required": ["distributionConfigurationArn"], "members": {"distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that you want to retrieve.</p>", "location": "querystring", "locationName": "distributionConfigurationArn"}}}, "GetDistributionConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "distributionConfiguration": {"shape": "DistributionConfiguration", "documentation": "<p>The distribution configuration object.</p>"}}}, "GetImagePipelineRequest": {"type": "structure", "required": ["imagePipelineArn"], "members": {"imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that you want to retrieve.</p>", "location": "querystring", "locationName": "imagePipelineArn"}}}, "GetImagePipelineResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imagePipeline": {"shape": "ImagePipeline", "documentation": "<p>The image pipeline object.</p>"}}}, "GetImagePolicyRequest": {"type": "structure", "required": ["imageArn"], "members": {"imageArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image whose policy you want to retrieve.</p>", "location": "querystring", "locationName": "imageArn"}}}, "GetImagePolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The image policy object.</p>"}}}, "GetImageRecipePolicyRequest": {"type": "structure", "required": ["imageRecipeArn"], "members": {"imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe whose policy you want to retrieve.</p>", "location": "querystring", "locationName": "imageRecipeArn"}}}, "GetImageRecipePolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The image recipe policy object.</p>"}}}, "GetImageRecipeRequest": {"type": "structure", "required": ["imageRecipeArn"], "members": {"imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that you want to retrieve.</p>", "location": "querystring", "locationName": "imageRecipeArn"}}}, "GetImageRecipeResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageRecipe": {"shape": "ImageRecipe", "documentation": "<p>The image recipe object.</p>"}}}, "GetImageRequest": {"type": "structure", "required": ["imageBuildVersionArn"], "members": {"imageBuildVersionArn": {"shape": "ImageVersionArnOrBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image that you want to get.</p>", "location": "querystring", "locationName": "imageBuildVersionArn"}}}, "GetImageResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "image": {"shape": "Image", "documentation": "<p>The image object.</p>"}}}, "GetInfrastructureConfigurationRequest": {"type": "structure", "required": ["infrastructureConfigurationArn"], "members": {"infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that you want to retrieve.</p>", "location": "querystring", "locationName": "infrastructureConfigurationArn"}}, "documentation": "<p>GetInfrastructureConfiguration request object.</p>"}, "GetInfrastructureConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "infrastructureConfiguration": {"shape": "InfrastructureConfiguration", "documentation": "<p>The infrastructure configuration object.</p>"}}, "documentation": "<p>GetInfrastructureConfiguration response object.</p>"}, "GetWorkflowExecutionRequest": {"type": "structure", "required": ["workflowExecutionId"], "members": {"workflowExecutionId": {"shape": "WorkflowExecutionId", "documentation": "<p>Use the unique identifier for a runtime instance of the workflow to get runtime details.</p>", "location": "querystring", "locationName": "workflowExecutionId"}}}, "GetWorkflowExecutionResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "workflowBuildVersionArn": {"shape": "WorkflowBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the build version for the Image Builder workflow resource that defines the specified runtime instance of the workflow.</p>"}, "workflowExecutionId": {"shape": "WorkflowExecutionId", "documentation": "<p>The unique identifier that Image Builder assigned to keep track of runtime details when it ran the workflow.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image resource build version that the specified runtime instance of the workflow created.</p>"}, "type": {"shape": "WorkflowType", "documentation": "<p>The type of workflow that Image Builder ran for the specified runtime instance of the workflow.</p>"}, "status": {"shape": "WorkflowExecutionStatus", "documentation": "<p>The current runtime status for the specified runtime instance of the workflow.</p>"}, "message": {"shape": "WorkflowExecutionMessage", "documentation": "<p>The output message from the specified runtime instance of the workflow, if applicable.</p>"}, "totalStepCount": {"shape": "WorkflowStepCount", "documentation": "<p>The total number of steps in the specified runtime instance of the workflow that ran. This number should equal the sum of the step counts for steps that succeeded, were skipped, and failed.</p>"}, "totalStepsSucceeded": {"shape": "WorkflowStepCount", "documentation": "<p>A runtime count for the number of steps that ran successfully in the specified runtime instance of the workflow.</p>"}, "totalStepsFailed": {"shape": "WorkflowStepCount", "documentation": "<p>A runtime count for the number of steps that failed in the specified runtime instance of the workflow.</p>"}, "totalStepsSkipped": {"shape": "WorkflowStepCount", "documentation": "<p>A runtime count for the number of steps that were skipped in the specified runtime instance of the workflow.</p>"}, "startTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the specified runtime instance of the workflow started.</p>"}, "endTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the specified runtime instance of the workflow finished.</p>"}}}, "GetWorkflowStepExecutionRequest": {"type": "structure", "required": ["stepExecutionId"], "members": {"stepExecutionId": {"shape": "WorkflowStepExecutionId", "documentation": "<p>Use the unique identifier for a specific runtime instance of the workflow step to get runtime details for that step.</p>", "location": "querystring", "locationName": "stepExecutionId"}}}, "GetWorkflowStepExecutionResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "stepExecutionId": {"shape": "WorkflowStepExecutionId", "documentation": "<p>The unique identifier for the runtime version of the workflow step that you specified in the request.</p>"}, "workflowBuildVersionArn": {"shape": "WorkflowBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the build version for the Image Builder workflow resource that defines this workflow step.</p>"}, "workflowExecutionId": {"shape": "WorkflowExecutionId", "documentation": "<p>The unique identifier that Image Builder assigned to keep track of runtime details when it ran the workflow.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image resource build version that the specified runtime instance of the workflow step creates.</p>"}, "name": {"shape": "WorkflowStepName", "documentation": "<p>The name of the specified runtime instance of the workflow step.</p>"}, "description": {"shape": "WorkflowStepDescription", "documentation": "<p>Describes the specified workflow step.</p>"}, "action": {"shape": "WorkflowStepAction", "documentation": "<p>The name of the action that the specified step performs.</p>"}, "status": {"shape": "WorkflowStepExecutionStatus", "documentation": "<p>The current status for the specified runtime version of the workflow step.</p>"}, "rollbackStatus": {"shape": "WorkflowStepExecutionRollbackStatus", "documentation": "<p>Reports on the rollback status of the specified runtime version of the workflow step, if applicable.</p>"}, "message": {"shape": "WorkflowStepMessage", "documentation": "<p>The output message from the specified runtime instance of the workflow step, if applicable.</p>"}, "inputs": {"shape": "WorkflowStepInputs", "documentation": "<p>Input parameters that Image Builder provided for the specified runtime instance of the workflow step.</p>"}, "outputs": {"shape": "WorkflowStepOutputs", "documentation": "<p>The file names that the specified runtime version of the workflow step created as output.</p>"}, "startTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the specified runtime version of the workflow step started.</p>"}, "endTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the specified runtime instance of the workflow step finished.</p>"}, "onFailure": {"shape": "NonEmptyString", "documentation": "<p>The action to perform if the workflow step fails.</p>"}, "timeoutSeconds": {"shape": "WorkflowStepTimeoutSecondsInteger", "documentation": "<p>The maximum duration in seconds for this step to complete its action.</p>"}}}, "HttpPutResponseHopLimit": {"type": "integer", "max": 64, "min": 1}, "HttpTokens": {"type": "string", "pattern": "optional|required"}, "IdempotentParameterMismatchException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have specified a client token for an operation using parameter values that differ from a previous request that used the same client token.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Image": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image.</p> <note> <p>Semantic versioning is included in each object's Amazon Resource Name (ARN), at the level that applies to that object as follows:</p> <ol> <li> <p>Versionless ARNs and Name ARNs do not include specific values in any of the nodes. The nodes are either left off entirely, or they are specified as wildcards, for example: x.x.x.</p> </li> <li> <p>Version ARNs have only the first three nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;</p> </li> <li> <p>Build version ARNs have all four nodes, and point to a specific build for a specific version of an object.</p> </li> </ol> </note>"}, "type": {"shape": "ImageType", "documentation": "<p>Specifies whether this image produces an AMI or a container image.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the image.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the image.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "platform": {"shape": "Platform", "documentation": "<p>The image operating system platform, such as Linux or Windows.</p>"}, "enhancedImageMetadataEnabled": {"shape": "NullableBoolean", "documentation": "<p>Indicates whether Image Builder collects additional information about the image, such as the operating system (OS) version and package list.</p>"}, "osVersion": {"shape": "OsVersion", "documentation": "<p>The operating system version for instances that launch from this image. For example, Amazon Linux 2, Ubuntu 18, or Microsoft Windows Server 2019.</p>"}, "state": {"shape": "ImageState", "documentation": "<p>The state of the image.</p>"}, "imageRecipe": {"shape": "ImageRecipe", "documentation": "<p>For images that distribute an AMI, this is the image recipe that Image Builder used to create the image. For container images, this is empty.</p>"}, "containerRecipe": {"shape": "ContainerRecipe", "documentation": "<p>For container images, this is the container recipe that Image Builder used to create the image. For images that distribute an AMI, this is empty.</p>"}, "sourcePipelineName": {"shape": "ResourceName", "documentation": "<p>The name of the image pipeline that created this image.</p>"}, "sourcePipelineArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that created this image.</p>"}, "infrastructureConfiguration": {"shape": "InfrastructureConfiguration", "documentation": "<p>The infrastructure that Image Builder used to create this image.</p>"}, "distributionConfiguration": {"shape": "DistributionConfiguration", "documentation": "<p>The distribution configuration that Image Builder used to create this image.</p>"}, "imageTestsConfiguration": {"shape": "ImageTestsConfiguration", "documentation": "<p>The image tests that ran when that Image Builder created this image.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which Image Builder created this image.</p>"}, "outputResources": {"shape": "OutputResources", "documentation": "<p>The output resources that Image Builder produces for this image.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that apply to this image.</p>"}, "buildType": {"shape": "BuildType", "documentation": "<p>Indicates the type of build that created this image. The build can be initiated in the following ways:</p> <ul> <li> <p> <b>USER_INITIATED</b> – A manual pipeline build request.</p> </li> <li> <p> <b>SCHEDULED</b> – A pipeline build initiated by a cron expression in the Image Builder pipeline, or from EventBridge.</p> </li> <li> <p> <b>IMPORT</b> – A VM import created the image to use as the base image for the recipe.</p> </li> </ul>"}, "imageSource": {"shape": "ImageSource", "documentation": "<p>The origin of the base image that Image Builder used to build this image.</p>"}, "scanState": {"shape": "ImageScanState", "documentation": "<p>Contains information about the current state of scans for this image.</p>"}, "imageScanningConfiguration": {"shape": "ImageScanningConfiguration", "documentation": "<p>Contains settings for vulnerability scans.</p>"}}, "documentation": "<p>An Image Builder image. You must specify exactly one recipe for the image – either a container recipe (<code>containerRecipe</code>), which creates a container image, or an image recipe (<code>imageRecipe</code>), which creates an AMI.</p>"}, "ImageAggregation": {"type": "structure", "members": {"imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the image for this aggregation.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>Counts by severity level for medium severity and higher level findings, plus a total for all of the findings for the specified image.</p>"}}, "documentation": "<p>Contains vulnerability counts for a specific image.</p>"}, "ImageBuildMessage": {"type": "string", "max": 500, "min": 0}, "ImageBuildVersionArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$"}, "ImageBuilderArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):(?:image-recipe|container-recipe|infrastructure-configuration|distribution-configuration|component|image|image-pipeline|workflow\\/(?:build|test|distribution))/[a-z0-9-_]+(?:/(?:(?:x|[0-9]+)\\.(?:x|[0-9]+)\\.(?:x|[0-9]+))(?:/[0-9]+)?)?$"}, "ImagePackage": {"type": "structure", "members": {"packageName": {"shape": "NonEmptyString", "documentation": "<p>The name of the package as reported to the operating system package manager.</p>"}, "packageVersion": {"shape": "NonEmptyString", "documentation": "<p>The version of the package as reported to the operating system package manager.</p>"}}, "documentation": "<p>Represents a package installed on an Image Builder image.</p>"}, "ImagePackageList": {"type": "list", "member": {"shape": "ImagePackage"}}, "ImagePipeline": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the image pipeline.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the image pipeline.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the image pipeline.</p>"}, "enhancedImageMetadataEnabled": {"shape": "NullableBoolean", "documentation": "<p>Collects additional information about the image being created, including the operating system (OS) version and package list. This information is used to enhance the overall experience of using EC2 Image Builder. Enabled by default.</p>"}, "imageRecipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe associated with this image pipeline.</p>"}, "containerRecipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe that is used for this pipeline.</p>"}, "infrastructureConfigurationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration associated with this image pipeline.</p>"}, "distributionConfigurationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration associated with this image pipeline.</p>"}, "imageTestsConfiguration": {"shape": "ImageTestsConfiguration", "documentation": "<p>The image tests configuration of the image pipeline.</p>"}, "schedule": {"shape": "Schedule", "documentation": "<p>The schedule of the image pipeline.</p>"}, "status": {"shape": "PipelineStatus", "documentation": "<p>The status of the image pipeline.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which this image pipeline was created.</p>"}, "dateUpdated": {"shape": "DateTime", "documentation": "<p>The date on which this image pipeline was last updated.</p>"}, "dateLastRun": {"shape": "DateTime", "documentation": "<p>This is no longer supported, and does not return a value.</p>"}, "dateNextRun": {"shape": "DateTime", "documentation": "<p>The next date when the pipeline is scheduled to run.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this image pipeline.</p>"}, "imageScanningConfiguration": {"shape": "ImageScanningConfiguration", "documentation": "<p>Contains settings for vulnerability scans.</p>"}}, "documentation": "<p>Details of an image pipeline.</p>"}, "ImagePipelineAggregation": {"type": "structure", "members": {"imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the image pipeline for this aggregation.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>Counts by severity level for medium severity and higher level findings, plus a total for all of the findings for the specified image pipeline.</p>"}}, "documentation": "<p>Contains vulnerability counts for a specific image pipeline.</p>"}, "ImagePipelineArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image-pipeline/[a-z0-9-_]+$"}, "ImagePipelineList": {"type": "list", "member": {"shape": "ImagePipeline"}}, "ImageRecipe": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe.</p>"}, "type": {"shape": "ImageType", "documentation": "<p>Specifies which type of image is created by the recipe - an AMI or a container image.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the image recipe.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the image recipe.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the image recipe.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the image recipe.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The version of the image recipe.</p>"}, "components": {"shape": "ComponentConfigurationList", "documentation": "<p>The components that are included in the image recipe. Recipes require a minimum of one build component, and can have a maximum of 20 build and test components in any combination.</p>"}, "parentImage": {"shape": "NonEmptyString", "documentation": "<p>The base image of the image recipe.</p>"}, "blockDeviceMappings": {"shape": "InstanceBlockDeviceMappings", "documentation": "<p>The block device mappings to apply when creating images from this recipe.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which this image recipe was created.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the image recipe.</p>"}, "workingDirectory": {"shape": "NonEmptyString", "documentation": "<p>The working directory to be used during build and test workflows.</p>"}, "additionalInstanceConfiguration": {"shape": "AdditionalInstanceConfiguration", "documentation": "<p>Before you create a new AMI, Image Builder launches temporary Amazon EC2 instances to build and test your image configuration. Instance configuration adds a layer of control over those instances. You can define settings and add scripts to run when an instance is launched from your AMI.</p>"}}, "documentation": "<p>An image recipe.</p>"}, "ImageRecipeArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image-recipe/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$"}, "ImageRecipeSummary": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the image recipe.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the image recipe.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the image recipe.</p>"}, "parentImage": {"shape": "NonEmptyString", "documentation": "<p>The base image of the image recipe.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which this image recipe was created.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the image recipe.</p>"}}, "documentation": "<p>A summary of an image recipe.</p>"}, "ImageRecipeSummaryList": {"type": "list", "member": {"shape": "ImageRecipeSummary"}}, "ImageScanFinding": {"type": "structure", "members": {"awsAccountId": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Web Services account ID that's associated with the finding.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image build version that's associated with the finding.</p>"}, "imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that's associated with the finding.</p>"}, "type": {"shape": "NonEmptyString", "documentation": "<p>The type of the finding. Image Builder looks for findings of the type <code>PACKAGE_VULNERABILITY</code> that apply to output images, and excludes other types.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the finding.</p>"}, "title": {"shape": "NonEmptyString", "documentation": "<p>The title of the finding.</p>"}, "remediation": {"shape": "Remediation", "documentation": "<p>An object that contains the details about how to remediate the finding.</p>"}, "severity": {"shape": "NonEmptyString", "documentation": "<p>The severity of the finding.</p>"}, "firstObservedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time when the finding was first observed.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The timestamp when the finding was last updated.</p>"}, "inspectorScore": {"shape": "NonNegativeDouble", "documentation": "<p>The score that Amazon Inspector assigned for the finding.</p>"}, "inspectorScoreDetails": {"shape": "InspectorScoreDetails", "documentation": "<p>An object that contains details of the Amazon Inspector score.</p>"}, "packageVulnerabilityDetails": {"shape": "PackageVulnerabilityDetails", "documentation": "<p>An object that contains the details of a package vulnerability finding.</p>"}, "fixAvailable": {"shape": "NonEmptyString", "documentation": "<p>Details about whether a fix is available for any of the packages that are identified in the finding through a version update.</p>"}}, "documentation": "<p>Contains details about a vulnerability scan finding.</p>"}, "ImageScanFindingAggregation": {"type": "structure", "members": {"accountAggregation": {"shape": "AccountAggregation", "documentation": "<p>Returns an object that contains severity counts based on an account ID.</p>"}, "imageAggregation": {"shape": "ImageAggregation", "documentation": "<p>Returns an object that contains severity counts based on the Amazon Resource Name (ARN) for a specific image.</p>"}, "imagePipelineAggregation": {"shape": "ImagePipelineAggregation", "documentation": "<p>Returns an object that contains severity counts based on an image pipeline ARN.</p>"}, "vulnerabilityIdAggregation": {"shape": "VulnerabilityIdAggregation", "documentation": "<p>Returns an object that contains severity counts based on vulnerability ID.</p>"}}, "documentation": "<p>This returns exactly one type of aggregation, based on the filter that Image Builder applies in its API action.</p>"}, "ImageScanFindingAggregationsList": {"type": "list", "member": {"shape": "ImageScanFindingAggregation"}}, "ImageScanFindingsFilter": {"type": "structure", "members": {"name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the image scan finding filter. Filter names are case-sensitive.</p>"}, "values": {"shape": "ImageScanFindingsFilterValues", "documentation": "<p>The filter values. Filter values are case-sensitive.</p>"}}, "documentation": "<p>A name value pair that Image Builder applies to streamline results from the vulnerability scan findings list action.</p>"}, "ImageScanFindingsFilterList": {"type": "list", "member": {"shape": "ImageScanFindingsFilter"}, "max": 1, "min": 1}, "ImageScanFindingsFilterValues": {"type": "list", "member": {"shape": "FilterValue"}, "max": 1, "min": 1}, "ImageScanFindingsList": {"type": "list", "member": {"shape": "ImageScanFinding"}, "max": 25}, "ImageScanState": {"type": "structure", "members": {"status": {"shape": "ImageScanStatus", "documentation": "<p>The current state of vulnerability scans for the image.</p>"}, "reason": {"shape": "NonEmptyString", "documentation": "<p>The reason for the scan status for the image.</p>"}}, "documentation": "<p>Shows the vulnerability scan status for a specific image, and the reason for that status.</p>"}, "ImageScanStatus": {"type": "string", "enum": ["PENDING", "SCANNING", "COLLECTING", "COMPLETED", "ABANDONED", "FAILED", "TIMED_OUT"]}, "ImageScanningConfiguration": {"type": "structure", "members": {"imageScanningEnabled": {"shape": "NullableBoolean", "documentation": "<p>A setting that indicates whether Image Builder keeps a snapshot of the vulnerability scans that Amazon Inspector runs against the build instance when you create a new image.</p>"}, "ecrConfiguration": {"shape": "EcrConfiguration", "documentation": "<p>Contains Amazon ECR settings for vulnerability scans.</p>"}}, "documentation": "<p>Contains settings for Image Builder image resource and container image scans.</p>"}, "ImageSource": {"type": "string", "enum": ["AMAZON_MANAGED", "AWS_MARKETPLACE", "IMPORTED", "CUSTOM"]}, "ImageState": {"type": "structure", "members": {"status": {"shape": "ImageStatus", "documentation": "<p>The status of the image.</p>"}, "reason": {"shape": "NonEmptyString", "documentation": "<p>The reason for the status of the image.</p>"}}, "documentation": "<p>Image status and the reason for that status.</p>"}, "ImageStatus": {"type": "string", "enum": ["PENDING", "CREATING", "BUILDING", "TESTING", "DISTRIBUTING", "INTEGRATING", "AVAILABLE", "CANCELLED", "FAILED", "DEPRECATED", "DELETED"]}, "ImageSummary": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the image.</p>"}, "type": {"shape": "ImageType", "documentation": "<p>Specifies whether this image produces an AMI or a container image.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>The version of the image.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The image operating system platform, such as Linux or Windows.</p>"}, "osVersion": {"shape": "OsVersion", "documentation": "<p>The operating system version of the instances that launch from this image. For example, Amazon Linux 2, Ubuntu 18, or Microsoft Windows Server 2019.</p>"}, "state": {"shape": "ImageState", "documentation": "<p>The state of the image.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the image.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which Image Builder created this image.</p>"}, "outputResources": {"shape": "OutputResources", "documentation": "<p>The output resources that Image Builder produced when it created this image.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that apply to this image.</p>"}, "buildType": {"shape": "BuildType", "documentation": "<p>Indicates the type of build that created this image. The build can be initiated in the following ways:</p> <ul> <li> <p> <b>USER_INITIATED</b> – A manual pipeline build request.</p> </li> <li> <p> <b>SCHEDULED</b> – A pipeline build initiated by a cron expression in the Image Builder pipeline, or from EventBridge.</p> </li> <li> <p> <b>IMPORT</b> – A VM import created the image to use as the base image for the recipe.</p> </li> </ul>"}, "imageSource": {"shape": "ImageSource", "documentation": "<p>The origin of the base image that Image Builder used to build this image.</p>"}}, "documentation": "<p>An image summary.</p>"}, "ImageSummaryList": {"type": "list", "member": {"shape": "ImageSummary"}}, "ImageTestsConfiguration": {"type": "structure", "members": {"imageTestsEnabled": {"shape": "NullableBoolean", "documentation": "<p>Determines if tests should run after building the image. Image Builder defaults to enable tests to run following the image build, before image distribution.</p>"}, "timeoutMinutes": {"shape": "ImageTestsTimeoutMinutes", "documentation": "<p>The maximum time in minutes that tests are permitted to run.</p> <note> <p>The timeoutMinutes attribute is not currently active. This value is ignored.</p> </note>"}}, "documentation": "<p>Configure image tests for your pipeline build. Tests run after building the image, to verify that the AMI or container image is valid before distributing it.</p>"}, "ImageTestsTimeoutMinutes": {"type": "integer", "max": 1440, "min": 60}, "ImageType": {"type": "string", "enum": ["AMI", "DOCKER"]}, "ImageVersion": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of a specific version of an Image Builder image.</p> <note> <p>Semantic versioning is included in each object's Amazon Resource Name (ARN), at the level that applies to that object as follows:</p> <ol> <li> <p>Versionless ARNs and Name ARNs do not include specific values in any of the nodes. The nodes are either left off entirely, or they are specified as wildcards, for example: x.x.x.</p> </li> <li> <p>Version ARNs have only the first three nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;</p> </li> <li> <p>Build version ARNs have all four nodes, and point to a specific build for a specific version of an object.</p> </li> </ol> </note>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of this specific version of an Image Builder image.</p>"}, "type": {"shape": "ImageType", "documentation": "<p>Specifies whether this image produces an AMI or a container image.</p>"}, "version": {"shape": "VersionNumber", "documentation": "<p>Details for a specific version of an Image Builder image. This version follows the semantic version syntax.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "platform": {"shape": "Platform", "documentation": "<p>The operating system platform of the image version, for example \"Windows\" or \"Linux\".</p>"}, "osVersion": {"shape": "OsVersion", "documentation": "<p>The operating system version of the Amazon EC2 build instance. For example, Amazon Linux 2, Ubuntu 18, or Microsoft Windows Server 2019.</p>"}, "owner": {"shape": "NonEmptyString", "documentation": "<p>The owner of the image version.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which this specific version of the Image Builder image was created.</p>"}, "buildType": {"shape": "BuildType", "documentation": "<p>Indicates the type of build that created this image. The build can be initiated in the following ways:</p> <ul> <li> <p> <b>USER_INITIATED</b> – A manual pipeline build request.</p> </li> <li> <p> <b>SCHEDULED</b> – A pipeline build initiated by a cron expression in the Image Builder pipeline, or from EventBridge.</p> </li> <li> <p> <b>IMPORT</b> – A VM import created the image to use as the base image for the recipe.</p> </li> </ul>"}, "imageSource": {"shape": "ImageSource", "documentation": "<p>The origin of the base image that Image Builder used to build this image.</p>"}}, "documentation": "<p>The defining characteristics of a specific version of an Image Builder image.</p>"}, "ImageVersionArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+$"}, "ImageVersionArnOrBuildVersionArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):image/[a-z0-9-_]+/(?:(?:([0-9]+|x)\\.([0-9]+|x)\\.([0-9]+|x))|(?:[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+))$"}, "ImageVersionList": {"type": "list", "member": {"shape": "ImageVersion"}}, "ImportComponentRequest": {"type": "structure", "required": ["name", "semanticVersion", "type", "format", "platform", "clientToken"], "members": {"name": {"shape": "ResourceName", "documentation": "<p>The name of the component.</p>"}, "semanticVersion": {"shape": "VersionNumber", "documentation": "<p>The semantic version of the component. This version follows the semantic version syntax.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the component. Describes the contents of the component.</p>"}, "changeDescription": {"shape": "NonEmptyString", "documentation": "<p>The change description of the component. This description indicates the change that has been made in this version, or what makes this version different from other versions of this component.</p>"}, "type": {"shape": "ComponentType", "documentation": "<p>The type of the component denotes whether the component is used to build the image, or only to test it.</p>"}, "format": {"shape": "ComponentFormat", "documentation": "<p>The format of the resource that you want to import as a component.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the component.</p>"}, "data": {"shape": "NonEmptyString", "documentation": "<p>The data of the component. Used to specify the data inline. Either <code>data</code> or <code>uri</code> can be used to specify the data within the component.</p>"}, "uri": {"shape": "<PERSON><PERSON>", "documentation": "<p>The uri of the component. Must be an Amazon S3 URL and the requester must have permission to access the Amazon S3 bucket. If you use Amazon S3, you can specify component content up to your service quota. Either <code>data</code> or <code>uri</code> can be used to specify the data within the component.</p>"}, "kmsKeyId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the KMS key that should be used to encrypt this component.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the component.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token of the component.</p>", "idempotencyToken": true}}}, "ImportComponentResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "componentBuildVersionArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the imported component.</p>"}}}, "ImportVmImageRequest": {"type": "structure", "required": ["name", "semanticVersion", "platform", "vmImportTaskId", "clientToken"], "members": {"name": {"shape": "NonEmptyString", "documentation": "<p>The name of the base image that is created by the import process.</p>"}, "semanticVersion": {"shape": "VersionNumber", "documentation": "<p>The semantic version to attach to the base image that was created during the import process. This version follows the semantic version syntax.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Assignment:</b> For the first three nodes you can assign any positive integer value, including zero, with an upper limit of 2^30-1, or 1073741823 for each node. Image Builder automatically assigns the build number to the fourth node.</p> <p> <b>Patterns:</b> You can use any numeric pattern that adheres to the assignment requirements for the nodes that you can assign. For example, you might choose a software version pattern, such as 1.0.0, or a date, such as 2021.01.01.</p> </note>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description for the base image that is created by the import process.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The operating system platform for the imported VM.</p>"}, "osVersion": {"shape": "OsVersion", "documentation": "<p>The operating system version for the imported VM.</p>"}, "vmImportTaskId": {"shape": "NonEmptyString", "documentation": "<p>The <code>importTaskId</code> (API) or <code>ImportTaskId</code> (CLI) from the Amazon EC2 VM import process. Image Builder retrieves information from the import process to pull in the AMI that is created from the VM source as the base image for your recipe.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags that are attached to the import resources.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Unique, case-sensitive identifier you provide to ensure idempotency of the request. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a> in the <i>Amazon EC2 API Reference</i>.</p>", "idempotencyToken": true}}}, "ImportVmImageResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the AMI that was created during the VM import process. This AMI is used as the base image for the recipe that imported the VM.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token that was used for this request.</p>"}}}, "InfrastructureConfiguration": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the infrastructure configuration.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the infrastructure configuration.</p>"}, "instanceTypes": {"shape": "InstanceTypeList", "documentation": "<p>The instance types of the infrastructure configuration.</p>"}, "instanceProfileName": {"shape": "InstanceProfileNameType", "documentation": "<p>The instance profile of the infrastructure configuration.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>The security group IDs of the infrastructure configuration.</p>"}, "subnetId": {"shape": "NonEmptyString", "documentation": "<p>The subnet ID of the infrastructure configuration.</p>"}, "logging": {"shape": "Logging", "documentation": "<p>The logging configuration of the infrastructure configuration.</p>"}, "keyPair": {"shape": "NonEmptyString", "documentation": "<p>The Amazon EC2 key pair of the infrastructure configuration.</p>"}, "terminateInstanceOnFailure": {"shape": "NullableBoolean", "documentation": "<p>The terminate instance on failure configuration of the infrastructure configuration.</p>"}, "snsTopicArn": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) for the SNS topic to which we send image build event notifications.</p> <note> <p>EC2 Image Builder is unable to send notifications to SNS topics that are encrypted using keys from other accounts. The key that is used to encrypt the SNS topic must reside in the account that the Image Builder service runs under.</p> </note>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which the infrastructure configuration was created.</p>"}, "dateUpdated": {"shape": "DateTime", "documentation": "<p>The date on which the infrastructure configuration was last updated.</p>"}, "resourceTags": {"shape": "ResourceTagMap", "documentation": "<p>The tags attached to the resource created by Image Builder.</p>"}, "instanceMetadataOptions": {"shape": "InstanceMetadataOptions", "documentation": "<p>The instance metadata option settings for the infrastructure configuration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the infrastructure configuration.</p>"}}, "documentation": "<p>Details of the infrastructure configuration.</p>"}, "InfrastructureConfigurationArn": {"type": "string", "pattern": "^arn:aws[^:]*:imagebuilder:[^:]+:(?:[0-9]{12}|aws):infrastructure-configuration/[a-z0-9-_]+$"}, "InfrastructureConfigurationSummary": {"type": "structure", "members": {"arn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the infrastructure configuration.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the infrastructure configuration.</p>"}, "dateCreated": {"shape": "DateTime", "documentation": "<p>The date on which the infrastructure configuration was created.</p>"}, "dateUpdated": {"shape": "DateTime", "documentation": "<p>The date on which the infrastructure configuration was last updated.</p>"}, "resourceTags": {"shape": "ResourceTagMap", "documentation": "<p>The tags attached to the image created by Image Builder.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of the infrastructure configuration.</p>"}, "instanceTypes": {"shape": "InstanceTypeList", "documentation": "<p>The instance types of the infrastructure configuration.</p>"}, "instanceProfileName": {"shape": "InstanceProfileNameType", "documentation": "<p>The instance profile of the infrastructure configuration.</p>"}}, "documentation": "<p>The infrastructure used when building Amazon EC2 AMIs.</p>"}, "InfrastructureConfigurationSummaryList": {"type": "list", "member": {"shape": "InfrastructureConfigurationSummary"}}, "InlineComponentData": {"type": "string", "max": 16000, "min": 1, "pattern": "[^\\x00]+"}, "InlineDockerFileTemplate": {"type": "string", "max": 16000, "min": 1, "pattern": "[^\\x00]+"}, "InspectorScoreDetails": {"type": "structure", "members": {"adjustedCvss": {"shape": "CvssScoreDetails", "documentation": "<p>An object that contains details about an adjustment that Amazon Inspector made to the CVSS score for the finding.</p>"}}, "documentation": "<p>Information about the factors that influenced the score that Amazon Inspector assigned for a finding.</p>"}, "InstanceBlockDeviceMapping": {"type": "structure", "members": {"deviceName": {"shape": "NonEmptyString", "documentation": "<p>The device to which these mappings apply.</p>"}, "ebs": {"shape": "EbsInstanceBlockDeviceSpecification", "documentation": "<p>Use to manage Amazon EBS-specific configuration for this mapping.</p>"}, "virtualName": {"shape": "NonEmptyString", "documentation": "<p>Use to manage instance ephemeral devices.</p>"}, "noDevice": {"shape": "EmptyString", "documentation": "<p>Use to remove a mapping from the base image.</p>"}}, "documentation": "<p>Defines block device mappings for the instance used to configure your image.</p>"}, "InstanceBlockDeviceMappings": {"type": "list", "member": {"shape": "InstanceBlockDeviceMapping"}}, "InstanceConfiguration": {"type": "structure", "members": {"image": {"shape": "NonEmptyString", "documentation": "<p>The AMI ID to use as the base image for a container build and test instance. If not specified, Image Builder will use the appropriate ECS-optimized AMI as a base image.</p>"}, "blockDeviceMappings": {"shape": "InstanceBlockDeviceMappings", "documentation": "<p>Defines the block devices to attach for building an instance from this Image Builder AMI.</p>"}}, "documentation": "<p>Defines a custom base AMI and block device mapping configurations of an instance used for building and testing container images.</p>"}, "InstanceMetadataOptions": {"type": "structure", "members": {"httpTokens": {"shape": "HttpTokens", "documentation": "<p>Indicates whether a signed token header is required for instance metadata retrieval requests. The values affect the response as follows:</p> <ul> <li> <p> <b>required</b> – When you retrieve the IAM role credentials, version 2.0 credentials are returned in all cases.</p> </li> <li> <p> <b>optional</b> – You can include a signed token header in your request to retrieve instance metadata, or you can leave it out. If you include it, version 2.0 credentials are returned for the IAM role. Otherwise, version 1.0 credentials are returned.</p> </li> </ul> <p>The default setting is <b>optional</b>.</p>"}, "httpPutResponseHopLimit": {"shape": "HttpPutResponseHopLimit", "documentation": "<p>Limit the number of hops that an instance metadata request can traverse to reach its destination. The default is one hop. However, if HTTP tokens are required, container image builds need a minimum of two hops.</p>"}}, "documentation": "<p>The instance metadata options that apply to the HTTP requests that pipeline builds use to launch EC2 build and test instances. For more information about instance metadata options, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/configuring-instance-metadata-options.html\">Configure the instance metadata options</a> in the <i> <i>Amazon EC2 User Guide</i> </i> for Linux instances, or <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/configuring-instance-metadata-options.html\">Configure the instance metadata options</a> in the <i> <i>Amazon EC2 Windows Guide</i> </i> for Windows instances.</p>"}, "InstanceProfileNameType": {"type": "string", "max": 256, "min": 1, "pattern": "^[\\w+=,.@-]+$"}, "InstanceType": {"type": "string"}, "InstanceTypeList": {"type": "list", "member": {"shape": "InstanceType"}}, "InvalidPaginationTokenException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have provided an invalid pagination token in your request.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidParameterCombinationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have specified two or more mutually exclusive parameters. Review the error message for details.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidParameterException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified parameter is invalid. Review the available parameters for the API request.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidParameterValueException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The value that you provided for the specified parameter is invalid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have requested an action that that the service doesn't support.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidVersionNumberException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Your version number is out of bounds or does not follow the required syntax.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "LaunchPermissionConfiguration": {"type": "structure", "members": {"userIds": {"shape": "AccountList", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "userGroups": {"shape": "StringList", "documentation": "<p>The name of the group.</p>"}, "organizationArns": {"shape": "OrganizationArnList", "documentation": "<p>The ARN for an Amazon Web Services Organization that you want to share your AMI with. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_introduction.html\">What is Organizations?</a>.</p>"}, "organizationalUnitArns": {"shape": "OrganizationalUnitArnList", "documentation": "<p>The ARN for an Organizations organizational unit (OU) that you want to share your AMI with. For more information about key concepts for Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_getting-started_concepts.html\">Organizations terminology and concepts</a>.</p>"}}, "documentation": "<p>Describes the configuration for a launch permission. The launch permission modification request is sent to the <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_ModifyImageAttribute.html\">Amazon EC2 ModifyImageAttribute</a> API on behalf of the user for each Region they have selected to distribute the AMI. To make an AMI public, set the launch permission authorized accounts to <code>all</code>. See the examples for making an AMI public at <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_ModifyImageAttribute.html\">Amazon EC2 ModifyImageAttribute</a>.</p>"}, "LaunchTemplateConfiguration": {"type": "structure", "required": ["launchTemplateId"], "members": {"launchTemplateId": {"shape": "LaunchTemplateId", "documentation": "<p>Identifies the Amazon EC2 launch template to use.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The account ID that this configuration applies to.</p>"}, "setDefaultVersion": {"shape": "Boolean", "documentation": "<p>Set the specified Amazon EC2 launch template as the default launch template for the specified account.</p>"}}, "documentation": "<p>Identifies an Amazon EC2 launch template to use for a specific account.</p>"}, "LaunchTemplateConfigurationList": {"type": "list", "member": {"shape": "LaunchTemplateConfiguration"}, "max": 100, "min": 1}, "LaunchTemplateId": {"type": "string", "pattern": "^lt-[a-z0-9-_]{17}$"}, "LicenseConfigurationArn": {"type": "string", "pattern": "^arn:aws[^:]*:license-manager:[^:]+:[0-9]{12}:license-configuration:lic-[a-z0-9-_]{32}$"}, "LicenseConfigurationArnList": {"type": "list", "member": {"shape": "LicenseConfigurationArn"}, "max": 50, "min": 1}, "ListComponentBuildVersionsRequest": {"type": "structure", "required": ["componentVersionArn"], "members": {"componentVersionArn": {"shape": "ComponentVersionArn", "documentation": "<p>The component version Amazon Resource Name (ARN) whose versions you want to list.</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListComponentBuildVersionsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "componentSummaryList": {"shape": "ComponentSummaryList", "documentation": "<p>The list of component summaries for the specified semantic version.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListComponentsRequest": {"type": "structure", "members": {"owner": {"shape": "Ownership", "documentation": "<p>Filters results based on the type of owner for the component. By default, this request returns a list of components that your account owns. To see results for other types of owners, you can specify components that Amazon manages, third party components, or components that other accounts have shared with you.</p>"}, "filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>description</code> </p> </li> <li> <p> <code>name</code> </p> </li> <li> <p> <code>platform</code> </p> </li> <li> <p> <code>supportedOsVersion</code> </p> </li> <li> <p> <code>type</code> </p> </li> <li> <p> <code>version</code> </p> </li> </ul>"}, "byName": {"shape": "Boolean", "documentation": "<p>Returns the list of components for the specified name.</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListComponentsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "componentVersionList": {"shape": "ComponentVersionList", "documentation": "<p>The list of component semantic versions.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> </note>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListContainerRecipesRequest": {"type": "structure", "members": {"owner": {"shape": "Ownership", "documentation": "<p>Returns container recipes belonging to the specified owner, that have been shared with you. You can omit this field to return container recipes belonging to your account.</p>"}, "filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>containerType</code> </p> </li> <li> <p> <code>name</code> </p> </li> <li> <p> <code>parentImage</code> </p> </li> <li> <p> <code>platform</code> </p> </li> </ul>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "NonEmptyString", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListContainerRecipesResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "containerRecipeSummaryList": {"shape": "ContainerRecipeSummaryList", "documentation": "<p>The list of container recipes returned for the request.</p>"}, "nextToken": {"shape": "NonEmptyString", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListDistributionConfigurationsRequest": {"type": "structure", "members": {"filters": {"shape": "FilterList", "documentation": "<p>You can filter on <code>name</code> to streamline results.</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListDistributionConfigurationsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "distributionConfigurationSummaryList": {"shape": "DistributionConfigurationSummaryList", "documentation": "<p>The list of distributions.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImageBuildVersionsRequest": {"type": "structure", "required": ["imageVersionArn"], "members": {"imageVersionArn": {"shape": "ImageVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image whose build versions you want to retrieve.</p>"}, "filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>name</code> </p> </li> <li> <p> <code>osVersion</code> </p> </li> <li> <p> <code>platform</code> </p> </li> <li> <p> <code>type</code> </p> </li> <li> <p> <code>version</code> </p> </li> </ul>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImageBuildVersionsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageSummaryList": {"shape": "ImageSummaryList", "documentation": "<p>The list of image build versions.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImagePackagesRequest": {"type": "structure", "required": ["imageBuildVersionArn"], "members": {"imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>Filter results for the ListImagePackages request by the Image Build Version ARN</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImagePackagesResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imagePackageList": {"shape": "ImagePackageList", "documentation": "<p>The list of Image Packages returned in the response.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImagePipelineImagesRequest": {"type": "structure", "required": ["imagePipelineArn"], "members": {"imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline whose images you want to view.</p>"}, "filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>name</code> </p> </li> <li> <p> <code>version</code> </p> </li> </ul>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImagePipelineImagesResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageSummaryList": {"shape": "ImageSummaryList", "documentation": "<p>The list of images built by this pipeline.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImagePipelinesRequest": {"type": "structure", "members": {"filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>description</code> </p> </li> <li> <p> <code>distributionConfigurationArn</code> </p> </li> <li> <p> <code>imageRecipeArn</code> </p> </li> <li> <p> <code>infrastructureConfigurationArn</code> </p> </li> <li> <p> <code>name</code> </p> </li> <li> <p> <code>status</code> </p> </li> </ul>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImagePipelinesResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imagePipelineList": {"shape": "ImagePipelineList", "documentation": "<p>The list of image pipelines.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImageRecipesRequest": {"type": "structure", "members": {"owner": {"shape": "Ownership", "documentation": "<p>The owner defines which image recipes you want to list. By default, this request will only show image recipes owned by your account. You can use this field to specify if you want to view image recipes owned by yourself, by Amazon, or those image recipes that have been shared with you by other customers.</p>"}, "filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>name</code> </p> </li> <li> <p> <code>parentImage</code> </p> </li> <li> <p> <code>platform</code> </p> </li> </ul>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImageRecipesResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageRecipeSummaryList": {"shape": "ImageRecipeSummaryList", "documentation": "<p>The list of image pipelines.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImageScanFindingAggregationsRequest": {"type": "structure", "members": {"filter": {"shape": "Filter"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImageScanFindingAggregationsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "aggregationType": {"shape": "NonEmptyString", "documentation": "<p>The aggregation type specifies what type of key is used to group the image scan findings. Image Builder returns results based on the request filter. If you didn't specify a filter in the request, the type defaults to <code>accountId</code>.</p> <p class=\"title\"> <b>Aggregation types</b> </p> <ul> <li> <p>accountId</p> </li> <li> <p>imageBuildVersionArn</p> </li> <li> <p>imagePipelineArn</p> </li> <li> <p>vulnerabilityId</p> </li> </ul> <p>Each aggregation includes counts by severity level for medium severity and higher level findings, plus a total for all of the findings for each key value.</p>"}, "responses": {"shape": "ImageScanFindingAggregationsList", "documentation": "<p>An array of image scan finding aggregations that match the filter criteria.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImageScanFindingsRequest": {"type": "structure", "members": {"filters": {"shape": "ImageScanFindingsFilterList", "documentation": "<p>An array of name value pairs that you can use to filter your results. You can use the following filters to streamline results:</p> <ul> <li> <p> <code>imageBuildVersionArn</code> </p> </li> <li> <p> <code>imagePipelineArn</code> </p> </li> <li> <p> <code>vulnerabilityId</code> </p> </li> <li> <p> <code>severity</code> </p> </li> </ul> <p>If you don't request a filter, then all findings in your account are listed.</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListImageScanFindingsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "findings": {"shape": "ImageScanFindingsList", "documentation": "<p>The image scan findings for your account that meet your request filter criteria.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListImagesRequest": {"type": "structure", "members": {"owner": {"shape": "Ownership", "documentation": "<p>The owner defines which images you want to list. By default, this request will only show images owned by your account. You can use this field to specify if you want to view images owned by yourself, by Amazon, or those images that have been shared with you by other customers.</p>"}, "filters": {"shape": "FilterList", "documentation": "<p>Use the following filters to streamline results:</p> <ul> <li> <p> <code>name</code> </p> </li> <li> <p> <code>osVersion</code> </p> </li> <li> <p> <code>platform</code> </p> </li> <li> <p> <code>type</code> </p> </li> <li> <p> <code>version</code> </p> </li> </ul>"}, "byName": {"shape": "Boolean", "documentation": "<p>Requests a list of images with a specific recipe name.</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}, "includeDeprecated": {"shape": "NullableBoolean", "documentation": "<p>Includes deprecated images in the response list.</p>"}}}, "ListImagesResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageVersionList": {"shape": "ImageVersionList", "documentation": "<p>The list of image semantic versions.</p> <note> <p>The semantic version has four nodes: &lt;major&gt;.&lt;minor&gt;.&lt;patch&gt;/&lt;build&gt;. You can assign values for the first three, and can filter on all of them.</p> <p> <b>Filtering:</b> With semantic versioning, you have the flexibility to use wildcards (x) to specify the most recent versions or nodes when selecting the base image or components for your recipe. When you use a wildcard in any node, all nodes to the right of the first wildcard must also be wildcards.</p> </note>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListInfrastructureConfigurationsRequest": {"type": "structure", "members": {"filters": {"shape": "FilterList", "documentation": "<p>You can filter on <code>name</code> to streamline results.</p>"}, "maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}}}, "ListInfrastructureConfigurationsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "infrastructureConfigurationSummaryList": {"shape": "InfrastructureConfigurationSummaryList", "documentation": "<p>The list of infrastructure configurations.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource whose tags you want to retrieve.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags for the specified resource.</p>"}}}, "ListWorkflowExecutionsRequest": {"type": "structure", "required": ["imageBuildVersionArn"], "members": {"maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>List all workflow runtime instances for the specified image build version resource ARN.</p>"}}}, "ListWorkflowExecutionsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "workflowExecutions": {"shape": "WorkflowExecutionsList", "documentation": "<p>Contains an array of runtime details that represents each time a workflow ran for the requested image build version.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The resource ARN of the image build version for which you requested a list of workflow runtime details.</p>"}, "message": {"shape": "ImageBuildMessage", "documentation": "<p>The output message from the list action, if applicable.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "ListWorkflowStepExecutionsRequest": {"type": "structure", "required": ["workflowExecutionId"], "members": {"maxResults": {"shape": "RestrictedInteger", "documentation": "<p>The maximum items to return in a request.</p>", "box": true}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token to specify where to start paginating. This is the NextToken from a previously truncated response.</p>"}, "workflowExecutionId": {"shape": "WorkflowExecutionId", "documentation": "<p>The unique identifier that Image Builder assigned to keep track of runtime details when it ran the workflow.</p>"}}}, "ListWorkflowStepExecutionsResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "steps": {"shape": "WorkflowStepExecutionsList", "documentation": "<p>Contains an array of runtime details that represents each step in this runtime instance of the workflow.</p>"}, "workflowBuildVersionArn": {"shape": "WorkflowBuildVersionArn", "documentation": "<p>The build version ARN for the Image Builder workflow resource that defines the steps for this runtime instance of the workflow.</p>"}, "workflowExecutionId": {"shape": "WorkflowExecutionId", "documentation": "<p>The unique identifier that Image Builder assigned to keep track of runtime details when it ran the workflow.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The image build version resource ARN that's associated with the specified runtime instance of the workflow.</p>"}, "message": {"shape": "ImageBuildMessage", "documentation": "<p>The output message from the list action, if applicable.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The next token used for paginated responses. When this field isn't empty, there are additional elements that the service has'ot included in this request. Use this token with the next request to retrieve additional objects.</p>"}}}, "Logging": {"type": "structure", "members": {"s3Logs": {"shape": "S3Logs", "documentation": "<p>The Amazon S3 logging configuration.</p>"}}, "documentation": "<p>Logging configuration defines where Image Builder uploads your logs.</p>"}, "MaxParallelLaunches": {"type": "integer", "max": 10000, "min": 1}, "NonEmptyString": {"type": "string", "max": 1024, "min": 1}, "NonEmptyStringList": {"type": "list", "member": {"shape": "NonEmptyString"}, "min": 1}, "NonNegativeDouble": {"type": "double", "min": 0}, "NullableBoolean": {"type": "boolean"}, "OrganizationArn": {"type": "string", "pattern": "^arn:aws[^:]*:organizations::[0-9]{12}:organization/o-[a-z0-9]{10,32}$"}, "OrganizationArnList": {"type": "list", "member": {"shape": "OrganizationArn"}, "max": 25, "min": 1}, "OrganizationalUnitArn": {"type": "string", "pattern": "^arn:aws[^:]*:organizations::[0-9]{12}:ou/o-[a-z0-9]{10,32}/ou-[0-9a-z]{4,32}-[0-9a-z]{8,32}"}, "OrganizationalUnitArnList": {"type": "list", "member": {"shape": "OrganizationalUnitArn"}, "max": 25, "min": 1}, "OsVersion": {"type": "string", "min": 1}, "OsVersionList": {"type": "list", "member": {"shape": "OsVersion"}, "max": 25, "min": 1}, "OutputResources": {"type": "structure", "members": {"amis": {"shape": "AmiList", "documentation": "<p>The Amazon EC2 AMIs created by this image.</p>"}, "containers": {"shape": "ContainerList", "documentation": "<p>Container images that the pipeline has generated and stored in the output repository.</p>"}}, "documentation": "<p>The resources produced by this image.</p>"}, "Ownership": {"type": "string", "enum": ["Self", "Shared", "Amazon", "ThirdParty"]}, "PackageArchitecture": {"type": "string"}, "PackageEpoch": {"type": "integer"}, "PackageVulnerabilityDetails": {"type": "structure", "required": ["vulnerabilityId"], "members": {"vulnerabilityId": {"shape": "VulnerabilityId", "documentation": "<p>A unique identifier for this vulnerability.</p>"}, "vulnerablePackages": {"shape": "VulnerablePackageList", "documentation": "<p>The packages that this vulnerability impacts.</p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p>The source of the vulnerability information.</p>"}, "cvss": {"shape": "CvssScoreList", "documentation": "<p>CVSS scores for one or more vulnerabilities that Amazon Inspector identified for a package.</p>"}, "relatedVulnerabilities": {"shape": "VulnerabilityIdList", "documentation": "<p>Vulnerabilities that are often related to the findings for the package.</p>"}, "sourceUrl": {"shape": "NonEmptyString", "documentation": "<p>A link to the source of the vulnerability information.</p>"}, "vendorSeverity": {"shape": "NonEmptyString", "documentation": "<p>The severity that the vendor assigned to this vulnerability type.</p>"}, "vendorCreatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time when this vulnerability was first added to the vendor's database.</p>"}, "vendorUpdatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time when the vendor last updated this vulnerability in their database.</p>"}, "referenceUrls": {"shape": "NonEmptyStringList", "documentation": "<p>Links to web pages that contain details about the vulnerabilities that Amazon Inspector identified for the package.</p>"}}, "documentation": "<p>Information about package vulnerability findings.</p>"}, "PaginationToken": {"type": "string", "max": 65535, "min": 1}, "PipelineExecutionStartCondition": {"type": "string", "enum": ["EXPRESSION_MATCH_ONLY", "EXPRESSION_MATCH_AND_DEPENDENCY_UPDATES_AVAILABLE"]}, "PipelineStatus": {"type": "string", "enum": ["DISABLED", "ENABLED"]}, "Platform": {"type": "string", "enum": ["Windows", "Linux"]}, "PutComponentPolicyRequest": {"type": "structure", "required": ["componentArn", "policy"], "members": {"componentArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component that this policy should be applied to.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The policy to apply.</p>"}}}, "PutComponentPolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "componentArn": {"shape": "ComponentBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the component that this policy was applied to.</p>"}}}, "PutContainerRecipePolicyRequest": {"type": "structure", "required": ["containerRecipeArn", "policy"], "members": {"containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe that this policy should be applied to.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The policy to apply to the container recipe.</p>"}}}, "PutContainerRecipePolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container recipe that this policy was applied to.</p>"}}}, "PutImagePolicyRequest": {"type": "structure", "required": ["imageArn", "policy"], "members": {"imageArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image that this policy should be applied to.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The policy to apply.</p>"}}}, "PutImagePolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image that this policy was applied to.</p>"}}}, "PutImageRecipePolicyRequest": {"type": "structure", "required": ["imageRecipeArn", "policy"], "members": {"imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that this policy should be applied to.</p>"}, "policy": {"shape": "ResourcePolicyDocument", "documentation": "<p>The policy to apply.</p>"}}}, "PutImageRecipePolicyResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that this policy was applied to.</p>"}}}, "RegionList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "Remediation": {"type": "structure", "members": {"recommendation": {"shape": "RemediationRecommendation", "documentation": "<p>An object that contains information about the recommended course of action to remediate the finding.</p>"}}, "documentation": "<p>Information about how to remediate a finding.</p>"}, "RemediationRecommendation": {"type": "structure", "members": {"text": {"shape": "NonEmptyString", "documentation": "<p>The recommended course of action to remediate the finding.</p>"}, "url": {"shape": "NonEmptyString", "documentation": "<p>A link to more information about the recommended remediation for this vulnerability.</p>"}}, "documentation": "<p>Details about the recommended course of action to remediate the finding.</p>"}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource that you are trying to create already exists.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceDependencyException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have attempted to mutate or delete a resource with a dependency that prohibits this action. See the error message for more details.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceInUseException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource that you are trying to operate on is currently in use. Review the message details and retry later.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceName": {"type": "string", "pattern": "^[-_A-Za-z-0-9][-_A-Za-z0-9 ]{1,126}[-_A-Za-z-0-9]$"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>At least one of the resources referenced by your request does not exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourcePolicyDocument": {"type": "string", "max": 30000, "min": 1}, "ResourceTagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 30, "min": 1}, "RestrictedInteger": {"type": "integer", "max": 25, "min": 1}, "S3ExportConfiguration": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "diskImageFormat", "s3Bucket"], "members": {"roleName": {"shape": "NonEmptyString", "documentation": "<p>The name of the role that grants VM Import/Export permission to export images to your S3 bucket.</p>"}, "diskImageFormat": {"shape": "DiskImageFormat", "documentation": "<p>Export the updated image to one of the following supported disk image formats:</p> <ul> <li> <p> <b>Virtual Hard Disk (VHD)</b> – Compatible with Citrix Xen and Microsoft Hyper-V virtualization products.</p> </li> <li> <p> <b>Stream-optimized ESX Virtual Machine Disk (VMDK)</b> – Compatible with VMware ESX and VMware vSphere versions 4, 5, and 6.</p> </li> <li> <p> <b>Raw</b> – Raw format.</p> </li> </ul>"}, "s3Bucket": {"shape": "NonEmptyString", "documentation": "<p>The S3 bucket in which to store the output disk images for your VM.</p>"}, "s3Prefix": {"shape": "NonEmptyString", "documentation": "<p>The Amazon S3 path for the bucket where the output disk images for your VM are stored.</p>"}}, "documentation": "<p>Properties that configure export from your build instance to a compatible file format for your VM.</p>"}, "S3Logs": {"type": "structure", "members": {"s3BucketName": {"shape": "NonEmptyString", "documentation": "<p>The S3 bucket in which to store the logs.</p>"}, "s3KeyPrefix": {"shape": "NonEmptyString", "documentation": "<p>The Amazon S3 path to the bucket where the logs are stored.</p>"}}, "documentation": "<p>Amazon S3 logging configuration.</p>"}, "Schedule": {"type": "structure", "members": {"scheduleExpression": {"shape": "NonEmptyString", "documentation": "<p>The cron expression determines how often EC2 Image Builder evaluates your <code>pipelineExecutionStartCondition</code>.</p> <p>For information on how to format a cron expression in Image Builder, see <a href=\"https://docs.aws.amazon.com/imagebuilder/latest/userguide/image-builder-cron.html\">Use cron expressions in EC2 Image Builder</a>.</p>"}, "timezone": {"shape": "Timezone", "documentation": "<p>The timezone that applies to the scheduling expression. For example, \"Etc/UTC\", \"America/Los_Angeles\" in the <a href=\"https://www.joda.org/joda-time/timezones.html\">IANA timezone format</a>. If not specified this defaults to UTC.</p>"}, "pipelineExecutionStartCondition": {"shape": "PipelineExecutionStartCondition", "documentation": "<p>The condition configures when the pipeline should trigger a new image build. When the <code>pipelineExecutionStartCondition</code> is set to <code>EXPRESSION_MATCH_AND_DEPENDENCY_UPDATES_AVAILABLE</code>, and you use semantic version filters on the base image or components in your image recipe, EC2 Image Builder will build a new image only when there are new versions of the image or components in your recipe that match the semantic version filter. When it is set to <code>EXPRESSION_MATCH_ONLY</code>, it will build a new image every time the CRON expression matches the current time. For semantic version syntax, see <a href=\"https://docs.aws.amazon.com/imagebuilder/latest/APIReference/API_CreateComponent.html\">CreateComponent</a> in the <i> EC2 Image Builder API Reference</i>.</p>"}}, "documentation": "<p>A schedule configures how often and when a pipeline will automatically create a new image.</p>"}, "SecurityGroupIds": {"type": "list", "member": {"shape": "NonEmptyString"}}, "ServiceException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>This exception is thrown when the service encounters an unrecoverable exception.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have exceeded the number of permitted resources or operations for this service. For service quotas, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/imagebuilder.html#limits_imagebuilder\">EC2 Image Builder endpoints and quotas</a>.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "ServiceUnavailableException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The service is unable to process your request at this time.</p>", "error": {"httpStatusCode": 503}, "exception": true}, "SeverityCountNumber": {"type": "long"}, "SeverityCounts": {"type": "structure", "members": {"all": {"shape": "SeverityCountNumber", "documentation": "<p>The total number of findings across all severity levels for the specified filter.</p>"}, "critical": {"shape": "SeverityCountNumber", "documentation": "<p>The number of critical severity findings for the specified filter.</p>"}, "high": {"shape": "SeverityCountNumber", "documentation": "<p>The number of high severity findings for the specified filter.</p>"}, "medium": {"shape": "SeverityCountNumber", "documentation": "<p>The number of medium severity findings for the specified filter.</p>"}}, "documentation": "<p>Includes counts by severity level for medium severity and higher level findings, plus a total for all of the findings for the specified filter.</p>"}, "SnsTopicArn": {"type": "string", "pattern": "^arn:aws[^:]*:sns:[^:]+:[0-9]{12}:[a-zA-Z0-9-_]{1,256}$"}, "SourceLayerHash": {"type": "string"}, "StartImagePipelineExecutionRequest": {"type": "structure", "required": ["imagePipelineArn", "clientToken"], "members": {"imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that you want to manually invoke.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}}}, "StartImagePipelineExecutionResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "imageBuildVersionArn": {"shape": "ImageBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image that was created by this request.</p>"}}}, "StringList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "SystemsManagerAgent": {"type": "structure", "members": {"uninstallAfterBuild": {"shape": "NullableBoolean", "documentation": "<p>Controls whether the Systems Manager agent is removed from your final build image, prior to creating the new AMI. If this is set to true, then the agent is removed from the final image. If it's set to false, then the agent is left in, so that it is included in the new AMI. The default value is false.</p>"}}, "documentation": "<p>Contains settings for the Systems Manager agent on your build instance.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to tag.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to apply to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256}, "TargetContainerRepository": {"type": "structure", "required": ["service", "repositoryName"], "members": {"service": {"shape": "ContainerRepositoryService", "documentation": "<p>Specifies the service in which this image was registered.</p>"}, "repositoryName": {"shape": "NonEmptyString", "documentation": "<p>The name of the container repository where the output container image is stored. This name is prefixed by the repository location.</p>"}}, "documentation": "<p>The container repository where the output container image is stored.</p>"}, "TargetResourceCount": {"type": "integer", "max": 10000, "min": 1}, "Timezone": {"type": "string", "max": 100, "min": 3, "pattern": "[a-zA-Z0-9]{2,}(?:\\/[a-zA-z0-9-_+]+)*"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ImageBuilderArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to untag.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateDistributionConfigurationRequest": {"type": "structure", "required": ["distributionConfigurationArn", "distributions", "clientToken"], "members": {"distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that you want to update.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the distribution configuration.</p>"}, "distributions": {"shape": "DistributionList", "documentation": "<p>The distributions of the distribution configuration.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token of the distribution configuration.</p>", "idempotencyToken": true}}}, "UpdateDistributionConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that was updated by this request.</p>"}}}, "UpdateImagePipelineRequest": {"type": "structure", "required": ["imagePipelineArn", "infrastructureConfigurationArn", "clientToken"], "members": {"imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that you want to update.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the image pipeline.</p>"}, "imageRecipeArn": {"shape": "ImageRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image recipe that will be used to configure images updated by this image pipeline.</p>"}, "containerRecipeArn": {"shape": "ContainerRecipeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the container pipeline to update.</p>"}, "infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that Image Builder uses to build images that this image pipeline has updated.</p>"}, "distributionConfigurationArn": {"shape": "DistributionConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the distribution configuration that Image Builder uses to configure and distribute images that this image pipeline has updated.</p>"}, "imageTestsConfiguration": {"shape": "ImageTestsConfiguration", "documentation": "<p>The image test configuration of the image pipeline.</p>"}, "enhancedImageMetadataEnabled": {"shape": "NullableBoolean", "documentation": "<p>Collects additional information about the image being created, including the operating system (OS) version and package list. This information is used to enhance the overall experience of using EC2 Image Builder. Enabled by default.</p>"}, "schedule": {"shape": "Schedule", "documentation": "<p>The schedule of the image pipeline.</p>"}, "status": {"shape": "PipelineStatus", "documentation": "<p>The status of the image pipeline.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}, "imageScanningConfiguration": {"shape": "ImageScanningConfiguration", "documentation": "<p>Contains settings for vulnerability scans.</p>"}}}, "UpdateImagePipelineResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "imagePipelineArn": {"shape": "ImagePipelineArn", "documentation": "<p>The Amazon Resource Name (ARN) of the image pipeline that was updated by this request.</p>"}}}, "UpdateInfrastructureConfigurationRequest": {"type": "structure", "required": ["infrastructureConfigurationArn", "instanceProfileName", "clientToken"], "members": {"infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that you want to update.</p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p>The description of the infrastructure configuration.</p>"}, "instanceTypes": {"shape": "InstanceTypeList", "documentation": "<p>The instance types of the infrastructure configuration. You can specify one or more instance types to use for this build. The service will pick one of these instance types based on availability.</p>"}, "instanceProfileName": {"shape": "InstanceProfileNameType", "documentation": "<p>The instance profile to associate with the instance used to customize your Amazon EC2 AMI.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>The security group IDs to associate with the instance used to customize your Amazon EC2 AMI.</p>"}, "subnetId": {"shape": "NonEmptyString", "documentation": "<p>The subnet ID to place the instance used to customize your Amazon EC2 AMI in.</p>"}, "logging": {"shape": "Logging", "documentation": "<p>The logging configuration of the infrastructure configuration.</p>"}, "keyPair": {"shape": "NonEmptyString", "documentation": "<p>The key pair of the infrastructure configuration. You can use this to log on to and debug the instance used to create your image.</p>"}, "terminateInstanceOnFailure": {"shape": "NullableBoolean", "documentation": "<p>The terminate instance on failure setting of the infrastructure configuration. Set to false if you want Image Builder to retain the instance used to configure your AMI if the build or test phase of your workflow fails.</p>"}, "snsTopicArn": {"shape": "SnsTopicArn", "documentation": "<p>The Amazon Resource Name (ARN) for the SNS topic to which we send image build event notifications.</p> <note> <p>EC2 Image Builder is unable to send notifications to SNS topics that are encrypted using keys from other accounts. The key that is used to encrypt the SNS topic must reside in the account that the Image Builder service runs under.</p> </note>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>", "idempotencyToken": true}, "resourceTags": {"shape": "ResourceTagMap", "documentation": "<p>The tags attached to the resource created by Image Builder.</p>"}, "instanceMetadataOptions": {"shape": "InstanceMetadataOptions", "documentation": "<p>The instance metadata options that you can set for the HTTP requests that pipeline builds use to launch EC2 build and test instances. For more information about instance metadata options, see one of the following links:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/configuring-instance-metadata-options.html\">Configure the instance metadata options</a> in the <i> <i>Amazon EC2 User Guide</i> </i> for Linux instances.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/configuring-instance-metadata-options.html\">Configure the instance metadata options</a> in the <i> <i>Amazon EC2 Windows Guide</i> </i> for Windows instances.</p> </li> </ul>"}}}, "UpdateInfrastructureConfigurationResponse": {"type": "structure", "members": {"requestId": {"shape": "NonEmptyString", "documentation": "<p>The request ID that uniquely identifies this request.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token used to make this request idempotent.</p>"}, "infrastructureConfigurationArn": {"shape": "InfrastructureConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the infrastructure configuration that was updated by this request.</p>"}}}, "Uri": {"type": "string"}, "UserDataOverride": {"type": "string", "max": 21847, "min": 1, "pattern": "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"}, "VersionNumber": {"type": "string", "pattern": "^[0-9]+\\.[0-9]+\\.[0-9]+$"}, "VulnerabilityId": {"type": "string"}, "VulnerabilityIdAggregation": {"type": "structure", "members": {"vulnerabilityId": {"shape": "NonEmptyString", "documentation": "<p>The vulnerability Id for this set of counts.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>Counts by severity level for medium severity and higher level findings, plus a total for all of the findings for the specified vulnerability.</p>"}}, "documentation": "<p>Includes counts of image and pipeline resource findings by vulnerability.</p>"}, "VulnerabilityIdList": {"type": "list", "member": {"shape": "VulnerabilityId"}}, "VulnerablePackage": {"type": "structure", "members": {"name": {"shape": "NonEmptyString", "documentation": "<p>The name of the vulnerable package.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The version of the vulnerable package.</p>"}, "sourceLayerHash": {"shape": "SourceLayerHash", "documentation": "<p>The source layer hash of the vulnerable package.</p>"}, "epoch": {"shape": "PackageEpoch", "documentation": "<p>The epoch of the vulnerable package.</p>"}, "release": {"shape": "NonEmptyString", "documentation": "<p>The release of the vulnerable package.</p>"}, "arch": {"shape": "PackageArchitecture", "documentation": "<p>The architecture of the vulnerable package.</p>"}, "packageManager": {"shape": "NonEmptyString", "documentation": "<p>The package manager of the vulnerable package.</p>"}, "filePath": {"shape": "NonEmptyString", "documentation": "<p>The file path of the vulnerable package.</p>"}, "fixedInVersion": {"shape": "NonEmptyString", "documentation": "<p>The version of the package that contains the vulnerability fix.</p>"}, "remediation": {"shape": "NonEmptyString", "documentation": "<p>The code to run in your environment to update packages with a fix available.</p>"}}, "documentation": "<p>Information about a vulnerable package that Amazon Inspector identifies in a finding.</p>"}, "VulnerablePackageList": {"type": "list", "member": {"shape": "VulnerablePackage"}}, "WorkflowBuildVersionArn": {"type": "string", "max": 1024, "pattern": "^arn:aws(?:-[a-z]+)*:imagebuilder:[a-z]{2,}(?:-[a-z]+)+-[0-9]+:(?:[0-9]{12}|aws):workflow/(build|test|distribution)/[a-z0-9-_]+/[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$"}, "WorkflowExecutionId": {"type": "string", "pattern": "^wf-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"}, "WorkflowExecutionMessage": {"type": "string", "max": 500, "min": 0}, "WorkflowExecutionMetadata": {"type": "structure", "members": {"workflowBuildVersionArn": {"shape": "WorkflowBuildVersionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the workflow resource build version that ran.</p>"}, "workflowExecutionId": {"shape": "WorkflowExecutionId", "documentation": "<p>Unique identifier that Image Builder assigns to keep track of runtime resources each time it runs a workflow.</p>"}, "type": {"shape": "WorkflowType", "documentation": "<p>Indicates what type of workflow that Image Builder ran for this runtime instance of the workflow.</p>"}, "status": {"shape": "WorkflowExecutionStatus", "documentation": "<p>The current runtime status for this workflow.</p>"}, "message": {"shape": "WorkflowExecutionMessage", "documentation": "<p>The runtime output message from the workflow, if applicable.</p>"}, "totalStepCount": {"shape": "WorkflowStepCount", "documentation": "<p>The total number of steps in the workflow. This should equal the sum of the step counts for steps that succeeded, were skipped, and failed.</p>"}, "totalStepsSucceeded": {"shape": "WorkflowStepCount", "documentation": "<p>A runtime count for the number of steps in the workflow that ran successfully.</p>"}, "totalStepsFailed": {"shape": "WorkflowStepCount", "documentation": "<p>A runtime count for the number of steps in the workflow that failed.</p>"}, "totalStepsSkipped": {"shape": "WorkflowStepCount", "documentation": "<p>A runtime count for the number of steps in the workflow that were skipped.</p>"}, "startTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the runtime instance of this workflow started.</p>"}, "endTime": {"shape": "DateTime", "documentation": "<p>The timestamp when this runtime instance of the workflow finished.</p>"}}, "documentation": "<p>Metadata that includes details and status from this runtime instance of the workflow.</p>"}, "WorkflowExecutionStatus": {"type": "string", "enum": ["PENDING", "SKIPPED", "RUNNING", "COMPLETED", "FAILED", "ROLLBACK_IN_PROGRESS", "ROLLBACK_COMPLETED"]}, "WorkflowExecutionsList": {"type": "list", "member": {"shape": "WorkflowExecutionMetadata"}}, "WorkflowStepAction": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9-_]{1,99}$"}, "WorkflowStepCount": {"type": "integer"}, "WorkflowStepDescription": {"type": "string", "max": 500, "min": 0}, "WorkflowStepExecutionId": {"type": "string", "pattern": "^step-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"}, "WorkflowStepExecutionRollbackStatus": {"type": "string", "enum": ["RUNNING", "COMPLETED", "SKIPPED", "FAILED"]}, "WorkflowStepExecutionStatus": {"type": "string", "enum": ["PENDING", "SKIPPED", "RUNNING", "COMPLETED", "FAILED"]}, "WorkflowStepExecutionsList": {"type": "list", "member": {"shape": "WorkflowStepMetadata"}}, "WorkflowStepInputs": {"type": "string"}, "WorkflowStepMessage": {"type": "string", "max": 500, "min": 0}, "WorkflowStepMetadata": {"type": "structure", "members": {"stepExecutionId": {"shape": "WorkflowStepExecutionId", "documentation": "<p>A unique identifier for the workflow step, assigned at runtime.</p>"}, "name": {"shape": "WorkflowStepName", "documentation": "<p>The name of the workflow step.</p>"}, "description": {"shape": "WorkflowStepDescription", "documentation": "<p>Description of the workflow step.</p>"}, "action": {"shape": "WorkflowStepAction", "documentation": "<p>The step action name.</p>"}, "status": {"shape": "WorkflowStepExecutionStatus", "documentation": "<p>Runtime status for the workflow step.</p>"}, "rollbackStatus": {"shape": "WorkflowStepExecutionRollbackStatus", "documentation": "<p>Reports on the rollback status of the step, if applicable.</p>"}, "message": {"shape": "WorkflowStepMessage", "documentation": "<p>Detailed output message that the workflow step provides at runtime.</p>"}, "inputs": {"shape": "WorkflowStepInputs", "documentation": "<p>Input parameters that Image Builder provides for the workflow step.</p>"}, "outputs": {"shape": "WorkflowStepOutputs", "documentation": "<p>The file names that the workflow step created as output for this runtime instance of the workflow.</p>"}, "startTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the workflow step started.</p>"}, "endTime": {"shape": "DateTime", "documentation": "<p>The timestamp when the workflow step finished.</p>"}}, "documentation": "<p>Runtime details and status for the workflow step.</p>"}, "WorkflowStepName": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9-_]{1,99}$"}, "WorkflowStepOutputs": {"type": "string"}, "WorkflowStepTimeoutSecondsInteger": {"type": "integer", "max": 43200, "min": 0}, "WorkflowType": {"type": "string", "enum": ["BUILD", "TEST", "DISTRIBUTION"]}}, "documentation": "<p>EC2 Image Builder is a fully managed Amazon Web Services service that makes it easier to automate the creation, management, and deployment of customized, secure, and up-to-date \"golden\" server images that are pre-installed and pre-configured with software and settings to meet specific IT standards.</p>"}