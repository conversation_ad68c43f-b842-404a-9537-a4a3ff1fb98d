var _JUPYTERLAB;(_JUPYTERLAB=void 0===_JUPYTERLAB?{}:_JUPYTERLAB)["@pyviz/jupyterlab_pyviz"]=(()=>{"use strict";var e,r,t,n,o,i,a,u,l,s,p,f,d,c,h,v={281:(e,r,t)=>{var n={"./index":()=>t.e(979).then((()=>()=>t(979))),"./extension":()=>t.e(979).then((()=>()=>t(979))),"./style":()=>t.e(534).then((()=>()=>t(534)))},o=(e,r)=>(t.R=r,r=t.o(n,e)?n[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),t.R=void 0,r),i=(e,r)=>{if(t.S){var n=t.S.default,o="default";if(n&&n!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[o]=e,t.I(o,r)}};t.d(r,{get:()=>o,init:()=>i})}},y={};function g(e){if(y[e])return y[e].exports;var r=y[e]={id:e,exports:{}};return v[e](r,r.exports,g),r.exports}return g.m=v,g.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return g.d(r,{a:r}),r},g.d=(e,r)=>{for(var t in r)g.o(r,t)&&!g.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},g.f={},g.e=e=>Promise.all(Object.keys(g.f).reduce(((r,t)=>(g.f[t](e,r),r)),[])),g.u=e=>e+"."+{534:"20c70d4645cf38a0db5b",979:"b175a7785a2d666802fa"}[e]+".js",g.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),g.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="@pyviz/jupyterlab_pyviz:",g.l=(t,n,o)=>{if(e[t])e[t].push(n);else{var i,a;if(void 0!==o)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var s=u[l];if(s.getAttribute("src")==t||s.getAttribute("data-webpack")==r+o){i=s;break}}i||(a=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,g.nc&&i.setAttribute("nonce",g.nc),i.setAttribute("data-webpack",r+o),i.src=t),e[t]=[n];var p=(r,n)=>{i.onerror=i.onload=null,clearTimeout(f);var o=e[t];if(delete e[t],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),r)return r(n)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),a&&document.head.appendChild(i)}},g.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{g.S={};var e={},r={};g.I=(t,n)=>{n||(n=[]);var o=r[t];if(o||(o=r[t]={}),!(n.indexOf(o)>=0)){if(n.push(o),e[t])return e[t];g.o(g.S,t)||(g.S[t]={});var i=g.S[t],a="@pyviz/jupyterlab_pyviz",u=[];switch(t){case"default":((e,r,t)=>{var n=i[e]=i[e]||{},o=n[r];(!o||!o.loaded&&a>o.from)&&(n[r]={get:()=>g.e(979).then((()=>()=>g(979))),from:a})})("@pyviz/jupyterlab_pyviz","2.0.2")}return e[t]=u.length?Promise.all(u).then((()=>e[t]=1)):1}}})(),(()=>{var e;g.g.importScripts&&(e=g.g.location+"");var r=g.g.document;if(!e&&r&&(r.currentScript&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");t.length&&(e=t[t.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),g.p=e})(),t=e=>{var r=e=>e.split(".").map((e=>+e==e?+e:e)),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),n=t[1]?r(t[1]):[];return t[2]&&(n.length++,n.push.apply(n,r(t[2]))),t[3]&&(n.push([]),n.push.apply(n,r(t[3]))),n},n=(e,r)=>{e=t(e),r=t(r);for(var n=0;;){if(n>=e.length)return n<r.length&&"u"!=(typeof r[n])[0];var o=e[n],i=(typeof o)[0];if(n>=r.length)return"u"==i;var a=r[n],u=(typeof a)[0];if(i!=u)return"o"==i&&"n"==u||"s"==u||"u"==i;if("o"!=i&&"u"!=i&&o!=a)return o<a;n++}},o=e=>{if(1===e.length)return"*";if(0 in e){var r="",t=e[0];r+=0==t?">=":-1==t?"<":1==t?"^":2==t?"~":t>0?"=":"!=";for(var n=1,i=1;i<e.length;i++)n--,r+="u"==(typeof(u=e[i]))[0]?"-":(n>0?".":"")+(n=2,u);return r}var a=[];for(i=1;i<e.length;i++){var u=e[i];a.push(0===u?"not("+l()+")":1===u?"("+l()+" || "+l()+")":2===u?a.pop()+" "+a.pop():o(u))}return l();function l(){return a.pop().replace(/^\((.+)\)$/,"$1")}},i=(e,r)=>{if(0 in e){r=t(r);var n=e[0],o=n<0;o&&(n=-n-1);for(var a=0,u=1,l=!0;;u++,a++){var s,p,f=u<e.length?(typeof e[u])[0]:"";if(a>=r.length||"o"==(p=(typeof(s=r[a]))[0]))return!l||("u"==f?u>n&&!o:""==f!=o);if("u"==p){if(!l||"u"!=f)return!1}else if(l)if(f==p)if(u<=n){if(s!=e[u])return!1}else{if(o?s>e[u]:s<e[u])return!1;s!=e[u]&&(l=!1)}else if("s"!=f&&"n"!=f){if(o||u<=n)return!1;l=!1,u--}else{if(u<=n||p<f!=o)return!1;l=!1}else"s"!=f&&"n"!=f&&(l=!1,u--)}}var d=[],c=d.pop.bind(d);for(a=1;a<e.length;a++){var h=e[a];d.push(1==h?c()|c():2==h?c()&c():h?i(h,r):!c())}return!!c()},a=(e,r)=>{var t=g.S[e];if(!t||!g.o(t,r))throw new Error("Shared module "+r+" doesn't exist in shared scope "+e);return t},u=(e,r)=>{var t=e[r];return Object.keys(t).reduce(((e,r)=>!e||!t[e].loaded&&n(e,r)?r:e),0)},l=(e,r,t)=>"Unsatisfied version "+r+" of shared singleton module "+e+" (required "+o(t)+")",s=(e,r,t,n)=>{var o=u(e,t);return i(n,o)||"undefined"!=typeof console&&console.warn&&console.warn(l(t,o,n)),p(e[t][o])},p=e=>(e.loaded=1,e.get()),f=(e=>function(r,t,n,o){var i=g.I(r);return i&&i.then?i.then(e.bind(e,r,g.S[r],t,n,o)):e(r,g.S[r],t,n)})(((e,r,t,n)=>(a(e,t),s(r,0,t,n)))),d={},c={129:()=>f("default","@lumino/disposable",[1,1,4,3]),292:()=>f("default","@jupyter-widgets/jupyterlab-manager",[1,3,0,0]),373:()=>f("default","@jupyterlab/docmanager",[1,3,0,11]),510:()=>f("default","@lumino/widgets",[1,1,16,1])},h={979:[129,292,373,510]},g.f.consumes=(e,r)=>{g.o(h,e)&&h[e].forEach((e=>{if(g.o(d,e))return r.push(d[e]);var t=r=>{d[e]=0,v[e]=t=>{delete y[e],t.exports=r()}},n=r=>{delete d[e],v[e]=t=>{throw delete y[e],r}};try{var o=c[e]();o.then?r.push(d[e]=o.then(t).catch(n)):t(o)}catch(e){n(e)}}))},(()=>{var e={924:0};g.f.j=(r,t)=>{var n=g.o(e,r)?e[r]:void 0;if(0!==n)if(n)t.push(n[2]);else{var o=new Promise(((t,o)=>{n=e[r]=[t,o]}));t.push(n[2]=o);var i=g.p+g.u(r),a=new Error;g.l(i,(t=>{if(g.o(e,r)&&(0!==(n=e[r])&&(e[r]=void 0),n)){var o=t&&("load"===t.type?"missing":t.type),i=t&&t.target&&t.target.src;a.message="Loading chunk "+r+" failed.\n("+o+": "+i+")",a.name="ChunkLoadError",a.type=o,a.request=i,n[1](a)}}),"chunk-"+r)}};var r=(r,t)=>{for(var n,o,[i,a,u]=t,l=0,s=[];l<i.length;l++)o=i[l],g.o(e,o)&&e[o]&&s.push(e[o][0]),e[o]=0;for(n in a)g.o(a,n)&&(g.m[n]=a[n]);for(u&&u(g),r&&r(t);s.length;)s.shift()()},t=self.webpackChunk_pyviz_jupyterlab_pyviz=self.webpackChunk_pyviz_jupyterlab_pyviz||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),g(281)})();