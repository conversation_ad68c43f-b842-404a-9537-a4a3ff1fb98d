.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <http://docutils.sourceforge.net>.

.. |af|                      unicode:: U+02061 .. FUNCTION APPLICATION
.. |asympeq|                 unicode:: U+0224D .. EQUIVALENT TO
.. |Cross|                   unicode:: U+02A2F .. VECTOR OR CROSS PRODUCT
.. |DD|                      unicode:: U+02145 .. DOUBLE-STRUCK ITALIC CAPITAL D
.. |dd|                      unicode:: U+02146 .. DOUBLE-STRUCK ITALIC SMALL D
.. |DownArrowBar|            unicode:: U+02913 .. DOWNWARDS ARROW TO BAR
.. |DownBreve|               unicode:: U+00311 .. COMBINING INVERTED BREVE
.. |DownLeftRightVector|     unicode:: U+02950 .. LEFT BARB DOWN RIGHT BARB DOWN HARPOON
.. |DownLeftTeeVector|       unicode:: U+0295E .. LEFTWARDS HARPOON WITH BARB DOWN FROM BAR
.. |DownLeftVectorBar|       unicode:: U+02956 .. LEFTWARDS HARPOON WITH BARB DOWN TO BAR
.. |DownRightTeeVector|      unicode:: U+0295F .. RIGHTWARDS HARPOON WITH BARB DOWN FROM BAR
.. |DownRightVectorBar|      unicode:: U+02957 .. RIGHTWARDS HARPOON WITH BARB DOWN TO BAR
.. |ee|                      unicode:: U+02147 .. DOUBLE-STRUCK ITALIC SMALL E
.. |EmptySmallSquare|        unicode:: U+025FB .. WHITE MEDIUM SQUARE
.. |EmptyVerySmallSquare|    unicode:: U+025AB .. WHITE SMALL SQUARE
.. |Equal|                   unicode:: U+02A75 .. TWO CONSECUTIVE EQUALS SIGNS
.. |FilledSmallSquare|       unicode:: U+025FC .. BLACK MEDIUM SQUARE
.. |FilledVerySmallSquare|   unicode:: U+025AA .. BLACK SMALL SQUARE
.. |GreaterGreater|          unicode:: U+02AA2 .. DOUBLE NESTED GREATER-THAN
.. |Hat|                     unicode:: U+0005E .. CIRCUMFLEX ACCENT
.. |HorizontalLine|          unicode:: U+02500 .. BOX DRAWINGS LIGHT HORIZONTAL
.. |ic|                      unicode:: U+02063 .. INVISIBLE SEPARATOR
.. |ii|                      unicode:: U+02148 .. DOUBLE-STRUCK ITALIC SMALL I
.. |it|                      unicode:: U+02062 .. INVISIBLE TIMES
.. |larrb|                   unicode:: U+021E4 .. LEFTWARDS ARROW TO BAR
.. |LeftDownTeeVector|       unicode:: U+02961 .. DOWNWARDS HARPOON WITH BARB LEFT FROM BAR
.. |LeftDownVectorBar|       unicode:: U+02959 .. DOWNWARDS HARPOON WITH BARB LEFT TO BAR
.. |LeftRightVector|         unicode:: U+0294E .. LEFT BARB UP RIGHT BARB UP HARPOON
.. |LeftTeeVector|           unicode:: U+0295A .. LEFTWARDS HARPOON WITH BARB UP FROM BAR
.. |LeftTriangleBar|         unicode:: U+029CF .. LEFT TRIANGLE BESIDE VERTICAL BAR
.. |LeftUpDownVector|        unicode:: U+02951 .. UP BARB LEFT DOWN BARB LEFT HARPOON
.. |LeftUpTeeVector|         unicode:: U+02960 .. UPWARDS HARPOON WITH BARB LEFT FROM BAR
.. |LeftUpVectorBar|         unicode:: U+02958 .. UPWARDS HARPOON WITH BARB LEFT TO BAR
.. |LeftVectorBar|           unicode:: U+02952 .. LEFTWARDS HARPOON WITH BARB UP TO BAR
.. |LessLess|                unicode:: U+02AA1 .. DOUBLE NESTED LESS-THAN
.. |mapstodown|              unicode:: U+021A7 .. DOWNWARDS ARROW FROM BAR
.. |mapstoleft|              unicode:: U+021A4 .. LEFTWARDS ARROW FROM BAR
.. |mapstoup|                unicode:: U+021A5 .. UPWARDS ARROW FROM BAR
.. |MediumSpace|             unicode:: U+0205F .. MEDIUM MATHEMATICAL SPACE
.. |nbump|                   unicode:: U+0224E U+00338 .. GEOMETRICALLY EQUIVALENT TO with slash
.. |nbumpe|                  unicode:: U+0224F U+00338 .. DIFFERENCE BETWEEN with slash
.. |nesim|                   unicode:: U+02242 U+00338 .. MINUS TILDE with slash
.. |NewLine|                 unicode:: U+0000A .. LINE FEED (LF)
.. |NoBreak|                 unicode:: U+02060 .. WORD JOINER
.. |NotCupCap|               unicode:: U+0226D .. NOT EQUIVALENT TO
.. |NotHumpEqual|            unicode:: U+0224F U+00338 .. DIFFERENCE BETWEEN with slash
.. |NotLeftTriangleBar|      unicode:: U+029CF U+00338 .. LEFT TRIANGLE BESIDE VERTICAL BAR with slash
.. |NotNestedGreaterGreater| unicode:: U+02AA2 U+00338 .. DOUBLE NESTED GREATER-THAN with slash
.. |NotNestedLessLess|       unicode:: U+02AA1 U+00338 .. DOUBLE NESTED LESS-THAN with slash
.. |NotRightTriangleBar|     unicode:: U+029D0 U+00338 .. VERTICAL BAR BESIDE RIGHT TRIANGLE with slash
.. |NotSquareSubset|         unicode:: U+0228F U+00338 .. SQUARE IMAGE OF with slash
.. |NotSquareSuperset|       unicode:: U+02290 U+00338 .. SQUARE ORIGINAL OF with slash
.. |NotSucceedsTilde|        unicode:: U+0227F U+00338 .. SUCCEEDS OR EQUIVALENT TO with slash
.. |OverBar|                 unicode:: U+000AF .. MACRON
.. |OverBrace|               unicode:: U+0FE37 .. PRESENTATION FORM FOR VERTICAL LEFT CURLY BRACKET
.. |OverBracket|             unicode:: U+023B4 .. TOP SQUARE BRACKET
.. |OverParenthesis|         unicode:: U+0FE35 .. PRESENTATION FORM FOR VERTICAL LEFT PARENTHESIS
.. |planckh|                 unicode:: U+0210E .. PLANCK CONSTANT
.. |Product|                 unicode:: U+0220F .. N-ARY PRODUCT
.. |rarrb|                   unicode:: U+021E5 .. RIGHTWARDS ARROW TO BAR
.. |RightDownTeeVector|      unicode:: U+0295D .. DOWNWARDS HARPOON WITH BARB RIGHT FROM BAR
.. |RightDownVectorBar|      unicode:: U+02955 .. DOWNWARDS HARPOON WITH BARB RIGHT TO BAR
.. |RightTeeVector|          unicode:: U+0295B .. RIGHTWARDS HARPOON WITH BARB UP FROM BAR
.. |RightTriangleBar|        unicode:: U+029D0 .. VERTICAL BAR BESIDE RIGHT TRIANGLE
.. |RightUpDownVector|       unicode:: U+0294F .. UP BARB RIGHT DOWN BARB RIGHT HARPOON
.. |RightUpTeeVector|        unicode:: U+0295C .. UPWARDS HARPOON WITH BARB RIGHT FROM BAR
.. |RightUpVectorBar|        unicode:: U+02954 .. UPWARDS HARPOON WITH BARB RIGHT TO BAR
.. |RightVectorBar|          unicode:: U+02953 .. RIGHTWARDS HARPOON WITH BARB UP TO BAR
.. |RoundImplies|            unicode:: U+02970 .. RIGHT DOUBLE ARROW WITH ROUNDED HEAD
.. |RuleDelayed|             unicode:: U+029F4 .. RULE-DELAYED
.. |Tab|                     unicode:: U+00009 .. CHARACTER TABULATION
.. |ThickSpace|              unicode:: U+02009 U+0200A U+0200A .. space of width 5/18 em
.. |UnderBar|                unicode:: U+00332 .. COMBINING LOW LINE
.. |UnderBrace|              unicode:: U+0FE38 .. PRESENTATION FORM FOR VERTICAL RIGHT CURLY BRACKET
.. |UnderBracket|            unicode:: U+023B5 .. BOTTOM SQUARE BRACKET
.. |UnderParenthesis|        unicode:: U+0FE36 .. PRESENTATION FORM FOR VERTICAL RIGHT PARENTHESIS
.. |UpArrowBar|              unicode:: U+02912 .. UPWARDS ARROW TO BAR
.. |Upsilon|                 unicode:: U+003A5 .. GREEK CAPITAL LETTER UPSILON
.. |VerticalLine|            unicode:: U+0007C .. VERTICAL LINE
.. |VerticalSeparator|       unicode:: U+02758 .. LIGHT VERTICAL BAR
.. |ZeroWidthSpace|          unicode:: U+0200B .. ZERO WIDTH SPACE
