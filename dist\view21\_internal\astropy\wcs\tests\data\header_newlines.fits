SIMPLE  =                    T / Fits standard                                  BITPIX  =                  -64 / FOUR-BYTE SINGLE PRECISION FLOATING POINT      NAXIS   =                    2 / STANDARD FITS FORMAT                           NAXIS1  =                    1 / STANDARD FITS FORMAT                           NAXIS2  =                    1 / STANDARD FITS FORMAT                           ORIGIN  = 'Palomar Transient Factory' / Origin of these image data              CREATOR = 'Infrared Processing and Analysis Center' / Creator of this FITS file DATE    = '2011-08-01T15:14:04' / File creation date (YYYY-MM-DDThh:mm:ss UT)                                                                                             / PTF DMASK BIT DEFINITIONS                                                                                                                           BIT00   =                    0 / AIRCRAFT/SATELLITE TRACK                       BIT01   =                    1 / OBJECT (detected by SExtractor)                BIT02   =                    2 / HIGH DARK-CURRENT                              BIT03   =                    3 / RESERVED FOR FUTURE USE                        BIT04   =                    4 / NOISY                                          BIT05   =                    5 / GHOST                                          BIT06   =                    6 / CCD BLEED                                      BIT07   =                    7 / RAD HIT                                        BIT08   =                    8 / SATURATED                                      BIT09   =                    9 / DEAD/BAD                                       BIT10   =                   10 / NAN (not a number)                             BIT11   =                   11 / DIRTY (10-sigma below coarse local median)     BIT12   =                   12 / HALO                                           BIT13   =                   13 / RESERVED FOR FUTURE USE                        BIT14   =                   14 / RESERVED FOR FUTURE USE                        BIT15   =                   15 / RESERVED FOR FUTURE USE                                                                                                                  / DATA FLOW                                                                                                                                           PMASKPTH= '/ptf/pos/archive/fallbackcal/pmasks/' / Pixel-mask pathname          PMASKFIL= 'bpm_2009060s_c07.v2.fits' / Pixel-mask filename                      UDMPROC = 'updatemask'         / Update bit in dmask                            UDMVERSN=                   1. / Version of updatemask program                  UDMOP   =                    0 / Operation type                                 UDMMBT  =                    4 / Mask bit template                              UDMIFIL = 'bpm_2009060s_c07.v1.fits' / Program updatemask input file            UDMOFIL = 'bpm_2009060s_c07.v2.fits' / Program updatemask output file           MCVERSN =                   1. / Version of ptfMaskCombine program              PTFPPROC= 'ptfPostProc'        / Flags proc NaNs, CCD-bleeds and rad hits       PPRVERSN=                   3. / Version of ptfPostProc program                                                                                                           / COPY OF IMAGE HEADER BELOW                                                                                                                          ORIGIN  = 'Palomar Transient Factory' / Origin of these image data              CREATOR = 'Infrared Processing and Analysis Center' / Creator of this FITS file TELESCOP= 'P48     '           / Name of telescope                              INSTRUME= 'PTF/MOSAIC'         / Instrument name                                OBSERVER= 'KulkarniPTF'        / Observer name and project                      CCDID   = '7       '           / CCD number (0..11)                             DATE-OBS= '2009-06-25T08:41:23.970' / UTC shutter time YYYY-MM-DDTHH:MM:SS.SSS  DATE    = '2011-07-30T15:04:59' / File creation date (YYYY-MM-DDThh:mm:ss UT)   REFERENC= 'http://www.astro.caltech.edu/ptf' / URL of PTF website                                                                                                         / PROPOSAL INFORMATION                                                                                                                                PTFPRPI = 'Kulkarni'           / PTF Project PI                                 PTFPID  = '20000   '           / Project type: 00000-49999                      OBJECT  = 'PTF_survey'         / Fields object                                  PTFFIELD= '2899    '           / PTF unique field ID                            PTFFLAG = '1       '           / 1 = PTF; 0 = non-PTF category                                                                                                            / TIME AND EXPOSURE INFORMATION                                                                                                                       FILTER  = 'R       '           / Filter name                                    FILTERID= '2       '           / Filter ID                                      FILTERSL= '2       '           / Filter changer slot position                   EXPTIME =                  60. / [s] Requested exposure time                    AEXPTIME=                  60. / actual exposure time (sec)                     UTC-OBS = '2009-06-25T08:41:23.970' / UTC time shutter open YYYY-MM-DDTHH:MM:SS.OBSJD   =        2455007.86207 / [day] Julian day corresponds to UTC-OBS        OBSMJD  =          55007.36207 / MJD corresponds to UTC-OBS (day)               OBSLST  = '19:08:26.70'        / Mean LST corresponds to UTC-OBS 'HH:MM:SS.S'   HOURANG = '-3:09:09.78'        / Mean HA (sHH:MM:SS.S) based on LMST at UTC-OBS HJD     =        2455007.86457 / [day] Heliocentric Julian Day                  OBSTYPE = 'object  '           / Image type (dark,science,bias,focus)           IMGTYP  = 'object  '           / Image type (dark,science,bias,focus)                                                                                                     / MOON AND SUN                                                                                                                                        MOONRA  =           131.676395 / [deg] Moon J2000.0 R.A.                        MOONDEC =            16.207046 / [deg] Moon J2000.0 Dec.                        MOONILLF=             0.095389 / [frac] Moon illuminated fraction               MOONPHAS=             144.0201 / [deg] Moon phase angle                         MOONESB =                  -0. / Moon excess in sky brightness V-band           MOONALT =            -35.16959 / [deg] Moon altitude                            SUNAZ   =             13.90467 / [deg] Sun azimuth                              SUNALT  =            -31.96011 / [deg] Sun altitude                                                                                                                       / PHOTOMETRY                                                                                                                                          BUNIT   = 'DN      '           / Data number (analog-to-digital units or ADU)   PHTCALEX=                    1 / Was phot.-cal. module executed?                PHTCALFL=                    0 / Flag for image is photometric (0=N, 1=Y)       PCALRMSE=             0.030059 / RMSE from (zeropoint, extinction) data fit     IMAGEZPT=             22.43948 / Image magnitude zeropoint                      IZPORIG = 'CALTRANS'           / Photometric-calibration origin                 ZPRULE  = 'COMPUTE '           / Photometric-calibration method                 MAGZPT  =             22.73683 / Magnitude zeropoint at airmass=1               EXTINCT =              0.17995 / Extinction                                     APSFILT = 'r       '           / SDSS filter used in abs phot cal               APSCOL  = 'r-i     '           / SDSS color used in abs phot cal                APRMS   =            0.0554857 / RMS in mag of final abs phot cal               APBSRMS =           0.03911367 / RMS in mag of final abs phot cal for bright staAPNSTDI1=                69501 / Number of standard stars in first iteration    APNSTDIF=                64858 / Number of standard stars in final iteration    APCHI2  =      325618.88012443 / Chi2 of final abs phot cal                     APDOF   =               64858. / Dof of chi2 of final abs phot cal              APMEDJD =     2455007.84500722 / Median JD used in abs phot cal                 APPN01  = 'ZeroPoint'          / Name of parameter abs phot cal 01              APPAR01 =          22.81878918 / Value of parameter abs phot cal 01             APPARE01=           0.00198923 / Error of parameter abs phot cal 01             APPN02  = 'ColorTerm'          / Name of parameter abs phot cal 02              APPAR02 =           0.20481246 / Value of parameter abs phot cal 02             APPARE02=           0.00278076 / Error of parameter abs phot cal 02             APPN03  = 'AirMassTerm'        / Name of parameter abs phot cal 03              APPAR03 =          -0.12104427 / Value of parameter abs phot cal 03             APPARE03=            0.0011621 / Error of parameter abs phot cal 03             APPN04  = 'AirMassColorTerm'   / Name of parameter abs phot cal 04              APPAR04 =           0.00904321 / Value of parameter abs phot cal 04             APPARE04=           0.00176968 / Error of parameter abs phot cal 04             APPN05  = 'TimeTerm'           / Name of parameter abs phot cal 05              APPAR05 =           0.03012546 / Value of parameter abs phot cal 05             APPARE05=           0.00459951 / Error of parameter abs phot cal 05             APPN06  = 'Time2Term'          / Name of parameter abs phot cal 06              APPAR06 =           1.27460327 / Value of parameter abs phot cal 06             APPARE06=           0.07646497 / Error of parameter abs phot cal 06             APPN07  = 'XTerm   '           / Name of parameter abs phot cal 07              APPAR07 =           0.00768843 / Value of parameter abs phot cal 07             APPARE07=           0.00083226 / Error of parameter abs phot cal 07             APPN08  = 'YTerm   '           / Name of parameter abs phot cal 08              APPAR08 =           0.06680527 / Value of parameter abs phot cal 08             APPARE08=           0.00203667 / Error of parameter abs phot cal 08             APPN09  = 'Y2Term  '           / Name of parameter abs phot cal 09              APPAR09 =           0.31486016 / Value of parameter abs phot cal 09             APPARE09=           0.00318862 / Error of parameter abs phot cal 09             APPN10  = 'Y3Term  '           / Name of parameter abs phot cal 10              APPAR10 =           0.69934253 / Value of parameter abs phot cal 10             APPARE10=           0.01257477 / Error of parameter abs phot cal 10             APPN11  = 'XYTerm  '           / Name of parameter abs phot cal 11              APPAR11 =          -0.04590337 / Value of parameter abs phot cal 11             APPARE11=           0.00286729 / Error of parameter abs phot cal 11                                                                                                       / ASTROMETRY                                                                                                                                          CRVAL1  =     333.443801401309 / [deg] RA of reference point                    CRVAL2  =     3.08905544069643 / [deg] DEC of reference point                   CRPIX1  =             1175.019 / [pix] Image reference point                    CRPIX2  =             945.8826 / [pix] Image reference point                    CTYPE1  = 'RA---TAN-SIP'       / TAN (gnomic) projection + SIP distortions      CTYPE2  = 'DEC--TAN-SIP'       / TAN (gnomic) projection + SIP distortions      CUNIT1  = 'deg     '           / Image axis-1 celestial-coordinate units        CUNIT2  = 'deg     '           / Image axis-2 celestial-coordinate units        CRTYPE1 = 'deg     '           / Data units of CRVAL1                           CRTYPE2 = 'deg     '           / Data units of CRVAL2                           CD1_1   = 0.000281094342514378 / Transformation matrix                          CD1_2   = -5.00875320999652E-09                                                 CD2_1   = -2.08930602680508E-07                                                 CD2_2   = -0.000281284158795544                                                 OBJRA   = '22:17:08.571'       / Requested field J2000.0 Ra.                    OBJDEC  = '+03:22:30.00'       / Requested field J2000.0 Dec.                   OBJRAD  =           334.285714 / [deg] Requested field RA (J2000.0)             OBJDECD =                3.375 / [deg] Requested field Dec (J2000.0)            PIXSCALE=                 1.01 / [arcsec/pix] Pixel scale                       WCSAXES =                    2                                                  EQUINOX =                2000. / [yr] Equatorial coordinates definition         LONPOLE =                 180.                                                  LATPOLE =                   0.                                                                                                                                            / IMAGE QUALITY                                                                                                                                       SEEING  =                 2.04 / [pix] Seeing FWHM                              PEAKDIST=    0.396793397122491 / [pix] Mean dist brightest pixel-centroid pixel ELLIP   =                0.063 / Mean image ellipticity A/B                     ELLIPPA =                48.65 / [deg] Mean image ellipticity PA                FBIAS   =             1060.884 / [DN] Floating bias of the image                SATURVAL=               17000. / [DN] Saturation value of the CCD array         FWHMSEX =                 2.45 / [arcsec] SExtractor SEEING estimate            MDSKYMAG=             20.53072 / [mag/s-arcsec^2] Median sky obsolete           MSMAPCZP=             20.70926 / [mag/s-arcsec^2] Median sky abs. phot. cal.    LIMITMAG=             20.92587 / [mag/s-arcsec^2] Limiting magnitude obsolete   LMGAPCZP=             21.10442 / [mag/s-arcsec^2] Limiting mag. abs. phot. cal. MEDFWHM =             2.931446 / [arcsecond] Median FWHM                        MEDELONG=             1.132416 / [dimensionless] Median elongation              STDELONG=            0.3298569 / [dimensionless] Std. dev. of elongation        MEDTHETA=            -42.28234 / [deg] Atan(median sin(theta)/median cos(theta))STDTHETA=             66.21399 / [deg] Atan(stddev sin(theta)/stddev cos(theta))MEDDLMAG=             33.85928 / [mag/s-arcsec^2] Median (MU_MAX-MAG_AUTO)      STDDLMAG=            0.4367887 / [mag/s-arcsec^2] Stddev of (MU_MAX-MAG_AUTO)                                                                                             / OBSERVATORY AND TCS                                                                                                                                 OCS_TIME= '2009-06-25T08:41:23.978' / UTC Date for OCS calc time-dep params     OPERMODE= 'OCS     '           / Mode of operation: OCS | Manual | N/A          SOFTVER = '1.1.1.1 '           / Softwere version (TCS.Camera.OCS.Sched)        OCS_VER = '1       '           / OCS software version and date                  TCS_VER = '1       '           / TCS software version and date                  SCH_VER = '1       '           / OCS-Scheduler software version and date        MAT_VER = '7.7.0.471'          / Matlab version                                 HDR_VER = '1       '           / Header version                                 TRIGGER = 'N/A     '           / trigger ID for TOO, e.g. VOEVENT-Nr            TCSMODE = 'Star    '           / TCS fundamental mode                           TCSSMODE= 'Active  '           / TCS fundamental submode                        TCSFMODE= 'Pos     '           / TCS focus mode                                 TCSFSMOD= 'On-Target'          / TCS focus submode                              TCSDMODE= 'Stop    '           / TCS dome mode                                  TCSDSMOD= 'N/A     '           / TCS dome submode                               TCSWMODE= 'Slave   '           / TCS windscreen mode                            TCSWSMOD= 'N/A     '           / TCS windscreen submode                         OBSLAT  =              33.3574 / [deg] Telescope geodetic latitude in WGS84     OBSLON  =             116.8599 / [deg] Telescope geodetic longitude in WGS84    OBSALT  =               1703.2 / [m] Telescope geodetic altitude in WGS84       DEFOCUS =                   0. / [mm] Focus position - nominal focus            FOCUSPOS=               1.3851 / [mm] Exposures focusPos                        DOMESTAT= 'open    '           / Dome status at begining of exposure            TRACKRA =                 23.7 / [arcsec/hr] Track speed RA rel to sidereal     TRACKDEC=                -11.2 / [arcsec/hr] Track speed Dec rel to sidereal    AZIMUTH =             113.8682 / [deg] Telescope Azimuth                        ALTITUDE=             36.81047 / [deg] Telescope altitude                       AIRMASS =             1.666232 / Telescope airmass                              TELRA   =              334.402 / [deg] Telescope ap equinox of date RA          TELDEC  =               3.4225 / [deg] Telescope ap equinox of date Dec         TELHA   =             312.7096 / [deg] Telescope ap equinox of date HA          DOMEAZ  =             112.8563 / [deg] Dome azimuth                             WINDSCAL=               10.466 / [deg] Wind screen altitude                     WINDDIR =                  2.2 / [deg] Azimuth of wind direction                WINDSPED=              14.6328 / Wind speed (km/hour)                           OUTTEMP =             20.94444 / [C] Outside temperature                        OUTRELHU=                 0.09 / [frac] Outside relative humidity               OUTDEWPT=            -12.94444 / [C] Outside dew point                                                                                                                    / INSTRUMENT TELEMETRY                                                                                                                                PANID   = '_p48s   '           / PAN identification                             DHSID   = '_p48s   '           / DHS identification                             ROISTATE= 'ROI     '           / ROI State (FULL | ROI)                         CCDSEC  = '[1:2048,1:4096]'    / CCD section                                    CCDSIZE = '[1:2048,1:4096]'    / CCD size                                       DATASEC = '[1:2048,1:4096]'    / Data section                                   DETSEC  = '[1:2048,1:4096]'    / Detector section                               ROISEC  = '[1:2048,1:4096]'    / ROI section                                    FPA     = 'P48MOSAIC'          / Focal plan array                               CCDNAME = 'W94C2   '           / Detector mfg serial number                     CHECKSUM= 'O3aBP1ZBO1aBO1YB'   / HDU checksum updated 2010-03-15T13:06:37       DATASUM = '395763289'          / Data unit checksum updated 2010-03-15T13:06:37 DHEINF  = 'SDSU, Gen-III'      / Controller info                                DHEFIRM = '/usr/src/dsp/tim_m.lod' / DSP software                               CAM_VER = '20090615.1.3.100000' / Camera server date.rev.cfitsio                LV_VER  = '8.5     '           / LabVIEW software version                       PCI_VER = '2.0c    '           / Astropci software version                      DETID   = 'PTF/MOSAIC'         / Detector ID                                    AUTHOR  = 'PTF/OCS/TCS/Camera' / Source for header information                  DATAMIN =                   0. / Minimum value for array                        ROISTATE= 'ROI     '           / ROI State (FULL | ROI)                         LEDBLUE = 'OFF     '           / 470nm LED state (ON | OFF)                     LEDRED  = 'OFF     '           / 660nm LED state (ON | OFF)                     LEDNIR  = 'OFF     '           / 880nm LED state (ON | OFF)                     CCD9TEMP=              175.003 / [K] 0x0 servo temp sensor on CCD09             HSTEMP  =              148.207 / [K] 0x1 heat spreader temp                     DHE0TEMP=              295.951 / [K] 0x2 detector head electronics temp, master DHE1TEMP=              298.162 / [K] 0x3 detector head electronics temp, slave  DEWWTEMP=               284.71 / [K] 0x4 dewar wall temp                        HEADTEMP=              138.414 / [K] 0x5 cryo cooler cold head temp             CCD5TEMP=               174.91 / [K] 0x6 temp sensor on CCD05                   CCD11TEM=              176.061 / [K] 0x7 temp sensor on CCD11                   CCD0TEMP=              169.515 / [K] 0x8 temp sensor on CCD00                   RSTEMP  =               232.61 / [K] 0x9 temp sensor on radiation shield        DEWPRESS=                 0.82 / [milli-torr] Dewar pressure                    DETHEAT =                  38. / [%] Detector focal plane heater power          NAMPSXY = '6 2     '           / Number of amplifiers in x y                    CCDSUM  = '1 1     '           / [pix] Binning in x and y                       MODELFOC= 'N/A     '           / MODELFOC                                       CHECKSUM= '6aLZ8ZKZ6aKZ6YKZ'   / HDU checksum updated 2010-03-15T13:06:37       DATASUM = '         0'         / Data unit checksum (2010-03-15T13:06:37)       GAIN    =                  1.7 / [e-/D.N.] Gain of detector.                    READNOI =                  5.1 / [e-] Read noise of detector.                   DARKCUR =                  0.1 / [e-/s] Dark current of detector                                                                                                          / SCAMP DISTORTION KEYWORDS                                                                                                                           RADECSYS= 'ICRS    '           / Astrometric system                             PV1_0   =                   0. / Projection distortion parameter                PV1_1   =                   1. / Projection distortion parameter                PV1_2   =                   0. / Projection distortion parameter                PV1_4   = 0.000811808026654439 / Projection distortion parameter                PV1_5   = 0.000610424561546246 / Projection distortion parameter                PV1_6   = 0.000247550637436069 / Projection distortion parameter                PV1_7   = 0.000103962986153903 / Projection distortion parameter                PV1_8   = -0.000463678684598807 / Projection distortion parameter               PV1_9   = -0.000431244263972048 / Projection distortion parameter               PV1_10  = -0.000152691163850316 / Projection distortion parameter               PV1_12  = -0.00204628855915067 / Projection distortion parameter                PV1_13  = -0.00173071932398225 / Projection distortion parameter                PV1_14  = 0.000212015319199711 / Projection distortion parameter                PV1_15  = -0.000489268678679085 / Projection distortion parameter               PV1_16  = -0.000182891514774611 / Projection distortion parameter               PV2_0   =                   0. / Projection distortion parameter                PV2_1   =                   1. / Projection distortion parameter                PV2_2   =                   0. / Projection distortion parameter                PV2_4   = 0.000273521447624334 / Projection distortion parameter                PV2_5   = 0.000876139200581004 / Projection distortion parameter                PV2_6   = -0.000122736852992318 / Projection distortion parameter               PV2_7   = -0.00115870481394187 / Projection distortion parameter                PV2_8   = 0.000744209714565589 / Projection distortion parameter                PV2_9   = -0.00031431316953523 / Projection distortion parameter                PV2_10  = -0.00025720525696749 / Projection distortion parameter                PV2_12  = -0.00074859772103692 / Projection distortion parameter                PV2_13  = 0.000838107200656415 / Projection distortion parameter                PV2_14  = -0.00012633881376049 / Projection distortion parameter                PV2_15  =  -0.0020312867769692 / Projection distortion parameter                PV2_16  =  0.00524608854745148 / Projection distortion parameter                FGROUPNO=                    1 / SCAMP field group label                        ASTIRMS1=                   0. / Astrom. dispersion RMS (intern., high S/N)     ASTIRMS2=                   0. / Astrom. dispersion RMS (intern., high S/N)     ASTRRMS1=         3.620458E-05 / Astrom. dispersion RMS (ref., high S/N)        ASTRRMS2=         3.332156E-05 / Astrom. dispersion RMS (ref., high S/N)        ASTINST =                    1 / SCAMP astrometric instrument label             FLXSCALE=                   0. / SCAMP relative flux scale                      MAGZEROP=                   0. / SCAMP zero-point                               PHOTIRMS=                   0. / mag dispersion RMS (internal, high S/N)        RA_RMS  =            0.1655724 / [arcsec] RMS of SCAMP fit from 2MASS matching  DEC_RMS =            0.1891921 / [arcsec] RMS of SCAMP fit from 2MASS matching  ASTROMN =                  384 / Number of stars in SCAMP astrometric solution  SCAMPPTH= '/ptf/pos/archive/fallbackcal/scamp/7/' / SCAMP catalog path          SCAMPFIL= 'PTF_201006174759_c_e_uca3_t112521_u001916251_f02_p002899_c07.fits'                                                                                             / SIP DISTORTION KEYWORDS                                                                                                                             A_ORDER =                    4 / Distortion order for A                         A_0_2   = 6.96807813586153E-08 / Projection distortion parameter                A_0_3   = 1.20881759870351E-11 / Projection distortion parameter                A_0_4   = -4.07297345125509E-15 / Projection distortion parameter               A_1_1   = -1.71602989085006E-07 / Projection distortion parameter               A_1_2   = -3.40958003336147E-11 / Projection distortion parameter               A_1_3   = 1.08769435952671E-14 / Projection distortion parameter                A_2_0   = 2.28067760155696E-07 / Projection distortion parameter                A_2_1   =  3.6610309234789E-11 / Projection distortion parameter                A_2_2   = 4.73755078335384E-15 / Projection distortion parameter                A_3_0   = 8.24210855193549E-12 / Projection distortion parameter                A_3_1   = 3.84753767306115E-14 / Projection distortion parameter                A_4_0   = -4.54223812412034E-14 / Projection distortion parameter               A_DMAX  =     1.53122472683886 / Projection distortion parameter                B_ORDER =                    4 / Distortion order for B                         B_0_2   = -7.69933957449607E-08 / Projection distortion parameter               B_0_3   = -9.16855566272424E-11 / Projection distortion parameter               B_0_4   = 1.66630509620112E-14 / Projection distortion parameter                B_1_1   = 2.46289708854316E-07 / Projection distortion parameter                B_1_2   = -5.90207917198792E-11 / Projection distortion parameter               B_1_3   = 1.86811615261732E-14 / Projection distortion parameter                B_2_0   = 3.44908367419592E-08 / Projection distortion parameter                B_2_1   = -2.49509936365959E-11 / Projection distortion parameter               B_2_2   = 2.84841315780067E-15 / Projection distortion parameter                B_3_0   = 2.02845080441181E-11 / Projection distortion parameter                B_3_1   = -4.51317603382652E-14 / Projection distortion parameter               B_4_0   = -1.16438849571175E-13 / Projection distortion parameter               B_DMAX  =     2.89468553502114 / Projection distortion parameter                AP_ORDER=                    4 / Distortion order for AP                        AP_0_1  = -2.3927681685928E-08 / Projection distortion parameter                AP_0_2  = -6.97379868441328E-08 / Projection distortion parameter               AP_0_3  = -1.21069584606865E-11 / Projection distortion parameter               AP_0_4  = 4.07524721573973E-15 / Projection distortion parameter                AP_1_0  = 5.65239128994064E-08 / Projection distortion parameter                AP_1_1  = 1.71734217296344E-07 / Projection distortion parameter                AP_1_2  = 3.41724875038451E-11 / Projection distortion parameter                AP_1_3  = -1.08775499102067E-14 / Projection distortion parameter               AP_2_0  = -2.28068482487158E-07 / Projection distortion parameter               AP_2_1  = -3.66548961802381E-11 / Projection distortion parameter               AP_2_2  = -4.75858241735224E-15 / Projection distortion parameter               AP_3_0  = -8.24781966878619E-12 / Projection distortion parameter               AP_3_1  = -3.85281201904104E-14 / Projection distortion parameter               AP_4_0  = 4.54275049666924E-14 / Projection distortion parameter                BP_ORDER=                    4 / Distortion order for BP                        BP_0_1  = -1.50638746640517E-07 / Projection distortion parameter               BP_0_2  = 7.70565767927487E-08 / Projection distortion parameter                BP_0_3  = 9.18374546897802E-11 / Projection distortion parameter                BP_0_4  = -1.66839467627906E-14 / Projection distortion parameter               BP_1_0  = -4.87195269294628E-08 / Projection distortion parameter               BP_1_1  = -2.46371690411844E-07 / Projection distortion parameter               BP_1_2  =  5.9111535979953E-11 / Projection distortion parameter                BP_1_3  = -1.87729776729012E-14 / Projection distortion parameter               BP_2_0  = -3.46046151217313E-08 / Projection distortion parameter               BP_2_1  = 2.51320825919019E-11 / Projection distortion parameter                BP_2_2  = -2.85758325791527E-15 / Projection distortion parameter               BP_3_0  = -2.04221364218494E-11 / Projection distortion parameter               BP_3_1  = 4.51336286236569E-14 / Projection distortion parameter                BP_4_0  = 1.16567578965612E-13 / Projection distortion parameter                                                                                                          / DATA FLOW                                                                                                                                           ORIGNAME= '/data/PTF_default_38068.fits' / Filename as written by the camera    FILENAME= 'PTF200906253621_2_o_38068.fits' / Filename of delivered camera image PROCORIG= 'IPAC-PTF pipelines' / Processing origin                              PROCDATE= 'Tue Feb 21 03:34:46 2012' / Processing date/time (Pacific time)      PTFVERSN=                   5. / Version of PTFSCIENCEPIPELINE program          PMASKPTH= '/ptf/pos/archive/fallbackcal/pmasks/' / Pathname of pixel mask       PMASKFIL= 'bpm_2009060s_c07.v2.fits' / Filename of pixel mask                   SFLATPTH= '/ptf/pos/sbx1/2009/06/25/f2/c7/cal/p4/cId45986/' / Pathname of super SFLATFIL= 'PTF_200906250000_i_s_flat_t120000_u000045986_f02_p000000_c07.fits'   SBIASPTH= '/ptf/pos/sbx1/2009/06/25/f2/c7/cal/p1/cId45922/' / Pathname of super SBIASFIL= 'PTF_200906250000_i_s_bias_t120000_u000045922_f00_p000000_c07.fits'   DBNID   =                  121 / Database night ID                              DBEXPID =                22920 / Database exposure ID                           DBRID   =              3663141 / Database raw-image ID                          DBPID   =             12052003 / Database processed-image ID                    DBFID   =                    2 / Database filter ID                             DBPIID  =                    1 / Database P.I. ID                               DBPRID  =                    3 / Database project ID                            DBFIELD =                22920 / Database field ID                              DBSVID  =                   50 / Database software-version ID                   DBCVID  =                   56 / Database config-data-file ID                   END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             