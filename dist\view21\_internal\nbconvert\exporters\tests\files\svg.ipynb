{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7f4c63ec5518>]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"252.018125pt\" version=\"1.1\" viewBox=\"0 0 375.603125 252.018125\" width=\"375.603125pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 252.018125 \n", "L 375.603125 252.018125 \n", "L 375.603125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.103125 228.14 \n", "L 364.903125 228.14 \n", "L 364.903125 10.7 \n", "L 30.103125 10.7 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 3.5 \n", "\" id=\"mbaa5d3ac27\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"45.321307\" xlink:href=\"#mbaa5d3ac27\" y=\"228.14\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "z\n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "z\n", "\" id=\"DejaVuSans-30\"/>\n", "      </defs>\n", "      <g transform=\"translate(42.140057 242.738437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"106.194034\" xlink:href=\"#mbaa5d3ac27\" y=\"228.14\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <defs>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"DejaVuSans-31\"/>\n", "      </defs>\n", "      <g transform=\"translate(103.012784 242.738437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"167.066761\" xlink:href=\"#mbaa5d3ac27\" y=\"228.14\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "z\n", "\" id=\"DejaVuSans-32\"/>\n", "      </defs>\n", "      <g transform=\"translate(163.885511 242.738437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"227.939489\" xlink:href=\"#mbaa5d3ac27\" y=\"228.14\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <defs>\n", "       <path d=\"M 40.578125 39.3125 \n", "Q 47.65625 37.796875 51.625 33 \n", "Q 55.609375 28.21875 55.609375 21.1875 \n", "Q 55.609375 10.40625 48.1875 4.484375 \n", "Q 40.765625 -1.421875 27.09375 -1.421875 \n", "Q 22.515625 -1.421875 17.65625 -0.515625 \n", "Q 12.796875 0.390625 7.625 2.203125 \n", "L 7.625 11.71875 \n", "Q 11.71875 9.328125 16.59375 8.109375 \n", "Q 21.484375 6.890625 26.8125 6.890625 \n", "Q 36.078125 6.890625 40.9375 10.546875 \n", "Q 45.796875 14.203125 45.796875 21.1875 \n", "Q 45.796875 27.640625 41.28125 31.265625 \n", "Q 36.765625 34.90625 28.71875 34.90625 \n", "L 20.21875 34.90625 \n", "L 20.21875 43.015625 \n", "L 29.109375 43.015625 \n", "Q 36.375 43.015625 40.234375 45.921875 \n", "Q 44.09375 48.828125 44.09375 54.296875 \n", "Q 44.09375 59.90625 40.109375 62.90625 \n", "Q 36.140625 65.921875 28.71875 65.921875 \n", "Q 24.65625 65.921875 20.015625 65.03125 \n", "Q 15.375 64.15625 9.8125 62.3125 \n", "L 9.8125 71.09375 \n", "Q 15.4375 72.65625 20.34375 73.4375 \n", "Q 25.25 74.21875 29.59375 74.21875 \n", "Q 40.828125 74.21875 47.359375 69.109375 \n", "Q 53.90625 64.015625 53.90625 55.328125 \n", "Q 53.90625 49.265625 50.4375 45.09375 \n", "Q 46.96875 40.921875 40.578125 39.3125 \n", "z\n", "\" id=\"DejaVuSans-33\"/>\n", "      </defs>\n", "      <g transform=\"translate(224.758239 242.738437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"288.812216\" xlink:href=\"#mbaa5d3ac27\" y=\"228.14\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"DejaVuSans-34\"/>\n", "      </defs>\n", "      <g transform=\"translate(285.630966 242.738437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"349.684943\" xlink:href=\"#mbaa5d3ac27\" y=\"228.14\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"DejaVuSans-35\"/>\n", "      </defs>\n", "      <g transform=\"translate(346.503693 242.738437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -3.5 0 \n", "\" id=\"m7fb83757f4\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"218.256364\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"DejaVuSans-2e\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 222.055582)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"193.547273\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 197.346491)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"168.838182\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 172.637401)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"144.129091\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 147.92831)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"119.42\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 123.219219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"94.710909\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(7.2 98.510128)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"70.001818\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(7.2 73.801037)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"45.292727\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 3.5 -->\n", "      <g transform=\"translate(7.2 49.091946)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"30.103125\" xlink:href=\"#m7fb83757f4\" y=\"20.583636\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 4.0 -->\n", "      <g transform=\"translate(7.2 24.382855)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-2e\"/>\n", "       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path clip-path=\"url(#pf878579141)\" d=\"M 45.321307 218.256364 \n", "L 106.194034 70.001818 \n", "L 167.066761 20.583636 \n", "L 227.939489 20.583636 \n", "L 288.812216 70.001818 \n", "L 349.684943 218.256364 \n", "\" style=\"fill:none;stroke:#1f77b4;stroke-linecap:square;stroke-width:1.5;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.103125 228.14 \n", "L 30.103125 10.7 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 364.903125 228.14 \n", "L 364.903125 10.7 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.103125 228.14 \n", "L 364.903125 228.14 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.7 \n", "L 364.903125 10.7 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf878579141\">\n", "   <rect height=\"217.44\" width=\"334.8\" x=\"30.103125\" y=\"10.7\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "%config InlineBackend.figure_formats = ['svg'] \n", "import matplotlib.pyplot as plt\n", "plt.plot((0,1,2,3,4,5),(0,3,4,4,3,0))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.3"}}, "nbformat": 4, "nbformat_minor": 2}