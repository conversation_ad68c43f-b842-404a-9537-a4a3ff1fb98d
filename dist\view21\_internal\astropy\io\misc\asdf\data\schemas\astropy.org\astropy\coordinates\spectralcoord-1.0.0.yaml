%YAML 1.1
---
$schema: "http://stsci.edu/schemas/yaml-schema/draft-01"
id: "http://astropy.org/schemas/astropy/coordinates/spectralcoord-1.0.0"
tag: "tag:astropy.org:astropy/coordinates/spectralcoord-1.0.0"

title: >
  Represents a SpectralCoord object from astropy

type: object
properties:
  value:
    description: |
      A vector of one or more values
    anyOf:
      - type: number
      - $ref: "http://stsci.edu/schemas/asdf/core/ndarray-1.0.0"
  unit:
    description: |
      The unit corresponding to the values
    $ref: "http://stsci.edu/schemas/asdf/unit/unit-1.0.0"
  observer:
    description: |
      The observer frame for this coordinate
    $ref: "http://astropy.org/schemas/astropy/coordinates/frames/baseframe-1.0.0"
  target:
    description: |
      The target frame for this coordinate
    $ref: "http://astropy.org/schemas/astropy/coordinates/frames/baseframe-1.0.0"
required: [value, unit]
...
