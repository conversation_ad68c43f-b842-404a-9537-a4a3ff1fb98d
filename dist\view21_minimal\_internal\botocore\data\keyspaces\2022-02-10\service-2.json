{"version": "2.0", "metadata": {"apiVersion": "2022-02-10", "endpointPrefix": "cassandra", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "Amazon Keyspaces", "serviceId": "Keyspaces", "signatureVersion": "v4", "signingName": "cassandra", "targetPrefix": "KeyspacesService", "uid": "keyspaces-2022-02-10"}, "operations": {"CreateKeyspace": {"name": "CreateKeyspace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateKeyspaceRequest"}, "output": {"shape": "CreateKeyspaceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>The <code>CreateKeyspace</code> operation adds a new keyspace to your account. In an Amazon Web Services account, keyspace names must be unique within each Region.</p> <p> <code>CreateKeyspace</code> is an asynchronous operation. You can monitor the creation status of the new keyspace by using the <code>GetKeyspace</code> operation.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/working-with-keyspaces.html#keyspaces-create\">Creating keyspaces</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "CreateTable": {"name": "CreateTable", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTableRequest"}, "output": {"shape": "CreateTableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>The <code>CreateTable</code> operation adds a new table to the specified keyspace. Within a keyspace, table names must be unique.</p> <p> <code>CreateTable</code> is an asynchronous operation. When the request is received, the status of the table is set to <code>CREATING</code>. You can monitor the creation status of the new table by using the <code>GetTable</code> operation, which returns the current <code>status</code> of the table. You can start using a table when the status is <code>ACTIVE</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/working-with-tables.html#tables-create\">Creating tables</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "DeleteKeyspace": {"name": "DeleteKeyspace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteKeyspaceRequest"}, "output": {"shape": "DeleteKeyspaceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>The <code>DeleteKeyspace</code> operation deletes a keyspace and all of its tables. </p>"}, "DeleteTable": {"name": "DeleteTable", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTableRequest"}, "output": {"shape": "DeleteTableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>The <code>DeleteTable</code> operation deletes a table and all of its data. After a <code>DeleteTable</code> request is received, the specified table is in the <code>DELETING</code> state until Amazon Keyspaces completes the deletion. If the table is in the <code>ACTIVE</code> state, you can delete it. If a table is either in the <code>CREATING</code> or <code>UPDATING</code> states, then Amazon Keyspaces returns a <code>ResourceInUseException</code>. If the specified table does not exist, Amazon Keyspaces returns a <code>ResourceNotFoundException</code>. If the table is already in the <code>DELETING</code> state, no error is returned.</p>"}, "GetKeyspace": {"name": "GetKeyspace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetKeyspaceRequest"}, "output": {"shape": "GetKeyspaceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the name and the Amazon Resource Name (ARN) of the specified table.</p>"}, "GetTable": {"name": "GetTable", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTableRequest"}, "output": {"shape": "GetTableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about the table, including the table's name and current status, the keyspace name, configuration settings, and metadata.</p> <p>To read table metadata using <code>GetTable</code>, <code>Select</code> action permissions for the table and system tables are required to complete the operation.</p>"}, "ListKeyspaces": {"name": "ListKeyspaces", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListKeyspacesRequest"}, "output": {"shape": "ListKeyspacesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of keyspaces.</p>"}, "ListTables": {"name": "ListTables", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTablesRequest"}, "output": {"shape": "ListTablesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of tables for a specified keyspace.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of all tags associated with the specified Amazon Keyspaces resource.</p>"}, "RestoreTable": {"name": "RestoreTable", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreTableRequest"}, "output": {"shape": "RestoreTableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Restores the specified table to the specified point in time within the <code>earliest_restorable_timestamp</code> and the current time. For more information about restore points, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/PointInTimeRecovery_HowItWorks.html#howitworks_backup_window\"> Time window for PITR continuous backups</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> <p>Any number of users can execute up to 4 concurrent restores (any type of restore) in a given account.</p> <p>When you restore using point in time recovery, Amazon Keyspaces restores your source table's schema and data to the state based on the selected timestamp <code>(day:hour:minute:second)</code> to a new table. The Time to Live (TTL) settings are also restored to the state based on the selected timestamp.</p> <p>In addition to the table's schema, data, and TTL settings, <code>RestoreTable</code> restores the capacity mode, encryption, and point-in-time recovery settings from the source table. Unlike the table's schema data and TTL settings, which are restored based on the selected timestamp, these settings are always restored based on the table's settings as of the current time or when the table was deleted.</p> <p>You can also overwrite these settings during restore:</p> <ul> <li> <p>Read/write capacity mode</p> </li> <li> <p>Provisioned throughput capacity settings</p> </li> <li> <p>Point-in-time (PITR) settings</p> </li> <li> <p>Tags</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/PointInTimeRecovery_HowItWorks.html#howitworks_backup_settings\">PITR restore settings</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> <p>Note that the following settings are not restored, and you must configure them manually for the new table:</p> <ul> <li> <p>Automatic scaling policies (for tables that use provisioned capacity mode)</p> </li> <li> <p>Identity and Access Management (IAM) policies</p> </li> <li> <p>Amazon CloudWatch metrics and alarms</p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Associates a set of tags with a Amazon Keyspaces resource. You can then activate these user-defined tags so that they appear on the Cost Management Console for cost allocation tracking. For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/tagging-keyspaces.html\">Adding tags and labels to Amazon Keyspaces resources</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> <p>For IAM policy examples that show how to control access to Amazon Keyspaces resources based on tags, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/security_iam_id-based-policy-examples.html#security_iam_id-based-policy-examples-tags\">Amazon Keyspaces resource access based on tags</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes the association of tags from a Amazon Keyspaces resource.</p>"}, "UpdateTable": {"name": "UpdateTable", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTableRequest"}, "output": {"shape": "UpdateTableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds new columns to the table or updates one of the table's settings, for example capacity mode, encryption, point-in-time recovery, or ttl settings. Note that you can only update one specific table setting per update operation.</p>"}}, "shapes": {"ARN": {"type": "string", "max": 1000, "min": 20, "pattern": "arn:(aws[a-zA-Z0-9-]*):cassandra:.+.*"}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action. </p>", "exception": true}, "CapacitySpecification": {"type": "structure", "required": ["throughputMode"], "members": {"throughputMode": {"shape": "ThroughputMode", "documentation": "<p>The read/write throughput capacity mode for a table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> and </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code> - Provisioned capacity mode requires <code>readCapacityUnits</code> and <code>writeCapacityUnits</code> as input.</p> </li> </ul> <p>The default is <code>throughput_mode:PAY_PER_REQUEST</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "readCapacityUnits": {"shape": "CapacityUnits", "documentation": "<p>The throughput capacity specified for <code>read</code> operations defined in <code>read capacity units</code> <code>(RCUs)</code>.</p>"}, "writeCapacityUnits": {"shape": "CapacityUnits", "documentation": "<p>The throughput capacity specified for <code>write</code> operations defined in <code>write capacity units</code> <code>(WCUs)</code>.</p>"}}, "documentation": "<p>Amazon Keyspaces has two read/write capacity modes for processing reads and writes on your tables: </p> <ul> <li> <p>On-demand (default)</p> </li> <li> <p>Provisioned</p> </li> </ul> <p>The read/write capacity mode that you choose controls how you are charged for read and write throughput and how table throughput capacity is managed.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "CapacitySpecificationSummary": {"type": "structure", "required": ["throughputMode"], "members": {"throughputMode": {"shape": "ThroughputMode", "documentation": "<p>The read/write throughput capacity mode for a table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> and </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code> - Provisioned capacity mode requires <code>readCapacityUnits</code> and <code>writeCapacityUnits</code> as input. </p> </li> </ul> <p>The default is <code>throughput_mode:PAY_PER_REQUEST</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "readCapacityUnits": {"shape": "CapacityUnits", "documentation": "<p>The throughput capacity specified for <code>read</code> operations defined in <code>read capacity units</code> <code>(RCUs)</code>.</p>"}, "writeCapacityUnits": {"shape": "CapacityUnits", "documentation": "<p>The throughput capacity specified for <code>write</code> operations defined in <code>write capacity units</code> <code>(WCUs)</code>.</p>"}, "lastUpdateToPayPerRequestTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of the last operation that changed the provisioned throughput capacity of a table.</p>"}}, "documentation": "<p>The read/write throughput capacity mode for a table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> and </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code>.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "CapacityUnits": {"type": "long", "box": true, "min": 1}, "ClientSideTimestamps": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "ClientSideTimestampsStatus", "documentation": "<p>Shows how to enable client-side timestamps settings for the specified table.</p>"}}, "documentation": "<p>The client-side timestamp setting of the table.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/client-side-timestamps-how-it-works.html\">How it works: Amazon Keyspaces client-side timestamps</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "ClientSideTimestampsStatus": {"type": "string", "enum": ["ENABLED"]}, "ClusteringKey": {"type": "structure", "required": ["name", "orderBy"], "members": {"name": {"shape": "GenericString", "documentation": "<p>The name(s) of the clustering column(s).</p>"}, "orderBy": {"shape": "SortOrder", "documentation": "<p>Sets the ascendant (<code>ASC</code>) or descendant (<code>DESC</code>) order modifier.</p>"}}, "documentation": "<p>The optional clustering column portion of your primary key determines how the data is clustered and sorted within each partition.</p>"}, "ClusteringKeyList": {"type": "list", "member": {"shape": "Clustering<PERSON><PERSON>"}}, "ColumnDefinition": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "GenericString", "documentation": "<p>The name of the column.</p>"}, "type": {"shape": "GenericString", "documentation": "<p>The data type of the column. For a list of available data types, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/cql.elements.html#cql.data-types\">Data types</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}}, "documentation": "<p>The names and data types of regular columns.</p>"}, "ColumnDefinitionList": {"type": "list", "member": {"shape": "ColumnDefinition"}, "min": 1}, "Comment": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>An optional description of the table.</p>"}}, "documentation": "<p>An optional comment that describes the table.</p>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Amazon Keyspaces could not complete the requested action. This error may occur if you try to perform an action and the same or a different action is already in progress, or if you try to create a resource that already exists. </p>", "exception": true}, "CreateKeyspaceRequest": {"type": "structure", "required": ["keyspaceName"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace to be created.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of key-value pair tags to be attached to the keyspace.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/tagging-keyspaces.html\">Adding tags and labels to Amazon Keyspaces resources</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "replicationSpecification": {"shape": "ReplicationSpecification", "documentation": "<p> The replication specification of the keyspace includes:</p> <ul> <li> <p> <code>replicationStrategy</code> - the required value is <code>SINGLE_REGION</code> or <code>MULTI_REGION</code>.</p> </li> <li> <p> <code>regionList</code> - if the <code>replicationStrategy</code> is <code>MULTI_REGION</code>, the <code>regionList</code> requires the current Region and at least one additional Amazon Web Services Region where the keyspace is going to be replicated in. The maximum number of supported replication Regions including the current Region is six.</p> </li> </ul>"}}}, "CreateKeyspaceResponse": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The unique identifier of the keyspace in the format of an Amazon Resource Name (ARN).</p>"}}}, "CreateTableRequest": {"type": "structure", "required": ["keyspaceName", "tableName", "schemaDefinition"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace that the table is going to be created in.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the table.</p>"}, "schemaDefinition": {"shape": "SchemaDefinition", "documentation": "<p>The <code>schemaDefinition</code> consists of the following parameters.</p> <p>For each column to be created:</p> <ul> <li> <p> <code>name</code> - The name of the column.</p> </li> <li> <p> <code>type</code> - An Amazon Keyspaces data type. For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/cql.elements.html#cql.data-types\">Data types</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> </li> </ul> <p>The primary key of the table consists of the following columns:</p> <ul> <li> <p> <code>partitionKeys</code> - The partition key can be a single column, or it can be a compound value composed of two or more columns. The partition key portion of the primary key is required and determines how Amazon Keyspaces stores your data.</p> </li> <li> <p> <code>name</code> - The name of each partition key column.</p> </li> <li> <p> <code>clusteringKeys</code> - The optional clustering column portion of your primary key determines how the data is clustered and sorted within each partition.</p> </li> <li> <p> <code>name</code> - The name of the clustering column. </p> </li> <li> <p> <code>orderBy</code> - Sets the ascendant (<code>ASC</code>) or descendant (<code>DESC</code>) order modifier.</p> <p>To define a column as static use <code>staticColumns</code> - Static columns store values that are shared by all rows in the same partition:</p> </li> <li> <p> <code>name</code> - The name of the column.</p> </li> <li> <p> <code>type</code> - An Amazon Keyspaces data type.</p> </li> </ul>"}, "comment": {"shape": "Comment", "documentation": "<p>This parameter allows to enter a description of the table.</p>"}, "capacitySpecification": {"shape": "CapacitySpecification", "documentation": "<p>Specifies the read/write throughput capacity mode for the table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> and </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code> - Provisioned capacity mode requires <code>readCapacityUnits</code> and <code>writeCapacityUnits</code> as input.</p> </li> </ul> <p>The default is <code>throughput_mode:PAY_PER_REQUEST</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "encryptionSpecification": {"shape": "EncryptionSpecification", "documentation": "<p>Specifies how the encryption key for encryption at rest is managed for the table. You can choose one of the following KMS key (KMS key):</p> <ul> <li> <p> <code>type:AWS_OWNED_KMS_KEY</code> - This key is owned by Amazon Keyspaces. </p> </li> <li> <p> <code>type:CUSTOMER_MANAGED_KMS_KEY</code> - This key is stored in your account and is created, owned, and managed by you. This option requires the <code>kms_key_identifier</code> of the KMS key in Amazon Resource Name (ARN) format as input.</p> </li> </ul> <p>The default is <code>type:AWS_OWNED_KMS_KEY</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/EncryptionAtRest.html\">Encryption at rest</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "pointInTimeRecovery": {"shape": "PointInTimeRecovery", "documentation": "<p>Specifies if <code>pointInTimeRecovery</code> is enabled or disabled for the table. The options are:</p> <ul> <li> <p> <code>status=ENABLED</code> </p> </li> <li> <p> <code>status=DISABLED</code> </p> </li> </ul> <p>If it's not specified, the default is <code>status=DISABLED</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/PointInTimeRecovery.html\">Point-in-time recovery</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "ttl": {"shape": "TimeToLive", "documentation": "<p>Enables Time to Live custom settings for the table. The options are:</p> <ul> <li> <p> <code>status:enabled</code> </p> </li> <li> <p> <code>status:disabled</code> </p> </li> </ul> <p>The default is <code>status:disabled</code>. After <code>ttl</code> is enabled, you can't disable it for the table.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/TTL.html\">Expiring data by using Amazon Keyspaces Time to Live (TTL)</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "defaultTimeToLive": {"shape": "DefaultTimeToLive", "documentation": "<p>The default Time to Live setting in seconds for the table.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/TTL-how-it-works.html#ttl-howitworks_default_ttl\">Setting the default TTL value for a table</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of key-value pair tags to be attached to the resource. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/tagging-keyspaces.html\">Adding tags and labels to Amazon Keyspaces resources</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "clientSideTimestamps": {"shape": "ClientSideTimestamps", "documentation": "<p> Enables client-side timestamps for the table. By default, the setting is disabled. You can enable client-side timestamps with the following option:</p> <ul> <li> <p> <code>status: \"enabled\"</code> </p> </li> </ul> <p>Once client-side timestamps are enabled for a table, this setting cannot be disabled.</p>"}}}, "CreateTableResponse": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The unique identifier of the table in the format of an Amazon Resource Name (ARN).</p>"}}}, "DefaultTimeToLive": {"type": "integer", "box": true, "max": 630720000, "min": 1}, "DeleteKeyspaceRequest": {"type": "structure", "required": ["keyspaceName"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace to be deleted.</p>"}}}, "DeleteKeyspaceResponse": {"type": "structure", "members": {}}, "DeleteTableRequest": {"type": "structure", "required": ["keyspaceName", "tableName"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace of the to be deleted table.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the table to be deleted.</p>"}}}, "DeleteTableResponse": {"type": "structure", "members": {}}, "EncryptionSpecification": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "EncryptionType", "documentation": "<p>The encryption option specified for the table. You can choose one of the following KMS keys (KMS keys):</p> <ul> <li> <p> <code>type:AWS_OWNED_KMS_KEY</code> - This key is owned by Amazon Keyspaces. </p> </li> <li> <p> <code>type:CUSTOMER_MANAGED_KMS_KEY</code> - This key is stored in your account and is created, owned, and managed by you. This option requires the <code>kms_key_identifier</code> of the KMS key in Amazon Resource Name (ARN) format as input. </p> </li> </ul> <p>The default is <code>type:AWS_OWNED_KMS_KEY</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/EncryptionAtRest.html\">Encryption at rest</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "kmsKeyIdentifier": {"shape": "kmsKeyARN", "documentation": "<p>The Amazon Resource Name (ARN) of the customer managed KMS key, for example <code>kms_key_identifier:ARN</code>.</p>"}}, "documentation": "<p>Amazon Keyspaces encrypts and decrypts the table data at rest transparently and integrates with Key Management Service for storing and managing the encryption key. You can choose one of the following KMS keys (KMS keys):</p> <ul> <li> <p>Amazon Web Services owned key - This is the default encryption type. The key is owned by Amazon Keyspaces (no additional charge). </p> </li> <li> <p>Customer managed key - This key is stored in your account and is created, owned, and managed by you. You have full control over the customer managed key (KMS charges apply).</p> </li> </ul> <p>For more information about encryption at rest in Amazon Keyspaces, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/EncryptionAtRest.html\">Encryption at rest</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> <p>For more information about KMS, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/EncryptionAtRest.html\">KMS management service concepts</a> in the <i>Key Management Service Developer Guide</i>.</p>"}, "EncryptionType": {"type": "string", "enum": ["CUSTOMER_MANAGED_KMS_KEY", "AWS_OWNED_KMS_KEY"]}, "GenericString": {"type": "string"}, "GetKeyspaceRequest": {"type": "structure", "required": ["keyspaceName"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace.</p>"}}}, "GetKeyspaceResponse": {"type": "structure", "required": ["keyspaceName", "resourceArn", "replicationStrategy"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace.</p>"}, "resourceArn": {"shape": "ARN", "documentation": "<p>Returns the ARN of the keyspace.</p>"}, "replicationStrategy": {"shape": "rs", "documentation": "<p> Returns the replication strategy of the keyspace. The options are <code>SINGLE_REGION</code> or <code>MULTI_REGION</code>. </p>"}, "replicationRegions": {"shape": "RegionList", "documentation": "<p> If the <code>replicationStrategy</code> of the keyspace is <code>MULTI_REGION</code>, a list of replication Regions is returned. </p>"}}}, "GetTableRequest": {"type": "structure", "required": ["keyspaceName", "tableName"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace that the table is stored in.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the table.</p>"}}}, "GetTableResponse": {"type": "structure", "required": ["keyspaceName", "tableName", "resourceArn"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace that the specified table is stored in.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the specified table.</p>"}, "resourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the specified table.</p>"}, "creationTimestamp": {"shape": "Timestamp", "documentation": "<p>The creation timestamp of the specified table.</p>"}, "status": {"shape": "TableStatus", "documentation": "<p>The current status of the specified table.</p>"}, "schemaDefinition": {"shape": "SchemaDefinition", "documentation": "<p>The schema definition of the specified table.</p>"}, "capacitySpecification": {"shape": "CapacitySpecificationSummary", "documentation": "<p>The read/write throughput capacity mode for a table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code> </p> </li> </ul>"}, "encryptionSpecification": {"shape": "EncryptionSpecification", "documentation": "<p>The encryption settings of the specified table.</p>"}, "pointInTimeRecovery": {"shape": "PointInTimeRecoverySummary", "documentation": "<p>The point-in-time recovery status of the specified table.</p>"}, "ttl": {"shape": "TimeToLive", "documentation": "<p>The custom Time to Live settings of the specified table.</p>"}, "defaultTimeToLive": {"shape": "DefaultTimeToLive", "documentation": "<p>The default Time to Live settings in seconds of the specified table.</p>"}, "comment": {"shape": "Comment", "documentation": "<p>The the description of the specified table.</p>"}, "clientSideTimestamps": {"shape": "ClientSideTimestamps", "documentation": "<p> The client-side timestamps setting of the table.</p>"}}}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Amazon Keyspaces was unable to fully process this request because of an internal server error.</p>", "exception": true, "fault": true}, "KeyspaceName": {"type": "string", "max": 48, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_]{0,47}"}, "KeyspaceSummary": {"type": "structure", "required": ["keyspaceName", "resourceArn", "replicationStrategy"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace.</p>"}, "resourceArn": {"shape": "ARN", "documentation": "<p>The unique identifier of the keyspace in the format of an Amazon Resource Name (ARN).</p>"}, "replicationStrategy": {"shape": "rs", "documentation": "<p> This property specifies if a keyspace is a single Region keyspace or a multi-Region keyspace. The available values are <code>SINGLE_REGION</code> or <code>MULTI_REGION</code>. </p>"}, "replicationRegions": {"shape": "RegionList", "documentation": "<p> If the <code>replicationStrategy</code> of the keyspace is <code>MULTI_REGION</code>, a list of replication Regions is returned. </p>"}}, "documentation": "<p>Represents the properties of a keyspace.</p>"}, "KeyspaceSummaryList": {"type": "list", "member": {"shape": "KeyspaceSummary"}}, "ListKeyspacesRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token. To resume pagination, provide the <code>NextToken</code> value as argument of a subsequent API invocation.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The total number of keyspaces to return in the output. If the total number of keyspaces available is more than the value specified, a <code>NextToken</code> is provided in the output. To resume pagination, provide the <code>NextToken</code> value as an argument of a subsequent API invocation.</p>"}}}, "ListKeyspacesResponse": {"type": "structure", "required": ["keyspaces"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token to specify where to start paginating. This is the <code>NextToken</code> from a previously truncated response.</p>"}, "keyspaces": {"shape": "KeyspaceSummaryList", "documentation": "<p>A list of keyspaces.</p>"}}}, "ListTablesRequest": {"type": "structure", "required": ["keyspaceName"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token. To resume pagination, provide the <code>NextToken</code> value as an argument of a subsequent API invocation.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The total number of tables to return in the output. If the total number of tables available is more than the value specified, a <code>NextToken</code> is provided in the output. To resume pagination, provide the <code>NextToken</code> value as an argument of a subsequent API invocation.</p>"}, "keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace.</p>"}}}, "ListTablesResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token to specify where to start paginating. This is the <code>NextToken</code> from a previously truncated response.</p>"}, "tables": {"shape": "TableSummaryList", "documentation": "<p>A list of tables.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Keyspaces resource.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token. To resume pagination, provide the <code>NextToken</code> value as argument of a subsequent API invocation.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The total number of tags to return in the output. If the total number of tags available is more than the value specified, a <code>NextToken</code> is provided in the output. To resume pagination, provide the <code>NextToken</code> value as an argument of a subsequent API invocation.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token to specify where to start paginating. This is the <code>NextToken</code> from a previously truncated response.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of tags.</p>"}}}, "MaxResults": {"type": "integer", "box": true, "max": 1000, "min": 1}, "NextToken": {"type": "string", "max": 2048, "min": 1}, "PartitionKey": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "GenericString", "documentation": "<p>The name(s) of the partition key column(s).</p>"}}, "documentation": "<p>The partition key portion of the primary key is required and determines how Amazon Keyspaces stores the data. The partition key can be a single column, or it can be a compound value composed of two or more columns.</p>"}, "PartitionKeyList": {"type": "list", "member": {"shape": "PartitionKey"}, "min": 1}, "PointInTimeRecovery": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "PointInTimeRecoveryStatus", "documentation": "<p>The options are:</p> <ul> <li> <p> <code>status=ENABLED</code> </p> </li> <li> <p> <code>status=DISABLED</code> </p> </li> </ul>"}}, "documentation": "<p>Point-in-time recovery (PITR) helps protect your Amazon Keyspaces tables from accidental write or delete operations by providing you continuous backups of your table data.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/PointInTimeRecovery.html\">Point-in-time recovery</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "PointInTimeRecoveryStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "PointInTimeRecoverySummary": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "PointInTimeRecoveryStatus", "documentation": "<p>Shows if point-in-time recovery is enabled or disabled for the specified table.</p>"}, "earliestRestorableTimestamp": {"shape": "Timestamp", "documentation": "<p>Specifies the earliest possible restore point of the table in ISO 8601 format.</p>"}}, "documentation": "<p>The point-in-time recovery status of the specified table.</p>"}, "RegionList": {"type": "list", "member": {"shape": "region"}, "max": 6, "min": 2}, "ReplicationSpecification": {"type": "structure", "required": ["replicationStrategy"], "members": {"replicationStrategy": {"shape": "rs", "documentation": "<p> The <code>replicationStrategy</code> of a keyspace, the required value is <code>SINGLE_REGION</code> or <code>MULTI_REGION</code>. </p>"}, "regionList": {"shape": "RegionList", "documentation": "<p> The <code>regionList</code> can contain up to six Amazon Web Services Regions where the keyspace is replicated in. </p>"}}, "documentation": "<p> The replication specification of the keyspace includes:</p> <ul> <li> <p> <code>regionList</code> - up to six Amazon Web Services Regions where the keyspace is replicated in.</p> </li> <li> <p> <code>replicationStrategy</code> - the required value is <code>SINGLE_REGION</code> or <code>MULTI_REGION</code>.</p> </li> </ul>"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}, "resourceArn": {"shape": "ARN", "documentation": "<p>The unique identifier in the format of Amazon Resource Name (ARN), for the resource not found.</p>"}}, "documentation": "<p>The operation tried to access a keyspace or table that doesn't exist. The resource might not be specified correctly, or its status might not be <code>ACTIVE</code>.</p>", "exception": true}, "RestoreTableRequest": {"type": "structure", "required": ["sourceKeyspaceName", "sourceTableName", "targetKeyspaceName", "targetTableName"], "members": {"sourceKeyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The keyspace name of the source table.</p>"}, "sourceTableName": {"shape": "TableName", "documentation": "<p>The name of the source table.</p>"}, "targetKeyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the target keyspace.</p>"}, "targetTableName": {"shape": "TableName", "documentation": "<p>The name of the target table.</p>"}, "restoreTimestamp": {"shape": "Timestamp", "documentation": "<p>The restore timestamp in ISO 8601 format.</p>"}, "capacitySpecificationOverride": {"shape": "CapacitySpecification", "documentation": "<p>Specifies the read/write throughput capacity mode for the target table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code> - Provisioned capacity mode requires <code>readCapacityUnits</code> and <code>writeCapacityUnits</code> as input.</p> </li> </ul> <p>The default is <code>throughput_mode:PAY_PER_REQUEST</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "encryptionSpecificationOverride": {"shape": "EncryptionSpecification", "documentation": "<p>Specifies the encryption settings for the target table. You can choose one of the following KMS key (KMS key):</p> <ul> <li> <p> <code>type:AWS_OWNED_KMS_KEY</code> - This key is owned by Amazon Keyspaces. </p> </li> <li> <p> <code>type:CUSTOMER_MANAGED_KMS_KEY</code> - This key is stored in your account and is created, owned, and managed by you. This option requires the <code>kms_key_identifier</code> of the KMS key in Amazon Resource Name (ARN) format as input. </p> </li> </ul> <p>The default is <code>type:AWS_OWNED_KMS_KEY</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/EncryptionAtRest.html\">Encryption at rest</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "pointInTimeRecoveryOverride": {"shape": "PointInTimeRecovery", "documentation": "<p>Specifies the <code>pointInTimeRecovery</code> settings for the target table. The options are:</p> <ul> <li> <p> <code>status=ENABLED</code> </p> </li> <li> <p> <code>status=DISABLED</code> </p> </li> </ul> <p>If it's not specified, the default is <code>status=DISABLED</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/PointInTimeRecovery.html\">Point-in-time recovery</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "tagsOverride": {"shape": "TagList", "documentation": "<p>A list of key-value pair tags to be attached to the restored table. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/tagging-keyspaces.html\">Adding tags and labels to Amazon Keyspaces resources</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}}}, "RestoreTableResponse": {"type": "structure", "required": ["restoredTableARN"], "members": {"restoredTableARN": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the restored table.</p>"}}}, "SchemaDefinition": {"type": "structure", "required": ["allColumns", "partitionKeys"], "members": {"allColumns": {"shape": "ColumnDefinitionList", "documentation": "<p>The regular columns of the table.</p>"}, "partitionKeys": {"shape": "PartitionKeyList", "documentation": "<p>The columns that are part of the partition key of the table .</p>"}, "clusteringKeys": {"shape": "ClusteringKeyList", "documentation": "<p>The columns that are part of the clustering key of the table.</p>"}, "staticColumns": {"shape": "StaticColumnList", "documentation": "<p>The columns that have been defined as <code>STATIC</code>. Static columns store values that are shared by all rows in the same partition.</p>"}}, "documentation": "<p>Describes the schema of the table.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation exceeded the service quota for this resource. For more information on service quotas, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/quotas.html\">Quotas</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>", "exception": true}, "SortOrder": {"type": "string", "enum": ["ASC", "DESC"]}, "StaticColumn": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "GenericString", "documentation": "<p>The name of the static column.</p>"}}, "documentation": "<p>The static columns of the table. Static columns store values that are shared by all rows in the same partition.</p>"}, "StaticColumnList": {"type": "list", "member": {"shape": "StaticColumn"}}, "String": {"type": "string"}, "TableName": {"type": "string", "max": 48, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_]{0,47}"}, "TableStatus": {"type": "string", "enum": ["ACTIVE", "CREATING", "UPDATING", "DELETING", "DELETED", "RESTORING", "INACCESSIBLE_ENCRYPTION_CREDENTIALS"]}, "TableSummary": {"type": "structure", "required": ["keyspaceName", "tableName", "resourceArn"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace that the table is stored in.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the table.</p>"}, "resourceArn": {"shape": "ARN", "documentation": "<p>The unique identifier of the table in the format of an Amazon Resource Name (ARN).</p>"}}, "documentation": "<p>Returns the name of the specified table, the keyspace it is stored in, and the unique identifier in the format of an Amazon Resource Name (ARN).</p>"}, "TableSummaryList": {"type": "list", "member": {"shape": "TableSummary"}}, "Tag": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "TagKey", "documentation": "<p>The key of the tag. Tag keys are case sensitive. Each Amazon Keyspaces resource can only have up to one tag with the same key. If you try to add an existing tag (same key), the existing tag value will be updated to the new value.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value of the tag. Tag values are case-sensitive and can be null.</p>"}}, "documentation": "<p>Describes a tag. A tag is a key-value pair. You can add up to 50 tags to a single Amazon Keyspaces resource.</p> <p>Amazon Web Services-assigned tag names and values are automatically assigned the <code>aws:</code> prefix, which the user cannot assign. Amazon Web Services-assigned tag names do not count towards the tag limit of 50. User-assigned tag names have the prefix <code>user:</code> in the Cost Allocation Report. You cannot backdate the application of a tag.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/tagging-keyspaces.html\">Adding tags and labels to Amazon Keyspaces resources</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 60, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Keyspaces resource to which to add tags.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to be assigned to the Amazon Keyspaces resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 1}, "ThroughputMode": {"type": "string", "enum": ["PAY_PER_REQUEST", "PROVISIONED"]}, "TimeToLive": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "TimeToLiveStatus", "documentation": "<p>Shows how to enable custom Time to Live (TTL) settings for the specified table.</p>"}}, "documentation": "<p>Enable custom Time to Live (TTL) settings for rows and columns without setting a TTL default for the specified table.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/TTL-how-it-works.html#ttl-howitworks_enabling\">Enabling TTL on tables</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "TimeToLiveStatus": {"type": "string", "enum": ["ENABLED"]}, "Timestamp": {"type": "timestamp"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Keyspaces resource that the tags will be removed from. This value is an Amazon Resource Name (ARN).</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of existing tags to be removed from the Amazon Keyspaces resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateTableRequest": {"type": "structure", "required": ["keyspaceName", "tableName"], "members": {"keyspaceName": {"shape": "KeyspaceName", "documentation": "<p>The name of the keyspace the specified table is stored in.</p>"}, "tableName": {"shape": "TableName", "documentation": "<p>The name of the table.</p>"}, "addColumns": {"shape": "ColumnDefinitionList", "documentation": "<p>For each column to be added to the specified table:</p> <ul> <li> <p> <code>name</code> - The name of the column.</p> </li> <li> <p> <code>type</code> - An Amazon Keyspaces data type. For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/cql.elements.html#cql.data-types\">Data types</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> </li> </ul>"}, "capacitySpecification": {"shape": "CapacitySpecification", "documentation": "<p>Modifies the read/write throughput capacity mode for the table. The options are:</p> <ul> <li> <p> <code>throughputMode:PAY_PER_REQUEST</code> and </p> </li> <li> <p> <code>throughputMode:PROVISIONED</code> - Provisioned capacity mode requires <code>readCapacityUnits</code> and <code>writeCapacityUnits</code> as input.</p> </li> </ul> <p>The default is <code>throughput_mode:PAY_PER_REQUEST</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/ReadWriteCapacityMode.html\">Read/write capacity modes</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "encryptionSpecification": {"shape": "EncryptionSpecification", "documentation": "<p>Modifies the encryption settings of the table. You can choose one of the following KMS key (KMS key):</p> <ul> <li> <p> <code>type:AWS_OWNED_KMS_KEY</code> - This key is owned by Amazon Keyspaces. </p> </li> <li> <p> <code>type:CUSTOMER_MANAGED_KMS_KEY</code> - This key is stored in your account and is created, owned, and managed by you. This option requires the <code>kms_key_identifier</code> of the KMS key in Amazon Resource Name (ARN) format as input. </p> </li> </ul> <p>The default is <code>AWS_OWNED_KMS_KEY</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/EncryptionAtRest.html\">Encryption at rest</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "pointInTimeRecovery": {"shape": "PointInTimeRecovery", "documentation": "<p>Modifies the <code>pointInTimeRecovery</code> settings of the table. The options are:</p> <ul> <li> <p> <code>status=ENABLED</code> </p> </li> <li> <p> <code>status=DISABLED</code> </p> </li> </ul> <p>If it's not specified, the default is <code>status=DISABLED</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/PointInTimeRecovery.html\">Point-in-time recovery</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "ttl": {"shape": "TimeToLive", "documentation": "<p>Modifies Time to Live custom settings for the table. The options are:</p> <ul> <li> <p> <code>status:enabled</code> </p> </li> <li> <p> <code>status:disabled</code> </p> </li> </ul> <p>The default is <code>status:disabled</code>. After <code>ttl</code> is enabled, you can't disable it for the table.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/TTL.html\">Expiring data by using Amazon Keyspaces Time to Live (TTL)</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "defaultTimeToLive": {"shape": "DefaultTimeToLive", "documentation": "<p>The default Time to Live setting in seconds for the table.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/TTL-how-it-works.html#ttl-howitworks_default_ttl\">Setting the default TTL value for a table</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p>"}, "clientSideTimestamps": {"shape": "ClientSideTimestamps", "documentation": "<p>Enables client-side timestamps for the table. By default, the setting is disabled. You can enable client-side timestamps with the following option:</p> <ul> <li> <p> <code>status: \"enabled\"</code> </p> </li> </ul> <p>Once client-side timestamps are enabled for a table, this setting cannot be disabled.</p>"}}}, "UpdateTableResponse": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the modified table.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed due to an invalid or malformed request.</p>", "exception": true}, "kmsKeyARN": {"type": "string", "max": 5096, "min": 1}, "region": {"type": "string", "max": 25, "min": 2}, "rs": {"type": "string", "enum": ["SINGLE_REGION", "MULTI_REGION"], "max": 20, "min": 1}}, "documentation": "<p>Amazon Keyspaces (for Apache Cassandra) is a scalable, highly available, and managed Apache Cassandra-compatible database service. Amazon Keyspaces makes it easy to migrate, run, and scale Cassandra workloads in the Amazon Web Services Cloud. With just a few clicks on the Amazon Web Services Management Console or a few lines of code, you can create keyspaces and tables in Amazon Keyspaces, without deploying any infrastructure or installing software. </p> <p>In addition to supporting Cassandra Query Language (CQL) requests via open-source Cassandra drivers, Amazon Keyspaces supports data definition language (DDL) operations to manage keyspaces and tables using the Amazon Web Services SDK and CLI, as well as infrastructure as code (IaC) services and tools such as CloudFormation and Terraform. This API reference describes the supported DDL operations in detail.</p> <p>For the list of all supported CQL APIs, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/cassandra-apis.html\">Supported Cassandra APIs, operations, and data types in Amazon Keyspaces</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> <p>To learn how Amazon Keyspaces API actions are recorded with CloudTrail, see <a href=\"https://docs.aws.amazon.com/keyspaces/latest/devguide/logging-using-cloudtrail.html#service-name-info-in-cloudtrail\">Amazon Keyspaces information in CloudTrail</a> in the <i>Amazon Keyspaces Developer Guide</i>.</p> <p>For more information about Amazon Web Services APIs, for example how to implement retry logic or how to sign Amazon Web Services API requests, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-apis.html\">Amazon Web Services APIs</a> in the <i>General Reference</i>.</p>"}