#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入PyQt6相关模块
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QLabel, QFrame, QSizePolicy, QMenu, QFileDialog, QDialog, QLineEdit, QCheckBox, QHBoxLayout, QMessageBox
)
from PyQt6.QtCore import pyqtSignal, Qt, QPoint, QSize
from PyQt6.QtGui import QIcon, QPixmap, QAction
from log_manager import log_user_action
from language_manager import get_text, set_language

# 添加spectrumeter目录到Python路径
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
spectrumeter_dir = os.path.join(os.path.dirname(current_dir), 'spectrumeter')
if spectrumeter_dir not in sys.path:
    sys.path.insert(0, spectrumeter_dir)

# 侧边栏模块，包含数据管理、算法、通讯、用户、设置等功能按钮
class Sidebar(QWidget):
    # 定义信号
    data_management_signal = pyqtSignal()         # 数据管理
    baseline_calibration_signal = pyqtSignal()    # 基线校准
    data_smoothing_signal = pyqtSignal()          # 数据平滑
    normalization_signal = pyqtSignal()           # 归一化
    peak_finding_signal = pyqtSignal()            # 寻峰
    restoration_signal = pyqtSignal()             # 还原
    clear_spectrum_signal = pyqtSignal()          # 清除图谱
    communication_signal = pyqtSignal()           # 通讯
    user_signal = pyqtSignal()                    # 用户
    settings_signal = pyqtSignal()                # 设置
    language_changed = pyqtSignal(str)            # 语言切换
    data_management_file_signal = pyqtSignal(list) # 数据文件路径信号，改为list
    communication_status_changed = pyqtSignal(bool, bool)  # 新增信号，两个设备状态
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.laser_connected = False
        self.spectrometer_connected = False
        self.setup_ui()
        self.communication_signal.connect(self.show_communication_dialog)
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)

        icon_size = QSize(28, 28)
        sidebar_width = 35
        self.setFixedWidth(sidebar_width)

    # ---------- 顶部区域 ----------
        data_section =QWidget()
        data_layout =QVBoxLayout(data_section)
        data_layout.setContentsMargins(4, 4, 4, 4)
        data_layout.setSpacing(12)


        self.data_management_btn = QPushButton()
        self.data_management_btn.setIcon(QIcon(r"./UI2/64/64_64/文件仓库@2x.png"))
        self.data_management_btn.setToolTip(get_text("data_management"))
        self.data_management_btn.setIconSize(icon_size)
        self.data_management_btn.setFixedSize(icon_size)
        self.data_management_btn.setFlat(True)
        self.data_management_btn.setStyleSheet("border:none; background:transparent; padding:0; color: white;")
        self.data_management_btn.clicked.connect(self.open_data_file_dialog)

        layout.addWidget(self.data_management_btn)
        layout.addSpacing(14)

        # ---------- 分隔线 ----------
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        #separator.setLineWidth(1)  # 设置分隔线宽度为2像素
        #separator.setStyleSheet("color: #bbb; background: #bbb; min-height: 2px;")  # 可选：设置颜色和最小高度
        layout.addWidget(separator)

        # ---------- 算法标题 ----------
        # algorithm_title = QLabel("  ")
        # algorithm_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # algorithm_title.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 8px; margin-bottom: 4px;")
        # layout.addWidget(algorithm_title)

        # ---------- 算法按钮 ----------
        algorithm_section = QWidget()
        algorithm_layout = QVBoxLayout(algorithm_section)
        algorithm_layout.setContentsMargins(4, 4, 4, 4)
        algorithm_layout.setSpacing(14)

        # 设置更小的图标尺寸
        small_icon_size = QSize(28, 28)  # 例如28x28像素

        # 保存算法按钮引用以便更新语言
        self.algorithm_buttons = {}
        
        algorithms = [
            ("baseline_calibration", r"./UI2/64/64_64/基线校准.png", self.baseline_calibration_signal),
            ("peak_finding", r"./UI2/64/64_64/寻峰.png", self.peak_finding_signal),
            ("data_smoothing", r"./UI2/64/64_64/平滑.png", self.data_smoothing_signal),
            ("normalization", r"./UI2/64/64_64/归一化.png", self.normalization_signal),
            ("restoration", r"./UI2/64/64_64/还原.png", self.restoration_signal),
        ]

        for key, icon, signal in algorithms:
            btn = QPushButton()
            btn.setIcon(QIcon(icon))
            btn.setToolTip(get_text(key))
            btn.setIconSize(small_icon_size)  # 使用更小的图标尺寸
            btn.setFixedSize(small_icon_size)
            btn.setFlat(True)
            btn.setStyleSheet("""
                QPushButton {
                    border:none; background:transparent; padding:0; color: white;
                }
                QPushButton:hover {
                    color: #222;
                }
            """)
            btn.clicked.connect(signal.emit)
            algorithm_layout.addWidget(btn)
            self.algorithm_buttons[key] = btn

        layout.addWidget(algorithm_section)


        # ---------- 分隔线 ----------
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        #separator.setLineWidth(1)  # 设置分隔线宽度为2像素
        #separator.setStyleSheet("color: #bbb; background: #bbb; min-height: 2px;")  # 可选：设置颜色和最小高度
        layout.addWidget(separator)

        # 清除图谱按钮单独添加
        clear_section = QWidget()
        clear_layout = QVBoxLayout(clear_section)
        clear_layout.setContentsMargins(4, 4, 4, 4)
        clear_layout.setSpacing(14)

        self.clear_btn = QPushButton()
        self.clear_btn.setIcon(QIcon(r"./UI2/64/64_64/清除图谱.png"))
        self.clear_btn.setToolTip(get_text("clear_spectrum"))
        self.clear_btn.setIconSize(small_icon_size)
        self.clear_btn.setFixedSize(small_icon_size)
        self.clear_btn.setFlat(True)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                border:none; background:transparent; padding:0; color: white;
            }
            QPushButton:hover {
                color: #222;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_spectrum_signal.emit)
        clear_layout.addWidget(self.clear_btn)

        layout.addWidget(clear_section)



        # ✅ 中间拉伸，使底部按钮固定在底部
        layout.addStretch(1)


        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        #separator.setLineWidth(1)  # 设置分隔线宽度为2像素
        #separator.setStyleSheet("color: #bbb; background: #bbb; min-height: 2px;")  # 可选：设置颜色和最小高度
        layout.addWidget(separator)

            # ---------- 底部按钮 ----------
        bottom_section = QWidget()
        bottom_layout = QVBoxLayout(bottom_section)
        bottom_layout.setContentsMargins(4, 4, 4, 4)
        bottom_layout.setSpacing(10)

        # 保存底部按钮引用以便更新语言
        self.bottom_buttons = {}
        
        bottom_buttons = [
            ("communication", r"./UI2/64/64_64/建立通讯.png", self.communication_signal),
            ("user", r"./UI2/64/64_64/用户@2x.png", self.user_signal),
            ("settings", r"./UI2/64/64_64/设置@2x.png", self.settings_signal)
            ]

        for key, icon, signal in bottom_buttons:
            btn = QPushButton()
            btn.setIcon(QIcon(icon))
            btn.setToolTip(get_text(key))
            btn.setIconSize(icon_size)
            btn.setFixedSize(icon_size)
            btn.setFlat(True)
            btn.setStyleSheet("""
                QPushButton {
                    border:none; background:transparent; padding:0; color: white;
                }
                QPushButton:hover {
                    color: #222;
                }
            """)
            btn.clicked.connect(signal.emit)
            if key == "settings":
                self.settings_btn = btn
                btn.clicked.connect(self.show_settings_menu)
            if key == "user":
                self.user_btn = btn
                btn.clicked.connect(self.show_user_settings_dialog)
            bottom_layout.addWidget(btn)
            self.bottom_buttons[key] = btn

        layout.addWidget(bottom_section)


        # 设置侧边栏宽度
        self.setFixedWidth(sidebar_width)

        # 设置菜单 - 与菜单栏保持一致
        self.settings_menu = QMenu(self)
        
        # 语言切换菜单
        self.language_menu = QMenu(get_text("language"), self)
        self.chinese_action = QAction(get_text("chinese"), self)
        self.english_action = QAction(get_text("english"), self)
        self.language_menu.addAction(self.chinese_action)
        self.language_menu.addAction(self.english_action)
        self.settings_menu.addMenu(self.language_menu)
        
        self.settings_menu.addSeparator()  # 添加分隔线
        
        self.update_service_action = QAction(get_text("update_service"), self)
        self.settings_menu.addAction(self.update_service_action)
        
        self.software_management_action = QAction(get_text("software_management"), self)
        self.settings_menu.addAction(self.software_management_action)
        
        # 添加日志查看器入口（仅管理员可见）
        self.log_viewer_action = QAction(get_text("log_viewer"), self)
        self.settings_menu.addAction(self.log_viewer_action)
        self.log_viewer_action.setVisible(False)  # 默认隐藏，管理员登录后显示
        
        # 连接设置菜单动作信号
        self.chinese_action.triggered.connect(lambda: self.change_language("zh"))
        self.english_action.triggered.connect(lambda: self.change_language("en"))
        self.update_service_action.triggered.connect(self.handle_update_service)
        self.software_management_action.triggered.connect(self.handle_software_management)
        self.log_viewer_action.triggered.connect(self.handle_log_viewer)

    def show_settings_menu(self):
        """显示设置菜单"""
        if self.settings_btn:
            pos = self.settings_btn.mapToGlobal(self.settings_btn.rect().bottomLeft())
            self.settings_menu.exec(pos)
    
    def handle_update_service(self):
        """处理更新服务"""
        import zipfile
        import shutil
        import tempfile
        import datetime
        import os
        from pathlib import Path
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QPushButton, QLabel, QFileDialog, QDialogButtonBox, QProgressDialog, QMessageBox
        from PyQt6.QtCore import Qt

        # 创建自定义对话框
        dialog = QDialog(self)
        dialog.setWindowTitle(get_text("service_update"))
        layout = QVBoxLayout()
        
        # ZIP文件选择
        zip_label = QLabel(get_text("update_package_location"))
        zip_path_label = QLabel(get_text("not_selected"))
        zip_path_label.setStyleSheet("color: gray;")
        zip_button = QPushButton(get_text("select_update_package"))
        layout.addWidget(zip_label)
        layout.addWidget(zip_path_label)
        layout.addWidget(zip_button)
        
        # 目标目录选择
        target_label = QLabel(f"\n{get_text('installation_directory')}")
        target_path_label = QLabel(get_text("not_selected"))
        target_path_label.setStyleSheet("color: gray;")
        target_button = QPushButton(get_text("select_installation_directory"))
        layout.addWidget(target_label)
        layout.addWidget(target_path_label)
        layout.addWidget(target_button)
        
        # 添加说明文字
        hint_label = QLabel(f"\n{get_text('update_instructions')}")
        hint_label.setStyleSheet("color: gray; font-size: 10pt;")
        layout.addWidget(hint_label)
        
        # 添加一些空间
        spacer = QLabel("")
        layout.addWidget(spacer)
        
        # 确定和取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        layout.addWidget(button_box)
        
        dialog.setLayout(layout)
        
        # 保存选择的路径
        selected_paths = {"zip": "", "target": ""}
        
        def choose_zip():
            path, _ = QFileDialog.getOpenFileName(
                dialog,
                get_text("select_service_update_package"),
                os.path.expanduser("~"),
                "ZIP文件 (*.zip);;所有文件 (*)"
            )
            if path:
                selected_paths["zip"] = path
                zip_path_label.setText(path)
                zip_path_label.setStyleSheet("color: black;")
        
        def choose_target():
            path = QFileDialog.getExistingDirectory(
                dialog,
                get_text("select_installation_directory_service"),
                os.path.expanduser("~")
            )
            if path:
                selected_paths["target"] = path
                target_path_label.setText(path)
                target_path_label.setStyleSheet("color: black;")
        
        # 连接信号
        zip_button.clicked.connect(choose_zip)
        target_button.clicked.connect(choose_target)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        # 显示对话框
        if dialog.exec() != QDialog.DialogCode.Accepted:
            return
            
        # 验证选择
        if not selected_paths["zip"] or not selected_paths["target"]:
            QMessageBox.warning(self, get_text("incomplete_input"), get_text("please_select_package_and_directory"))
            return
            
        # 设置路径
        zip_file_path = Path(selected_paths["zip"])
        target_service_dir = Path(selected_paths["target"]) / "Service"
        
        try:
            # 确认更新操作
            reply = QMessageBox.question(
                self, get_text("confirm_update"), 
                get_text("about_to_update_service").format(zip_file_path.name, target_service_dir),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # 创建进度对话框
            progress = QProgressDialog(get_text("updating_service"), get_text("cancel"), 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setAutoClose(False)
            progress.show()
            
            # 停止服务
            progress.setLabelText(get_text("stopping_service"))
            progress.setValue(10)
            self._run_service_command(target_service_dir / "停止服务.bat")
            
            # 备份现有文件
            progress.setLabelText(get_text("backing_up_files"))
            progress.setValue(25)
            backup_dir = target_service_dir.parent / f"Service_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if target_service_dir.exists():
                shutil.copytree(target_service_dir, backup_dir)
            
            # 解压新文件
            progress.setLabelText(get_text("extracting_package"))
            progress.setValue(50)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 解压文件
                if zip_file_path.suffix.lower() == '.zip':
                    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                        zip_ref.extractall(temp_path)
                else:
                    QMessageBox.critical(self, get_text("update_failed"), get_text("unsupported_format").format(zip_file_path.suffix))
                    return
                
                # 复制文件到目标目录
                progress.setLabelText(get_text("copying_new_files"))
                progress.setValue(75)
                
                # 确保目标目录存在
                target_service_dir.mkdir(parents=True, exist_ok=True)
                
                # 查找解压后的服务文件
                extracted_files = list(temp_path.rglob("*"))
                service_files = [f for f in extracted_files if f.is_file()]
                
                for file_path in service_files:
                    relative_path = file_path.relative_to(temp_path)
                    target_file = target_service_dir / relative_path
                    
                    # 创建目标目录
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 复制文件
                    shutil.copy2(file_path, target_file)
            
            # 重新安装并启动服务
            progress.setLabelText(get_text("reinstalling_service"))
            progress.setValue(90)
            
            # 卸载旧服务
            self._run_service_command(target_service_dir / "卸载服务.bat")
            
            # 安装新服务
            self._run_service_command(target_service_dir / "安装服务.bat")
            
            # 启动服务
            progress.setLabelText(get_text("starting_service"))
            progress.setValue(95)
            self._run_service_command(target_service_dir / "启动服务.bat")
            
            # 完成
            progress.setValue(100)
            progress.close()
            
            QMessageBox.information(
                self, "更新成功", 
                f"服务更新完成！\n\n"
                f"更新包：{zip_file_path.name}\n"
                f"备份位置：{backup_dir}\n"
                f"服务目录：{target_service_dir}\n\n"
                "服务已重新启动。"
            )
            
        except Exception as e:
            QMessageBox.critical(self, get_text("update_failed"), get_text("service_update_failed").format(str(e)))
            print(f"服务更新失败: {e}")
    
    def _run_service_command(self, bat_file_path):
        """运行服务相关的批处理命令"""
        import subprocess
        from pathlib import Path
        
        bat_path = Path(bat_file_path)
        if not bat_path.exists():
            print(f"批处理文件不存在: {bat_path}")
            return False
        
        try:
            # 在Windows上运行批处理文件
            result = subprocess.run(
                [str(bat_path)], 
                cwd=bat_path.parent,
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"成功执行: {bat_path.name}")
                return True
            else:
                print(f"执行失败: {bat_path.name}, 错误: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"执行超时: {bat_path.name}")
            return False
        except Exception as e:
            print(f"执行异常: {bat_path.name}, 错误: {e}")
            return False
    
    def handle_software_management(self):
        """处理软件管理"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox, QDialogButtonBox, QGridLayout
        
        # 创建软件管理对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("软件管理")
        dialog.setFixedSize(400, 300)
        
        # 创建主布局
        layout = QVBoxLayout()
        
        # 创建网格布局用于表单
        form_layout = QGridLayout()
        
        # 用户名设置
        username_label = QLabel("用户名：")
        username_edit = QLineEdit()
        username_edit.setText("admin")  # 默认用户名
        reset_password_btn = QPushButton("重置密码")
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(username_edit, 0, 1)
        form_layout.addWidget(reset_password_btn, 0, 2)
        
        # 最大曲线数设置
        max_curves_label = QLabel("最大曲线数：")
        max_curves_spin = QSpinBox()
        max_curves_spin.setRange(1, 1000)
        max_curves_spin.setValue(100)  # 默认值100
        form_layout.addWidget(max_curves_label, 1, 0)
        form_layout.addWidget(max_curves_spin, 1, 1)
        
        # 默认横坐标设置
        x_axis_label = QLabel("默认横坐标：")
        x_axis_combo = QComboBox()
        x_axis_combo.addItems(["拉曼位移", "像素"])
        x_axis_combo.setCurrentText("拉曼位移")  # 默认选择
        form_layout.addWidget(x_axis_label, 2, 0)
        form_layout.addWidget(x_axis_combo, 2, 1)
        
        # 输出格式设置
        output_format_label = QLabel("输出格式：")
        output_format_combo = QComboBox()
        output_format_combo.addItems(["nod", "txt", "csv"])
        output_format_combo.setCurrentText("nod")  # 默认选择
        form_layout.addWidget(output_format_label, 3, 0)
        form_layout.addWidget(output_format_combo, 3, 1)
        
        # 重置按钮
        reset_btn = QPushButton("重置")
        form_layout.addWidget(reset_btn, 4, 0, 1, 2)
        
        # 添加表单到主布局
        layout.addLayout(form_layout)
        
        # 添加一些空间
        layout.addStretch()
        
        # 底部按钮
        button_layout = QHBoxLayout()
        change_btn = QPushButton("更改")
        cancel_btn = QPushButton("取消")
        button_layout.addStretch()
        button_layout.addWidget(change_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 连接信号
        def handle_reset_password():
            QMessageBox.information(dialog, "重置密码", "密码重置功能正在开发中...")
        

        
        def handle_reset():
            # 重置所有设置到默认值
            username_edit.setText("admin")
            max_curves_spin.setValue(100)
            x_axis_combo.setCurrentText("拉曼位移")
            output_format_combo.setCurrentText("nod")
            QMessageBox.information(dialog, "重置", "设置已重置为默认值")
        
        def handle_change():
            # 保存设置
            settings = {
                "username": username_edit.text(),
                "max_curves": max_curves_spin.value(),
                "x_axis": x_axis_combo.currentText(),
                "output_format": output_format_combo.currentText()
            }
            QMessageBox.information(dialog, "更改", f"设置已保存：\n{settings}")
            dialog.accept()
        
        def handle_cancel():
            dialog.reject()
        
        # 连接按钮信号
        reset_password_btn.clicked.connect(handle_reset_password)
        reset_btn.clicked.connect(handle_reset)
        change_btn.clicked.connect(handle_change)
        cancel_btn.clicked.connect(handle_cancel)
        
        # 显示对话框
        dialog.exec()
    


    def handle_log_viewer(self):
        """处理打开日志查看器"""
        try:
            from log_viewer import LogViewerWindow
            
            # 检查是否已经打开了日志查看器窗口
            if not hasattr(self, '_log_viewer_window') or self._log_viewer_window is None:
                self._log_viewer_window = LogViewerWindow()
                # 绑定关闭事件，窗口关闭时清空变量
                self._log_viewer_window.destroyed.connect(lambda: setattr(self, '_log_viewer_window', None))
            
            self._log_viewer_window.show()
            self._log_viewer_window.raise_()
            self._log_viewer_window.activateWindow()
            
        except ImportError as e:
            QMessageBox.critical(self, get_text("error"), f"无法导入日志查看器模块: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, get_text("error"), f"打开日志查看器时出错: {str(e)}")

    def change_language(self, lang):
        """切换语言 - 直接连接到菜单栏的语言切换"""
        # 发送语言切换信号，与菜单栏保持一致
        self.language_changed.emit(lang)
        # 更新侧边栏自身的语言显示
        self.update_language(lang)

    def update_language(self, language):
        """更新侧边栏语言"""
        set_language(language)
        
        # 更新数据管理按钮tooltip
        if hasattr(self, 'data_management_btn'):
            self.data_management_btn.setToolTip(get_text("data_management"))
        
        # 更新算法按钮tooltip
        if hasattr(self, 'algorithm_buttons'):
            for key, btn in self.algorithm_buttons.items():
                btn.setToolTip(get_text(key))
        
        # 更新清除图谱按钮tooltip
        if hasattr(self, 'clear_btn'):
            self.clear_btn.setToolTip(get_text("clear_spectrum"))
        
        # 更新底部按钮tooltip
        if hasattr(self, 'bottom_buttons'):
            for key, btn in self.bottom_buttons.items():
                btn.setToolTip(get_text(key))
        
        # 更新设置菜单项文本
        if hasattr(self, 'settings_menu'):
            # 更新语言菜单标题
            if hasattr(self, 'language_menu'):
                self.language_menu.setTitle(get_text("language"))
            
            # 更新语言菜单项
            if hasattr(self, 'chinese_action'):
                self.chinese_action.setText(get_text("chinese"))
            if hasattr(self, 'english_action'):
                self.english_action.setText(get_text("english"))
            
            # 更新其他菜单项文本
            if hasattr(self, 'update_service_action'):
                self.update_service_action.setText(get_text("update_service"))
            if hasattr(self, 'software_management_action'):
                self.software_management_action.setText(get_text("software_management"))
            if hasattr(self, 'log_viewer_action'):
                self.log_viewer_action.setText(get_text("log_viewer"))

    def set_log_viewer_visible(self, visible):
        """设置系统日志菜单项的可见性"""
        if hasattr(self, 'log_viewer_action'):
            self.log_viewer_action.setVisible(visible)

    def open_data_file_dialog(self):
        """打开文件选择对话框并发送文件路径"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, 
            get_text("select_spectrum_data_files"), 
            "", 
            "光谱文件 (*.csv *.txt *.nod);;CSV文件 (*.csv);;TXT文件 (*.txt);;NOD文件 (*.nod);;所有文件 (*)"
        )
        if file_paths:
            self.data_management_file_signal.emit(file_paths)

    def show_user_settings_dialog(self):
        dialog = UserSettingsDialog(self)
        dialog.exec()

    def show_communication_dialog(self):
        dialog = CommunicationDialog(self.laser_connected, self.spectrometer_connected, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.laser_connected = dialog.laser_connected
            self.spectrometer_connected = dialog.spectrometer_connected
            self.communication_status_changed.emit(self.laser_connected, self.spectrometer_connected)

class UserSettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(get_text("user_settings"))
        self.setModal(True)
        self.setFixedWidth(340)
        self.setStyleSheet("""
            QDialog {
                background: #e6f2ff;
                border-radius: 10px;
            }
            QLabel {
                font-size: 15px;
                color: #1a237e;
                margin-top: 6px;
                margin-bottom: 2px;
            }
            QLineEdit {
                border: 1px solid #90caf9;
                border-radius: 5px;
                padding: 6px 8px;
                font-size: 14px;
                background: #ffffff;
                margin-bottom: 4px;
            }
            QCheckBox {
                font-size: 14px;
                color: #1565c0;
                margin-top: 8px;
                margin-bottom: 8px;
            }
            QPushButton {
                background: #1976d2;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 7px 18px;
                font-size: 14px;
                margin: 6px 4px 0 0;
            }
            QPushButton:hover {
                background: #1565c0;
            }
            QPushButton#cancelBtn {
                background: #b0bec5;
                color: #263238;
            }
            QPushButton#cancelBtn:hover {
                background: #90a4ae;
            }
            QLabel#successLabel {
                color: #388e3c;
                font-size: 14px;
                margin-top: 10px;
            }
        """)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 18, 24, 18)
        # 操作员
        self.operator_edit = QLineEdit()
        layout.addWidget(QLabel(get_text("operator")))
        layout.addWidget(self.operator_edit)
        # 工作单位
        self.org_edit = QLineEdit()
        layout.addWidget(QLabel(get_text("work_unit")))
        layout.addWidget(self.org_edit)
        # 联系电话
        self.phone_edit = QLineEdit()
        layout.addWidget(QLabel(get_text("contact_phone")))
        layout.addWidget(self.phone_edit)
        # 工作地址
        self.addr_edit = QLineEdit()
        layout.addWidget(QLabel(get_text("work_address")))
        layout.addWidget(self.addr_edit)
        # 自动登陆
        self.auto_login_checkbox = QCheckBox(get_text("auto_login"))
        layout.addWidget(self.auto_login_checkbox)
        # 按钮区
        btn_layout = QHBoxLayout()
        self.set_btn = QPushButton(get_text("settings"))
        self.cancel_btn = QPushButton(get_text("cancel"))
        self.cancel_btn.setObjectName("cancelBtn")
        btn_layout.addStretch(1)
        btn_layout.addWidget(self.set_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addLayout(btn_layout)
        # 设置成功提示
        self.success_label = QLabel("")
        self.success_label.setObjectName("successLabel")
        layout.addWidget(self.success_label)
        self.set_btn.clicked.connect(self.on_set_clicked)
        self.cancel_btn.clicked.connect(self.reject)
    def on_set_clicked(self):
        self.success_label.setText(get_text("settings_successful"))

        # 通讯弹窗
class CommunicationDialog(QDialog):
    def __init__(self, laser_connected, spectrometer_connected, parent=None):
        super().__init__(parent)
        self.setWindowTitle(get_text("establish_communication"))
        self.setModal(True)
        self.setFixedWidth(400)
        self.laser_connected = laser_connected
        self.spectrometer_connected = spectrometer_connected
        
        # 初始化设备控制器
        self.laser_controller = None
        self.spectrometer = None
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 18, 24, 18)
        
        # 标题
        title_label = QLabel(get_text("establish_communication"))
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 激光器状态
        self.laser_label = QLabel("激光器：检测中...")
        self.laser_label.setStyleSheet("font-size: 14px; margin: 5px 0;")
        layout.addWidget(self.laser_label)
        
        # 光谱仪状态
        self.spectrometer_label = QLabel("光谱仪：检测中...")
        self.spectrometer_label.setStyleSheet("font-size: 14px; margin: 5px 0;")
        layout.addWidget(self.spectrometer_label)
               
        # 确定按钮
        btn_layout = QHBoxLayout()
        btn_layout.addStretch(1)
        self.ok_btn = QPushButton(get_text("ok"))
        self.ok_btn.clicked.connect(self._on_ok)
        btn_layout.addWidget(self.ok_btn)
        layout.addLayout(btn_layout)
        
        # 初始化时检查设备状态
        self.check_device_status()
    
    def check_device_status(self):
        """检查设备连接状态"""
        # 检查激光器状态
        self.check_laser_status()
        
        # 检查光谱仪状态
        self.check_spectrometer_status()
    
    def check_laser_status(self):
        """检查激光器连接状态"""
        try:
            # 导入激光器控制器
            from laser_controller_usb import LaserController
            
            # 如果已经有控制器实例，直接使用，不重新创建
            if not hasattr(self, 'laser_controller') or not self.laser_controller:
                # 创建激光器控制器实例
                self.laser_controller = LaserController()
            
            # 获取状态摘要
            status = self.laser_controller.get_status_summary()
            
            # 检查硬件电源开关和串口连接状态
            hardware_power_on = status.get("hardware_power_on", False)
            serial_connected = status.get("serial_connected", False)
            port = status.get("port", "未知")
            
            if hardware_power_on and serial_connected:
                self.laser_connected = True
                self.laser_label.setText(f"激光器：已连接 (端口: {port})")
                self.laser_label.setStyleSheet("font-size: 14px; margin: 5px 0; color: green;")
            else:
                self.laser_connected = False
                if not hardware_power_on:
                    self.laser_label.setText(f"激光器：开关未开 (端口: {port})")
                elif not serial_connected:
                    self.laser_label.setText(f"激光器：串口未连接 (端口: {port})")
                else:
                    self.laser_label.setText(f"激光器：连接失败 (端口: {port})")
                self.laser_label.setStyleSheet("font-size: 14px; margin: 5px 0; color: red;")
                
        except Exception as e:
            self.laser_connected = False
            self.laser_label.setText(f"激光器：检测失败 - {str(e)}")
            self.laser_label.setStyleSheet("font-size: 14px; margin: 5px 0; color: red;")
    
    def check_spectrometer_status(self):
        """检查光谱仪连接状态"""
        try:
            # 导入光谱仪
            from raman_spectrometer import RamanSpectrometer
            
            # 如果已经有光谱仪实例，直接使用，不重新创建
            if not hasattr(self, 'spectrometer') or not self.spectrometer:
                # 创建光谱仪实例
                self.spectrometer = RamanSpectrometer()
            
            # 尝试连接光谱仪（如果还没连接的话）
            if not hasattr(self.spectrometer, 'hand') or not self.spectrometer.hand:
                connection_result = self.spectrometer.connect_device()
            else:
                connection_result = "yes"  # 已经连接
            
            if connection_result == "yes":
                self.spectrometer_connected = True
                self.spectrometer_label.setText("光谱仪：已连接")
                self.spectrometer_label.setStyleSheet("font-size: 14px; margin: 5px 0; color: green;")
            else:
                self.spectrometer_connected = False
                self.spectrometer_label.setText("光谱仪：连接失败")
                self.spectrometer_label.setStyleSheet("font-size: 14px; margin: 5px 0; color: red;")
                
        except Exception as e:
            self.spectrometer_connected = False
            self.spectrometer_label.setText(f"光谱仪：检测失败 - {str(e)}")
            self.spectrometer_label.setStyleSheet("font-size: 14px; margin: 5px 0; color: red;")
    
    def _on_ok(self):
        """确定按钮点击事件"""
        # 保持当前状态，不强制设置为True
        self.accept()
    
    def closeEvent(self, event):
        """对话框关闭时清理资源"""
        # try:
        #     #不关闭激光器串口，保持连接状态
        #     if hasattr(self, 'laser_controller') and self.laser_controller:
        #         self.laser_controller.close()
            
        #     # 不关闭光谱仪连接，保持连接状态
        #     if hasattr(self, 'spectrometer') and self.spectrometer:
        #         if hasattr(self.spectrometer, 'hand') and self.spectrometer.hand:
        #             import PythonExample_OtOGeneral as oto
        #             oto.closeDevice(self.spectrometer.hand)
        # except:
        #     pass
        super().closeEvent(event)

    # 辅助函数：获取当前用户名
    def _get_current_user(self):
        mainwin = self.parent() if hasattr(self, 'parent') else None
        if mainwin and hasattr(mainwin, 'user_manager') and hasattr(mainwin.user_manager, 'current_user'):
            return mainwin.user_manager.current_user
        return 'unknown'

    def handle_data_management(self):
        log_user_action("侧边栏-数据管理", self._get_current_user(), {})
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, 
            "选择光谱数据文件", 
            "", 
            "光谱文件 (*.csv *.txt *.nod);;CSV文件 (*.csv);;TXT文件 (*.txt);;NOD文件 (*.nod);;所有文件 (*)"
        )
        if file_paths:
            self.data_management_file_signal.emit(file_paths)

    # def handle_baseline_calibration(self):
    #     log_user_action("侧边栏-基线校准", self._get_current_user(), {})
    #     # 基线校准功能待实现
    #     QMessageBox.information(self, "提示", "基线校准功能待实现")

    # def handle_data_smoothing(self):
    #     log_user_action("侧边栏-平滑", self._get_current_user(), {})
    #     # 数据平滑功能待实现
    #     QMessageBox.information(self, "提示", "数据平滑功能待实现")

    # def handle_normalization(self):
    #     log_user_action("侧边栏-归一化", self._get_current_user(), {})
    #     # 归一化功能待实现
    #     QMessageBox.information(self, "提示", "归一化功能待实现")

    # def handle_peak_finding(self):
    #     log_user_action("侧边栏-寻峰", self._get_current_user(), {})
    #     # 寻峰功能待实现
    #     QMessageBox.information(self, "提示", "寻峰功能待实现")

    # def handle_restoration(self):
    #     log_user_action("侧边栏-还原", self._get_current_user(), {})
    #     # 还原功能待实现
    #     QMessageBox.information(self, "提示", "还原功能待实现")

    # def handle_clear_spectrum(self):
    #     log_user_action("侧边栏-清空光谱", self._get_current_user(), {})
    #     # 清除图谱功能待实现
    #     QMessageBox.information(self, "提示", "清除图谱功能待实现")