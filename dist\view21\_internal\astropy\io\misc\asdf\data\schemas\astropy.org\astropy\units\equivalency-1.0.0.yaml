%YAML 1.1
---
$schema: "http://stsci.edu/schemas/yaml-schema/draft-01"
id: "http://astropy.org/schemas/astropy/units/equivalency-1.0.0"
tag: "tag:astropy.org:astropy/units/equivalency-1.0.0"

title: |
  Represents unit equivalency.

description: |
  Supports serialization of equivalencies between units
  in certain contexts

definitions:
  equivalency:
    type: object
    properties:
      name:
        type: string
      kwargs_names:
        type: array
        items:
          type: string
      kwargs_values:
        type: array
        items:
          anyOf:
            - $ref: "tag:stsci.edu:asdf/unit/quantity-1.1.0"
            - type: number
            - type: "null"

type: array
items:
  $ref: "#/definitions/equivalency"
...
