<?xml version="1.0" encoding="%(encoding)s" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<!--
This HTML is auto-generated.  DO NOT EDIT THIS FILE!  If you are writing a new
PEP, see http://www.python.org/dev/peps/pep-0001 for instructions and links
to templates.  DO NOT USE THIS HTML FILE AS YOUR TEMPLATE!
-->
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=%(encoding)s" />
  <meta name="generator" content="Docutils %(version)s: http://docutils.sourceforge.net/" />
  <title>PEP %(pep)s -- %(title)s</title>
  %(stylesheet)s
</head>
<body bgcolor="white">
<table class="navigation" cellpadding="0" cellspacing="0"
       width="100%%" border="0">
<tr><td class="navicon" width="150" height="35">
<a href="%(pyhome)s/" title="Python Home Page">
<img src="%(pyhome)s/pics/PyBanner%(banner)03d.gif" alt="[Python]"
 border="0" width="150" height="35" /></a></td>
<td class="textlinks" align="left">
[<b><a href="%(pyhome)s/">Python Home</a></b>]
[<b><a href="%(pepindex)s/">PEP Index</a></b>]
[<b><a href="%(pephome)s/pep-%(pepnum)s.txt">PEP Source</a></b>]
</td></tr></table>
<div class="document">
%(body)s
%(body_suffix)s
