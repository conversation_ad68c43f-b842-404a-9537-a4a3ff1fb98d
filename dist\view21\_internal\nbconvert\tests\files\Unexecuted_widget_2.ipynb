{"cells": [{"cell_type": "code", "execution_count": null, "id": "036cb3ce-96fe-4159-8145-40bb74b263f9", "metadata": {}, "outputs": [], "source": ["import ipywidgets as widgets\n", "widgets.IntSlider(\n", "    value=7,\n", "    min=0,\n", "    max=10,\n", "    step=1,\n", "    description='Test:',\n", "    disabled=False,\n", "    continuous_update=False,\n", "    orientation='horizontal',\n", "    readout=True,\n", "    readout_format='d'\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}