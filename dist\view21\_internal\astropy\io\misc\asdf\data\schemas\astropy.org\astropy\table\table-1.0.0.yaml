%YAML 1.1
---
$schema: "http://stsci.edu/schemas/yaml-schema/draft-01"
id: "http://astropy.org/schemas/astropy/table/table-1.0.0"
tag: "tag:astropy.org:astropy/table/table-1.0.0"

title: >
  A table.

description: |
  A table is represented as a list of columns, where each entry is a
  [column](ref:http://stsci.edu/schemas/asdf/core/column-1.0.0)
  object, containing the data and some additional information.

  The data itself may be stored inline as text, or in binary in either
  row- or column-major order by use of the `strides` property on the
  individual column arrays.

  Each column in the table must have the same first (slowest moving)
  dimension.

examples:
  -
    - A table stored in column-major order, with each column in a separate block
    - |
        !<tag:astropy.org:astropy/table/table-1.0.0>
          columns:
          - !core/column-1.0.0
            data: !core/ndarray-1.0.0
              source: 0
              datatype: float64
              byteorder: little
              shape: [3]
            description: RA
            meta: {foo: bar}
            name: a
            unit: !unit/unit-1.0.0 deg
          - !core/column-1.0.0
            data: !core/ndarray-1.0.0
              source: 1
              datatype: float64
              byteorder: little
              shape: [3]
            description: DEC
            name: b
          - !core/column-1.0.0
            data: !core/ndarray-1.0.0
              source: 2
              datatype: [ascii, 1]
              byteorder: big
              shape: [3]
            description: The target name
            name: c
          colnames: [a, b, c]

  -
    - A table stored in row-major order, all stored in the same block
    - |
        !<tag:astropy.org:astropy/table/table-1.0.0>
          columns:
          - !core/column-1.0.0
            data: !core/ndarray-1.0.0
              source: 0
              datatype: float64
              byteorder: little
              shape: [3]
              strides: [13]
            description: RA
            meta: {foo: bar}
            name: a
            unit: !unit/unit-1.0.0 deg
          - !core/column-1.0.0
            data: !core/ndarray-1.0.0
              source: 0
              datatype: float64
              byteorder: little
              shape: [3]
              offset: 4
              strides: [13]
            description: DEC
            name: b
          - !core/column-1.0.0
            data: !core/ndarray-1.0.0
              source: 0
              datatype: [ascii, 1]
              byteorder: big
              shape: [3]
              offset: 12
              strides: [13]
            description: The target name
            name: c
          colnames: [a, b, c]

type: object
properties:
  columns:
    description: |
      A list of columns in the table.
    type: array
    items:
      anyOf:
        - $ref: "tag:stsci.edu:asdf/core/column-1.0.0"
        - $ref: "tag:stsci.edu:asdf/core/ndarray-1.0.0"
        - $ref: "tag:stsci.edu:asdf/time/time-1.1.0"
        - $ref: "tag:stsci.edu:asdf/unit/quantity-1.1.0"
        - $ref: "../coordinates/skycoord-1.0.0"
        - $ref: "../coordinates/earthlocation-1.0.0"
        - $ref: "../time/timedelta-1.0.0"

  colnames:
    description: |
      A list containing the names of the columns in the table (in order).
    type: array
    items:
      - type: string

  qtable:
    description: |
      A flag indicating whether or not the serialized type was a QTable
    type: boolean
    default: False

  meta:
    description: |
      Additional free-form metadata about the table.
    type: object
    default: {}

additionalProperties: false
required: [columns, colnames]
