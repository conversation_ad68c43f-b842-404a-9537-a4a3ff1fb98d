attr/__init__.py,sha256=dSRUBxRVTh-dXMrMR_oQ3ZISu2QSfhSZlik03Mjbu30,3241
attr/__init__.pyi,sha256=rIK-2IakIoehVtqXK5l5rs9_fJNCbnYtKTS3cOAVJD8,17609
attr/__pycache__/__init__.cpython-39.pyc,,
attr/__pycache__/_cmp.cpython-39.pyc,,
attr/__pycache__/_compat.cpython-39.pyc,,
attr/__pycache__/_config.cpython-39.pyc,,
attr/__pycache__/_funcs.cpython-39.pyc,,
attr/__pycache__/_make.cpython-39.pyc,,
attr/__pycache__/_next_gen.cpython-39.pyc,,
attr/__pycache__/_version_info.cpython-39.pyc,,
attr/__pycache__/converters.cpython-39.pyc,,
attr/__pycache__/exceptions.cpython-39.pyc,,
attr/__pycache__/filters.cpython-39.pyc,,
attr/__pycache__/setters.cpython-39.pyc,,
attr/__pycache__/validators.cpython-39.pyc,,
attr/_cmp.py,sha256=diMUQV-BIg7IjIb6-o1hswtnjrR4qdAUz_tE8gxS96w,4098
attr/_cmp.pyi,sha256=sGQmOM0w3_K4-X8cTXR7g0Hqr290E8PTObA9JQxWQqc,399
attr/_compat.py,sha256=d3cpIu60IbKrLywPni17RUEQY7MvkqqKifyzJ5H3zRU,5803
attr/_config.py,sha256=5W8lgRePuIOWu1ZuqF1899e2CmXGc95-ipwTpF1cEU4,826
attr/_funcs.py,sha256=YMtzHRSOnFvOVJ7at3E0K95A2lW26HDjby96TMTDbc0,16730
attr/_make.py,sha256=JIyKV-HRh3IcHi-EvOj2dw6tRoqATlx2kBHFrrxZpk0,96979
attr/_next_gen.py,sha256=8lB_S5SFgX2KsflksK8Zygk6XDXToQYtIlmgd37I9aY,6271
attr/_typing_compat.pyi,sha256=XDP54TUn-ZKhD62TOQebmzrwFyomhUCoGRpclb6alRA,469
attr/_version_info.py,sha256=exSqb3b5E-fMSsgZAlEw9XcLpEgobPORCZpcaEglAM4,2121
attr/_version_info.pyi,sha256=x_M3L3WuB7r_ULXAWjx959udKQ4HLB8l-hsc1FDGNvk,209
attr/converters.py,sha256=xfGVSPRgWGcym6N5FZM9fyfvCQePqFyApWeC5BXKvoM,3602
attr/converters.pyi,sha256=jKlpHBEt6HVKJvgrMFJRrHq8p61GXg4-Nd5RZWKJX7M,406
attr/exceptions.py,sha256=0ZTyH_mHmI9utwTTbBWrdS_ck5jps9R2M_fYJPXxH_U,1890
attr/exceptions.pyi,sha256=zZq8bCUnKAy9mDtBEw42ZhPhAUIHoTKedDQInJD883M,539
attr/filters.py,sha256=9pYvXqdg6mtLvKIIb56oALRMoHFnQTcGCO4EXTc1qyM,1470
attr/filters.pyi,sha256=0mRCjLKxdcvAo0vD-Cr81HfRXXCp9j_cAXjOoAHtPGM,225
attr/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
attr/setters.py,sha256=pbCZQ-pE6ZxjDqZfWWUhUFefXtpekIU4qS_YDMLPQ50,1400
attr/setters.pyi,sha256=pyY8TVNBu8TWhOldv_RxHzmGvdgFQH981db70r0fn5I,567
attr/validators.py,sha256=C2MQgX7ubL_cs5YzibWa8m0YxdMq5_3Ch3dVIzsLO-Y,20702
attr/validators.pyi,sha256=167Dl9nt7NUhE9wht1I-buo039qyUT1nEUT_nKjSWr4,2580
attrs-23.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
attrs-23.1.0.dist-info/METADATA,sha256=yglwUXko75Q-IJ6LmPVQ4Y99KJS3CPK0NW8ovXFYsDg,11348
attrs-23.1.0.dist-info/RECORD,,
attrs-23.1.0.dist-info/WHEEL,sha256=EI2JsGydwUL5GP9t6kzZv7G3HDPi7FuZDDf9In6amRM,87
attrs-23.1.0.dist-info/licenses/LICENSE,sha256=iCEVyV38KvHutnFPjsbVy8q_Znyv-HKfQkINpj9xTp8,1109
attrs/__init__.py,sha256=9_5waVbFs7rLqtXZ73tNDrxhezyZ8VZeX4BbvQ3EeJw,1039
attrs/__init__.pyi,sha256=s_ajQ_U14DOsOz0JbmAKDOi46B3v2PcdO0UAV1MY6Ek,2168
attrs/__pycache__/__init__.cpython-39.pyc,,
attrs/__pycache__/converters.cpython-39.pyc,,
attrs/__pycache__/exceptions.cpython-39.pyc,,
attrs/__pycache__/filters.cpython-39.pyc,,
attrs/__pycache__/setters.cpython-39.pyc,,
attrs/__pycache__/validators.cpython-39.pyc,,
attrs/converters.py,sha256=fCBEdlYWcmI3sCnpUk2pz22GYtXzqTkp6NeOpdI64PY,70
attrs/exceptions.py,sha256=SlDli6AY77f6ny-H7oy98OkQjsrw-D_supEuErIVYkE,70
attrs/filters.py,sha256=dc_dNey29kH6KLU1mT2Dakq7tZ3kBfzEGwzOmDzw1F8,67
attrs/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
attrs/setters.py,sha256=oKw51C72Hh45wTwYvDHJP9kbicxiMhMR4Y5GvdpKdHQ,67
attrs/validators.py,sha256=4ag1SyVD2Hm3PYKiNG_NOtR_e7f81Hr6GiNl4YvXo4Q,70
