{"cells": [{"cell_type": "raw", "metadata": {"raw_mimetype": "text/html"}, "source": ["<b>raw html</b>"]}, {"cell_type": "raw", "metadata": {"raw_mimetype": "text/markdown"}, "source": ["* raw markdown\n", "* bullet\n", "* list"]}, {"cell_type": "raw", "metadata": {"raw_mimetype": "text/restructuredtext"}, "source": ["``raw rst``\n", "\n", ".. sourcecode:: python\n", "\n", "    def foo(): pass\n"]}, {"cell_type": "raw", "metadata": {"raw_mimetype": "text/x-python"}, "source": ["def bar():\n", "    \"\"\"raw python\"\"\"\n", "    pass"]}, {"cell_type": "raw", "metadata": {"raw_mimetype": "text/latex"}, "source": ["\\LaTeX\n", "% raw latex"]}, {"cell_type": "raw", "metadata": {}, "source": ["# no raw_mimetype metadata, should be included by default"]}, {"cell_type": "raw", "metadata": {"raw_mimetype": "doesnotexist"}, "source": ["garbage format defined, should never be included"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 0}