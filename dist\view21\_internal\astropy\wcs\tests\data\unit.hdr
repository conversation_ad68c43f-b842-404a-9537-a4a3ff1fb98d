SIMPLE  =                    T / conforms to FITS standard                      BITPIX  =                  -64 / array data type                                NAXIS   =                    3 / number of array dimensions                     NAXIS1  =                  400                                                  NAXIS2  =                  300                                                  NAXIS3  =                  251                                                  EQUINOX =               2000.0                                                  CTYPE1  = 'GLON-CAR'                                                            CTYPE2  = 'GLAT-CAR'                                                            CTYPE3  = 'VRAD'                                                                SPECSYS = 'LSRK'                                                                CUNIT3  = 'km/s    '                                                            BUNIT   = 'K       '                                                            CRVAL1  =            49.209553                                                  CRVAL2  =                  0.0                                                  CRVAL3  =                 50.0                                                  CRPIX1  =                200.0                                                  CRPIX2  =                288.0                                                  CRPIX3  =                125.0                                                  CDELT1  = -0.00333333333333333                                                  CDELT2  = 0.003333333333333333                                                  CDELT3  =                  0.5                                                  RESTFREQ=                1000.                                                  TELESCOP= 'Arecibo'                                                             END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             