#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库用户管理工具
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QLineEdit,
    QComboBox, QMessageBox, QDialog, QFormLayout, QCheckBox,
    QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import Qt
from db_user_manager import DatabaseUserManager

class UserManagementDialog(QDialog):
    """用户管理对话框"""
    
    def __init__(self, user_manager, parent=None):
        super().__init__(parent)
        self.user_manager = user_manager
        self.setWindowTitle("数据库用户管理")
        self.setFixedSize(800, 600)
        self.setup_ui()
        self.load_users()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("用户管理")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 用户表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['用户名', '角色', '可修改密码', '创建时间', '更新时间'])
        
        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        
        # 调整列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(self.table)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("添加用户")
        self.edit_btn = QPushButton("编辑用户")
        self.delete_btn = QPushButton("删除用户")
        self.reset_password_btn = QPushButton("重置密码")
        self.refresh_btn = QPushButton("刷新")
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.reset_password_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.add_btn.clicked.connect(self.add_user)
        self.edit_btn.clicked.connect(self.edit_user)
        self.delete_btn.clicked.connect(self.delete_user)
        self.reset_password_btn.clicked.connect(self.reset_password)
        self.refresh_btn.clicked.connect(self.load_users)
    
    def load_users(self):
        """加载用户数据"""
        users = self.user_manager.get_all_users_info()
        self.table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            self.table.setItem(row, 0, QTableWidgetItem(user['username']))
            self.table.setItem(row, 1, QTableWidgetItem(user['role']))
            self.table.setItem(row, 2, QTableWidgetItem('是' if user['can_change_password'] else '否'))
            self.table.setItem(row, 3, QTableWidgetItem(user['created_at']))
            self.table.setItem(row, 4, QTableWidgetItem(user['updated_at']))
    
    def get_selected_username(self):
        """获取选中的用户名"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            return self.table.item(current_row, 0).text()
        return None
    
    def add_user(self):
        """添加用户"""
        dialog = UserEditDialog(self.user_manager, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_users()
    
    def edit_user(self):
        """编辑用户"""
        username = self.get_selected_username()
        if not username:
            QMessageBox.warning(self, "警告", "请选择要编辑的用户")
            return
        
        dialog = UserEditDialog(self.user_manager, self, username)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_users()
    
    def delete_user(self):
        """删除用户"""
        username = self.get_selected_username()
        if not username:
            QMessageBox.warning(self, "警告", "请选择要删除的用户")
            return
        
        if username in ["admin", "user"]:
            QMessageBox.warning(self, "警告", "不能删除默认用户")
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除用户 '{username}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self.user_manager.delete_user(username):
                QMessageBox.information(self, "成功", f"用户 '{username}' 已删除")
                self.load_users()
            else:
                QMessageBox.warning(self, "错误", "删除用户失败")
    
    def reset_password(self):
        """重置密码"""
        username = self.get_selected_username()
        if not username:
            QMessageBox.warning(self, "警告", "请选择要重置密码的用户")
            return
        
        new_password = "123456"  # 默认密码
        reply = QMessageBox.question(
            self, "确认重置", 
            f"确定要将用户 '{username}' 的密码重置为 '{new_password}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self.user_manager.update_user_password(username, new_password):
                QMessageBox.information(self, "成功", f"用户 '{username}' 的密码已重置")
            else:
                QMessageBox.warning(self, "错误", "重置密码失败")

class UserEditDialog(QDialog):
    """用户编辑对话框"""
    
    def __init__(self, user_manager, parent=None, username=None):
        super().__init__(parent)
        self.user_manager = user_manager
        self.username = username
        self.is_edit_mode = username is not None
        
        self.setWindowTitle("编辑用户" if self.is_edit_mode else "添加用户")
        self.setFixedSize(300, 200)
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_user_data()
    
    def setup_ui(self):
        """设置UI"""
        layout = QFormLayout(self)
        
        # 用户名
        self.username_edit = QLineEdit()
        if self.is_edit_mode:
            self.username_edit.setEnabled(False)
        layout.addRow("用户名:", self.username_edit)
        
        # 密码（仅在添加模式下显示）
        if not self.is_edit_mode:
            self.password_edit = QLineEdit()
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
            layout.addRow("密码:", self.password_edit)
        
        # 角色
        self.role_combo = QComboBox()
        self.role_combo.addItems(["user", "admin"])
        layout.addRow("角色:", self.role_combo)
        
        # 可修改密码
        self.can_change_password_cb = QCheckBox()
        layout.addRow("可修改密码:", self.can_change_password_cb)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addRow(button_layout)
        
        # 连接信号
        self.save_btn.clicked.connect(self.save_user)
        self.cancel_btn.clicked.connect(self.reject)
    
    def load_user_data(self):
        """加载用户数据"""
        if self.username:
            self.username_edit.setText(self.username)
            role = self.user_manager.get_user_role(self.username)
            if role:
                index = self.role_combo.findText(role)
                if index >= 0:
                    self.role_combo.setCurrentIndex(index)
            
            # 加载其他信息
            user_info = self.user_manager.get_user_info()
            if user_info:
                self.can_change_password_cb.setChecked(user_info.get('can_change_password', False))
    
    def save_user(self):
        """保存用户"""
        username = self.username_edit.text().strip()
        role = self.role_combo.currentText()
        can_change_password = self.can_change_password_cb.isChecked()
        
        if not username:
            QMessageBox.warning(self, "错误", "请输入用户名")
            return
        
        if self.is_edit_mode:
            # 编辑模式：更新角色
            if self.user_manager.update_user_role(username, role):
                QMessageBox.information(self, "成功", "用户信息已更新")
                self.accept()
            else:
                QMessageBox.warning(self, "错误", "更新用户信息失败")
        else:
            # 添加模式：创建新用户
            password = self.password_edit.text().strip()
            if not password:
                QMessageBox.warning(self, "错误", "请输入密码")
                return
            
            if self.user_manager.create_user(username, password, role, can_change_password):
                QMessageBox.information(self, "成功", "用户已创建")
                self.accept()
            else:
                QMessageBox.warning(self, "错误", "创建用户失败，可能用户名已存在")

class UserManagementTool(QMainWindow):
    """用户管理工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("数据库用户管理工具")
        self.setGeometry(100, 100, 900, 700)
        
        self.user_manager = DatabaseUserManager()
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加用户管理对话框内容
        self.user_dialog = UserManagementDialog(self.user_manager, self)
        layout.addWidget(self.user_dialog)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = UserManagementTool()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
