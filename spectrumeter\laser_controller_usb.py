import serial
import threading
import time
import os

class LaserController:
    def __init__(self, port='COM3', baudrate=115200, max_retries=3, serial_timeout=0.5):
        self.port = port
        self.baudrate = baudrate
        self.max_retries = max_retries
        self.serial_timeout = serial_timeout
        self.ser = None
        self._serial_lock = threading.Lock()
        self.is_enabled = False
        self.current_power = 10.0
        
        # 添加电源状态监控
        self.power_on_status = None  # 硬件电源开关状态
        self.last_status_check = 0   # 上次状态检查时间
        self.status_check_interval = 2.0  # 状态检查间隔（秒）
        self.monitoring_thread = None
        self.monitoring_active = False

        self.open_port()
        # 初始化时检查电源状态
        self.check_power_status()

    def check_power_status(self):
        """检查并更新电源开关状态"""
        try:
            status = self.is_laser_power_on()
            if status != self.power_on_status:
                if status is True:
                    print("检测到激光器电源开关已打开")
                elif status is False:
                    print("检测到激光器电源开关已关闭")
                else:
                    print("无法检测激光器电源开关状态")
                self.power_on_status = status
            self.last_status_check = time.time()
        except Exception as e:
            print(f"电源状态检查失败: {e}")

    def start_monitoring(self):
        """启动电源状态监控线程"""
        if self.monitoring_thread is None or not self.monitoring_thread.is_alive():
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitor_power_status, daemon=True)
            self.monitoring_thread.start()
            print("🔄 启动激光器电源状态监控")

    def stop_monitoring(self):
        """停止电源状态监控"""
        self.monitoring_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        print("⏹️ 停止激光器电源状态监控")

    def _monitor_power_status(self):
        """电源状态监控线程"""
        while self.monitoring_active:
            try:
                if time.time() - self.last_status_check >= self.status_check_interval:
                    self.check_power_status()
                time.sleep(0.5)  # 监控循环间隔
            except Exception as e:
                print(f"电源状态监控异常: {e}")
                time.sleep(1.0)

    def is_port_available(self):
        # 检查端口是否存在
        try:
            import serial.tools.list_ports
            ports = [p.device for p in serial.tools.list_ports.comports()]
            return self.port in ports
        except Exception as e:
            print(f"检查串口{self.port}可用性异常: {e}")
            return False

    def safe_close_port(self):
        try:
            if self.ser and self.ser.is_open:
                self.ser.close()
                self.ser = None
        except Exception as e:
            print(f"安全关闭串口异常: {e}")

    def open_port(self):
        with self._serial_lock:
            retry_count = 0
            while retry_count < self.max_retries:
                if not self.is_port_available():
                    print(f"串口{self.port}不存在或不可用")
                    self.ser = None
                    return False
                try:
                    self.safe_close_port()
                    self.ser = serial.Serial(
                        port=self.port,
                        baudrate=self.baudrate,
                        bytesize=serial.EIGHTBITS,
                        parity=serial.PARITY_NONE,
                        stopbits=serial.STOPBITS_ONE,
                        timeout=self.serial_timeout,
                        write_timeout=5
                    )
                    print(f"已连接到激光串口: {self.port}")
                    return True
                except serial.SerialException as e:
                    retry_count += 1
                    print(f"打开串口{self.port}失败，重试{retry_count}/{self.max_retries}: {e}")
                    time.sleep(0.2 * retry_count)
            print(f"无法打开激光串口 {self.port}，已达到最大重试次数")
            self.ser = None
            return False

    def send_command(self, command: bytes):
        with self._serial_lock:
            if not self.ser or not self.ser.is_open:
                print("串口未打开，无法发送命令")
                return False
            try:
                self.ser.write(command)
                time.sleep(0.05)
                return True
            except Exception as e:
                print(f"发送命令异常: {e}")
                self.safe_close_port()
                return False

    def enable_laser(self):
        # 激光器使能
        try:
            # 先检查当前状态，如果已经使能则先失能再重新使能
            if self.is_enabled:
                print("激光已处于使能状态，先失能再重新使能...")
                self.disable_laser()
                time.sleep(0.2)  # 等待状态稳定
            
            command = bytes.fromhex("AA 54 07 00 00 00 00 01 08 AA 55")
            if self.send_command(command):
                print("激光已使能")
                self.is_enabled = True
                # 使能后立即检查电源状态
                time.sleep(0.5)
                self.check_power_status()
                return True
            else:
                print("激光使能命令发送失败")
                return False
        except Exception as e:
            print(f"激光使能失败: {e}")
            return False

    def disable_laser(self):
        # 激光器失能
        try:
            command = bytes.fromhex("AA 54 08 00 00 00 00 01 09 AA 55")
            if self.send_command(command):
                print("激光已失能")
                self.is_enabled = False
                # 失能后立即检查电源状态
                time.sleep(0.5)
                self.check_power_status()
                return True
            else:
                print("激光失能命令发送失败")
                return False
        except Exception as e:
            print(f"激光失能失败: {e}")
            return False

    def set_power(self, power_mw):
        # 功率命令生成方式与C#一致
        # power_mw: 功率值，单位毫瓦(mW)，范围0-500mW
        power_mw = self._validate_power_mw(power_mw)
        self.current_power = power_mw
        # 参考C# CalPower
        data = int((power_mw / 500) * 65535)
        data_hex = f"{data:04X}"
        data3 = int(data_hex[:2], 16)
        data4 = int(data_hex[2:], 16)
        # 参考C# CalCRC
        crc = (5 + 1 + data3 + data4) % 256
        command = bytearray([0xAA, 0x54, 0x05, 0x00, 0x00, 0x01, data3, data4, crc, 0xAA, 0x55])
        if self.send_command(command):
            print(f"设置激光功率为 {power_mw}mW, command={command.hex()}")

    def set_power_percent(self, power_percent):
        """设置激光功率（百分比方式）
        power_percent: 功率百分比，范围0-100
        """
        if not isinstance(power_percent, (int, float)):
            raise ValueError("功率百分比必须是数字")
        if power_percent < 0.0 or power_percent > 100.0:
            raise ValueError(f"功率百分比必须在0-100范围内: {power_percent}%")
        
        # 将百分比转换为毫瓦
        power_mw = (power_percent / 100.0) * 500.0
        self.set_power(power_mw)

    def _validate_power_mw(self, power_mw):
        if not isinstance(power_mw, (int, float)):
            raise ValueError("功率必须是数字")
        if power_mw < 0.0 or power_mw > 500.0:
            raise ValueError(f"功率必须在0-500mW范围内: {power_mw}mW")
        return float(power_mw)

    def read_laser_status(self):
        """读取激光器实际电源开关状态"""
        try:
            query_command = bytes.fromhex("AA 54 01 00 00 00 00 00 01 AA 55")
            with self._serial_lock:
                if not self.ser or not self.ser.is_open:
                    print("串口未打开，无法读取激光状态")
                    return None
                self.ser.reset_input_buffer()
                self.ser.write(query_command)
                time.sleep(0.1)
                if self.ser.in_waiting > 0:
                    response = self.ser.read(self.ser.in_waiting)
                    response_hex = response.hex().upper()
                    print(f"收到激光器状态响应: {response_hex}")
                    # 解析响应（可根据实际协议调整）
                    # 例：AA54810000007001F2AA55 表示某型号激光器已开
                    if response_hex.startswith("AA5481"):
                        # 只要不是全部为0，认为电源开关已打开
                        if response_hex[14:16] != "00":
                            return True
                        else:
                            return False
                    else:
                        print("未知响应，无法判断激光器状态")
                        return None
                else:
                    print("未收到激光器状态响应")
                    return None
        except Exception as e:
            print(f"读取激光器状态失败: {e}")
            return None

    def is_laser_power_on(self):
        """返回激光器电源开关是否真正打开（硬件状态）"""
        status = self.read_laser_status()
        if status is True:
            print("激光器电源开关已打开（硬件反馈）")
            return True
        elif status is False:
            print("激光器电源开关未打开（硬件反馈）")
            return False
        else:
            print("无法判断激光器电源开关状态")
            return None

    def get_status_summary(self):
        """获取激光器完整状态摘要"""
        power_percent = (self.current_power / 500.0) * 100.0 if self.current_power else 0.0
        return {
            "hardware_power_on": self.power_on_status,
            "serial_connected": self.ser is not None and self.ser.is_open,
            "port": self.port,
            "current_power_mw": self.current_power,
            "current_power_percent": power_percent
        }


    def close(self):
        self.stop_monitoring()
        self.disable_laser()
        self.safe_close_port()

    # def __del__(self):
    #     # 只关闭串口和停止监控，不自动失能激光器
    #     # 避免在对象被垃圾回收时意外失能激光器
    #     try:
    #         self.stop_monitoring()
    #         self.safe_close_port()
    #     except:
    #         pass

# 使用示例
#if __name__ == '__main__':
    # laser = LaserController('COM3', 115200)
    # try:
    #     # 启动电源状态监控
    #     laser.start_monitoring()
        
    #     # 显示初始状态
    #     print("=== 激光器初始状态 ===")
    #     status = laser.get_status_summary()
    #     for key, value in status.items():
    #         print(f"{key}: {value}")
        
    #     # 使能激光器
    #     print("\n=== 使能激光器 ===")
    #     laser.enable_laser()
    #     time.sleep(1)
        
    #     # 设置功率
    #     print("\n=== 设置功率 ===")
    #     laser.set_power_percent(90)  # 90%功率，相当于450mW
    #     time.sleep(1)
        
    #     # 再次检查状态
    #     print("\n=== 当前状态 ===")
    #     status = laser.get_status_summary()
    #     for key, value in status.items():
    #         print(f"{key}: {value}")
        
    #     # 运行一段时间（监控线程会自动检测状态变化）
    #     print("\n=== 运行中（监控线程检测状态变化）===")
    #     time.sleep(10)
        
    #     # 失能激光器
    #     print("\n=== 失能激光器 ===")
    #     laser.disable_laser()
    #     time.sleep(1)
        
    #     # 最终状态
    #     print("\n=== 最终状态 ===")
    #     status = laser.get_status_summary()
    #     for key, value in status.items():
    #         print(f"{key}: {value}")
            
    # except KeyboardInterrupt:
    #     print("\n用户中断程序")
    # finally:
    #     laser.close()