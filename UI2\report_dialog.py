#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QToolBar, QLabel, 
    QPushButton, QFontDialog, QColorDialog, QFileDialog, QMessageBox,
    QSplitter, QWidget, QFrame, QScrollArea, QSizePolicy
)
from PyQt6.QtGui import (
    QFont, QColor, QPixmap, QIcon, QTextCursor, QTextCharFormat,
    QPainter, QTextDocument
)
from PyQt6.QtCore import Qt, QSize, QRect
# PyQt6中打印相关类在QtPrintSupport模块
from PyQt6.QtPrintSupport import QPrinter, QPageSetupDialog, QPrintDialog
# 简化matplotlib导入，避免backend冲突
import matplotlib
# 导入语言管理器
from language_manager import get_text
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np


class ReportDialog(QDialog):
    """Word风格的报告编辑窗口"""
    
    def __init__(self, parent=None, files_info=None, spectrum_image_path=None):
        super().__init__(parent)
        self.files_info = files_info or []
        self.spectrum_image_path = spectrum_image_path
        self.current_font = QFont("SimHei", 12)
        self.current_color = QColor(0, 0, 0)
        self.current_background = QColor(255, 255, 255)
        
        self.setup_ui()
        self.generate_report_content()
        
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle(get_text("raman_spectrum_analysis_report"))
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)
        
        # 顶部工具栏
        self.setup_toolbar()
        main_layout.addWidget(self.toolbar)
        
        # 分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(line)
        
        # 报告内容区域
        self.setup_report_area()
        main_layout.addWidget(self.report_widget)
        
        # 底部按钮
        self.setup_bottom_buttons()
        main_layout.addWidget(self.bottom_widget)
        
    def setup_toolbar(self):
        """设置工具栏"""
        self.toolbar = QToolBar()
        self.toolbar.setIconSize(QSize(24, 24))
        self.toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # 保存按钮
        save_action = self.toolbar.addAction(get_text("save_files"))
        save_action.setIcon(QIcon("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE5IDNIMTlDMTguNDQ3NyAzIDE4IDMuNDQ3NzIgMTggNFYxOEMxOCAxOC41NTIzIDE4LjQ0NzcgMTkgMTkgMTlIMTlDMTkuNTUyMyAxOSAyMCAxOC41NTIzIDIwIDE4VjRDMjAgMy40NDc3MiAxOS41NTIzIDMgMTkgM1oiIGZpbGw9IiMwMDAwMDAiLz4KPHBhdGggZD0iTTE2IDdIMTZDNi42MTI4NiA3IDIgMTEuNjEyOSAyIDE3VjE5QzIgMTkuNTUyMyAyLjQ0NzcyIDIwIDMgMjBIMjFDMjEuNTUyMyAyMCAyMiAxOS41NTIzIDIyIDE5VjE3QzIyIDExLjYxMjkgMTcuMzg3MSA3IDE2IDdaIiBmaWxsPSIjMDAwMDAwIi8+Cjwvc3ZnPgo="))
        save_action.triggered.connect(self.save_report)
        
        # 字体按钮
        font_action = self.toolbar.addAction(get_text("font"))
        font_action.setIcon(QIcon("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMC4xMiA5LjI3TDEzLjA5IDEwLjI4TDEyIDE2LjVMMTAuOTEgMTAuMjhMMy44OCA5LjI3TDEwLjkxIDguMjZMMTIgMloiIGZpbGw9IiMwMDAwMDAiLz4KPC9zdmc+Cg=="))
        font_action.triggered.connect(self.change_font)
        
        # 颜色按钮
        color_action = self.toolbar.addAction(get_text("color"))
        color_action.setIcon(QIcon("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyUzYuNDggMjIgMTIgMjJTMjIgMTcuNTIgMjIgMTJIMTJWMloiIGZpbGw9IiMwMDAwMDAiLz4KPC9zdmc+Cg=="))
        color_action.triggered.connect(self.change_color)
        
        # 背景按钮
        bg_action = self.toolbar.addAction(get_text("background"))
        bg_action.setIcon(QIcon("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyUzYuNDggMjIgMTIgMjJTMjIgMTcuNTIgMjIgMTJIMTJWMloiIGZpbGw9IiMwMDAwMDAiLz4KPC9zdmc+Cg=="))
        bg_action.triggered.connect(self.change_background)
        
        # 打印按钮
        print_action = self.toolbar.addAction(get_text("print_report"))
        print_action.setIcon(QIcon("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE5IDhIMTlDMTguNDQ3NyA4IDE4IDguNDQ3NzIgMTggOVYxN0MxOCAxNy41NTIzIDE4LjQ0NzcgMTggMTkgMThIMTlDMTkuNTUyMyAxOCAyMCAxNy41NTIzIDIwIDE3VjlDMjAgOC40NDc3MiAxOS41NTIzIDggMTkgOFoiIGZpbGw9IiMwMDAwMDAiLz4KPHBhdGggZD0iTTYgMTJIMTZDNi42MTI4NiAxMiAyIDEyLjYxMjkgMiAxM1YxN0MyIDE3LjU1MjMgMi40NDc3MiAxOCAzIDE4SDIxQzIxLjU1MjMgMTggMjIgMTcuNTUyMyAyMiAxN1YxM0MyMiAxMi42MTI5IDE3LjM4NzEgMTIgMTYgMTJINloiIGZpbGw9IiMwMDAwMDAiLz4KPC9zdmc+Cg=="))
        print_action.triggered.connect(self.print_report)
        
    def setup_report_area(self):
        """设置报告内容区域"""
        self.report_widget = QWidget()
        report_layout = QHBoxLayout(self.report_widget)
        report_layout.setContentsMargins(0, 0, 0, 0)
        report_layout.setSpacing(10)
        
        # 左侧：公司logo和标题
        left_widget = QWidget()
        left_widget.setFixedWidth(200)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)
        
        # 公司logo
        logo_label = QLabel()
        logo_path = os.path.join(os.path.dirname(__file__), "logo48.ico")
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path).scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
        else:
            logo_label.setText("LOGO")
            logo_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #1976d2;")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(logo_label)
        
        # Report标题
        report_title = QLabel("REPORT")
        report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1976d2; margin-top: 20px;")
        report_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(report_title)
        
        left_layout.addStretch()
        report_layout.addWidget(left_widget)
        
        # 右侧：报告内容
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # 报告文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                background-color: white;
                font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        right_layout.addWidget(self.text_edit)
        
        report_layout.addWidget(right_widget, 1)
        
    def setup_bottom_buttons(self):
        """设置底部按钮"""
        self.bottom_widget = QWidget()
        bottom_layout = QHBoxLayout(self.bottom_widget)
        bottom_layout.setContentsMargins(0, 10, 0, 0)
        
        # 取消按钮
        cancel_btn = QPushButton(get_text("cancel"))
        cancel_btn.clicked.connect(self.reject)
        bottom_layout.addWidget(cancel_btn)
        
        bottom_layout.addStretch()
        
        # 确定按钮
        ok_btn = QPushButton(get_text("ok"))
        ok_btn.clicked.connect(self.accept)
        bottom_layout.addWidget(ok_btn)
        
    def generate_report_content(self):
        """生成报告内容"""
        content = []
        
        # 报告标题
        content.append(f"<h1 style='text-align: center; color: #1976d2;'>{get_text('raman_spectrum_analysis_report')}</h1>")
        content.append(f"<p style='text-align: right; color: #666;'>{get_text('report_generation_time').format(datetime.datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'))}</p>")
        content.append("<hr>")
        
        # 第一部分：文件信息
        content.append(f"<h2 style='color: #1976d2;'>{get_text('file_information')}</h2>")
        
        if self.files_info:
            for i, file_info in enumerate(self.files_info, 1):
                content.append(f"<h3>{get_text('file_number').format(i)}: {file_info.get('文件名', '未知文件')}</h3>")
                content.append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>")
                content.append(f"<tr><th style='background-color: #f5f5f5;'>{get_text('parameter')}</th><th style='background-color: #f5f5f5;'>{get_text('value')}</th></tr>")
                
                # 文件信息表格
                info_fields = [
                    (get_text('device_serial_number'), '设备序列号'),
                    (get_text('operator'), '操作员'),
                    (get_text('integration_time'), '积分时间'),
                    (get_text('laser_power'), '激光功率'),
                    (get_text('average_count'), '平均次数'),
                    (get_text('acquisition_mode'), '采集模式'),
                    (get_text('acquisition_interval'), '采集间隔'),
                    (get_text('pixel'), '像素')
                ]
                
                for field_name, key in info_fields:
                    value = file_info.get(key, get_text('none'))
                    content.append(f"<tr><td>{field_name}</td><td>{value}</td></tr>")
                
                content.append("</table><br>")
        else:
            content.append(f"<p style='color: #999;'>{get_text('no_files_selected')}</p>")
        
        # 第二部分：光谱视图
        content.append(f"<h2 style='color: #1976d2;'>{get_text('spectrum_analysis_results')}</h2>")
        
        if self.spectrum_image_path and os.path.exists(self.spectrum_image_path):
            content.append(f"<img src='{self.spectrum_image_path}' style='max-width: 100%; height: auto;' alt='光谱图'>")
        else:
            content.append(f"<p style='color: #999;'>{get_text('spectrum_image_unavailable')}</p>")
        
        # 设置HTML内容
        self.text_edit.setHtml("".join(content))
        
    def change_font(self):
        """更改字体"""
        font, ok = QFontDialog.getFont(self.current_font, self)
        if ok:
            self.current_font = font
            cursor = self.text_edit.textCursor()
            if cursor.hasSelection():
                format = QTextCharFormat()
                format.setFont(font)
                cursor.mergeCharFormat(format)
            else:
                self.text_edit.setFont(font)
                
    def change_color(self):
        """更改文字颜色"""
        color = QColorDialog.getColor(self.current_color, self)
        if color.isValid():
            self.current_color = color
            cursor = self.text_edit.textCursor()
            if cursor.hasSelection():
                format = QTextCharFormat()
                format.setForeground(color)
                cursor.mergeCharFormat(format)
                
    def change_background(self):
        """更改背景颜色"""
        color = QColorDialog.getColor(self.current_background, self)
        if color.isValid():
            self.current_background = color
            self.text_edit.setStyleSheet(f"""
                QTextEdit {{
                    border: 1px solid #ccc;
                    background-color: {color.name()};
                    font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
                    font-size: 12px;
                    line-height: 1.5;
                }}
            """)
            
    def save_report(self):
        """保存报告"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, get_text("save_report"), "", 
            "HTML文件 (*.html);;文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.html'):
                    # 保存为HTML
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(self.text_edit.toHtml())
                else:
                    # 保存为纯文本
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(self.text_edit.toPlainText())
                        
                QMessageBox.information(self, "保存成功", f"报告已保存到：{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存文件时出错：{str(e)}")
                
    def print_report(self):
        """打印报告"""
        try:
            # 创建打印机对象
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            
            # 显示打印对话框
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QDialog.DialogCode.Accepted:
                # 创建文档对象
                document = QTextDocument()
                document.setHtml(self.text_edit.toHtml())
                
                # 打印文档
                document.print_(printer)
                QMessageBox.information(self, "打印成功", "报告已发送到打印机")
                
        except Exception as e:
            QMessageBox.critical(self, "打印失败", f"打印时出错：{str(e)}")


def capture_spectrum_as_image(spectrum_view, save_path):
    """将光谱视图捕获为图片"""
    try:
        # 获取matplotlib画布
        canvas = spectrum_view.canvas
        
        # 保存图片
        canvas.figure.savefig(save_path, dpi=150, bbox_inches='tight', 
                             facecolor='white', edgecolor='none')
        return True
    except Exception as e:
        print(f"捕获光谱图片失败: {e}")
        return False 