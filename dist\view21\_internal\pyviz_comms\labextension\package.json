{"name": "@pyviz/jupyterlab_pyviz", "version": "2.0.2", "description": "A JupyterLab extension for rendering HoloViz content.", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/holoviz/pyviz_comms", "bugs": {"url": "https://github.com/holoviz/pyviz_comms/issues"}, "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON>", "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,.js,eot,gif,html,jpg,json,png,svg,woff2,ttf}"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "repository": {"type": "git", "url": "https://github.com/holoviz/pyviz_comms.git"}, "scripts": {"build": "jlpm run build:lib && jlpm run build:labextension:dev", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc", "build:prod": "jlpm run build:lib && jlpm run build:labextension", "clean": "jlpm run clean:lib", "clean:all": "jlpm run clean:lib && jlpm run clean:labextension", "clean:labextension": "rimraf pyviz_comms/labextension", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "eslint": "eslint . --ext .ts,.tsx --fix", "eslint:check": "eslint . --ext .ts,.tsx", "install:extension": "jupyter labextension develop --overwrite .", "prepare": "jlpm run clean && jlpm run build:prod", "watch": "run-p watch:src watch:labextension", "watch:labextension": "jupyter labextension watch .", "watch:src": "tsc -w"}, "dependencies": {"@jupyterlab/application": "^3.0.0", "@jupyterlab/docregistry": "^3.0.0", "@jupyterlab/notebook": "^3.0.0"}, "peerDependencies": {"@jupyter-widgets/jupyterlab-manager": "^3.0.0"}, "devDependencies": {"@jupyter-widgets/jupyterlab-manager": "^3.0.0", "@jupyterlab/builder": "^3.0.0", "@jupyterlab/testutils": "^3.0.0", "@types/node": "^14.14.16", "@typescript-eslint/eslint-plugin": "^2.27.0", "@typescript-eslint/parser": "^2.27.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.2", "npm-run-all": "^4.1.5", "prettier": "^1.19.0", "rimraf": "^3.0.2", "typescript": "~4.1.3"}, "sideEffects": ["style/*.css", "style/index.js"], "styleModule": "style/index.js", "jupyterlab": {"extension": true, "outputDir": "pyviz_comms/labextension", "schemaDir": "schema", "sharedPackages": {"@jupyter-widgets/jupyterlab-manager": {"bundled": false, "singleton": true}}, "_build": {"load": "static/remoteEntry.c267118552c4161d6406.js", "extension": "./extension", "style": "./style"}}}