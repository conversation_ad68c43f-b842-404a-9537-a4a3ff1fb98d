#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备监控功能测试脚本
用于测试设备监控器的各项功能
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QHBoxLayout
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from device_monitor import DeviceMonitor
from status_bar import StatusBar

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("设备监控功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建状态栏
        self.status_bar = StatusBar()
        self.setStatusBar(self.status_bar)
        
        # 创建设备监控器
        self.device_monitor = DeviceMonitor(self.status_bar)
        
        # 设置UI
        self.setup_ui()
        
        # 设置定时器更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_display)
        self.status_timer.start(1000)  # 每秒更新一次
        
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        start_btn = QPushButton("启动监控")
        start_btn.clicked.connect(self.start_monitoring)
        button_layout.addWidget(start_btn)
        
        stop_btn = QPushButton("停止监控")
        stop_btn.clicked.connect(self.stop_monitoring)
        button_layout.addWidget(stop_btn)
        
        test_disconnect_btn = QPushButton("测试断开")
        test_disconnect_btn.clicked.connect(self.test_disconnect)
        button_layout.addWidget(test_disconnect_btn)
        
        test_reconnect_btn = QPushButton("测试重连")
        test_reconnect_btn.clicked.connect(self.test_reconnect)
        button_layout.addWidget(test_reconnect_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.status_display = QTextEdit()
        self.status_display.setReadOnly(True)
        self.status_display.setMaximumHeight(200)
        layout.addWidget(self.status_display)
        
        # 配置测试
        config_layout = QHBoxLayout()
        
        config_btn = QPushButton("显示配置")
        config_btn.clicked.connect(self.show_config)
        config_layout.addWidget(config_btn)
        
        apply_config_btn = QPushButton("应用测试配置")
        apply_config_btn.clicked.connect(self.apply_test_config)
        config_layout.addWidget(apply_config_btn)
        
        layout.addLayout(config_layout)
        
        # 日志显示
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        
        # 连接信号
        self.device_monitor.device_status_changed.connect(self.on_device_status_changed)
        self.device_monitor.connection_attempt.connect(self.on_connection_attempt)
        self.device_monitor.auto_reconnect_started.connect(self.on_auto_reconnect_started)
        self.device_monitor.auto_reconnect_completed.connect(self.on_auto_reconnect_completed)
        
    def start_monitoring(self):
        """启动监控"""
        self.device_monitor.start_monitoring()
        self.log_display.append("🔄 设备监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.device_monitor.stop_monitoring()
        self.log_display.append("⏹️ 设备监控已停止")
        
    def test_disconnect(self):
        """测试设备断开"""
        # 模拟设备断开
        self.device_monitor._update_device_status('spectrometer', False, "测试断开")
        self.log_display.append("🧪 模拟光谱仪断开连接")
        
    def test_reconnect(self):
        """测试设备重连"""
        # 模拟设备重连
        self.device_monitor._update_device_status('spectrometer', True, "测试重连")
        self.log_display.append("🧪 模拟光谱仪重连成功")
        
    def show_config(self):
        """显示当前配置"""
        config = self.device_monitor.get_config()
        config_text = "当前配置:\n"
        for key, value in config.items():
            config_text += f"  {key}: {value}\n"
        self.status_display.setText(config_text)
        
    def apply_test_config(self):
        """应用测试配置"""
        test_config = {
            'check_interval': 1.0,
            'auto_reconnect_enabled': True,
            'max_reconnect_attempts': 5,
            'reconnect_delay': 2.0,
            'notify_disconnect': True,
            'notify_reconnect': True,
            'log_events': True,
            'monitor_spectrometer': True,
            'monitor_laser': True,
            'monitor_power': True
        }
        
        self.device_monitor.apply_config(test_config)
        self.log_display.append("✅ 测试配置已应用")
        
    def update_status_display(self):
        """更新状态显示"""
        status_summary = self.device_monitor.get_device_status_summary()
        status_text = "设备状态:\n"
        for device_type, status in status_summary.items():
            connected = "已连接" if status['connected'] else "未连接"
            status_text += f"  {device_type}: {connected} - {status.get('message', '')}\n"
        
        self.status_display.setText(status_text)
        
    def on_device_status_changed(self, device_type: str, status: str, message: str):
        """设备状态变化处理"""
        self.log_display.append(f"📡 设备状态变化: {device_type} -> {status}: {message}")
        
    def on_connection_attempt(self, device_type: str, message: str):
        """连接尝试处理"""
        self.log_display.append(f"🔄 连接尝试: {device_type} - {message}")
        
    def on_auto_reconnect_started(self, device_type: str):
        """自动重连开始处理"""
        self.log_display.append(f"🔄 开始自动重连: {device_type}")
        
    def on_auto_reconnect_completed(self, device_type: str, success: bool):
        """自动重连完成处理"""
        result = "成功" if success else "失败"
        self.log_display.append(f"✅ 自动重连完成: {device_type} - {result}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
