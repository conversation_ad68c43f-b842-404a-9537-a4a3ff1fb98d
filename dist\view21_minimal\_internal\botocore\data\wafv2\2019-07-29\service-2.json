{"version": "2.0", "metadata": {"apiVersion": "2019-07-29", "endpointPrefix": "wafv2", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "WAFV2", "serviceFullName": "AWS WAFV2", "serviceId": "WAFV2", "signatureVersion": "v4", "targetPrefix": "AWSWAF_20190729", "uid": "wafv2-2019-07-29"}, "operations": {"AssociateWebACL": {"name": "AssociateWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateWebACLRequest"}, "output": {"shape": "AssociateWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Associates a web ACL with a regional application resource, to protect the resource. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>For Amazon CloudFront, don't use this call. Instead, use your CloudFront distribution configuration. To associate a web ACL, in the CloudFront call <code>UpdateDistribution</code>, set the web ACL ID to the Amazon Resource Name (ARN) of the web ACL. For information, see <a href=\"https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_UpdateDistribution.html\">UpdateDistribution</a> in the <i>Amazon CloudFront Developer Guide</i>. </p> <p>When you make changes to web ACLs or web ACL components, like rules and rule groups, WAF propagates the changes everywhere that the web ACL and its components are stored and used. Your changes are applied within seconds, but there might be a brief period of inconsistency when the changes have arrived in some places and not in others. So, for example, if you change a rule action setting, the action might be the old action in one area and the new action in another area. Or if you add an IP address to an IP set used in a blocking rule, the new address might briefly be blocked in one area while still allowed in another. This temporary inconsistency can occur when you first associate a web ACL with an Amazon Web Services resource and when you change a web ACL that is already associated with a resource. Generally, any inconsistencies of this type last only a few seconds.</p>"}, "CheckCapacity": {"name": "CheckCapacity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CheckCapacityRequest"}, "output": {"shape": "CheckCapacityResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFInvalidResourceException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFSubscriptionNotFoundException"}, {"shape": "WAFExpiredManagedRuleGroupVersionException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Returns the web ACL capacity unit (WCU) requirements for a specified scope and set of rules. You can use this to check the capacity requirements for the rules you want to use in a <a>RuleGroup</a> or <a>WebACL</a>. </p> <p>WAF uses WCUs to calculate and control the operating resources that are used to run your rules, rule groups, and web ACLs. WAF calculates capacity differently for each rule type, to reflect the relative cost of each rule. Simple rules that cost little to run use fewer WCUs than more complex rules that use more processing power. Rule group capacity is fixed at creation, which helps users plan their web ACL WCU usage when they use a rule group. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-waf-capacity-units.html\">WAF web ACL capacity units (WCU)</a> in the <i>WAF Developer Guide</i>. </p>"}, "CreateAPIKey": {"name": "CreateAPIKey", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAPIKeyRequest"}, "output": {"shape": "CreateAPIKeyResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFLimitsExceededException"}], "documentation": "<p>Creates an API key that contains a set of token domains.</p> <p>API keys are required for the integration of the CAPTCHA API in your JavaScript client applications. The API lets you customize the placement and characteristics of the CAPTCHA puzzle for your end users. For more information about the CAPTCHA JavaScript integration, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p> <p>You can use a single key for up to 5 domains. After you generate a key, you can copy it for use in your JavaScript integration. </p>"}, "CreateIPSet": {"name": "CreateIPSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateIPSetRequest"}, "output": {"shape": "CreateIPSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Creates an <a>IPSet</a>, which you use to identify web requests that originate from specific IP addresses or ranges of IP addresses. For example, if you're receiving a lot of requests from a ranges of IP addresses, you can configure WAF to block them using an IPSet that lists those IP addresses. </p>"}, "CreateRegexPatternSet": {"name": "CreateRegexPatternSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateRegexPatternSetRequest"}, "output": {"shape": "CreateRegexPatternSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Creates a <a>RegexPatternSet</a>, which you reference in a <a>RegexPatternSetReferenceStatement</a>, to have WAF inspect a web request component for the specified patterns.</p>"}, "CreateRuleGroup": {"name": "CreateRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateRuleGroupRequest"}, "output": {"shape": "CreateRuleGroupResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFSubscriptionNotFoundException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Creates a <a>RuleGroup</a> per the specifications provided. </p> <p> A rule group defines a collection of rules to inspect and control web requests that you can use in a <a>WebACL</a>. When you create a rule group, you define an immutable capacity limit. If you update a rule group, you must stay within the capacity. This allows others to reuse the rule group with confidence in its capacity requirements. </p>"}, "CreateWebACL": {"name": "CreateWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWebACLRequest"}, "output": {"shape": "CreateWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFInvalidResourceException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFSubscriptionNotFoundException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFConfigurationWarningException"}, {"shape": "WAFExpiredManagedRuleGroupVersionException"}], "documentation": "<p>Creates a <a>WebACL</a> per the specifications provided.</p> <p> A web ACL defines a collection of rules to use to inspect and control web requests. Each rule has a statement that defines what to look for in web requests and an action that WAF applies to requests that match the statement. In the web ACL, you assign a default action to take (allow, block) for any request that does not match any of the rules. The rules in a web ACL can be a combination of the types <a>Rule</a>, <a>RuleGroup</a>, and managed rule group. You can associate a web ACL with one or more Amazon Web Services resources to protect. The resources can be an Amazon CloudFront distribution, an Amazon API Gateway REST API, an Application Load Balancer, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p>"}, "DeleteFirewallManagerRuleGroups": {"name": "DeleteFirewallManagerRuleGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFirewallManagerRuleGroupsRequest"}, "output": {"shape": "DeleteFirewallManagerRuleGroupsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Deletes all rule groups that are managed by Firewall Manager for the specified web ACL. </p> <p>You can only use this if <code>ManagedByFirewallManager</code> is false in the specified <a>WebACL</a>. </p>"}, "DeleteIPSet": {"name": "DeleteIPSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIPSetRequest"}, "output": {"shape": "DeleteIPSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFAssociatedItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Deletes the specified <a>IPSet</a>. </p>"}, "DeleteLoggingConfiguration": {"name": "DeleteLoggingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteLoggingConfigurationRequest"}, "output": {"shape": "DeleteLoggingConfigurationResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Deletes the <a>LoggingConfiguration</a> from the specified web ACL.</p>"}, "DeletePermissionPolicy": {"name": "DeletePermissionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePermissionPolicyRequest"}, "output": {"shape": "DeletePermissionPolicyResponse"}, "errors": [{"shape": "WAFNonexistentItemException"}, {"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}], "documentation": "<p>Permanently deletes an IAM policy from the specified rule group.</p> <p>You must be the owner of the rule group to perform this operation.</p>"}, "DeleteRegexPatternSet": {"name": "DeleteRegexPatternSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRegexPatternSetRequest"}, "output": {"shape": "DeleteRegexPatternSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFAssociatedItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Deletes the specified <a>RegexPatternSet</a>.</p>"}, "DeleteRuleGroup": {"name": "DeleteRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRuleGroupRequest"}, "output": {"shape": "DeleteRuleGroupResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFAssociatedItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Deletes the specified <a>RuleGroup</a>.</p>"}, "DeleteWebACL": {"name": "DeleteWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWebACLRequest"}, "output": {"shape": "DeleteWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFAssociatedItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Deletes the specified <a>WebACL</a>. </p> <p>You can only use this if <code>ManagedByFirewallManager</code> is false in the specified <a>WebACL</a>. </p> <note> <p>Before deleting any web ACL, first disassociate it from all resources.</p> <ul> <li> <p>To retrieve a list of the resources that are associated with a web ACL, use the following calls:</p> <ul> <li> <p>For regional resources, call <a>ListResourcesForWebACL</a>.</p> </li> <li> <p>For Amazon CloudFront distributions, use the CloudFront call <code>ListDistributionsByWebACLId</code>. For information, see <a href=\"https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_ListDistributionsByWebACLId.html\">ListDistributionsByWebACLId</a> in the <i>Amazon CloudFront API Reference</i>. </p> </li> </ul> </li> <li> <p>To disassociate a resource from a web ACL, use the following calls:</p> <ul> <li> <p>For regional resources, call <a>DisassociateWebACL</a>.</p> </li> <li> <p>For Amazon CloudFront distributions, provide an empty web ACL ID in the CloudFront call <code>UpdateDistribution</code>. For information, see <a href=\"https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_UpdateDistribution.html\">UpdateDistribution</a> in the <i>Amazon CloudFront API Reference</i>. </p> </li> </ul> </li> </ul> </note>"}, "DescribeAllManagedProducts": {"name": "DescribeAllManagedProducts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAllManagedProductsRequest"}, "output": {"shape": "DescribeAllManagedProductsResponse"}, "errors": [{"shape": "WAFInvalidOperationException"}, {"shape": "WAFInternalErrorException"}], "documentation": "<p>Provides high-level information for the Amazon Web Services Managed Rules rule groups and Amazon Web Services Marketplace managed rule groups. </p>"}, "DescribeManagedProductsByVendor": {"name": "DescribeManagedProductsByVendor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeManagedProductsByVendorRequest"}, "output": {"shape": "DescribeManagedProductsByVendorResponse"}, "errors": [{"shape": "WAFInvalidOperationException"}, {"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}], "documentation": "<p>Provides high-level information for the managed rule groups owned by a specific vendor. </p>"}, "DescribeManagedRuleGroup": {"name": "DescribeManagedRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeManagedRuleGroupRequest"}, "output": {"shape": "DescribeManagedRuleGroupResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidResourceException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFExpiredManagedRuleGroupVersionException"}], "documentation": "<p>Provides high-level information for a managed rule group, including descriptions of the rules. </p>"}, "DisassociateWebACL": {"name": "DisassociateWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateWebACLRequest"}, "output": {"shape": "DisassociateWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Disassociates the specified regional application resource from any existing web ACL association. A resource can have at most one web ACL association. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>For Amazon CloudFront, don't use this call. Instead, use your CloudFront distribution configuration. To disassociate a web ACL, provide an empty web ACL ID in the CloudFront call <code>UpdateDistribution</code>. For information, see <a href=\"https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_UpdateDistribution.html\">UpdateDistribution</a> in the <i>Amazon CloudFront API Reference</i>. </p>"}, "GenerateMobileSdkReleaseUrl": {"name": "GenerateMobileSdkReleaseUrl", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GenerateMobileSdkReleaseUrlRequest"}, "output": {"shape": "GenerateMobileSdkReleaseUrlResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Generates a presigned download URL for the specified release of the mobile SDK.</p> <p>The mobile SDK is not generally available. Customers who have access to the mobile SDK can use it to establish and manage WAF tokens for use in HTTP(S) requests from a mobile device to WAF. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "GetDecryptedAPIKey": {"name": "GetDecryptedAPIKey", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDecryptedAPIKeyRequest"}, "output": {"shape": "GetDecryptedAPIKeyResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFInvalidResourceException"}], "documentation": "<p>Returns your API key in decrypted form. Use this to check the token domains that you have defined for the key. </p> <p>API keys are required for the integration of the CAPTCHA API in your JavaScript client applications. The API lets you customize the placement and characteristics of the CAPTCHA puzzle for your end users. For more information about the CAPTCHA JavaScript integration, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "GetIPSet": {"name": "GetIPSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetIPSetRequest"}, "output": {"shape": "GetIPSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the specified <a>IPSet</a>.</p>"}, "GetLoggingConfiguration": {"name": "GetLoggingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLoggingConfigurationRequest"}, "output": {"shape": "GetLoggingConfigurationResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Returns the <a>LoggingConfiguration</a> for the specified web ACL.</p>"}, "GetManagedRuleSet": {"name": "GetManagedRuleSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetManagedRuleSetRequest"}, "output": {"shape": "GetManagedRuleSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the specified managed rule set. </p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "GetMobileSdkRelease": {"name": "GetMobileSdkRelease", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetMobileSdkReleaseRequest"}, "output": {"shape": "GetMobileSdkReleaseResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves information for the specified mobile SDK release, including release notes and tags.</p> <p>The mobile SDK is not generally available. Customers who have access to the mobile SDK can use it to establish and manage WAF tokens for use in HTTP(S) requests from a mobile device to WAF. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "GetPermissionPolicy": {"name": "GetPermissionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPermissionPolicyRequest"}, "output": {"shape": "GetPermissionPolicyResponse"}, "errors": [{"shape": "WAFNonexistentItemException"}, {"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}], "documentation": "<p>Returns the IAM policy that is attached to the specified rule group.</p> <p>You must be the owner of the rule group to perform this operation.</p>"}, "GetRateBasedStatementManagedKeys": {"name": "GetRateBasedStatementManagedKeys", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRateBasedStatementManagedKeysRequest"}, "output": {"shape": "GetRateBasedStatementManagedKeysResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFUnsupportedAggregateKeyTypeException"}], "documentation": "<p>Retrieves the IP addresses that are currently blocked by a rate-based rule instance. This is only available for rate-based rules that aggregate solely on the IP address or on the forwarded IP address. </p> <p>The maximum number of addresses that can be blocked for a single rate-based rule instance is 10,000. If more than 10,000 addresses exceed the rate limit, those with the highest rates are blocked.</p> <p>For a rate-based rule that you've defined inside a rule group, provide the name of the rule group reference statement in your request, in addition to the rate-based rule name and the web ACL name. </p> <p>WAF monitors web requests and manages keys independently for each unique combination of web ACL, optional rule group, and rate-based rule. For example, if you define a rate-based rule inside a rule group, and then use the rule group in a web ACL, WAF monitors web requests and manages keys for that web ACL, rule group reference statement, and rate-based rule instance. If you use the same rule group in a second web ACL, WAF monitors web requests and manages keys for this second usage completely independent of your first. </p>"}, "GetRegexPatternSet": {"name": "GetRegexPatternSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRegexPatternSetRequest"}, "output": {"shape": "GetRegexPatternSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the specified <a>RegexPatternSet</a>.</p>"}, "GetRuleGroup": {"name": "GetRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRuleGroupRequest"}, "output": {"shape": "GetRuleGroupResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the specified <a>RuleGroup</a>.</p>"}, "GetSampledRequests": {"name": "GetSampledRequests", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSampledRequestsRequest"}, "output": {"shape": "GetSampledRequestsResponse"}, "errors": [{"shape": "WAFNonexistentItemException"}, {"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}], "documentation": "<p>Gets detailed information about a specified number of requests--a sample--that WAF randomly selects from among the first 5,000 requests that your Amazon Web Services resource received during a time range that you choose. You can specify a sample size of up to 500 requests, and you can specify any time range in the previous three hours.</p> <p> <code>GetSampledRequests</code> returns a time range, which is usually the time range that you specified. However, if your resource (such as a CloudFront distribution) received 5,000 requests before the specified time range elapsed, <code>GetSampledRequests</code> returns an updated time range. This new time range indicates the actual period during which WAF selected the requests in the sample.</p>"}, "GetWebACL": {"name": "GetWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetWebACLRequest"}, "output": {"shape": "GetWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the specified <a>WebACL</a>.</p>"}, "GetWebACLForResource": {"name": "GetWebACLForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetWebACLForResourceRequest"}, "output": {"shape": "GetWebACLForResourceResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the <a>WebACL</a> for the specified resource. </p>"}, "ListAPIKeys": {"name": "ListAPIKeys", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAPIKeysRequest"}, "output": {"shape": "ListAPIKeysResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFInvalidResourceException"}], "documentation": "<p>Retrieves a list of the API keys that you've defined for the specified scope. </p> <p>API keys are required for the integration of the CAPTCHA API in your JavaScript client applications. The API lets you customize the placement and characteristics of the CAPTCHA puzzle for your end users. For more information about the CAPTCHA JavaScript integration, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "ListAvailableManagedRuleGroupVersions": {"name": "ListAvailableManagedRuleGroupVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailableManagedRuleGroupVersionsRequest"}, "output": {"shape": "ListAvailableManagedRuleGroupVersionsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Returns a list of the available versions for the specified managed rule group. </p>"}, "ListAvailableManagedRuleGroups": {"name": "ListAvailableManagedRuleGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailableManagedRuleGroupsRequest"}, "output": {"shape": "ListAvailableManagedRuleGroupsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of managed rule groups that are available for you to use. This list includes all Amazon Web Services Managed Rules rule groups and all of the Amazon Web Services Marketplace managed rule groups that you're subscribed to.</p>"}, "ListIPSets": {"name": "ListIPSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListIPSetsRequest"}, "output": {"shape": "ListIPSetsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of <a>IPSetSummary</a> objects for the IP sets that you manage.</p>"}, "ListLoggingConfigurations": {"name": "ListLoggingConfigurations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListLoggingConfigurationsRequest"}, "output": {"shape": "ListLoggingConfigurationsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of your <a>LoggingConfiguration</a> objects.</p>"}, "ListManagedRuleSets": {"name": "ListManagedRuleSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListManagedRuleSetsRequest"}, "output": {"shape": "ListManagedRuleSetsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the managed rule sets that you own. </p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "ListMobileSdkReleases": {"name": "ListMobileSdkReleases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMobileSdkReleasesRequest"}, "output": {"shape": "ListMobileSdkReleasesResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves a list of the available releases for the mobile SDK and the specified device platform. </p> <p>The mobile SDK is not generally available. Customers who have access to the mobile SDK can use it to establish and manage WAF tokens for use in HTTP(S) requests from a mobile device to WAF. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "ListRegexPatternSets": {"name": "ListRegexPatternSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRegexPatternSetsRequest"}, "output": {"shape": "ListRegexPatternSetsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of <a>RegexPatternSetSummary</a> objects for the regex pattern sets that you manage.</p>"}, "ListResourcesForWebACL": {"name": "ListResourcesForWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourcesForWebACLRequest"}, "output": {"shape": "ListResourcesForWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of the Amazon Resource Names (ARNs) for the regional resources that are associated with the specified web ACL. If you want the list of Amazon CloudFront resources, use the CloudFront call <code>ListDistributionsByWebACLId</code>. </p>"}, "ListRuleGroups": {"name": "ListRuleGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRuleGroupsRequest"}, "output": {"shape": "ListRuleGroupsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of <a>RuleGroupSummary</a> objects for the rule groups that you manage. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves the <a>TagInfoForResource</a> for the specified resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing. For example, you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can tag the Amazon Web Services resources that you manage through WAF: web ACLs, rule groups, IP sets, and regex pattern sets. You can't manage or view tags through the WAF console. </p>"}, "ListWebACLs": {"name": "ListWebACLs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWebACLsRequest"}, "output": {"shape": "ListWebACLsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Retrieves an array of <a>WebACLSummary</a> objects for the web ACLs that you manage.</p>"}, "PutLoggingConfiguration": {"name": "PutLoggingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutLoggingConfigurationRequest"}, "output": {"shape": "PutLoggingConfigurationResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFServiceLinkedRoleErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFLogDestinationPermissionIssueException"}], "documentation": "<p>Enables the specified <a>LoggingConfiguration</a>, to start logging from a web ACL, according to the configuration provided. </p> <note> <p>This operation completely replaces any mutable specifications that you already have for a logging configuration with the ones that you provide to this call. </p> <p>To modify an existing logging configuration, do the following: </p> <ol> <li> <p>Retrieve it by calling <a>GetLoggingConfiguration</a> </p> </li> <li> <p>Update its settings as needed</p> </li> <li> <p>Provide the complete logging configuration specification to this call</p> </li> </ol> </note> <note> <p>You can define one logging destination per web ACL.</p> </note> <p>You can access information about the traffic that WAF inspects using the following steps:</p> <ol> <li> <p>Create your logging destination. You can use an Amazon CloudWatch Logs log group, an Amazon Simple Storage Service (Amazon S3) bucket, or an Amazon Kinesis Data Firehose. </p> <p>The name that you give the destination must start with <code>aws-waf-logs-</code>. Depending on the type of destination, you might need to configure additional settings or permissions. </p> <p>For configuration requirements and pricing information for each destination type, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging.html\">Logging web ACL traffic</a> in the <i>WAF Developer Guide</i>.</p> </li> <li> <p>Associate your logging destination to your web ACL using a <code>PutLoggingConfiguration</code> request.</p> </li> </ol> <p>When you successfully enable logging using a <code>PutLoggingConfiguration</code> request, WAF creates an additional role or policy that is required to write logs to the logging destination. For an Amazon CloudWatch Logs log group, WAF creates a resource policy on the log group. For an Amazon S3 bucket, WAF creates a bucket policy. For an Amazon Kinesis Data Firehose, WAF creates a service-linked role.</p> <p>For additional information about web ACL logging, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging.html\">Logging web ACL traffic information</a> in the <i>WAF Developer Guide</i>.</p>"}, "PutManagedRuleSetVersions": {"name": "PutManagedRuleSetVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutManagedRuleSetVersionsRequest"}, "output": {"shape": "PutManagedRuleSetVersionsResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Defines the versions of your managed rule set that you are offering to the customers. Customers see your offerings as managed rule groups with versioning.</p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note> <p>Customers retrieve their managed rule group list by calling <a>ListAvailableManagedRuleGroups</a>. The name that you provide here for your managed rule set is the name the customer sees for the corresponding managed rule group. Customers can retrieve the available versions for a managed rule group by calling <a>ListAvailableManagedRuleGroupVersions</a>. You provide a rule group specification for each version. For each managed rule set, you must specify a version that you recommend using. </p> <p>To initiate the expiration of a managed rule group version, use <a>UpdateManagedRuleSetVersionExpiryDate</a>.</p>"}, "PutPermissionPolicy": {"name": "PutPermissionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPermissionPolicyRequest"}, "output": {"shape": "PutPermissionPolicyResponse"}, "errors": [{"shape": "WAFNonexistentItemException"}, {"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFInvalidPermissionPolicyException"}], "documentation": "<p>Attaches an IAM policy to the specified resource. Use this to share a rule group across accounts.</p> <p>You must be the owner of the rule group to perform this operation.</p> <p>This action is subject to the following restrictions:</p> <ul> <li> <p>You can attach only one policy with each <code>PutPermissionPolicy</code> request.</p> </li> <li> <p>The ARN in the request must be a valid WAF <a>RuleGroup</a> ARN and the rule group must exist in the same Region.</p> </li> <li> <p>The user making the request must be the owner of the rule group.</p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Associates tags with the specified Amazon Web Services resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing. For example, you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can tag the Amazon Web Services resources that you manage through WAF: web ACLs, rule groups, IP sets, and regex pattern sets. You can't manage or view tags through the WAF console. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFTagOperationException"}, {"shape": "WAFTagOperationInternalErrorException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Disassociates tags from an Amazon Web Services resource. Tags are key:value pairs that you can associate with Amazon Web Services resources. For example, the tag key might be \"customer\" and the tag value might be \"companyA.\" You can specify one or more tags to add to each container. You can add up to 50 tags to each Amazon Web Services resource.</p>"}, "UpdateIPSet": {"name": "UpdateIPSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateIPSetRequest"}, "output": {"shape": "UpdateIPSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Updates the specified <a>IPSet</a>. </p> <note> <p>This operation completely replaces the mutable specifications that you already have for the IP set with the ones that you provide to this call. </p> <p>To modify an IP set, do the following: </p> <ol> <li> <p>Retrieve it by calling <a>GetIPSet</a> </p> </li> <li> <p>Update its settings as needed</p> </li> <li> <p>Provide the complete IP set specification to this call</p> </li> </ol> </note> <p>When you make changes to web ACLs or web ACL components, like rules and rule groups, WAF propagates the changes everywhere that the web ACL and its components are stored and used. Your changes are applied within seconds, but there might be a brief period of inconsistency when the changes have arrived in some places and not in others. So, for example, if you change a rule action setting, the action might be the old action in one area and the new action in another area. Or if you add an IP address to an IP set used in a blocking rule, the new address might briefly be blocked in one area while still allowed in another. This temporary inconsistency can occur when you first associate a web ACL with an Amazon Web Services resource and when you change a web ACL that is already associated with a resource. Generally, any inconsistencies of this type last only a few seconds.</p>"}, "UpdateManagedRuleSetVersionExpiryDate": {"name": "UpdateManagedRuleSetVersionExpiryDate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateManagedRuleSetVersionExpiryDateRequest"}, "output": {"shape": "UpdateManagedRuleSetVersionExpiryDateResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Updates the expiration information for your managed rule set. Use this to initiate the expiration of a managed rule group version. After you initiate expiration for a version, WAF excludes it from the response to <a>ListAvailableManagedRuleGroupVersions</a> for the managed rule group. </p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "UpdateRegexPatternSet": {"name": "UpdateRegexPatternSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateRegexPatternSetRequest"}, "output": {"shape": "UpdateRegexPatternSetResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFInvalidOperationException"}], "documentation": "<p>Updates the specified <a>RegexPatternSet</a>.</p> <note> <p>This operation completely replaces the mutable specifications that you already have for the regex pattern set with the ones that you provide to this call. </p> <p>To modify a regex pattern set, do the following: </p> <ol> <li> <p>Retrieve it by calling <a>GetRegexPatternSet</a> </p> </li> <li> <p>Update its settings as needed</p> </li> <li> <p>Provide the complete regex pattern set specification to this call</p> </li> </ol> </note> <p>When you make changes to web ACLs or web ACL components, like rules and rule groups, WAF propagates the changes everywhere that the web ACL and its components are stored and used. Your changes are applied within seconds, but there might be a brief period of inconsistency when the changes have arrived in some places and not in others. So, for example, if you change a rule action setting, the action might be the old action in one area and the new action in another area. Or if you add an IP address to an IP set used in a blocking rule, the new address might briefly be blocked in one area while still allowed in another. This temporary inconsistency can occur when you first associate a web ACL with an Amazon Web Services resource and when you change a web ACL that is already associated with a resource. Generally, any inconsistencies of this type last only a few seconds.</p>"}, "UpdateRuleGroup": {"name": "UpdateRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateRuleGroupRequest"}, "output": {"shape": "UpdateRuleGroupResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFSubscriptionNotFoundException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFConfigurationWarningException"}], "documentation": "<p>Updates the specified <a>RuleGroup</a>.</p> <note> <p>This operation completely replaces the mutable specifications that you already have for the rule group with the ones that you provide to this call. </p> <p>To modify a rule group, do the following: </p> <ol> <li> <p>Retrieve it by calling <a>GetRuleGroup</a> </p> </li> <li> <p>Update its settings as needed</p> </li> <li> <p>Provide the complete rule group specification to this call</p> </li> </ol> </note> <p>When you make changes to web ACLs or web ACL components, like rules and rule groups, WAF propagates the changes everywhere that the web ACL and its components are stored and used. Your changes are applied within seconds, but there might be a brief period of inconsistency when the changes have arrived in some places and not in others. So, for example, if you change a rule action setting, the action might be the old action in one area and the new action in another area. Or if you add an IP address to an IP set used in a blocking rule, the new address might briefly be blocked in one area while still allowed in another. This temporary inconsistency can occur when you first associate a web ACL with an Amazon Web Services resource and when you change a web ACL that is already associated with a resource. Generally, any inconsistencies of this type last only a few seconds.</p> <p> A rule group defines a collection of rules to inspect and control web requests that you can use in a <a>WebACL</a>. When you create a rule group, you define an immutable capacity limit. If you update a rule group, you must stay within the capacity. This allows others to reuse the rule group with confidence in its capacity requirements. </p>"}, "UpdateWebACL": {"name": "UpdateWebACL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateWebACLRequest"}, "output": {"shape": "UpdateWebACLResponse"}, "errors": [{"shape": "WAFInternalErrorException"}, {"shape": "WAFInvalidParameterException"}, {"shape": "WAFNonexistentItemException"}, {"shape": "WAFDuplicateItemException"}, {"shape": "WAFOptimisticLockException"}, {"shape": "WAFLimitsExceededException"}, {"shape": "WAFInvalidResourceException"}, {"shape": "WAFUnavailableEntityException"}, {"shape": "WAFSubscriptionNotFoundException"}, {"shape": "WAFInvalidOperationException"}, {"shape": "WAFExpiredManagedRuleGroupVersionException"}, {"shape": "WAFConfigurationWarningException"}], "documentation": "<p>Updates the specified <a>WebACL</a>. While updating a web ACL, WAF provides continuous coverage to the resources that you have associated with the web ACL. </p> <note> <p>This operation completely replaces the mutable specifications that you already have for the web ACL with the ones that you provide to this call. </p> <p>To modify a web ACL, do the following: </p> <ol> <li> <p>Retrieve it by calling <a>GetWebACL</a> </p> </li> <li> <p>Update its settings as needed</p> </li> <li> <p>Provide the complete web ACL specification to this call</p> </li> </ol> </note> <p>When you make changes to web ACLs or web ACL components, like rules and rule groups, WAF propagates the changes everywhere that the web ACL and its components are stored and used. Your changes are applied within seconds, but there might be a brief period of inconsistency when the changes have arrived in some places and not in others. So, for example, if you change a rule action setting, the action might be the old action in one area and the new action in another area. Or if you add an IP address to an IP set used in a blocking rule, the new address might briefly be blocked in one area while still allowed in another. This temporary inconsistency can occur when you first associate a web ACL with an Amazon Web Services resource and when you change a web ACL that is already associated with a resource. Generally, any inconsistencies of this type last only a few seconds.</p> <p> A web ACL defines a collection of rules to use to inspect and control web requests. Each rule has a statement that defines what to look for in web requests and an action that WAF applies to requests that match the statement. In the web ACL, you assign a default action to take (allow, block) for any request that does not match any of the rules. The rules in a web ACL can be a combination of the types <a>Rule</a>, <a>RuleGroup</a>, and managed rule group. You can associate a web ACL with one or more Amazon Web Services resources to protect. The resources can be an Amazon CloudFront distribution, an Amazon API Gateway REST API, an Application Load Balancer, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p>"}}, "shapes": {"APIKey": {"type": "string", "max": 2048, "min": 1, "pattern": ".*\\S.*"}, "APIKeySummaries": {"type": "list", "member": {"shape": "APIKeySummary"}}, "APIKeySummary": {"type": "structure", "members": {"TokenDomains": {"shape": "TokenDomains", "documentation": "<p>The token domains that are defined in this API key. </p>"}, "APIKey": {"shape": "APIKey", "documentation": "<p>The generated, encrypted API key. You can copy this for use in your JavaScript CAPTCHA integration. </p>"}, "CreationTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the key was created. </p>"}, "Version": {"shape": "APIKeyVersion", "documentation": "<p>Internal value used by WAF to manage the key. </p>"}}, "documentation": "<p>Information for a single API key. </p> <p>API keys are required for the integration of the CAPTCHA API in your JavaScript client applications. The API lets you customize the placement and characteristics of the CAPTCHA puzzle for your end users. For more information about the CAPTCHA JavaScript integration, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "APIKeyTokenDomains": {"type": "list", "member": {"shape": "TokenDomain"}, "min": 1}, "APIKeyVersion": {"type": "integer", "min": 0}, "AWSManagedRulesACFPRuleSet": {"type": "structure", "required": ["CreationPath", "RegistrationPagePath", "RequestInspection"], "members": {"CreationPath": {"shape": "CreationPathString", "documentation": "<p>The path of the account creation endpoint for your application. This is the page on your website that accepts the completed registration form for a new user. This page must accept <code>POST</code> requests.</p> <p>For example, for the URL <code>https://example.com/web/newaccount</code>, you would provide the path <code>/web/newaccount</code>. Account creation page paths that start with the path that you provide are considered a match. For example <code>/web/newaccount</code> matches the account creation paths <code>/web/newaccount</code>, <code>/web/newaccount/</code>, <code>/web/newaccountPage</code>, and <code>/web/newaccount/thisPage</code>, but doesn't match the path <code>/home/<USER>/newaccount</code> or <code>/website/newaccount</code>. </p>"}, "RegistrationPagePath": {"shape": "RegistrationPagePathString", "documentation": "<p>The path of the account registration endpoint for your application. This is the page on your website that presents the registration form to new users. </p> <note> <p>This page must accept <code>GET</code> text/html requests.</p> </note> <p>For example, for the URL <code>https://example.com/web/registration</code>, you would provide the path <code>/web/registration</code>. Registration page paths that start with the path that you provide are considered a match. For example <code>/web/registration</code> matches the registration paths <code>/web/registration</code>, <code>/web/registration/</code>, <code>/web/registrationPage</code>, and <code>/web/registration/thisPage</code>, but doesn't match the path <code>/home/<USER>/registration</code> or <code>/website/registration</code>. </p>"}, "RequestInspection": {"shape": "RequestInspectionACFP", "documentation": "<p>The criteria for inspecting account creation requests, used by the ACFP rule group to validate and track account creation attempts. </p>"}, "ResponseInspection": {"shape": "ResponseInspection", "documentation": "<p>The criteria for inspecting responses to account creation requests, used by the ACFP rule group to track account creation success rates. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note> <p>The ACFP rule group evaluates the responses that your protected resources send back to client account creation attempts, keeping count of successful and failed attempts from each IP address and client session. Using this information, the rule group labels and mitigates requests from client sessions and IP addresses that have had too many successful account creation attempts in a short amount of time. </p>"}, "EnableRegexInPath": {"shape": "Boolean", "documentation": "<p>Allow the use of regular expressions in the registration page path and the account creation path. </p>"}}, "documentation": "<p>Details for your use of the account creation fraud prevention managed rule group, <code>AWSManagedRulesACFPRuleSet</code>. This configuration is used in <code>ManagedRuleGroupConfig</code>. </p>"}, "AWSManagedRulesATPRuleSet": {"type": "structure", "required": ["<PERSON>gin<PERSON><PERSON>"], "members": {"LoginPath": {"shape": "String", "documentation": "<p>The path of the login endpoint for your application. For example, for the URL <code>https://example.com/web/login</code>, you would provide the path <code>/web/login</code>. Login paths that start with the path that you provide are considered a match. For example <code>/web/login</code> matches the login paths <code>/web/login</code>, <code>/web/login/</code>, <code>/web/loginPage</code>, and <code>/web/login/thisPage</code>, but doesn't match the login path <code>/home/<USER>/login</code> or <code>/website/login</code>.</p> <p>The rule group inspects only HTTP <code>POST</code> requests to your specified login endpoint.</p>"}, "RequestInspection": {"shape": "RequestInspection", "documentation": "<p>The criteria for inspecting login requests, used by the ATP rule group to validate credentials usage. </p>"}, "ResponseInspection": {"shape": "ResponseInspection", "documentation": "<p>The criteria for inspecting responses to login requests, used by the ATP rule group to track login failure rates. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note> <p>The ATP rule group evaluates the responses that your protected resources send back to client login attempts, keeping count of successful and failed attempts for each IP address and client session. Using this information, the rule group labels and mitigates requests from client sessions and IP addresses that have had too many failed login attempts in a short amount of time. </p>"}, "EnableRegexInPath": {"shape": "Boolean", "documentation": "<p>Allow the use of regular expressions in the login page path. </p>"}}, "documentation": "<p>Details for your use of the account takeover prevention managed rule group, <code>AWSManagedRulesATPRuleSet</code>. This configuration is used in <code>ManagedRuleGroupConfig</code>. </p>"}, "AWSManagedRulesBotControlRuleSet": {"type": "structure", "required": ["InspectionLevel"], "members": {"InspectionLevel": {"shape": "InspectionLevel", "documentation": "<p>The inspection level to use for the Bot Control rule group. The common level is the least expensive. The targeted level includes all common level rules and adds rules with more advanced inspection criteria. For details, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-managed-rule-groups-bot.html\">WAF Bot Control rule group</a> in the <i>WAF Developer Guide</i>.</p>"}, "EnableMachineLearning": {"shape": "Boolean", "documentation": "<p>Applies only to the targeted inspection level. </p> <p>Determines whether to use machine learning (ML) to analyze your web traffic for bot-related activity. Machine learning is required for the Bot Control rules <code>TGT_ML_CoordinatedActivityLow</code> and <code>TGT_ML_CoordinatedActivityMedium</code>, which inspect for anomalous behavior that might indicate distributed, coordinated bot activity.</p> <p>For more information about this choice, see the listing for these rules in the table at <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-managed-rule-groups-bot.html#aws-managed-rule-groups-bot-rules\">Bot Control rules listing</a> in the <i>WAF Developer Guide</i>.</p> <p>Default: <code>TRUE</code> </p>"}}, "documentation": "<p>Details for your use of the Bot Control managed rule group, <code>AWSManagedRulesBotControlRuleSet</code>. This configuration is used in <code>ManagedRuleGroupConfig</code>. </p>"}, "Action": {"type": "string"}, "ActionCondition": {"type": "structure", "required": ["Action"], "members": {"Action": {"shape": "ActionValue", "documentation": "<p>The action setting that a log record must contain in order to meet the condition. This is the action that WAF applied to the web request. </p> <p>For rule groups, this is either the configured rule action setting, or if you've applied a rule action override to the rule, it's the override action. The value <code>EXCLUDED_AS_COUNT</code> matches on excluded rules and also on rules that have a rule action override of Count. </p>"}}, "documentation": "<p>A single action condition for a <a>Condition</a> in a logging filter.</p>"}, "ActionValue": {"type": "string", "enum": ["ALLOW", "BLOCK", "COUNT", "CAPTCHA", "CHALLENGE", "EXCLUDED_AS_COUNT"]}, "AddressField": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "FieldIdentifier", "documentation": "<p>The name of a single primary address field. </p> <p>How you specify the address fields depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field identifiers in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"primaryaddressline1\": \"THE_ADDRESS1\", \"primaryaddressline2\": \"THE_ADDRESS2\", \"primaryaddressline3\": \"THE_ADDRESS3\" } }</code>, the address field idenfiers are <code>/form/primaryaddressline1</code>, <code>/form/primaryaddressline2</code>, and <code>/form/primaryaddressline3</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with input elements named <code>primaryaddressline1</code>, <code>primaryaddressline2</code>, and <code>primaryaddressline3</code>, the address fields identifiers are <code>primaryaddressline1</code>, <code>primaryaddressline2</code>, and <code>primaryaddressline3</code>. </p> </li> </ul>"}}, "documentation": "<p>The name of a field in the request payload that contains part or all of your customer's primary physical address. </p> <p>This data type is used in the <code>RequestInspectionACFP</code> data type. </p>"}, "AddressFields": {"type": "list", "member": {"shape": "AddressField"}}, "All": {"type": "structure", "members": {}, "documentation": "<p>Inspect all of the elements that WAF has parsed and extracted from the web request component that you've identified in your <a>FieldToMatch</a> specifications. </p> <p>This is used in the <a>FieldToMatch</a> specification for some web request component types. </p> <p>JSON specification: <code>\"All\": {}</code> </p>"}, "AllQueryArguments": {"type": "structure", "members": {}, "documentation": "<p>Inspect all query arguments of the web request. </p> <p>This is used in the <a>FieldToMatch</a> specification for some web request component types. </p> <p>JSON specification: <code>\"AllQueryArguments\": {}</code> </p>"}, "AllowAction": {"type": "structure", "members": {"CustomRequestHandling": {"shape": "CustomRequestHandling", "documentation": "<p>Defines custom handling for the web request.</p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>Specifies that WAF should allow the request and optionally defines additional custom handling for the request.</p> <p>This is used in the context of other settings, for example to specify values for <a>RuleAction</a> and web ACL <a>DefaultAction</a>. </p>"}, "AndStatement": {"type": "structure", "required": ["Statements"], "members": {"Statements": {"shape": "Statements", "documentation": "<p>The statements to combine with AND logic. You can use any statements that can be nested. </p>"}}, "documentation": "<p>A logical rule statement used to combine other rule statements with AND logic. You provide more than one <a>Statement</a> within the <code>AndStatement</code>. </p>"}, "AssociateWebACLRequest": {"type": "structure", "required": ["WebACLArn", "ResourceArn"], "members": {"WebACLArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL that you want to associate with the resource.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to associate with the web ACL. </p> <p>The ARN must be in one of the following formats:</p> <ul> <li> <p>For an Application Load Balancer: <code>arn:<i>partition</i>:elasticloadbalancing:<i>region</i>:<i>account-id</i>:loadbalancer/app/<i>load-balancer-name</i>/<i>load-balancer-id</i> </code> </p> </li> <li> <p>For an Amazon API Gateway REST API: <code>arn:<i>partition</i>:apigateway:<i>region</i>::/restapis/<i>api-id</i>/stages/<i>stage-name</i> </code> </p> </li> <li> <p>For an AppSync GraphQL API: <code>arn:<i>partition</i>:appsync:<i>region</i>:<i>account-id</i>:apis/<i>GraphQLApiId</i> </code> </p> </li> <li> <p>For an Amazon Cognito user pool: <code>arn:<i>partition</i>:cognito-idp:<i>region</i>:<i>account-id</i>:userpool/<i>user-pool-id</i> </code> </p> </li> <li> <p>For an App Runner service: <code>arn:<i>partition</i>:apprunner:<i>region</i>:<i>account-id</i>:service/<i>apprunner-service-name</i>/<i>apprunner-service-id</i> </code> </p> </li> <li> <p>For an Amazon Web Services Verified Access instance: <code>arn:<i>partition</i>:ec2:<i>region</i>:<i>account-id</i>:verified-access-instance/<i>instance-id</i> </code> </p> </li> </ul>"}}}, "AssociateWebACLResponse": {"type": "structure", "members": {}}, "AssociatedResourceType": {"type": "string", "enum": ["CLOUDFRONT"]}, "AssociationConfig": {"type": "structure", "members": {"RequestBody": {"shape": "RequestBody", "documentation": "<p>Customizes the maximum size of the request body that your protected CloudFront distributions forward to WAF for inspection. The default size is 16 KB (16,384 bytes). </p> <note> <p>You are charged additional fees when your protected resources forward body sizes that are larger than the default. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}}, "documentation": "<p>Specifies custom configurations for the associations between the web ACL and protected resources. </p> <p>Use this to customize the maximum size of the request body that your protected CloudFront distributions forward to WAF for inspection. The default is 16 KB (16,384 bytes). </p> <note> <p>You are charged additional fees when your protected resources forward body sizes that are larger than the default. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}, "BlockAction": {"type": "structure", "members": {"CustomResponse": {"shape": "CustomResponse", "documentation": "<p>Defines a custom response for the web request.</p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>Specifies that WAF should block the request and optionally defines additional custom handling for the response to the web request.</p> <p>This is used in the context of other settings, for example to specify values for <a>RuleAction</a> and web ACL <a>DefaultAction</a>. </p>"}, "Body": {"type": "structure", "members": {"OversizeHandling": {"shape": "OversizeHandling", "documentation": "<p>What WAF should do if the body is larger than WAF can inspect. WAF does not support inspecting the entire contents of the web request body if the body exceeds the limit for the resource type. If the body is larger than the limit, the underlying host service only forwards the contents that are below the limit to WAF for inspection. </p> <p>The default limit is 8 KB (8,192 bytes) for regional resources and 16 KB (16,384 bytes) for CloudFront distributions. For CloudFront distributions, you can increase the limit in the web ACL <code>AssociationConfig</code>, for additional processing fees. </p> <p>The options for oversize handling are the following:</p> <ul> <li> <p> <code>CONTINUE</code> - Inspect the available body contents normally, according to the rule inspection criteria. </p> </li> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul> <p>You can combine the <code>MATCH</code> or <code>NO_MATCH</code> settings for oversize handling with your rule and web ACL action settings, so that you block any request whose body is over the limit. </p> <p>Default: <code>CONTINUE</code> </p>"}}, "documentation": "<p>Inspect the body of the web request. The body immediately follows the request headers.</p> <p>This is used to indicate the web request component to inspect, in the <a>FieldToMatch</a> specification. </p>"}, "BodyParsingFallbackBehavior": {"type": "string", "enum": ["MATCH", "NO_MATCH", "EVALUATE_AS_STRING"]}, "Boolean": {"type": "boolean"}, "ByteMatchStatement": {"type": "structure", "required": ["SearchString", "FieldToMatch", "TextTransformations", "PositionalConstraint"], "members": {"SearchString": {"shape": "SearchString", "documentation": "<p>A string value that you want WAF to search for. WAF searches only in the part of web requests that you designate for inspection in <a>FieldToMatch</a>. The maximum length of the value is 200 bytes.</p> <p>Valid values depend on the component that you specify for inspection in <code>FieldToMatch</code>:</p> <ul> <li> <p> <code>Method</code>: The HTTP method that you want WAF to search for. This indicates the type of operation specified in the request. </p> </li> <li> <p> <code>UriPath</code>: The value that you want WAF to search for in the URI path, for example, <code>/images/daily-ad.jpg</code>. </p> </li> <li> <p> <code>JA3Fingerprint</code>: Match against the request's JA3 fingerprint. The JA3 fingerprint is a 32-character hash derived from the TLS Client Hello of an incoming request. This fingerprint serves as a unique identifier for the client's TLS configuration. You can use this choice only with a string match <code>ByteMatchStatement</code> with the <code>PositionalConstraint</code> set to <code>EXACTLY</code>. </p> <p>You can obtain the JA3 fingerprint for client requests from the web ACL logs. If WAF is able to calculate the fingerprint, it includes it in the logs. For information about the logging fields, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging-fields.html\">Log fields</a> in the <i>WAF Developer Guide</i>. </p> </li> <li> <p> <code>HeaderOrder</code>: The comma-separated list of header names to match for. WAF creates a string that contains the ordered list of header names, from the headers in the web request, and then matches against that string. </p> </li> </ul> <p>If <code>SearchString</code> includes alphabetic characters A-Z and a-z, note that the value is case sensitive.</p> <p> <b>If you're using the WAF API</b> </p> <p>Specify a base64-encoded version of the value. The maximum length of the value before you base64-encode it is 200 bytes.</p> <p>For example, suppose the value of <code>Type</code> is <code>HEADER</code> and the value of <code>Data</code> is <code>User-Agent</code>. If you want to search the <code>User-Agent</code> header for the value <code>BadBot</code>, you base64-encode <code>BadBot</code> using MIME base64-encoding and include the resulting value, <code>QmFkQm90</code>, in the value of <code>SearchString</code>.</p> <p> <b>If you're using the CLI or one of the Amazon Web Services SDKs</b> </p> <p>The value that you want WAF to search for. The SDK automatically base64 encodes the value.</p>"}, "FieldToMatch": {"shape": "FieldToMatch", "documentation": "<p>The part of the web request that you want WAF to inspect. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}, "PositionalConstraint": {"shape": "PositionalConstraint", "documentation": "<p>The area within the portion of the web request that you want WAF to search for <code>SearchString</code>. Valid values include the following:</p> <p> <b>CONTAINS</b> </p> <p>The specified part of the web request must include the value of <code>SearchString</code>, but the location doesn't matter.</p> <p> <b>CONTAINS_WORD</b> </p> <p>The specified part of the web request must include the value of <code>SearchString</code>, and <code>SearchString</code> must contain only alphanumeric characters or underscore (A-Z, a-z, 0-9, or _). In addition, <code>SearchString</code> must be a word, which means that both of the following are true:</p> <ul> <li> <p> <code>SearchString</code> is at the beginning of the specified part of the web request or is preceded by a character other than an alphanumeric character or underscore (_). Examples include the value of a header and <code>;BadBot</code>.</p> </li> <li> <p> <code>SearchString</code> is at the end of the specified part of the web request or is followed by a character other than an alphanumeric character or underscore (_), for example, <code>BadBot;</code> and <code>-BadBot;</code>.</p> </li> </ul> <p> <b>EXACTLY</b> </p> <p>The value of the specified part of the web request must exactly match the value of <code>SearchString</code>.</p> <p> <b>STARTS_WITH</b> </p> <p>The value of <code>SearchString</code> must appear at the beginning of the specified part of the web request.</p> <p> <b>ENDS_WITH</b> </p> <p>The value of <code>SearchString</code> must appear at the end of the specified part of the web request.</p>"}}, "documentation": "<p>A rule statement that defines a string match search for WAF to apply to web requests. The byte match statement provides the bytes to search for, the location in requests that you want WAF to search, and other settings. The bytes to search for are typically a string that corresponds with ASCII characters. In the WAF console and the developer guide, this is called a string match statement.</p>"}, "CapacityUnit": {"type": "long", "min": 1}, "CaptchaAction": {"type": "structure", "members": {"CustomRequestHandling": {"shape": "CustomRequestHandling", "documentation": "<p>Defines custom handling for the web request, used when the <code>CAPTCHA</code> inspection determines that the request's token is valid and unexpired.</p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>Specifies that WAF should run a <code>CAPTCHA</code> check against the request: </p> <ul> <li> <p>If the request includes a valid, unexpired <code>CAPTCHA</code> token, WAF applies any custom request handling and labels that you've configured and then allows the web request inspection to proceed to the next rule, similar to a <code>CountAction</code>. </p> </li> <li> <p>If the request doesn't include a valid, unexpired token, WAF discontinues the web ACL evaluation of the request and blocks it from going to its intended destination.</p> <p>WAF generates a response that it sends back to the client, which includes the following: </p> <ul> <li> <p>The header <code>x-amzn-waf-action</code> with a value of <code>captcha</code>. </p> </li> <li> <p>The HTTP status code <code>405 Method Not Allowed</code>. </p> </li> <li> <p>If the request contains an <code>Accept</code> header with a value of <code>text/html</code>, the response includes a <code>CAPTCHA</code> JavaScript page interstitial. </p> </li> </ul> </li> </ul> <p>You can configure the expiration time in the <code>CaptchaConfig</code> <code>ImmunityTimeProperty</code> setting at the rule and web ACL level. The rule setting overrides the web ACL setting. </p> <p>This action option is available for rules. It isn't available for web ACL default actions. </p>"}, "CaptchaConfig": {"type": "structure", "members": {"ImmunityTimeProperty": {"shape": "ImmunityTimeProperty", "documentation": "<p>Determines how long a <code>CAPTCHA</code> timestamp in the token remains valid after the client successfully solves a <code>CAPTCHA</code> puzzle. </p>"}}, "documentation": "<p>Specifies how WAF should handle <code>CAPTCHA</code> evaluations. This is available at the web ACL level and in each rule. </p>"}, "CaptchaResponse": {"type": "structure", "members": {"ResponseCode": {"shape": "ResponseCode", "documentation": "<p>The HTTP response code indicating the status of the <code>CAPTCHA</code> token in the web request. If the token is missing, invalid, or expired, this code is <code>405 Method Not Allowed</code>.</p>"}, "SolveTimestamp": {"shape": "SolveTimestamp", "documentation": "<p>The time that the <code>CAPTCHA</code> was last solved for the supplied token. </p>"}, "FailureReason": {"shape": "FailureReason", "documentation": "<p>The reason for failure, populated when the evaluation of the token fails.</p>"}}, "documentation": "<p>The result from the inspection of the web request for a valid <code>CAPTCHA</code> token. </p>"}, "ChallengeAction": {"type": "structure", "members": {"CustomRequestHandling": {"shape": "CustomRequestHandling", "documentation": "<p>Defines custom handling for the web request, used when the challenge inspection determines that the request's token is valid and unexpired.</p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>Specifies that WAF should run a <code>Challenge</code> check against the request to verify that the request is coming from a legitimate client session: </p> <ul> <li> <p>If the request includes a valid, unexpired challenge token, WAF applies any custom request handling and labels that you've configured and then allows the web request inspection to proceed to the next rule, similar to a <code>CountAction</code>. </p> </li> <li> <p>If the request doesn't include a valid, unexpired challenge token, WAF discontinues the web ACL evaluation of the request and blocks it from going to its intended destination.</p> <p>WAF then generates a challenge response that it sends back to the client, which includes the following: </p> <ul> <li> <p>The header <code>x-amzn-waf-action</code> with a value of <code>challenge</code>. </p> </li> <li> <p>The HTTP status code <code>202 Request Accepted</code>. </p> </li> <li> <p>If the request contains an <code>Accept</code> header with a value of <code>text/html</code>, the response includes a JavaScript page interstitial with a challenge script. </p> </li> </ul> <p>Challenges run silent browser interrogations in the background, and don't generally affect the end user experience. </p> <p>A challenge enforces token acquisition using an interstitial JavaScript challenge that inspects the client session for legitimate behavior. The challenge blocks bots or at least increases the cost of operating sophisticated bots. </p> <p>After the client session successfully responds to the challenge, it receives a new token from WAF, which the challenge script uses to resubmit the original request. </p> </li> </ul> <p>You can configure the expiration time in the <code>ChallengeConfig</code> <code>ImmunityTimeProperty</code> setting at the rule and web ACL level. The rule setting overrides the web ACL setting. </p> <p>This action option is available for rules. It isn't available for web ACL default actions. </p>"}, "ChallengeConfig": {"type": "structure", "members": {"ImmunityTimeProperty": {"shape": "ImmunityTimeProperty", "documentation": "<p>Determines how long a challenge timestamp in the token remains valid after the client successfully responds to a challenge. </p>"}}, "documentation": "<p>Specifies how WAF should handle <code>Challenge</code> evaluations. This is available at the web ACL level and in each rule. </p>"}, "ChallengeResponse": {"type": "structure", "members": {"ResponseCode": {"shape": "ResponseCode", "documentation": "<p>The HTTP response code indicating the status of the challenge token in the web request. If the token is missing, invalid, or expired, this code is <code>202 Request Accepted</code>.</p>"}, "SolveTimestamp": {"shape": "SolveTimestamp", "documentation": "<p>The time that the challenge was last solved for the supplied token. </p>"}, "FailureReason": {"shape": "FailureReason", "documentation": "<p>The reason for failure, populated when the evaluation of the token fails.</p>"}}, "documentation": "<p>The result from the inspection of the web request for a valid challenge token. </p>"}, "CheckCapacityRequest": {"type": "structure", "required": ["<PERSON><PERSON>", "Rules"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Rules": {"shape": "Rules", "documentation": "<p>An array of <a>Rule</a> that you're configuring to use in a rule group or web ACL. </p>"}}}, "CheckCapacityResponse": {"type": "structure", "members": {"Capacity": {"shape": "ConsumedCapacity", "documentation": "<p>The capacity required by the rules and scope.</p>"}}}, "ComparisonOperator": {"type": "string", "enum": ["EQ", "NE", "LE", "LT", "GE", "GT"]}, "Condition": {"type": "structure", "members": {"ActionCondition": {"shape": "ActionCondition", "documentation": "<p>A single action condition. This is the action setting that a log record must contain in order to meet the condition.</p>"}, "LabelNameCondition": {"shape": "LabelNameCondition", "documentation": "<p>A single label name condition. This is the fully qualified label name that a log record must contain in order to meet the condition. Fully qualified labels have a prefix, optional namespaces, and label name. The prefix identifies the rule group or web ACL context of the rule that added the label. </p>"}}, "documentation": "<p>A single match condition for a <a>Filter</a>.</p>"}, "Conditions": {"type": "list", "member": {"shape": "Condition"}, "min": 1}, "ConsumedCapacity": {"type": "long", "min": 0}, "CookieMatchPattern": {"type": "structure", "members": {"All": {"shape": "All", "documentation": "<p>Inspect all cookies. </p>"}, "IncludedCookies": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Inspect only the cookies that have a key that matches one of the strings specified here. </p>"}, "ExcludedCookies": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Inspect only the cookies whose keys don't match any of the strings specified here. </p>"}}, "documentation": "<p>The filter to use to identify the subset of cookies to inspect in a web request. </p> <p>You must specify exactly one setting: either <code>All</code>, <code>IncludedCookies</code>, or <code>ExcludedCookies</code>.</p> <p>Example JSON: <code>\"MatchPattern\": { \"IncludedCookies\": [ \"session-id-time\", \"session-id\" ] }</code> </p>"}, "CookieNames": {"type": "list", "member": {"shape": "SingleCookieName"}, "max": 199, "min": 1}, "Cookies": {"type": "structure", "required": ["MatchPattern", "MatchScope", "OversizeHandling"], "members": {"MatchPattern": {"shape": "CookieMatchPattern", "documentation": "<p>The filter to use to identify the subset of cookies to inspect in a web request. </p> <p>You must specify exactly one setting: either <code>All</code>, <code>IncludedCookies</code>, or <code>ExcludedCookies</code>.</p> <p>Example JSON: <code>\"MatchPattern\": { \"IncludedCookies\": [ \"session-id-time\", \"session-id\" ] }</code> </p>"}, "MatchScope": {"shape": "MapMatchScope", "documentation": "<p>The parts of the cookies to inspect with the rule inspection criteria. If you specify <code>All</code>, WAF inspects both keys and values. </p>"}, "OversizeHandling": {"shape": "OversizeHandling", "documentation": "<p>What WAF should do if the cookies of the request are more numerous or larger than WAF can inspect. WAF does not support inspecting the entire contents of request cookies when they exceed 8 KB (8192 bytes) or 200 total cookies. The underlying host service forwards a maximum of 200 cookies and at most 8 KB of cookie contents to WAF. </p> <p>The options for oversize handling are the following:</p> <ul> <li> <p> <code>CONTINUE</code> - Inspect the available cookies normally, according to the rule inspection criteria. </p> </li> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul>"}}, "documentation": "<p>Inspect the cookies in the web request. You can specify the parts of the cookies to inspect and you can narrow the set of cookies to inspect by including or excluding specific keys.</p> <p>This is used to indicate the web request component to inspect, in the <a>FieldToMatch</a> specification. </p> <p>Example JSON: <code>\"Cookies\": { \"MatchPattern\": { \"All\": {} }, \"MatchScope\": \"KEY\", \"OversizeHandling\": \"MATCH\" }</code> </p>"}, "CountAction": {"type": "structure", "members": {"CustomRequestHandling": {"shape": "CustomRequestHandling", "documentation": "<p>Defines custom handling for the web request.</p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>Specifies that WAF should count the request. Optionally defines additional custom handling for the request.</p> <p>This is used in the context of other settings, for example to specify values for <a>RuleAction</a> and web ACL <a>DefaultAction</a>. </p>"}, "Country": {"type": "string"}, "CountryCode": {"type": "string", "enum": ["AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "VA", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KP", "KR", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "SS", "ES", "LK", "SD", "SR", "SJ", "SZ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "US", "UM", "UY", "UZ", "VU", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW", "XK"]}, "CountryCodes": {"type": "list", "member": {"shape": "CountryCode"}, "min": 1}, "CreateAPIKeyRequest": {"type": "structure", "required": ["<PERSON><PERSON>", "TokenDomains"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "TokenDomains": {"shape": "APIKeyTokenDomains", "documentation": "<p>The client application domains that you want to use this API key for. </p> <p>Example JSON: <code>\"TokenDomains\": [\"abc.com\", \"store.abc.com\"]</code> </p> <p>Public suffixes aren't allowed. For example, you can't use <code>usa.gov</code> or <code>co.uk</code> as token domains.</p>"}}}, "CreateAPIKeyResponse": {"type": "structure", "members": {"APIKey": {"shape": "APIKey", "documentation": "<p>The generated, encrypted API key. You can copy this for use in your JavaScript CAPTCHA integration. </p>"}}}, "CreateIPSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "IPAddressVersion", "Addresses"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the IP set. You cannot change the name of an <code>IPSet</code> after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the IP set that helps with identification. </p>"}, "IPAddressVersion": {"shape": "IPAddressVersion", "documentation": "<p>The version of the IP addresses, either <code>IPV4</code> or <code>IPV6</code>. </p>"}, "Addresses": {"shape": "IPAddresses", "documentation": "<p>Contains an array of strings that specifies zero or more IP addresses or blocks of IP addresses that you want WAF to inspect for in incoming requests. All addresses must be specified using Classless Inter-Domain Routing (CIDR) notation. WAF supports all IPv4 and IPv6 CIDR ranges except for <code>/0</code>. </p> <p>Example address strings: </p> <ul> <li> <p>For requests that originated from the IP address **********, specify <code>**********/32</code>.</p> </li> <li> <p>For requests that originated from IP addresses from ********* to ***********, specify <code>*********/24</code>.</p> </li> <li> <p>For requests that originated from the IP address 1111:0000:0000:0000:0000:0000:0000:0111, specify <code>1111:0000:0000:0000:0000:0000:0000:0111/128</code>.</p> </li> <li> <p>For requests that originated from IP addresses 1111:0000:0000:0000:0000:0000:0000:0000 to 1111:0000:0000:0000:ffff:ffff:ffff:ffff, specify <code>1111:0000:0000:0000:0000:0000:0000:0000/64</code>.</p> </li> </ul> <p>For more information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>.</p> <p>Example JSON <code>Addresses</code> specifications: </p> <ul> <li> <p>Empty array: <code>\"Addresses\": []</code> </p> </li> <li> <p>Array with one address: <code>\"Addresses\": [\"**********/32\"]</code> </p> </li> <li> <p>Array with three addresses: <code>\"Addresses\": [\"**********/32\", \"*********/24\", \"*********/16\"]</code> </p> </li> <li> <p>INVALID specification: <code>\"Addresses\": [\"\"]</code> INVALID </p> </li> </ul>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key:value pairs to associate with the resource.</p>"}}}, "CreateIPSetResponse": {"type": "structure", "members": {"Summary": {"shape": "IPSetSummary", "documentation": "<p>High-level information about an <a>IPSet</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage an <code>IPSet</code>, and the ARN, that you provide to the <a>IPSetReferenceStatement</a> to use the address set in a <a>Rule</a>.</p>"}}}, "CreateRegexPatternSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "RegularExpressionList"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the set. You cannot change the name after you create the set.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the set that helps with identification. </p>"}, "RegularExpressionList": {"shape": "RegularExpressionList", "documentation": "<p>Array of regular expression strings. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key:value pairs to associate with the resource.</p>"}}}, "CreateRegexPatternSetResponse": {"type": "structure", "members": {"Summary": {"shape": "RegexPatternSetSummary", "documentation": "<p>High-level information about a <a>RegexPatternSet</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage a <code>RegexPatternSet</code>, and the ARN, that you provide to the <a>RegexPatternSetReferenceStatement</a> to use the pattern set in a <a>Rule</a>.</p>"}}}, "CreateRuleGroupRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Capacity", "VisibilityConfig"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule group. You cannot change the name of a rule group after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Capacity": {"shape": "CapacityUnit", "documentation": "<p>The web ACL capacity units (WCUs) required for this rule group.</p> <p>When you create your own rule group, you define this, and you cannot change it after creation. When you add or modify the rules in a rule group, WAF enforces this limit. You can check the capacity for a set of rules using <a>CheckCapacity</a>.</p> <p>WAF uses WCUs to calculate and control the operating resources that are used to run your rules, rule groups, and web ACLs. WAF calculates capacity differently for each rule type, to reflect the relative cost of each rule. Simple rules that cost little to run use fewer WCUs than more complex rules that use more processing power. Rule group capacity is fixed at creation, which helps users plan their web ACL WCU usage when they use a rule group. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-waf-capacity-units.html\">WAF web ACL capacity units (WCU)</a> in the <i>WAF Developer Guide</i>. </p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the rule group that helps with identification. </p>"}, "Rules": {"shape": "Rules", "documentation": "<p>The <a>Rule</a> statements used to identify the web requests that you want to manage. Each rule includes one top-level statement that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key:value pairs to associate with the resource.</p>"}, "CustomResponseBodies": {"shape": "CustomResponseBodies", "documentation": "<p>A map of custom response keys and content bodies. When you create a rule with a block action, you can send a custom response to the web request. You define these for the rule group, and then use them in the rules that you define in the rule group. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}}}, "CreateRuleGroupResponse": {"type": "structure", "members": {"Summary": {"shape": "RuleGroupSummary", "documentation": "<p>High-level information about a <a>RuleGroup</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage a <code>RuleGroup</code>, and the ARN, that you provide to the <a>RuleGroupReferenceStatement</a> to use the rule group in a <a>Rule</a>.</p>"}}}, "CreateWebACLRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "DefaultAction", "VisibilityConfig"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "DefaultAction": {"shape": "DefaultAction", "documentation": "<p>The action to perform if none of the <code>Rules</code> contained in the <code>WebACL</code> match. </p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the web ACL that helps with identification. </p>"}, "Rules": {"shape": "Rules", "documentation": "<p>The <a>Rule</a> statements used to identify the web requests that you want to manage. Each rule includes one top-level statement that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key:value pairs to associate with the resource.</p>"}, "CustomResponseBodies": {"shape": "CustomResponseBodies", "documentation": "<p>A map of custom response keys and content bodies. When you create a rule with a block action, you can send a custom response to the web request. You define these for the web ACL, and then use them in the rules and default actions that you define in the web ACL. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}, "CaptchaConfig": {"shape": "CaptchaConfig", "documentation": "<p>Specifies how WAF should handle <code>CAPTCHA</code> evaluations for rules that don't have their own <code>CaptchaConfig</code> settings. If you don't specify this, WAF uses its default settings for <code>CaptchaConfig</code>. </p>"}, "ChallengeConfig": {"shape": "ChallengeConfig", "documentation": "<p>Specifies how WAF should handle challenge evaluations for rules that don't have their own <code>ChallengeConfig</code> settings. If you don't specify this, WAF uses its default settings for <code>ChallengeConfig</code>. </p>"}, "TokenDomains": {"shape": "TokenDomains", "documentation": "<p>Specifies the domains that WAF should accept in a web request token. This enables the use of tokens across multiple protected websites. When WAF provides a token, it uses the domain of the Amazon Web Services resource that the web ACL is protecting. If you don't specify a list of token domains, WAF accepts tokens only for the domain of the protected resource. With a token domain list, WAF accepts the resource's host domain plus all domains in the token domain list, including their prefixed subdomains.</p> <p>Example JSON: <code>\"TokenDomains\": { \"mywebsite.com\", \"myotherwebsite.com\" }</code> </p> <p>Public suffixes aren't allowed. For example, you can't use <code>usa.gov</code> or <code>co.uk</code> as token domains.</p>"}, "AssociationConfig": {"shape": "AssociationConfig", "documentation": "<p>Specifies custom configurations for the associations between the web ACL and protected resources. </p> <p>Use this to customize the maximum size of the request body that your protected CloudFront distributions forward to WAF for inspection. The default is 16 KB (16,384 bytes). </p> <note> <p>You are charged additional fees when your protected resources forward body sizes that are larger than the default. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}}}, "CreateWebACLResponse": {"type": "structure", "members": {"Summary": {"shape": "WebACLSummary", "documentation": "<p>High-level information about a <a>WebACL</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage a <code>WebACL</code>, and the ARN, that you provide to operations like <a>AssociateWebACL</a>.</p>"}}}, "CreationPathString": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "CustomHTTPHeader": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "CustomHTTPHeaderName", "documentation": "<p>The name of the custom header. </p> <p>For custom request header insertion, when WAF inserts the header into the request, it prefixes this name <code>x-amzn-waf-</code>, to avoid confusion with the headers that are already in the request. For example, for the header name <code>sample</code>, WAF inserts the header <code>x-amzn-waf-sample</code>.</p>"}, "Value": {"shape": "CustomHTTPHeaderValue", "documentation": "<p>The value of the custom header.</p>"}}, "documentation": "<p>A custom header for custom request and response handling. This is used in <a>CustomResponse</a> and <a>CustomRequestHandling</a>.</p>"}, "CustomHTTPHeaderName": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9._$-]+$"}, "CustomHTTPHeaderValue": {"type": "string", "max": 255, "min": 1, "pattern": ".*"}, "CustomHTTPHeaders": {"type": "list", "member": {"shape": "CustomHTTPHeader"}, "min": 1}, "CustomRequestHandling": {"type": "structure", "required": ["InsertHeaders"], "members": {"InsertHeaders": {"shape": "CustomHTTPHeaders", "documentation": "<p>The HTTP headers to insert into the request. Duplicate header names are not allowed. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>Custom request handling behavior that inserts custom headers into a web request. You can add custom request handling for WAF to use when the rule action doesn't block the request. For example, <code>CaptchaAction</code> for requests with valid t okens, and <code>AllowAction</code>. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}, "CustomResponse": {"type": "structure", "required": ["ResponseCode"], "members": {"ResponseCode": {"shape": "ResponseStatusCode", "documentation": "<p>The HTTP status code to return to the client. </p> <p>For a list of status codes that you can use in your custom responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/customizing-the-response-status-codes.html\">Supported status codes for custom response</a> in the <i>WAF Developer Guide</i>. </p>"}, "CustomResponseBodyKey": {"shape": "EntityName", "documentation": "<p>References the response body that you want WAF to return to the web request client. You can define a custom response for a rule action or a default web ACL action that is set to block. To do this, you first define the response body key and value in the <code>CustomResponseBodies</code> setting for the <a>WebACL</a> or <a>RuleGroup</a> where you want to use it. Then, in the rule action or web ACL default action <code>BlockAction</code> setting, you reference the response body using this key. </p>"}, "ResponseHeaders": {"shape": "CustomHTTPHeaders", "documentation": "<p>The HTTP headers to use in the response. You can specify any header name except for <code>content-type</code>. Duplicate header names are not allowed.</p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>A custom response to send to the client. You can define a custom response for rule actions and default web ACL actions that are set to <a>BlockAction</a>. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p>"}, "CustomResponseBodies": {"type": "map", "key": {"shape": "EntityName"}, "value": {"shape": "CustomResponseBody"}, "min": 1}, "CustomResponseBody": {"type": "structure", "required": ["ContentType", "Content"], "members": {"ContentType": {"shape": "ResponseContentType", "documentation": "<p>The type of content in the payload that you are defining in the <code>Content</code> string.</p>"}, "Content": {"shape": "ResponseContent", "documentation": "<p>The payload of the custom response. </p> <p>You can use JSON escape strings in JSON content. To do this, you must specify JSON content in the <code>ContentType</code> setting. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}}, "documentation": "<p>The response body to use in a custom response to a web request. This is referenced by key from <a>CustomResponse</a> <code>CustomResponseBodyKey</code>.</p>"}, "DefaultAction": {"type": "structure", "members": {"Block": {"shape": "BlockAction", "documentation": "<p>Specifies that WAF should block requests by default. </p>"}, "Allow": {"shape": "AllowAction", "documentation": "<p>Specifies that WAF should allow requests by default.</p>"}}, "documentation": "<p>In a <a>WebACL</a>, this is the action that you want WAF to perform when a web request doesn't match any of the rules in the <code>WebACL</code>. The default action must be a terminating action.</p>"}, "DeleteFirewallManagerRuleGroupsRequest": {"type": "structure", "required": ["WebACLArn", "WebACLLockToken"], "members": {"WebACLArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL.</p>"}, "WebACLLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "DeleteFirewallManagerRuleGroupsResponse": {"type": "structure", "members": {"NextWebACLLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "DeleteIPSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the IP set. You cannot change the name of an <code>IPSet</code> after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "DeleteIPSetResponse": {"type": "structure", "members": {}}, "DeleteLoggingConfigurationRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL from which you want to delete the <a>LoggingConfiguration</a>.</p>"}}}, "DeleteLoggingConfigurationResponse": {"type": "structure", "members": {}}, "DeletePermissionPolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group from which you want to delete the policy.</p> <p>You must be the owner of the rule group to perform this operation.</p>"}}}, "DeletePermissionPolicyResponse": {"type": "structure", "members": {}}, "DeleteRegexPatternSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the set. You cannot change the name after you create the set.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "DeleteRegexPatternSetResponse": {"type": "structure", "members": {}}, "DeleteRuleGroupRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule group. You cannot change the name of a rule group after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the rule group. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "DeleteRuleGroupResponse": {"type": "structure", "members": {}}, "DeleteWebACLRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>The unique identifier for the web ACL. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "DeleteWebACLResponse": {"type": "structure", "members": {}}, "DescribeAllManagedProductsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}}}, "DescribeAllManagedProductsResponse": {"type": "structure", "members": {"ManagedProducts": {"shape": "ManagedProductDescriptors", "documentation": "<p>High-level information for the Amazon Web Services Managed Rules rule groups and Amazon Web Services Marketplace managed rule groups. </p>"}}}, "DescribeManagedProductsByVendorRequest": {"type": "structure", "required": ["VendorName", "<PERSON><PERSON>"], "members": {"VendorName": {"shape": "VendorName", "documentation": "<p>The name of the managed rule group vendor. You use this, along with the rule group name, to identify a rule group.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}}}, "DescribeManagedProductsByVendorResponse": {"type": "structure", "members": {"ManagedProducts": {"shape": "ManagedProductDescriptors", "documentation": "<p>High-level information for the managed rule groups owned by the specified vendor. </p>"}}}, "DescribeManagedRuleGroupRequest": {"type": "structure", "required": ["VendorName", "Name", "<PERSON><PERSON>"], "members": {"VendorName": {"shape": "VendorName", "documentation": "<p>The name of the managed rule group vendor. You use this, along with the rule group name, to identify a rule group.</p>"}, "Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule group. You use this, along with the vendor name, to identify the rule group.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "VersionName": {"shape": "VersionKeyString", "documentation": "<p>The version of the rule group. You can only use a version that is not scheduled for expiration. If you don't provide this, WAF uses the vendor's default version. </p>"}}}, "DescribeManagedRuleGroupResponse": {"type": "structure", "members": {"VersionName": {"shape": "VersionKeyString", "documentation": "<p>The managed rule group's version. </p>"}, "SnsTopicArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon resource name (ARN) of the Amazon Simple Notification Service SNS topic that's used to provide notification of changes to the managed rule group. You can subscribe to the SNS topic to receive notifications when the managed rule group is modified, such as for new versions and for version expiration. For more information, see the <a href=\"https://docs.aws.amazon.com/sns/latest/dg/welcome.html\">Amazon Simple Notification Service Developer Guide</a>.</p>"}, "Capacity": {"shape": "CapacityUnit", "documentation": "<p>The web ACL capacity units (WCUs) required for this rule group.</p> <p>WAF uses WCUs to calculate and control the operating resources that are used to run your rules, rule groups, and web ACLs. WAF calculates capacity differently for each rule type, to reflect the relative cost of each rule. Simple rules that cost little to run use fewer WCUs than more complex rules that use more processing power. Rule group capacity is fixed at creation, which helps users plan their web ACL WCU usage when they use a rule group. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-waf-capacity-units.html\">WAF web ACL capacity units (WCU)</a> in the <i>WAF Developer Guide</i>. </p>"}, "Rules": {"shape": "RuleSummaries", "documentation": "<p/>"}, "LabelNamespace": {"shape": "LabelName", "documentation": "<p>The label namespace prefix for this rule group. All labels added by rules in this rule group have this prefix. </p> <ul> <li> <p>The syntax for the label namespace prefix for a managed rule group is the following: </p> <p> <code>awswaf:managed:&lt;vendor&gt;:&lt;rule group name&gt;</code>:</p> </li> <li> <p>When a rule with a label matches a web request, WAF adds the fully qualified label to the request. A fully qualified label is made up of the label namespace from the rule group or web ACL where the rule is defined and the label from the rule, separated by a colon: </p> <p> <code>&lt;label namespace&gt;:&lt;label from rule&gt;</code> </p> </li> </ul>"}, "AvailableLabels": {"shape": "LabelSummaries", "documentation": "<p>The labels that one or more rules in this rule group add to matching web requests. These labels are defined in the <code>RuleLabels</code> for a <a>Rule</a>.</p>"}, "ConsumedLabels": {"shape": "LabelSummaries", "documentation": "<p>The labels that one or more rules in this rule group match against in label match statements. These labels are defined in a <code>LabelMatchStatement</code> specification, in the <a>Statement</a> definition of a rule. </p>"}}}, "DisassociateWebACLRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to disassociate from the web ACL. </p> <p>The ARN must be in one of the following formats:</p> <ul> <li> <p>For an Application Load Balancer: <code>arn:<i>partition</i>:elasticloadbalancing:<i>region</i>:<i>account-id</i>:loadbalancer/app/<i>load-balancer-name</i>/<i>load-balancer-id</i> </code> </p> </li> <li> <p>For an Amazon API Gateway REST API: <code>arn:<i>partition</i>:apigateway:<i>region</i>::/restapis/<i>api-id</i>/stages/<i>stage-name</i> </code> </p> </li> <li> <p>For an AppSync GraphQL API: <code>arn:<i>partition</i>:appsync:<i>region</i>:<i>account-id</i>:apis/<i>GraphQLApiId</i> </code> </p> </li> <li> <p>For an Amazon Cognito user pool: <code>arn:<i>partition</i>:cognito-idp:<i>region</i>:<i>account-id</i>:userpool/<i>user-pool-id</i> </code> </p> </li> <li> <p>For an App Runner service: <code>arn:<i>partition</i>:apprunner:<i>region</i>:<i>account-id</i>:service/<i>apprunner-service-name</i>/<i>apprunner-service-id</i> </code> </p> </li> <li> <p>For an Amazon Web Services Verified Access instance: <code>arn:<i>partition</i>:ec2:<i>region</i>:<i>account-id</i>:verified-access-instance/<i>instance-id</i> </code> </p> </li> </ul>"}}}, "DisassociateWebACLResponse": {"type": "structure", "members": {}}, "DownloadUrl": {"type": "string"}, "EmailField": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "FieldIdentifier", "documentation": "<p>The name of the email field. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"email\": \"THE_EMAIL\" } }</code>, the email field specification is <code>/form/email</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>email1</code>, the email field specification is <code>email1</code>.</p> </li> </ul>"}}, "documentation": "<p>The name of the field in the request payload that contains your customer's email. </p> <p>This data type is used in the <code>RequestInspectionACFP</code> data type. </p>"}, "EntityDescription": {"type": "string", "max": 256, "min": 1, "pattern": "^[\\w+=:#@/\\-,\\.][\\w+=:#@/\\-,\\.\\s]+[\\w+=:#@/\\-,\\.]$"}, "EntityId": {"type": "string", "max": 36, "min": 1, "pattern": "^[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$"}, "EntityName": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\w\\-]+$"}, "ErrorMessage": {"type": "string"}, "ErrorReason": {"type": "string"}, "ExcludedRule": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule whose action you want to override to <code>Count</code>.</p>"}}, "documentation": "<p>Specifies a single rule in a rule group whose action you want to override to <code>Count</code>. </p> <note> <p>Instead of this option, use <code>RuleActionOverrides</code>. It accepts any valid action setting, including <code>Count</code>.</p> </note>"}, "ExcludedRules": {"type": "list", "member": {"shape": "ExcludedRule"}, "max": 100}, "FailureCode": {"type": "integer", "max": 999, "min": 0}, "FailureReason": {"type": "string", "enum": ["TOKEN_MISSING", "TOKEN_EXPIRED", "TOKEN_INVALID", "TOKEN_DOMAIN_MISMATCH"]}, "FailureValue": {"type": "string", "max": 100, "min": 1, "pattern": ".*\\S.*"}, "FallbackBehavior": {"type": "string", "enum": ["MATCH", "NO_MATCH"]}, "FieldIdentifier": {"type": "string", "max": 512, "min": 1, "pattern": ".*\\S.*"}, "FieldToMatch": {"type": "structure", "members": {"SingleHeader": {"shape": "SingleHeader", "documentation": "<p>Inspect a single header. Provide the name of the header to inspect, for example, <code>User-Agent</code> or <code>Referer</code>. This setting isn't case sensitive.</p> <p>Example JSON: <code>\"SingleHeader\": { \"Name\": \"haystack\" }</code> </p> <p>Alternately, you can filter and inspect all headers with the <code>Headers</code> <code>FieldToMatch</code> setting. </p>"}, "SingleQueryArgument": {"shape": "SingleQueryArgument", "documentation": "<p>Inspect a single query argument. Provide the name of the query argument to inspect, such as <i>UserName</i> or <i>SalesRegion</i>. The name can be up to 30 characters long and isn't case sensitive. </p> <p>Example JSON: <code>\"SingleQueryArgument\": { \"Name\": \"myArgument\" }</code> </p>"}, "AllQueryArguments": {"shape": "AllQueryArguments", "documentation": "<p>Inspect all query arguments. </p>"}, "UriPath": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Inspect the request URI path. This is the part of the web request that identifies a resource, for example, <code>/images/daily-ad.jpg</code>.</p>"}, "QueryString": {"shape": "QueryString", "documentation": "<p>Inspect the query string. This is the part of a URL that appears after a <code>?</code> character, if any.</p>"}, "Body": {"shape": "Body", "documentation": "<p>Inspect the request body as plain text. The request body immediately follows the request headers. This is the part of a request that contains any additional data that you want to send to your web server as the HTTP request body, such as data from a form. </p> <p>A limited amount of the request body is forwarded to WAF for inspection by the underlying host service. For regional resources, the limit is 8 KB (8,192 bytes) and for CloudFront distributions, the limit is 16 KB (16,384 bytes). For CloudFront distributions, you can increase the limit in the web ACL's <code>AssociationConfig</code>, for additional processing fees. </p> <p>For information about how to handle oversized request bodies, see the <code>Body</code> object configuration. </p>"}, "Method": {"shape": "Method", "documentation": "<p>Inspect the HTTP method. The method indicates the type of operation that the request is asking the origin to perform. </p>"}, "JsonBody": {"shape": "JsonBody", "documentation": "<p>Inspect the request body as JSON. The request body immediately follows the request headers. This is the part of a request that contains any additional data that you want to send to your web server as the HTTP request body, such as data from a form. </p> <p>A limited amount of the request body is forwarded to WAF for inspection by the underlying host service. For regional resources, the limit is 8 KB (8,192 bytes) and for CloudFront distributions, the limit is 16 KB (16,384 bytes). For CloudFront distributions, you can increase the limit in the web ACL's <code>AssociationConfig</code>, for additional processing fees. </p> <p>For information about how to handle oversized request bodies, see the <code>JsonBody</code> object configuration. </p>"}, "Headers": {"shape": "Headers", "documentation": "<p>Inspect the request headers. You must configure scope and pattern matching filters in the <code>Headers</code> object, to define the set of headers to and the parts of the headers that WAF inspects. </p> <p>Only the first 8 KB (8192 bytes) of a request's headers and only the first 200 headers are forwarded to WAF for inspection by the underlying host service. You must configure how to handle any oversize header content in the <code>Headers</code> object. WAF applies the pattern matching filters to the headers that it receives from the underlying host service. </p>"}, "Cookies": {"shape": "Cookies", "documentation": "<p>Inspect the request cookies. You must configure scope and pattern matching filters in the <code>Cookies</code> object, to define the set of cookies and the parts of the cookies that WAF inspects. </p> <p>Only the first 8 KB (8192 bytes) of a request's cookies and only the first 200 cookies are forwarded to WAF for inspection by the underlying host service. You must configure how to handle any oversize cookie content in the <code>Cookies</code> object. WAF applies the pattern matching filters to the cookies that it receives from the underlying host service. </p>"}, "HeaderOrder": {"shape": "<PERSON>er<PERSON><PERSON><PERSON>", "documentation": "<p>Inspect a string containing the list of the request's header names, ordered as they appear in the web request that WAF receives for inspection. WAF generates the string and then uses that as the field to match component in its inspection. WAF separates the header names in the string using colons and no added spaces, for example <code>host:user-agent:accept:authorization:referer</code>.</p>"}, "JA3Fingerprint": {"shape": "JA3Fingerprint", "documentation": "<p>Match against the request's JA3 fingerprint. The JA3 fingerprint is a 32-character hash derived from the TLS Client Hello of an incoming request. This fingerprint serves as a unique identifier for the client's TLS configuration. WAF calculates and logs this fingerprint for each request that has enough TLS Client Hello information for the calculation. Almost all web requests include this information.</p> <note> <p>You can use this choice only with a string match <code>ByteMatchStatement</code> with the <code>PositionalConstraint</code> set to <code>EXACTLY</code>. </p> </note> <p>You can obtain the JA3 fingerprint for client requests from the web ACL logs. If WAF is able to calculate the fingerprint, it includes it in the logs. For information about the logging fields, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging-fields.html\">Log fields</a> in the <i>WAF Developer Guide</i>. </p> <p>Provide the JA3 fingerprint string from the logs in your string match statement specification, to match with any future requests that have the same TLS configuration.</p>"}}, "documentation": "<p>The part of the web request that you want WAF to inspect. Include the single <code>FieldToMatch</code> type that you want to inspect, with additional specifications as needed, according to the type. You specify a single request component in <code>FieldToMatch</code> for each rule statement that requires it. To inspect more than one component of the web request, create a separate rule statement for each component.</p> <p>Example JSON for a <code>QueryString</code> field to match: </p> <p> <code> \"FieldToMatch\": { \"QueryString\": {} }</code> </p> <p>Example JSON for a <code>Method</code> field to match specification:</p> <p> <code> \"FieldToMatch\": { \"Method\": { \"Name\": \"DELETE\" } }</code> </p>"}, "FieldToMatchData": {"type": "string", "max": 64, "min": 1, "pattern": ".*\\S.*"}, "Filter": {"type": "structure", "required": ["Behavior", "Requirement", "Conditions"], "members": {"Behavior": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>How to handle logs that satisfy the filter's conditions and requirement. </p>"}, "Requirement": {"shape": "FilterRequirement", "documentation": "<p>Logic to apply to the filtering conditions. You can specify that, in order to satisfy the filter, a log must match all conditions or must match at least one condition.</p>"}, "Conditions": {"shape": "Conditions", "documentation": "<p>Match conditions for the filter.</p>"}}, "documentation": "<p>A single logging filter, used in <a>LoggingFilter</a>. </p>"}, "FilterBehavior": {"type": "string", "enum": ["KEEP", "DROP"]}, "FilterRequirement": {"type": "string", "enum": ["MEETS_ALL", "MEETS_ANY"]}, "Filters": {"type": "list", "member": {"shape": "Filter"}, "min": 1}, "FirewallManagerRuleGroup": {"type": "structure", "required": ["Name", "Priority", "FirewallManagerStatement", "OverrideAction", "VisibilityConfig"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule group. You cannot change the name of a rule group after you create it.</p>"}, "Priority": {"shape": "RulePriority", "documentation": "<p>If you define more than one rule group in the first or last Firewall Manager rule groups, WAF evaluates each request against the rule groups in order, starting from the lowest priority setting. The priorities don't need to be consecutive, but they must all be different.</p>"}, "FirewallManagerStatement": {"shape": "FirewallManagerStatement", "documentation": "<p>The processing guidance for an Firewall Manager rule. This is like a regular rule <a>Statement</a>, but it can only contain a rule group reference.</p>"}, "OverrideAction": {"shape": "OverrideAction", "documentation": "<p>The action to use in the place of the action that results from the rule group evaluation. Set the override action to none to leave the result of the rule group alone. Set it to count to override the result to count only. </p> <p>You can only use this for rule statements that reference a rule group, like <code>RuleGroupReferenceStatement</code> and <code>ManagedRuleGroupStatement</code>. </p> <note> <p>This option is usually set to none. It does not affect how the rules in the rule group are evaluated. If you want the rules in the rule group to only count matches, do not use this and instead use the rule action override option, with <code>Count</code> action, in your rule group reference statement settings. </p> </note>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}}, "documentation": "<p>A rule group that's defined for an Firewall Manager WAF policy.</p>"}, "FirewallManagerRuleGroups": {"type": "list", "member": {"shape": "FirewallManagerRuleGroup"}}, "FirewallManagerStatement": {"type": "structure", "members": {"ManagedRuleGroupStatement": {"shape": "ManagedRuleGroupStatement", "documentation": "<p>A statement used by Firewall Manager to run the rules that are defined in a managed rule group. This is managed by Firewall Manager for an Firewall Manager WAF policy.</p>"}, "RuleGroupReferenceStatement": {"shape": "RuleGroupReferenceStatement", "documentation": "<p>A statement used by Firewall Manager to run the rules that are defined in a rule group. This is managed by Firewall Manager for an Firewall Manager WAF policy.</p>"}}, "documentation": "<p>The processing guidance for an Firewall Manager rule. This is like a regular rule <a>Statement</a>, but it can only contain a single rule group reference.</p>"}, "ForwardedIPConfig": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"HeaderName": {"shape": "ForwardedIPHeaderName", "documentation": "<p>The name of the HTTP header to use for the IP address. For example, to use the X-Forwarded-For (XFF) header, set this to <code>X-Forwarded-For</code>.</p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note>"}, "FallbackBehavior": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The match status to assign to the web request if the request doesn't have a valid IP address in the specified position.</p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note> <p>You can specify the following fallback behaviors:</p> <ul> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul>"}}, "documentation": "<p>The configuration for inspecting IP addresses in an HTTP header that you specify, instead of using the IP address that's reported by the web request origin. Commonly, this is the X-Forwarded-For (XFF) header, but you can specify any header name. </p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note> <p>This configuration is used for <a>GeoMatchStatement</a> and <a>RateBasedStatement</a>. For <a>IPSetReferenceStatement</a>, use <a>IPSetForwardedIPConfig</a> instead. </p> <p>WAF only evaluates the first IP address found in the specified HTTP header. </p>"}, "ForwardedIPHeaderName": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z0-9-]+$"}, "ForwardedIPPosition": {"type": "string", "enum": ["FIRST", "LAST", "ANY"]}, "GenerateMobileSdkReleaseUrlRequest": {"type": "structure", "required": ["Platform", "ReleaseVersion"], "members": {"Platform": {"shape": "Platform", "documentation": "<p>The device platform.</p>"}, "ReleaseVersion": {"shape": "VersionKeyString", "documentation": "<p>The release version. For the latest available version, specify <code>LATEST</code>.</p>"}}}, "GenerateMobileSdkReleaseUrlResponse": {"type": "structure", "members": {"Url": {"shape": "DownloadUrl", "documentation": "<p>The presigned download URL for the specified SDK release.</p>"}}}, "GeoMatchStatement": {"type": "structure", "members": {"CountryCodes": {"shape": "CountryCodes", "documentation": "<p>An array of two-character country codes that you want to match against, for example, <code>[ \"US\", \"CN\" ]</code>, from the alpha-2 country ISO codes of the ISO 3166 international standard. </p> <p>When you use a geo match statement just for the region and country labels that it adds to requests, you still have to supply a country code for the rule to evaluate. In this case, you configure the rule to only count matching requests, but it will still generate logging and count metrics for any matches. You can reduce the logging and metrics that the rule produces by specifying a country that's unlikely to be a source of traffic to your site.</p>"}, "ForwardedIPConfig": {"shape": "ForwardedIPConfig", "documentation": "<p>The configuration for inspecting IP addresses in an HTTP header that you specify, instead of using the IP address that's reported by the web request origin. Commonly, this is the X-Forwarded-For (XFF) header, but you can specify any header name. </p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note>"}}, "documentation": "<p>A rule statement that labels web requests by country and region and that matches against web requests based on country code. A geo match rule labels every request that it inspects regardless of whether it finds a match.</p> <ul> <li> <p>To manage requests only by country, you can use this statement by itself and specify the countries that you want to match against in the <code>CountryCodes</code> array. </p> </li> <li> <p>Otherwise, configure your geo match rule with Count action so that it only labels requests. Then, add one or more label match rules to run after the geo match rule and configure them to match against the geographic labels and handle the requests as needed. </p> </li> </ul> <p>WAF labels requests using the alpha-2 country and region codes from the International Organization for Standardization (ISO) 3166 standard. WAF determines the codes using either the IP address in the web request origin or, if you specify it, the address in the geo match <code>ForwardedIPConfig</code>. </p> <p>If you use the web request origin, the label formats are <code>awswaf:clientip:geo:region:&lt;ISO country code&gt;-&lt;ISO region code&gt;</code> and <code>awswaf:clientip:geo:country:&lt;ISO country code&gt;</code>.</p> <p>If you use a forwarded IP address, the label formats are <code>awswaf:forwardedip:geo:region:&lt;ISO country code&gt;-&lt;ISO region code&gt;</code> and <code>awswaf:forwardedip:geo:country:&lt;ISO country code&gt;</code>.</p> <p>For additional details, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rule-statement-type-geo-match.html\">Geographic match rule statement</a> in the <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-chapter.html\">WAF Developer Guide</a>. </p>"}, "GetDecryptedAPIKeyRequest": {"type": "structure", "required": ["<PERSON><PERSON>", "APIKey"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "APIKey": {"shape": "APIKey", "documentation": "<p>The encrypted API key. </p>"}}}, "GetDecryptedAPIKeyResponse": {"type": "structure", "members": {"TokenDomains": {"shape": "TokenDomains", "documentation": "<p>The token domains that are defined in this API key. </p>"}, "CreationTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the key was created. </p>"}}}, "GetIPSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the IP set. You cannot change the name of an <code>IPSet</code> after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}}}, "GetIPSetResponse": {"type": "structure", "members": {"IPSet": {"shape": "IPSet", "documentation": "<p/>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "GetLoggingConfigurationRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL for which you want to get the <a>LoggingConfiguration</a>.</p>"}}}, "GetLoggingConfigurationResponse": {"type": "structure", "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>The <a>LoggingConfiguration</a> for the specified web ACL.</p>"}}}, "GetManagedRuleSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule set. You use this, along with the rule set ID, to identify the rule set.</p> <p>This name is assigned to the corresponding managed rule group, which your customers can access and use. </p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the managed rule set. The ID is returned in the responses to commands like <code>list</code>. You provide it to operations like <code>get</code> and <code>update</code>.</p>"}}}, "GetManagedRuleSetResponse": {"type": "structure", "members": {"ManagedRuleSet": {"shape": "ManagedRuleSet", "documentation": "<p>The managed rule set that you requested. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "GetMobileSdkReleaseRequest": {"type": "structure", "required": ["Platform", "ReleaseVersion"], "members": {"Platform": {"shape": "Platform", "documentation": "<p>The device platform.</p>"}, "ReleaseVersion": {"shape": "VersionKeyString", "documentation": "<p>The release version. For the latest available version, specify <code>LATEST</code>.</p>"}}}, "GetMobileSdkReleaseResponse": {"type": "structure", "members": {"MobileSdkRelease": {"shape": "MobileSdkRelease", "documentation": "<p>Information for a specified SDK release, including release notes and tags.</p>"}}}, "GetPermissionPolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group for which you want to get the policy.</p>"}}}, "GetPermissionPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "PolicyString", "documentation": "<p>The IAM policy that is attached to the specified rule group.</p>"}}}, "GetRateBasedStatementManagedKeysRequest": {"type": "structure", "required": ["<PERSON><PERSON>", "WebACLName", "WebACLId", "RuleName"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "WebACLName": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "WebACLId": {"shape": "EntityId", "documentation": "<p>The unique identifier for the web ACL. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "RuleGroupRuleName": {"shape": "EntityName", "documentation": "<p>The name of the rule group reference statement in your web ACL. This is required only when you have the rate-based rule nested inside a rule group. </p>"}, "RuleName": {"shape": "EntityName", "documentation": "<p>The name of the rate-based rule to get the keys for. If you have the rule defined inside a rule group that you're using in your web ACL, also provide the name of the rule group reference statement in the request parameter <code>RuleGroupRuleName</code>.</p>"}}}, "GetRateBasedStatementManagedKeysResponse": {"type": "structure", "members": {"ManagedKeysIPV4": {"shape": "RateBasedStatementManagedKeysIPSet", "documentation": "<p>The keys that are of Internet Protocol version 4 (IPv4). </p>"}, "ManagedKeysIPV6": {"shape": "RateBasedStatementManagedKeysIPSet", "documentation": "<p>The keys that are of Internet Protocol version 6 (IPv6). </p>"}}}, "GetRegexPatternSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the set. You cannot change the name after you create the set.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}}}, "GetRegexPatternSetResponse": {"type": "structure", "members": {"RegexPatternSet": {"shape": "RegexPatternSet", "documentation": "<p/>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "GetRuleGroupRequest": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule group. You cannot change the name of a rule group after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the rule group. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}}}, "GetRuleGroupResponse": {"type": "structure", "members": {"RuleGroup": {"shape": "RuleGroup", "documentation": "<p/>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "GetSampledRequestsRequest": {"type": "structure", "required": ["WebAclArn", "RuleMetricName", "<PERSON><PERSON>", "TimeWindow", "MaxItems"], "members": {"WebAclArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon resource name (ARN) of the <code>WebACL</code> for which you want a sample of requests.</p>"}, "RuleMetricName": {"shape": "MetricName", "documentation": "<p>The metric name assigned to the <code>Rule</code> or <code>RuleGroup</code> dimension for which you want a sample of requests.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "TimeWindow": {"shape": "TimeWindow", "documentation": "<p>The start date and time and the end date and time of the range for which you want <code>GetSampledRequests</code> to return a sample of requests. You must specify the times in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <code>Z</code>. For example, <code>\"2016-09-27T14:50Z\"</code>. You can specify any time range in the previous three hours. If you specify a start time that's earlier than three hours ago, WAF sets it to three hours ago.</p>"}, "MaxItems": {"shape": "ListMaxItems", "documentation": "<p>The number of requests that you want WAF to return from among the first 5,000 requests that your Amazon Web Services resource received during the time range. If your resource received fewer requests than the value of <code>MaxItems</code>, <code>GetSampledRequests</code> returns information about all of them. </p>"}}}, "GetSampledRequestsResponse": {"type": "structure", "members": {"SampledRequests": {"shape": "SampledHTTPRequests", "documentation": "<p>A complex type that contains detailed information about each of the requests in the sample.</p>"}, "PopulationSize": {"shape": "PopulationSize", "documentation": "<p>The total number of requests from which <code>GetSampledRequests</code> got a sample of <code>MaxItems</code> requests. If <code>PopulationSize</code> is less than <code>MaxItems</code>, the sample includes every request that your Amazon Web Services resource received during the specified time range.</p>"}, "TimeWindow": {"shape": "TimeWindow", "documentation": "<p>Usually, <code>TimeWindow</code> is the time range that you specified in the <code>GetSampledRequests</code> request. However, if your Amazon Web Services resource received more than 5,000 requests during the time range that you specified in the request, <code>GetSampledRequests</code> returns the time range for the first 5,000 requests. Times are in Coordinated Universal Time (UTC) format.</p>"}}}, "GetWebACLForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource whose web ACL you want to retrieve. </p> <p>The ARN must be in one of the following formats:</p> <ul> <li> <p>For an Application Load Balancer: <code>arn:<i>partition</i>:elasticloadbalancing:<i>region</i>:<i>account-id</i>:loadbalancer/app/<i>load-balancer-name</i>/<i>load-balancer-id</i> </code> </p> </li> <li> <p>For an Amazon API Gateway REST API: <code>arn:<i>partition</i>:apigateway:<i>region</i>::/restapis/<i>api-id</i>/stages/<i>stage-name</i> </code> </p> </li> <li> <p>For an AppSync GraphQL API: <code>arn:<i>partition</i>:appsync:<i>region</i>:<i>account-id</i>:apis/<i>GraphQLApiId</i> </code> </p> </li> <li> <p>For an Amazon Cognito user pool: <code>arn:<i>partition</i>:cognito-idp:<i>region</i>:<i>account-id</i>:userpool/<i>user-pool-id</i> </code> </p> </li> <li> <p>For an App Runner service: <code>arn:<i>partition</i>:apprunner:<i>region</i>:<i>account-id</i>:service/<i>apprunner-service-name</i>/<i>apprunner-service-id</i> </code> </p> </li> <li> <p>For an Amazon Web Services Verified Access instance: <code>arn:<i>partition</i>:ec2:<i>region</i>:<i>account-id</i>:verified-access-instance/<i>instance-id</i> </code> </p> </li> </ul>"}}}, "GetWebACLForResourceResponse": {"type": "structure", "members": {"WebACL": {"shape": "WebACL", "documentation": "<p>The web ACL that is associated with the resource. If there is no associated resource, WAF returns a null web ACL.</p>"}}}, "GetWebACLRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>The unique identifier for the web ACL. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}}}, "GetWebACLResponse": {"type": "structure", "members": {"WebACL": {"shape": "WebACL", "documentation": "<p>The web ACL specification. You can modify the settings in this web ACL and use it to update this web ACL or create a new one.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "ApplicationIntegrationURL": {"shape": "OutputUrl", "documentation": "<p>The URL to use in SDK integrations with Amazon Web Services managed rule groups. For example, you can use the integration SDKs with the account takeover prevention managed rule group <code>AWSManagedRulesATPRuleSet</code> and the account creation fraud prevention managed rule group <code>AWSManagedRulesACFPRuleSet</code>. This is only populated if you are using a rule group in your web ACL that integrates with your applications in this way. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}}}, "HTTPHeader": {"type": "structure", "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the HTTP header.</p>"}, "Value": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the HTTP header.</p>"}}, "documentation": "<p>Part of the response from <a>GetSampledRequests</a>. This is a complex type that appears as <code>Headers</code> in the response syntax. <code>HTTPHeader</code> contains the names and values of all of the headers that appear in one of the web requests. </p>"}, "HTTPHeaders": {"type": "list", "member": {"shape": "HTTPHeader"}}, "HTTPMethod": {"type": "string"}, "HTTPRequest": {"type": "structure", "members": {"ClientIP": {"shape": "IPString", "documentation": "<p>The IP address that the request originated from. If the web ACL is associated with a CloudFront distribution, this is the value of one of the following fields in CloudFront access logs:</p> <ul> <li> <p> <code>c-ip</code>, if the viewer did not use an HTTP proxy or a load balancer to send the request</p> </li> <li> <p> <code>x-forwarded-for</code>, if the viewer did use an HTTP proxy or a load balancer to send the request</p> </li> </ul>"}, "Country": {"shape": "Country", "documentation": "<p>The two-letter country code for the country that the request originated from. For a current list of country codes, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2\">ISO 3166-1 alpha-2</a>.</p>"}, "URI": {"shape": "URIString", "documentation": "<p>The URI path of the request, which identifies the resource, for example, <code>/images/daily-ad.jpg</code>.</p>"}, "Method": {"shape": "HTTPMethod", "documentation": "<p>The HTTP method specified in the sampled web request. </p>"}, "HTTPVersion": {"shape": "HTTPVersion", "documentation": "<p>The HTTP version specified in the sampled web request, for example, <code>HTTP/1.1</code>.</p>"}, "Headers": {"shape": "HTTPHeaders", "documentation": "<p>A complex type that contains the name and value for each header in the sampled web request.</p>"}}, "documentation": "<p>Part of the response from <a>GetSampledRequests</a>. This is a complex type that appears as <code>Request</code> in the response syntax. <code>HTTPRequest</code> contains information about one of the web requests. </p>"}, "HTTPVersion": {"type": "string"}, "HeaderMatchPattern": {"type": "structure", "members": {"All": {"shape": "All", "documentation": "<p>Inspect all headers. </p>"}, "IncludedHeaders": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Inspect only the headers that have a key that matches one of the strings specified here. </p>"}, "ExcludedHeaders": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Inspect only the headers whose keys don't match any of the strings specified here. </p>"}}, "documentation": "<p>The filter to use to identify the subset of headers to inspect in a web request. </p> <p>You must specify exactly one setting: either <code>All</code>, <code>IncludedHeaders</code>, or <code>ExcludedHeaders</code>.</p> <p>Example JSON: <code>\"MatchPattern\": { \"ExcludedHeaders\": [ \"KeyToExclude1\", \"KeyToExclude2\" ] }</code> </p>"}, "HeaderName": {"type": "string"}, "HeaderNames": {"type": "list", "member": {"shape": "FieldToMatchData"}, "max": 199, "min": 1}, "HeaderOrder": {"type": "structure", "required": ["OversizeHandling"], "members": {"OversizeHandling": {"shape": "OversizeHandling", "documentation": "<p>What WAF should do if the headers of the request are more numerous or larger than WAF can inspect. WAF does not support inspecting the entire contents of request headers when they exceed 8 KB (8192 bytes) or 200 total headers. The underlying host service forwards a maximum of 200 headers and at most 8 KB of header contents to WAF. </p> <p>The options for oversize handling are the following:</p> <ul> <li> <p> <code>CONTINUE</code> - Inspect the available headers normally, according to the rule inspection criteria. </p> </li> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul>"}}, "documentation": "<p>Inspect a string containing the list of the request's header names, ordered as they appear in the web request that WAF receives for inspection. WAF generates the string and then uses that as the field to match component in its inspection. WAF separates the header names in the string using colons and no added spaces, for example <code>host:user-agent:accept:authorization:referer</code>.</p>"}, "HeaderValue": {"type": "string"}, "Headers": {"type": "structure", "required": ["MatchPattern", "MatchScope", "OversizeHandling"], "members": {"MatchPattern": {"shape": "HeaderMatchPattern", "documentation": "<p>The filter to use to identify the subset of headers to inspect in a web request. </p> <p>You must specify exactly one setting: either <code>All</code>, <code>IncludedHeaders</code>, or <code>ExcludedHeaders</code>.</p> <p>Example JSON: <code>\"MatchPattern\": { \"ExcludedHeaders\": [ \"KeyToExclude1\", \"KeyToExclude2\" ] }</code> </p>"}, "MatchScope": {"shape": "MapMatchScope", "documentation": "<p>The parts of the headers to match with the rule inspection criteria. If you specify <code>All</code>, WAF inspects both keys and values. </p>"}, "OversizeHandling": {"shape": "OversizeHandling", "documentation": "<p>What WAF should do if the headers of the request are more numerous or larger than WAF can inspect. WAF does not support inspecting the entire contents of request headers when they exceed 8 KB (8192 bytes) or 200 total headers. The underlying host service forwards a maximum of 200 headers and at most 8 KB of header contents to WAF. </p> <p>The options for oversize handling are the following:</p> <ul> <li> <p> <code>CONTINUE</code> - Inspect the available headers normally, according to the rule inspection criteria. </p> </li> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul>"}}, "documentation": "<p>Inspect all headers in the web request. You can specify the parts of the headers to inspect and you can narrow the set of headers to inspect by including or excluding specific keys.</p> <p>This is used to indicate the web request component to inspect, in the <a>FieldToMatch</a> specification. </p> <p>If you want to inspect just the value of a single header, use the <code>SingleHeader</code> <code>FieldToMatch</code> setting instead.</p> <p>Example JSON: <code>\"Headers\": { \"MatchPattern\": { \"All\": {} }, \"MatchScope\": \"KEY\", \"OversizeHandling\": \"MATCH\" }</code> </p>"}, "IPAddress": {"type": "string", "max": 50, "min": 1, "pattern": ".*\\S.*"}, "IPAddressVersion": {"type": "string", "enum": ["IPV4", "IPV6"]}, "IPAddresses": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}}, "IPSet": {"type": "structure", "required": ["Name", "Id", "ARN", "IPAddressVersion", "Addresses"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the IP set. You cannot change the name of an <code>IPSet</code> after you create it.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the IP set that helps with identification. </p>"}, "IPAddressVersion": {"shape": "IPAddressVersion", "documentation": "<p>The version of the IP addresses, either <code>IPV4</code> or <code>IPV6</code>. </p>"}, "Addresses": {"shape": "IPAddresses", "documentation": "<p>Contains an array of strings that specifies zero or more IP addresses or blocks of IP addresses that you want WAF to inspect for in incoming requests. All addresses must be specified using Classless Inter-Domain Routing (CIDR) notation. WAF supports all IPv4 and IPv6 CIDR ranges except for <code>/0</code>. </p> <p>Example address strings: </p> <ul> <li> <p>For requests that originated from the IP address **********, specify <code>**********/32</code>.</p> </li> <li> <p>For requests that originated from IP addresses from ********* to ***********, specify <code>*********/24</code>.</p> </li> <li> <p>For requests that originated from the IP address 1111:0000:0000:0000:0000:0000:0000:0111, specify <code>1111:0000:0000:0000:0000:0000:0000:0111/128</code>.</p> </li> <li> <p>For requests that originated from IP addresses 1111:0000:0000:0000:0000:0000:0000:0000 to 1111:0000:0000:0000:ffff:ffff:ffff:ffff, specify <code>1111:0000:0000:0000:0000:0000:0000:0000/64</code>.</p> </li> </ul> <p>For more information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>.</p> <p>Example JSON <code>Addresses</code> specifications: </p> <ul> <li> <p>Empty array: <code>\"Addresses\": []</code> </p> </li> <li> <p>Array with one address: <code>\"Addresses\": [\"**********/32\"]</code> </p> </li> <li> <p>Array with three addresses: <code>\"Addresses\": [\"**********/32\", \"*********/24\", \"*********/16\"]</code> </p> </li> <li> <p>INVALID specification: <code>\"Addresses\": [\"\"]</code> INVALID </p> </li> </ul>"}}, "documentation": "<p>Contains zero or more IP addresses or blocks of IP addresses specified in Classless Inter-Domain Routing (CIDR) notation. WAF supports all IPv4 and IPv6 CIDR ranges except for /0. For information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>. </p> <p>WAF assigns an ARN to each <code>IPSet</code> that you create. To use an IP set in a rule, you provide the ARN to the <a>Rule</a> statement <a>IPSetReferenceStatement</a>. </p>"}, "IPSetForwardedIPConfig": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Position"], "members": {"HeaderName": {"shape": "ForwardedIPHeaderName", "documentation": "<p>The name of the HTTP header to use for the IP address. For example, to use the X-Forwarded-For (XFF) header, set this to <code>X-Forwarded-For</code>.</p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note>"}, "FallbackBehavior": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The match status to assign to the web request if the request doesn't have a valid IP address in the specified position.</p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note> <p>You can specify the following fallback behaviors:</p> <ul> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul>"}, "Position": {"shape": "ForwardedIPPosition", "documentation": "<p>The position in the header to search for the IP address. The header can contain IP addresses of the original client and also of proxies. For example, the header value could be <code>********, *********, ***********</code> where the first IP address identifies the original client and the rest identify proxies that the request went through. </p> <p>The options for this setting are the following: </p> <ul> <li> <p>FIRST - Inspect the first IP address in the list of IP addresses in the header. This is usually the client's original IP.</p> </li> <li> <p>LAST - Inspect the last IP address in the list of IP addresses in the header.</p> </li> <li> <p>ANY - Inspect all IP addresses in the header for a match. If the header contains more than 10 IP addresses, WAF inspects the last 10.</p> </li> </ul>"}}, "documentation": "<p>The configuration for inspecting IP addresses in an HTTP header that you specify, instead of using the IP address that's reported by the web request origin. Commonly, this is the X-Forwarded-For (XFF) header, but you can specify any header name. </p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note> <p>This configuration is used only for <a>IPSetReferenceStatement</a>. For <a>GeoMatchStatement</a> and <a>RateBasedStatement</a>, use <a>ForwardedIPConfig</a> instead. </p>"}, "IPSetReferenceStatement": {"type": "structure", "required": ["ARN"], "members": {"ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the <a>IPSet</a> that this statement references.</p>"}, "IPSetForwardedIPConfig": {"shape": "IPSetForwardedIPConfig", "documentation": "<p>The configuration for inspecting IP addresses in an HTTP header that you specify, instead of using the IP address that's reported by the web request origin. Commonly, this is the X-Forwarded-For (XFF) header, but you can specify any header name. </p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note>"}}, "documentation": "<p>A rule statement used to detect web requests coming from particular IP addresses or address ranges. To use this, create an <a>IPSet</a> that specifies the addresses you want to detect, then use the ARN of that set in this statement. To create an IP set, see <a>CreateIPSet</a>.</p> <p>Each IP set rule statement references an IP set. You create and maintain the set independent of your rules. This allows you to use the single set in multiple rules. When you update the referenced set, WAF automatically updates all rules that reference it.</p>"}, "IPSetSummaries": {"type": "list", "member": {"shape": "IPSetSummary"}}, "IPSetSummary": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the IP set. You cannot change the name of an <code>IPSet</code> after you create it.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the IP set that helps with identification. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}}, "documentation": "<p>High-level information about an <a>IPSet</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage an <code>IPSet</code>, and the ARN, that you provide to the <a>IPSetReferenceStatement</a> to use the address set in a <a>Rule</a>.</p>"}, "IPString": {"type": "string"}, "ImmunityTimeProperty": {"type": "structure", "required": ["ImmunityTime"], "members": {"ImmunityTime": {"shape": "TimeWindowSecond", "documentation": "<p>The amount of time, in seconds, that a <code>CAPTCHA</code> or challenge timestamp is considered valid by WAF. The default setting is 300. </p> <p>For the Challenge action, the minimum setting is 300. </p>"}}, "documentation": "<p>Used for CAPTCHA and challenge token settings. Determines how long a <code>CAPTCHA</code> or challenge timestamp remains valid after WAF updates it for a successful <code>CAPTCHA</code> or challenge response. </p>"}, "InspectionLevel": {"type": "string", "enum": ["COMMON", "TARGETED"]}, "JA3Fingerprint": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"FallbackBehavior": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The match status to assign to the web request if the request doesn't have a JA3 fingerprint. </p> <p>You can specify the following fallback behaviors:</p> <ul> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul>"}}, "documentation": "<p>Match against the request's JA3 fingerprint. The JA3 fingerprint is a 32-character hash derived from the TLS Client Hello of an incoming request. This fingerprint serves as a unique identifier for the client's TLS configuration. WAF calculates and logs this fingerprint for each request that has enough TLS Client Hello information for the calculation. Almost all web requests include this information.</p> <note> <p>You can use this choice only with a string match <code>ByteMatchStatement</code> with the <code>PositionalConstraint</code> set to <code>EXACTLY</code>. </p> </note> <p>You can obtain the JA3 fingerprint for client requests from the web ACL logs. If WAF is able to calculate the fingerprint, it includes it in the logs. For information about the logging fields, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging-fields.html\">Log fields</a> in the <i>WAF Developer Guide</i>. </p> <p>Provide the JA3 fingerprint string from the logs in your string match statement specification, to match with any future requests that have the same TLS configuration.</p>"}, "JsonBody": {"type": "structure", "required": ["MatchPattern", "MatchScope"], "members": {"MatchPattern": {"shape": "JsonMatchPattern", "documentation": "<p>The patterns to look for in the JSON body. WAF inspects the results of these pattern matches against the rule inspection criteria. </p>"}, "MatchScope": {"shape": "JsonMatchScope", "documentation": "<p>The parts of the JSON to match against using the <code>MatchPattern</code>. If you specify <code>All</code>, WAF matches against keys and values. </p>"}, "InvalidFallbackBehavior": {"shape": "BodyParsingFallbackBehavior", "documentation": "<p>What WAF should do if it fails to completely parse the JSON body. The options are the following:</p> <ul> <li> <p> <code>EVALUATE_AS_STRING</code> - Inspect the body as plain text. WAF applies the text transformations and inspection criteria that you defined for the JSON inspection to the body text string.</p> </li> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul> <p>If you don't provide this setting, WAF parses and evaluates the content only up to the first parsing failure that it encounters. </p> <p>WAF does its best to parse the entire JSON body, but might be forced to stop for reasons such as invalid characters, duplicate keys, truncation, and any content whose root node isn't an object or an array. </p> <p>WAF parses the JSON in the following examples as two valid key, value pairs: </p> <ul> <li> <p>Missing comma: <code>{\"key1\":\"value1\"\"key2\":\"value2\"}</code> </p> </li> <li> <p>Missing colon: <code>{\"key1\":\"value1\",\"key2\"\"value2\"}</code> </p> </li> <li> <p>Extra colons: <code>{\"key1\"::\"value1\",\"key2\"\"value2\"}</code> </p> </li> </ul>"}, "OversizeHandling": {"shape": "OversizeHandling", "documentation": "<p>What WAF should do if the body is larger than WAF can inspect. WAF does not support inspecting the entire contents of the web request body if the body exceeds the limit for the resource type. If the body is larger than the limit, the underlying host service only forwards the contents that are below the limit to WAF for inspection. </p> <p>The default limit is 8 KB (8,192 bytes) for regional resources and 16 KB (16,384 bytes) for CloudFront distributions. For CloudFront distributions, you can increase the limit in the web ACL <code>AssociationConfig</code>, for additional processing fees. </p> <p>The options for oversize handling are the following:</p> <ul> <li> <p> <code>CONTINUE</code> - Inspect the available body contents normally, according to the rule inspection criteria. </p> </li> <li> <p> <code>MATCH</code> - Treat the web request as matching the rule statement. WAF applies the rule action to the request.</p> </li> <li> <p> <code>NO_MATCH</code> - Treat the web request as not matching the rule statement.</p> </li> </ul> <p>You can combine the <code>MATCH</code> or <code>NO_MATCH</code> settings for oversize handling with your rule and web ACL action settings, so that you block any request whose body is over the limit. </p> <p>Default: <code>CONTINUE</code> </p>"}}, "documentation": "<p>Inspect the body of the web request as JSON. The body immediately follows the request headers. </p> <p>This is used to indicate the web request component to inspect, in the <a>FieldToMatch</a> specification. </p> <p>Use the specifications in this object to indicate which parts of the JSON body to inspect using the rule's inspection criteria. WAF inspects only the parts of the JSON that result from the matches that you indicate. </p> <p>Example JSON: <code>\"JsonBody\": { \"MatchPattern\": { \"All\": {} }, \"MatchScope\": \"ALL\" }</code> </p>"}, "JsonMatchPattern": {"type": "structure", "members": {"All": {"shape": "All", "documentation": "<p>Match all of the elements. See also <code>MatchScope</code> in <a>JsonBody</a>. </p> <p>You must specify either this setting or the <code>IncludedPaths</code> setting, but not both.</p>"}, "IncludedPaths": {"shape": "JsonPointerPaths", "documentation": "<p>Match only the specified include paths. See also <code>MatchScope</code> in <a>JsonBody</a>. </p> <p>Provide the include paths using JSON Pointer syntax. For example, <code>\"IncludedPaths\": [\"/dogs/0/name\", \"/dogs/1/name\"]</code>. For information about this syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>You must specify either this setting or the <code>All</code> setting, but not both.</p> <note> <p>Don't use this option to include all paths. Instead, use the <code>All</code> setting. </p> </note>"}}, "documentation": "<p>The patterns to look for in the JSON body. WAF inspects the results of these pattern matches against the rule inspection criteria. This is used with the <a>FieldToMatch</a> option <code>JsonBody</code>. </p>"}, "JsonMatchScope": {"type": "string", "enum": ["ALL", "KEY", "VALUE"]}, "JsonPointerPath": {"type": "string", "max": 512, "min": 1, "pattern": "([/])|([/](([^~])|(~[01]))+)"}, "JsonPointerPaths": {"type": "list", "member": {"shape": "JsonPointer<PERSON>ath"}, "min": 1}, "Label": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "LabelName", "documentation": "<p>The label string. </p>"}}, "documentation": "<p>A single label container. This is used as an element of a label array in multiple contexts, for example, in <code>RuleLabels</code> inside a <a>Rule</a> and in <code>Labels</code> inside a <a>SampledHTTPRequest</a>. </p>"}, "LabelMatchKey": {"type": "string", "max": 1024, "min": 1, "pattern": "^[0-9A-Za-z_\\-:]+$"}, "LabelMatchScope": {"type": "string", "enum": ["LABEL", "NAMESPACE"]}, "LabelMatchStatement": {"type": "structure", "required": ["<PERSON><PERSON>", "Key"], "members": {"Scope": {"shape": "LabelMatchScope", "documentation": "<p>Specify whether you want to match using the label name or just the namespace. </p>"}, "Key": {"shape": "LabelMatchKey", "documentation": "<p>The string to match against. The setting you provide for this depends on the match statement's <code>Scope</code> setting: </p> <ul> <li> <p>If the <code>Scope</code> indicates <code>LABEL</code>, then this specification must include the name and can include any number of preceding namespace specifications and prefix up to providing the fully qualified label name. </p> </li> <li> <p>If the <code>Scope</code> indicates <code>NAMESPACE</code>, then this specification can include any number of contiguous namespace strings, and can include the entire label namespace prefix from the rule group or web ACL where the label originates.</p> </li> </ul> <p>Labels are case sensitive and components of a label must be separated by colon, for example <code>NS1:NS2:name</code>.</p>"}}, "documentation": "<p>A rule statement to match against labels that have been added to the web request by rules that have already run in the web ACL. </p> <p>The label match statement provides the label or namespace string to search for. The label string can represent a part or all of the fully qualified label name that had been added to the web request. Fully qualified labels have a prefix, optional namespaces, and label name. The prefix identifies the rule group or web ACL context of the rule that added the label. If you do not provide the fully qualified name in your label match string, WAF performs the search for labels that were added in the same context as the label match statement. </p>"}, "LabelName": {"type": "string", "max": 1024, "min": 1, "pattern": "^[0-9A-Za-z_\\-:]+$"}, "LabelNameCondition": {"type": "structure", "required": ["LabelName"], "members": {"LabelName": {"shape": "LabelName", "documentation": "<p>The label name that a log record must contain in order to meet the condition. This must be a fully qualified label name. Fully qualified labels have a prefix, optional namespaces, and label name. The prefix identifies the rule group or web ACL context of the rule that added the label. </p>"}}, "documentation": "<p>A single label name condition for a <a>Condition</a> in a logging filter.</p>"}, "LabelNamespace": {"type": "string", "max": 1024, "min": 1, "pattern": "^[0-9A-Za-z_\\-:]+:$"}, "LabelSummaries": {"type": "list", "member": {"shape": "LabelSummary"}}, "LabelSummary": {"type": "structure", "members": {"Name": {"shape": "LabelName", "documentation": "<p>An individual label specification.</p>"}}, "documentation": "<p>List of labels used by one or more of the rules of a <a>RuleGroup</a>. This summary object is used for the following rule group lists: </p> <ul> <li> <p> <code>AvailableLabels</code> - Labels that rules add to matching requests. These labels are defined in the <code>RuleLabels</code> for a <a>Rule</a>. </p> </li> <li> <p> <code>ConsumedLabels</code> - Labels that rules match against. These labels are defined in a <code>LabelMatchStatement</code> specification, in the <a>Statement</a> definition of a rule. </p> </li> </ul>"}, "Labels": {"type": "list", "member": {"shape": "Label"}}, "ListAPIKeysRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListAPIKeysResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "APIKeySummaries": {"shape": "APIKeySummaries", "documentation": "<p>The array of key summaries. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}, "ApplicationIntegrationURL": {"shape": "OutputUrl", "documentation": "<p>The CAPTCHA application integration URL, for use in your JavaScript implementation. </p>"}}}, "ListAvailableManagedRuleGroupVersionsRequest": {"type": "structure", "required": ["VendorName", "Name", "<PERSON><PERSON>"], "members": {"VendorName": {"shape": "VendorName", "documentation": "<p>The name of the managed rule group vendor. You use this, along with the rule group name, to identify a rule group.</p>"}, "Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule group. You use this, along with the vendor name, to identify the rule group.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListAvailableManagedRuleGroupVersionsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Versions": {"shape": "ManagedRuleGroupVersions", "documentation": "<p>The versions that are currently available for the specified managed rule group. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}, "CurrentDefaultVersion": {"shape": "VersionKeyString", "documentation": "<p>The name of the version that's currently set as the default. </p>"}}}, "ListAvailableManagedRuleGroupsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListAvailableManagedRuleGroupsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "ManagedRuleGroups": {"shape": "ManagedRuleGroupSummaries", "documentation": "<p>Array of managed rule groups that you can use. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "ListIPSetsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListIPSetsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "IPSets": {"shape": "IPSetSummaries", "documentation": "<p>Array of IPSets. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "ListLoggingConfigurationsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListLoggingConfigurationsResponse": {"type": "structure", "members": {"LoggingConfigurations": {"shape": "LoggingConfigurations", "documentation": "<p>Array of logging configurations. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}}}, "ListManagedRuleSetsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListManagedRuleSetsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "ManagedRuleSets": {"shape": "ManagedRuleSetSummaries", "documentation": "<p>Your managed rule sets. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "ListMaxItems": {"type": "long", "max": 500, "min": 1}, "ListMobileSdkReleasesRequest": {"type": "structure", "required": ["Platform"], "members": {"Platform": {"shape": "Platform", "documentation": "<p>The device platform to retrieve the list for.</p>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListMobileSdkReleasesResponse": {"type": "structure", "members": {"ReleaseSummaries": {"shape": "ReleaseSummaries", "documentation": "<p>The high level information for the available SDK releases. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}}}, "ListRegexPatternSetsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListRegexPatternSetsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "RegexPatternSets": {"shape": "RegexPatternSetSummaries", "documentation": "<p>Array of regex pattern sets. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "ListResourcesForWebACLRequest": {"type": "structure", "required": ["WebACLArn"], "members": {"WebACLArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Used for web ACLs that are scoped for regional applications. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <note> <p>If you don't provide a resource type, the call uses the resource type <code>APPLICATION_LOAD_BALANCER</code>. </p> </note> <p>Default: <code>APPLICATION_LOAD_BALANCER</code> </p>"}}}, "ListResourcesForWebACLResponse": {"type": "structure", "members": {"ResourceArns": {"shape": "ResourceArns", "documentation": "<p>The array of Amazon Resource Names (ARNs) of the associated resources.</p>"}}}, "ListRuleGroupsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListRuleGroupsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "RuleGroups": {"shape": "RuleGroupSummaries", "documentation": "<p>Array of rule groups. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "ResourceARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "TagInfoForResource": {"shape": "TagInfoForResource", "documentation": "<p>The collection of tagging definitions for the resource. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "ListWebACLsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "Limit": {"shape": "PaginationLimit", "documentation": "<p>The maximum number of objects that you want WAF to return for this request. If more objects are available, in the response, WAF provides a <code>NextMarker</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListWebACLsResponse": {"type": "structure", "members": {"NextMarker": {"shape": "NextMarker", "documentation": "<p>When you request a list of objects with a <code>Limit</code> setting, if the number of objects that are still available for retrieval exceeds the limit, WAF returns a <code>NextMarker</code> value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "WebACLs": {"shape": "WebACLSummaries", "documentation": "<p>Array of web ACLs. If you specified a <code>Limit</code> in your request, this might not be the full list. </p>"}}}, "LockToken": {"type": "string", "max": 36, "min": 1, "pattern": "^[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$"}, "LogDestinationConfigs": {"type": "list", "member": {"shape": "ResourceArn"}, "max": 100, "min": 1}, "LoggingConfiguration": {"type": "structure", "required": ["ResourceArn", "LogDestinationConfigs"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL that you want to associate with <code>LogDestinationConfigs</code>.</p>"}, "LogDestinationConfigs": {"shape": "LogDestinationConfigs", "documentation": "<p>The logging destination configuration that you want to associate with the web ACL.</p> <note> <p>You can associate one logging destination to a web ACL.</p> </note>"}, "RedactedFields": {"shape": "RedactedFields", "documentation": "<p>The parts of the request that you want to keep out of the logs.</p> <p>For example, if you redact the <code>SingleHeader</code> field, the <code>HEADER</code> field in the logs will be <code>REDACTED</code> for all rules that use the <code>SingleHeader</code> <code>FieldToMatch</code> setting. </p> <p>Redaction applies only to the component that's specified in the rule's <code>FieldToMatch</code> setting, so the <code>SingleHeader</code> redaction doesn't apply to rules that use the <code>Headers</code> <code>FieldToMatch</code>.</p> <note> <p>You can specify only the following fields for redaction: <code>UriPath</code>, <code>QueryString</code>, <code>SingleHeader</code>, and <code>Method</code>.</p> </note>"}, "ManagedByFirewallManager": {"shape": "Boolean", "documentation": "<p>Indicates whether the logging configuration was created by Firewall Manager, as part of an WAF policy configuration. If true, only Firewall Manager can modify or delete the configuration. </p>"}, "LoggingFilter": {"shape": "LoggingFilter", "documentation": "<p>Filtering that specifies which web requests are kept in the logs and which are dropped. You can filter on the rule action and on the web request labels that were applied by matching rules during web ACL evaluation. </p>"}}, "documentation": "<p>Defines an association between logging destinations and a web ACL resource, for logging from WAF. As part of the association, you can specify parts of the standard logging fields to keep out of the logs and you can specify filters so that you log only a subset of the logging records. </p> <note> <p>You can define one logging destination per web ACL.</p> </note> <p>You can access information about the traffic that WAF inspects using the following steps:</p> <ol> <li> <p>Create your logging destination. You can use an Amazon CloudWatch Logs log group, an Amazon Simple Storage Service (Amazon S3) bucket, or an Amazon Kinesis Data Firehose. </p> <p>The name that you give the destination must start with <code>aws-waf-logs-</code>. Depending on the type of destination, you might need to configure additional settings or permissions. </p> <p>For configuration requirements and pricing information for each destination type, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging.html\">Logging web ACL traffic</a> in the <i>WAF Developer Guide</i>.</p> </li> <li> <p>Associate your logging destination to your web ACL using a <code>PutLoggingConfiguration</code> request.</p> </li> </ol> <p>When you successfully enable logging using a <code>PutLoggingConfiguration</code> request, WAF creates an additional role or policy that is required to write logs to the logging destination. For an Amazon CloudWatch Logs log group, WAF creates a resource policy on the log group. For an Amazon S3 bucket, WAF creates a bucket policy. For an Amazon Kinesis Data Firehose, WAF creates a service-linked role.</p> <p>For additional information about web ACL logging, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging.html\">Logging web ACL traffic information</a> in the <i>WAF Developer Guide</i>.</p>"}, "LoggingConfigurations": {"type": "list", "member": {"shape": "LoggingConfiguration"}}, "LoggingFilter": {"type": "structure", "required": ["Filters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"Filters": {"shape": "Filters", "documentation": "<p>The filters that you want to apply to the logs. </p>"}, "DefaultBehavior": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Default handling for logs that don't match any of the specified filtering conditions. </p>"}}, "documentation": "<p>Filtering that specifies which web requests are kept in the logs and which are dropped, defined for a web ACL's <a>LoggingConfiguration</a>. </p> <p>You can filter on the rule action and on the web request labels that were applied by matching rules during web ACL evaluation. </p>"}, "LoginPathString": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "ManagedProductDescriptor": {"type": "structure", "members": {"VendorName": {"shape": "VendorName", "documentation": "<p>The name of the managed rule group vendor. You use this, along with the rule group name, to identify a rule group.</p>"}, "ManagedRuleSetName": {"shape": "EntityName", "documentation": "<p>The name of the managed rule group. For example, <code>AWSManagedRulesAnonymousIpList</code> or <code>AWSManagedRulesATPRuleSet</code>.</p>"}, "ProductId": {"shape": "ProductId", "documentation": "<p>A unique identifier for the rule group. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "ProductLink": {"shape": "ProductLink", "documentation": "<p>For Amazon Web Services Marketplace managed rule groups only, the link to the rule group product page. </p>"}, "ProductTitle": {"shape": "ProductTitle", "documentation": "<p>The display name for the managed rule group. For example, <code>Anonymous IP list</code> or <code>Account takeover prevention</code>.</p>"}, "ProductDescription": {"shape": "ProductDescription", "documentation": "<p>A short description of the managed rule group.</p>"}, "SnsTopicArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon resource name (ARN) of the Amazon Simple Notification Service SNS topic that's used to provide notification of changes to the managed rule group. You can subscribe to the SNS topic to receive notifications when the managed rule group is modified, such as for new versions and for version expiration. For more information, see the <a href=\"https://docs.aws.amazon.com/sns/latest/dg/welcome.html\">Amazon Simple Notification Service Developer Guide</a>.</p>"}, "IsVersioningSupported": {"shape": "Boolean", "documentation": "<p>Indicates whether the rule group is versioned. </p>"}, "IsAdvancedManagedRuleSet": {"shape": "Boolean", "documentation": "<p>Indicates whether the rule group provides an advanced set of protections, such as the the Amazon Web Services Managed Rules rule groups that are used for WAF intelligent threat mitigation. </p>"}}, "documentation": "<p>The properties of a managed product, such as an Amazon Web Services Managed Rules rule group or an Amazon Web Services Marketplace managed rule group. </p>"}, "ManagedProductDescriptors": {"type": "list", "member": {"shape": "ManagedProductDescriptor"}}, "ManagedRuleGroupConfig": {"type": "structure", "members": {"LoginPath": {"shape": "LoginPathString", "documentation": "<note> <p>Instead of this setting, provide your configuration under <code>AWSManagedRulesATPRuleSet</code>. </p> </note>", "deprecated": true, "deprecatedMessage": "Deprecated. Use AWSManagedRulesATPRuleSet LoginPath"}, "PayloadType": {"shape": "PayloadType", "documentation": "<note> <p>Instead of this setting, provide your configuration under the request inspection configuration for <code>AWSManagedRulesATPRuleSet</code> or <code>AWSManagedRulesACFPRuleSet</code>. </p> </note>", "deprecated": true, "deprecatedMessage": "Deprecated. Use AWSManagedRulesATPRuleSet RequestInspection PayloadType"}, "UsernameField": {"shape": "UsernameField", "documentation": "<note> <p>Instead of this setting, provide your configuration under the request inspection configuration for <code>AWSManagedRulesATPRuleSet</code> or <code>AWSManagedRulesACFPRuleSet</code>. </p> </note>", "deprecated": true, "deprecatedMessage": "Deprecated. Use AWSManagedRulesATPRuleSet RequestInspection UsernameField"}, "PasswordField": {"shape": "PasswordField", "documentation": "<note> <p>Instead of this setting, provide your configuration under the request inspection configuration for <code>AWSManagedRulesATPRuleSet</code> or <code>AWSManagedRulesACFPRuleSet</code>. </p> </note>", "deprecated": true, "deprecatedMessage": "Deprecated. Use AWSManagedRulesATPRuleSet RequestInspection PasswordField"}, "AWSManagedRulesBotControlRuleSet": {"shape": "AWSManagedRulesBotControlRuleSet", "documentation": "<p>Additional configuration for using the Bot Control managed rule group. Use this to specify the inspection level that you want to use. For information about using the Bot Control managed rule group, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-managed-rule-groups-bot.html\">WAF Bot Control rule group</a> and <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-bot-control.html\">WAF Bot Control</a> in the <i>WAF Developer Guide</i>.</p>"}, "AWSManagedRulesATPRuleSet": {"shape": "AWSManagedRulesATPRuleSet", "documentation": "<p>Additional configuration for using the account takeover prevention (ATP) managed rule group, <code>AWSManagedRulesATPRuleSet</code>. Use this to provide login request information to the rule group. For web ACLs that protect CloudFront distributions, use this to also provide the information about how your distribution responds to login requests. </p> <p>This configuration replaces the individual configuration fields in <code>ManagedRuleGroupConfig</code> and provides additional feature configuration. </p> <p>For information about using the ATP managed rule group, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-managed-rule-groups-atp.html\">WAF Fraud Control account takeover prevention (ATP) rule group</a> and <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-atp.html\">WAF Fraud Control account takeover prevention (ATP)</a> in the <i>WAF Developer Guide</i>.</p>"}, "AWSManagedRulesACFPRuleSet": {"shape": "AWSManagedRulesACFPRuleSet", "documentation": "<p>Additional configuration for using the account creation fraud prevention (ACFP) managed rule group, <code>AWSManagedRulesACFPRuleSet</code>. Use this to provide account creation request information to the rule group. For web ACLs that protect CloudFront distributions, use this to also provide the information about how your distribution responds to account creation requests. </p> <p>For information about using the ACFP managed rule group, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-managed-rule-groups-acfp.html\">WAF Fraud Control account creation fraud prevention (ACFP) rule group</a> and <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-acfp.html\">WAF Fraud Control account creation fraud prevention (ACFP)</a> in the <i>WAF Developer Guide</i>.</p>"}}, "documentation": "<p>Additional information that's used by a managed rule group. Many managed rule groups don't require this.</p> <p>The rule groups used for intelligent threat mitigation require additional configuration: </p> <ul> <li> <p>Use the <code>AWSManagedRulesACFPRuleSet</code> configuration object to configure the account creation fraud prevention managed rule group. The configuration includes the registration and sign-up pages of your application and the locations in the account creation request payload of data, such as the user email and phone number fields. </p> </li> <li> <p>Use the <code>AWSManagedRulesATPRuleSet</code> configuration object to configure the account takeover prevention managed rule group. The configuration includes the sign-in page of your application and the locations in the login request payload of data such as the username and password. </p> </li> <li> <p>Use the <code>AWSManagedRulesBotControlRuleSet</code> configuration object to configure the protection level that you want the Bot Control rule group to use. </p> </li> </ul> <p>For example specifications, see the examples section of <a>CreateWebACL</a>.</p>"}, "ManagedRuleGroupConfigs": {"type": "list", "member": {"shape": "ManagedRuleGroupConfig"}}, "ManagedRuleGroupStatement": {"type": "structure", "required": ["VendorName", "Name"], "members": {"VendorName": {"shape": "VendorName", "documentation": "<p>The name of the managed rule group vendor. You use this, along with the rule group name, to identify a rule group.</p>"}, "Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule group. You use this, along with the vendor name, to identify the rule group.</p>"}, "Version": {"shape": "VersionKeyString", "documentation": "<p>The version of the managed rule group to use. If you specify this, the version setting is fixed until you change it. If you don't specify this, WAF uses the vendor's default version, and then keeps the version at the vendor's default when the vendor updates the managed rule group settings. </p>"}, "ExcludedRules": {"shape": "ExcludedRules", "documentation": "<p>Rules in the referenced rule group whose actions are set to <code>Count</code>. </p> <note> <p>Instead of this option, use <code>RuleActionOverrides</code>. It accepts any valid action setting, including <code>Count</code>.</p> </note>"}, "ScopeDownStatement": {"shape": "Statement", "documentation": "<p>An optional nested statement that narrows the scope of the web requests that are evaluated by the managed rule group. Requests are only evaluated by the rule group if they match the scope-down statement. You can use any nestable <a>Statement</a> in the scope-down statement, and you can nest statements at any level, the same as you can for a rule statement. </p>"}, "ManagedRuleGroupConfigs": {"shape": "ManagedRuleGroupConfigs", "documentation": "<p>Additional information that's used by a managed rule group. Many managed rule groups don't require this.</p> <p>The rule groups used for intelligent threat mitigation require additional configuration: </p> <ul> <li> <p>Use the <code>AWSManagedRulesACFPRuleSet</code> configuration object to configure the account creation fraud prevention managed rule group. The configuration includes the registration and sign-up pages of your application and the locations in the account creation request payload of data, such as the user email and phone number fields. </p> </li> <li> <p>Use the <code>AWSManagedRulesATPRuleSet</code> configuration object to configure the account takeover prevention managed rule group. The configuration includes the sign-in page of your application and the locations in the login request payload of data such as the username and password. </p> </li> <li> <p>Use the <code>AWSManagedRulesBotControlRuleSet</code> configuration object to configure the protection level that you want the Bot Control rule group to use. </p> </li> </ul>"}, "RuleActionOverrides": {"shape": "RuleActionOverrides", "documentation": "<p>Action settings to use in the place of the rule actions that are configured inside the rule group. You specify one override for each rule whose action you want to change. </p> <p>You can use overrides for testing, for example you can override all of rule actions to <code>Count</code> and then monitor the resulting count metrics to understand how the rule group would handle your web traffic. You can also permanently override some or all actions, to modify how the rule group manages your web traffic.</p>"}}, "documentation": "<p>A rule statement used to run the rules that are defined in a managed rule group. To use this, provide the vendor name and the name of the rule group in this statement. You can retrieve the required names by calling <a>ListAvailableManagedRuleGroups</a>.</p> <p>You cannot nest a <code>ManagedRuleGroupStatement</code>, for example for use inside a <code>NotStatement</code> or <code>OrStatement</code>. You cannot use a managed rule group inside another rule group. You can only reference a managed rule group as a top-level statement within a rule that you define in a web ACL.</p> <note> <p>You are charged additional fees when you use the WAF Bot Control managed rule group <code>AWSManagedRulesBotControlRuleSet</code>, the WAF Fraud Control account takeover prevention (ATP) managed rule group <code>AWSManagedRulesATPRuleSet</code>, or the WAF Fraud Control account creation fraud prevention (ACFP) managed rule group <code>AWSManagedRulesACFPRuleSet</code>. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}, "ManagedRuleGroupSummaries": {"type": "list", "member": {"shape": "ManagedRuleGroupSummary"}}, "ManagedRuleGroupSummary": {"type": "structure", "members": {"VendorName": {"shape": "VendorName", "documentation": "<p>The name of the managed rule group vendor. You use this, along with the rule group name, to identify a rule group.</p>"}, "Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule group. You use this, along with the vendor name, to identify the rule group.</p>"}, "VersioningSupported": {"shape": "Boolean", "documentation": "<p>Indicates whether the managed rule group is versioned. If it is, you can retrieve the versions list by calling <a>ListAvailableManagedRuleGroupVersions</a>. </p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>The description of the managed rule group, provided by Amazon Web Services Managed Rules or the Amazon Web Services Marketplace seller who manages it.</p>"}}, "documentation": "<p>High-level information about a managed rule group, returned by <a>ListAvailableManagedRuleGroups</a>. This provides information like the name and vendor name, that you provide when you add a <a>ManagedRuleGroupStatement</a> to a web ACL. Managed rule groups include Amazon Web Services Managed Rules rule groups and Amazon Web Services Marketplace managed rule groups. To use any Amazon Web Services Marketplace managed rule group, first subscribe to the rule group through Amazon Web Services Marketplace. </p>"}, "ManagedRuleGroupVersion": {"type": "structure", "members": {"Name": {"shape": "VersionKeyString", "documentation": "<p>The version name. </p>"}, "LastUpdateTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the managed rule group owner updated the rule group version information. </p>"}}, "documentation": "<p>Describes a single version of a managed rule group. </p>"}, "ManagedRuleGroupVersions": {"type": "list", "member": {"shape": "ManagedRuleGroupVersion"}}, "ManagedRuleSet": {"type": "structure", "required": ["Name", "Id", "ARN"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule set. You use this, along with the rule set ID, to identify the rule set.</p> <p>This name is assigned to the corresponding managed rule group, which your customers can access and use. </p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the managed rule set. The ID is returned in the responses to commands like <code>list</code>. You provide it to operations like <code>get</code> and <code>update</code>.</p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the set that helps with identification. </p>"}, "PublishedVersions": {"shape": "PublishedVersions", "documentation": "<p>The versions of this managed rule set that are available for use by customers. </p>"}, "RecommendedVersion": {"shape": "VersionKeyString", "documentation": "<p>The version that you would like your customers to use.</p>"}, "LabelNamespace": {"shape": "LabelName", "documentation": "<p>The label namespace prefix for the managed rule groups that are offered to customers from this managed rule set. All labels that are added by rules in the managed rule group have this prefix. </p> <ul> <li> <p>The syntax for the label namespace prefix for a managed rule group is the following: </p> <p> <code>awswaf:managed:&lt;vendor&gt;:&lt;rule group name&gt;</code>:</p> </li> <li> <p>When a rule with a label matches a web request, WAF adds the fully qualified label to the request. A fully qualified label is made up of the label namespace from the rule group or web ACL where the rule is defined and the label from the rule, separated by a colon: </p> <p> <code>&lt;label namespace&gt;:&lt;label from rule&gt;</code> </p> </li> </ul>"}}, "documentation": "<p>A set of rules that is managed by Amazon Web Services and Amazon Web Services Marketplace sellers to provide versioned managed rule groups for customers of WAF.</p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "ManagedRuleSetSummaries": {"type": "list", "member": {"shape": "ManagedRuleSetSummary"}}, "ManagedRuleSetSummary": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule set. You use this, along with the rule set ID, to identify the rule set.</p> <p>This name is assigned to the corresponding managed rule group, which your customers can access and use. </p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the managed rule set. The ID is returned in the responses to commands like <code>list</code>. You provide it to operations like <code>get</code> and <code>update</code>.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the set that helps with identification. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}, "LabelNamespace": {"shape": "LabelName", "documentation": "<p>The label namespace prefix for the managed rule groups that are offered to customers from this managed rule set. All labels that are added by rules in the managed rule group have this prefix. </p> <ul> <li> <p>The syntax for the label namespace prefix for a managed rule group is the following: </p> <p> <code>awswaf:managed:&lt;vendor&gt;:&lt;rule group name&gt;</code>:</p> </li> <li> <p>When a rule with a label matches a web request, WAF adds the fully qualified label to the request. A fully qualified label is made up of the label namespace from the rule group or web ACL where the rule is defined and the label from the rule, separated by a colon: </p> <p> <code>&lt;label namespace&gt;:&lt;label from rule&gt;</code> </p> </li> </ul>"}}, "documentation": "<p>High-level information for a managed rule set. </p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "ManagedRuleSetVersion": {"type": "structure", "members": {"AssociatedRuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the vendor rule group that's used to define the published version of your managed rule group. </p>"}, "Capacity": {"shape": "CapacityUnit", "documentation": "<p>The web ACL capacity units (WCUs) required for this rule group.</p> <p>WAF uses WCUs to calculate and control the operating resources that are used to run your rules, rule groups, and web ACLs. WAF calculates capacity differently for each rule type, to reflect the relative cost of each rule. Simple rules that cost little to run use fewer WCUs than more complex rules that use more processing power. Rule group capacity is fixed at creation, which helps users plan their web ACL WCU usage when they use a rule group. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-waf-capacity-units.html\">WAF web ACL capacity units (WCU)</a> in the <i>WAF Developer Guide</i>. </p>"}, "ForecastedLifetime": {"shape": "TimeWindowDay", "documentation": "<p>The amount of time you expect this version of your managed rule group to last, in days. </p>"}, "PublishTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that you first published this version. </p> <p>Times are in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <PERSON>. For example, \"2016-09-27T14:50Z\". </p>"}, "LastUpdateTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that you updated this version. </p> <p>Times are in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <PERSON>. For example, \"2016-09-27T14:50Z\". </p>"}, "ExpiryTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that this version is set to expire.</p> <p>Times are in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <PERSON>. For example, \"2016-09-27T14:50Z\". </p>"}}, "documentation": "<p>Information for a single version of a managed rule set. </p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "MapMatchScope": {"type": "string", "enum": ["ALL", "KEY", "VALUE"]}, "Method": {"type": "structure", "members": {}, "documentation": "<p>Inspect the HTTP method of the web request. The method indicates the type of operation that the request is asking the origin to perform. </p> <p>This is used in the <a>FieldToMatch</a> specification for some web request component types. </p> <p>JSON specification: <code>\"Method\": {}</code> </p>"}, "MetricName": {"type": "string", "max": 255, "min": 1, "pattern": "^[\\w#:\\.\\-/]+$"}, "MobileSdkRelease": {"type": "structure", "members": {"ReleaseVersion": {"shape": "VersionKeyString", "documentation": "<p>The release version. </p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of the release. </p>"}, "ReleaseNotes": {"shape": "ReleaseNotes", "documentation": "<p>Notes describing the release.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Tags that are associated with the release. </p>"}}, "documentation": "<p>Information for a release of the mobile SDK, including release notes and tags.</p> <p>The mobile SDK is not generally available. Customers who have access to the mobile SDK can use it to establish and manage WAF tokens for use in HTTP(S) requests from a mobile device to WAF. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-application-integration.html\">WAF client application integration</a> in the <i>WAF Developer Guide</i>.</p>"}, "NextMarker": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "NoneAction": {"type": "structure", "members": {}, "documentation": "<p>Specifies that WAF should do nothing. This is used for the <code>OverrideAction</code> setting on a <a>Rule</a> when the rule uses a rule group reference statement. </p> <p>This is used in the context of other settings, for example to specify values for <a>RuleAction</a> and web ACL <a>DefaultAction</a>. </p> <p>JSON specification: <code>\"None\": {}</code> </p>"}, "NotStatement": {"type": "structure", "required": ["Statement"], "members": {"Statement": {"shape": "Statement", "documentation": "<p>The statement to negate. You can use any statement that can be nested.</p>"}}, "documentation": "<p>A logical rule statement used to negate the results of another rule statement. You provide one <a>Statement</a> within the <code>NotStatement</code>.</p>"}, "OrStatement": {"type": "structure", "required": ["Statements"], "members": {"Statements": {"shape": "Statements", "documentation": "<p>The statements to combine with OR logic. You can use any statements that can be nested.</p>"}}, "documentation": "<p>A logical rule statement used to combine other rule statements with OR logic. You provide more than one <a>Statement</a> within the <code>OrStatement</code>. </p>"}, "OutputUrl": {"type": "string"}, "OverrideAction": {"type": "structure", "members": {"Count": {"shape": "CountAction", "documentation": "<p>Override the rule group evaluation result to count only. </p> <note> <p>This option is usually set to none. It does not affect how the rules in the rule group are evaluated. If you want the rules in the rule group to only count matches, do not use this and instead use the rule action override option, with <code>Count</code> action, in your rule group reference statement settings. </p> </note>"}, "None": {"shape": "NoneAction", "documentation": "<p>Don't override the rule group evaluation result. This is the most common setting.</p>"}}, "documentation": "<p>The action to use in the place of the action that results from the rule group evaluation. Set the override action to none to leave the result of the rule group alone. Set it to count to override the result to count only. </p> <p>You can only use this for rule statements that reference a rule group, like <code>RuleGroupReferenceStatement</code> and <code>ManagedRuleGroupStatement</code>. </p> <note> <p>This option is usually set to none. It does not affect how the rules in the rule group are evaluated. If you want the rules in the rule group to only count matches, do not use this and instead use the rule action override option, with <code>Count</code> action, in your rule group reference statement settings. </p> </note>"}, "OversizeHandling": {"type": "string", "enum": ["CONTINUE", "MATCH", "NO_MATCH"]}, "PaginationLimit": {"type": "integer", "max": 100, "min": 1}, "ParameterExceptionField": {"type": "string", "enum": ["WEB_ACL", "RULE_GROUP", "REGEX_PATTERN_SET", "IP_SET", "MANAGED_RULE_SET", "RULE", "EXCLUDED_RULE", "STATEMENT", "BYTE_MATCH_STATEMENT", "SQLI_MATCH_STATEMENT", "XSS_MATCH_STATEMENT", "SIZE_CONSTRAINT_STATEMENT", "GEO_MATCH_STATEMENT", "RATE_BASED_STATEMENT", "RULE_GROUP_REFERENCE_STATEMENT", "REGEX_PATTERN_REFERENCE_STATEMENT", "IP_SET_REFERENCE_STATEMENT", "MANAGED_RULE_SET_STATEMENT", "LABEL_MATCH_STATEMENT", "AND_STATEMENT", "OR_STATEMENT", "NOT_STATEMENT", "IP_ADDRESS", "IP_ADDRESS_VERSION", "FIELD_TO_MATCH", "TEXT_TRANSFORMATION", "SINGLE_QUERY_ARGUMENT", "SINGLE_HEADER", "DEFAULT_ACTION", "RULE_ACTION", "ENTITY_LIMIT", "OVERRIDE_ACTION", "SCOPE_VALUE", "RESOURCE_ARN", "RESOURCE_TYPE", "TAGS", "TAG_KEYS", "METRIC_NAME", "FIREWALL_MANAGER_STATEMENT", "FALLBACK_BEHAVIOR", "POSITION", "FORWARDED_IP_CONFIG", "IP_SET_FORWARDED_IP_CONFIG", "HEADER_NAME", "CUSTOM_REQUEST_HANDLING", "RESPONSE_CONTENT_TYPE", "CUSTOM_RESPONSE", "CUSTOM_RESPONSE_BODY", "JSON_MATCH_PATTERN", "JSON_MATCH_SCOPE", "BODY_PARSING_FALLBACK_BEHAVIOR", "LOGGING_FILTER", "FILTER_CONDITION", "EXPIRE_TIMESTAMP", "CHANGE_PROPAGATION_STATUS", "ASSOCIABLE_RESOURCE", "LOG_DESTINATION", "MANAGED_RULE_GROUP_CONFIG", "PAYLOAD_TYPE", "HEADER_MATCH_PATTERN", "COOKIE_MATCH_PATTERN", "MAP_MATCH_SCOPE", "OVERSIZE_HANDLING", "CHALLENGE_CONFIG", "TOKEN_DOMAIN", "ATP_RULE_SET_RESPONSE_INSPECTION", "ASSOCIATED_RESOURCE_TYPE", "SCOPE_DOWN", "CUSTOM_KEYS", "ACP_RULE_SET_RESPONSE_INSPECTION"]}, "ParameterExceptionParameter": {"type": "string", "min": 1}, "PasswordField": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "FieldIdentifier", "documentation": "<p>The name of the password field. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"password\": \"THE_PASSWORD\" } }</code>, the password field specification is <code>/form/password</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>password1</code>, the password field specification is <code>password1</code>.</p> </li> </ul>"}}, "documentation": "<p>The name of the field in the request payload that contains your customer's password. </p> <p>This data type is used in the <code>RequestInspection</code> and <code>RequestInspectionACFP</code> data types. </p>"}, "PayloadType": {"type": "string", "enum": ["JSON", "FORM_ENCODED"]}, "PhoneNumberField": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "FieldIdentifier", "documentation": "<p>The name of a single primary phone number field. </p> <p>How you specify the phone number fields depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field identifiers in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"primaryphoneline1\": \"THE_PHONE1\", \"primaryphoneline2\": \"THE_PHONE2\", \"primaryphoneline3\": \"THE_PHONE3\" } }</code>, the phone number field identifiers are <code>/form/primaryphoneline1</code>, <code>/form/primaryphoneline2</code>, and <code>/form/primaryphoneline3</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with input elements named <code>primaryphoneline1</code>, <code>primaryphoneline2</code>, and <code>primaryphoneline3</code>, the phone number field identifiers are <code>primaryphoneline1</code>, <code>primaryphoneline2</code>, and <code>primaryphoneline3</code>. </p> </li> </ul>"}}, "documentation": "<p>The name of a field in the request payload that contains part or all of your customer's primary phone number. </p> <p>This data type is used in the <code>RequestInspectionACFP</code> data type. </p>"}, "PhoneNumberFields": {"type": "list", "member": {"shape": "PhoneNumberField"}}, "Platform": {"type": "string", "enum": ["IOS", "ANDROID"]}, "PolicyString": {"type": "string", "max": 395000, "min": 1, "pattern": ".*\\S.*"}, "PopulationSize": {"type": "long"}, "PositionalConstraint": {"type": "string", "enum": ["EXACTLY", "STARTS_WITH", "ENDS_WITH", "CONTAINS", "CONTAINS_WORD"]}, "ProductDescription": {"type": "string", "min": 1, "pattern": ".*\\S.*"}, "ProductId": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "ProductLink": {"type": "string", "max": 2048, "min": 1, "pattern": ".*\\S.*"}, "ProductTitle": {"type": "string", "min": 1, "pattern": ".*\\S.*"}, "PublishedVersions": {"type": "map", "key": {"shape": "VersionKeyString"}, "value": {"shape": "ManagedRuleSetVersion"}}, "PutLoggingConfigurationRequest": {"type": "structure", "required": ["LoggingConfiguration"], "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p/>"}}}, "PutLoggingConfigurationResponse": {"type": "structure", "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p/>"}}}, "PutManagedRuleSetVersionsRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule set. You use this, along with the rule set ID, to identify the rule set.</p> <p>This name is assigned to the corresponding managed rule group, which your customers can access and use. </p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the managed rule set. The ID is returned in the responses to commands like <code>list</code>. You provide it to operations like <code>get</code> and <code>update</code>.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "RecommendedVersion": {"shape": "VersionKeyString", "documentation": "<p>The version of the named managed rule group that you'd like your customers to choose, from among your version offerings. </p>"}, "VersionsToPublish": {"shape": "VersionsToPublish", "documentation": "<p>The versions of the named managed rule group that you want to offer to your customers. </p>"}}}, "PutManagedRuleSetVersionsResponse": {"type": "structure", "members": {"NextLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "PutPermissionPolicyRequest": {"type": "structure", "required": ["ResourceArn", "Policy"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the <a>RuleGroup</a> to which you want to attach the policy.</p>"}, "Policy": {"shape": "PolicyString", "documentation": "<p>The policy to attach to the specified rule group. </p> <p>The policy specifications must conform to the following:</p> <ul> <li> <p>The policy must be composed using IAM Policy version 2012-10-17.</p> </li> <li> <p>The policy must include specifications for <code>Effect</code>, <code>Action</code>, and <code>Principal</code>.</p> </li> <li> <p> <code>Effect</code> must specify <code>Allow</code>.</p> </li> <li> <p> <code>Action</code> must specify <code>wafv2:CreateWebACL</code>, <code>wafv2:UpdateWebACL</code>, and <code>wafv2:PutFirewallManagerRuleGroups</code> and may optionally specify <code>wafv2:GetRuleGroup</code>. WAF rejects any extra actions or wildcard actions in the policy.</p> </li> <li> <p>The policy must not include a <code>Resource</code> parameter.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies.html\">IAM Policies</a>. </p>"}}}, "PutPermissionPolicyResponse": {"type": "structure", "members": {}}, "QueryString": {"type": "structure", "members": {}, "documentation": "<p>Inspect the query string of the web request. This is the part of a URL that appears after a <code>?</code> character, if any.</p> <p>This is used in the <a>FieldToMatch</a> specification for some web request component types. </p> <p>JSON specification: <code>\"QueryString\": {}</code> </p>"}, "RateBasedStatement": {"type": "structure", "required": ["Limit", "AggregateKeyType"], "members": {"Limit": {"shape": "RateLimit", "documentation": "<p>The limit on requests per 5-minute period for a single aggregation instance for the rate-based rule. If the rate-based statement includes a <code>ScopeDownStatement</code>, this limit is applied only to the requests that match the statement.</p> <p>Examples: </p> <ul> <li> <p>If you aggregate on just the IP address, this is the limit on requests from any single IP address. </p> </li> <li> <p>If you aggregate on the HTTP method and the query argument name \"city\", then this is the limit on requests for any single method, city pair. </p> </li> </ul>"}, "AggregateKeyType": {"shape": "RateBasedStatementAggregateKeyType", "documentation": "<p>Setting that indicates how to aggregate the request counts. </p> <note> <p>Web requests that are missing any of the components specified in the aggregation keys are omitted from the rate-based rule evaluation and handling. </p> </note> <ul> <li> <p> <code>CONSTANT</code> - Count and limit the requests that match the rate-based rule's scope-down statement. With this option, the counted requests aren't further aggregated. The scope-down statement is the only specification used. When the count of all requests that satisfy the scope-down statement goes over the limit, WAF applies the rule action to all requests that satisfy the scope-down statement. </p> <p>With this option, you must configure the <code>ScopeDownStatement</code> property. </p> </li> <li> <p> <code>CUSTOM_KEYS</code> - Aggregate the request counts using one or more web request components as the aggregate keys.</p> <p>With this option, you must specify the aggregate keys in the <code>CustomKeys</code> property. </p> <p>To aggregate on only the IP address or only the forwarded IP address, don't use custom keys. Instead, set the aggregate key type to <code>IP</code> or <code>FORWARDED_IP</code>.</p> </li> <li> <p> <code>FORWARDED_IP</code> - Aggregate the request counts on the first IP address in an HTTP header. </p> <p>With this option, you must specify the header to use in the <code>ForwardedIPConfig</code> property. </p> <p>To aggregate on a combination of the forwarded IP address with other aggregate keys, use <code>CUSTOM_KEYS</code>. </p> </li> <li> <p> <code>IP</code> - Aggregate the request counts on the IP address from the web request origin.</p> <p>To aggregate on a combination of the IP address with other aggregate keys, use <code>CUSTOM_KEYS</code>. </p> </li> </ul>"}, "ScopeDownStatement": {"shape": "Statement", "documentation": "<p>An optional nested statement that narrows the scope of the web requests that are evaluated and managed by the rate-based statement. When you use a scope-down statement, the rate-based rule only tracks and rate limits requests that match the scope-down statement. You can use any nestable <a>Statement</a> in the scope-down statement, and you can nest statements at any level, the same as you can for a rule statement. </p>"}, "ForwardedIPConfig": {"shape": "ForwardedIPConfig", "documentation": "<p>The configuration for inspecting IP addresses in an HTTP header that you specify, instead of using the IP address that's reported by the web request origin. Commonly, this is the X-Forwarded-For (XFF) header, but you can specify any header name. </p> <note> <p>If the specified header isn't present in the request, WAF doesn't apply the rule to the web request at all.</p> </note> <p>This is required if you specify a forwarded IP in the rule's aggregate key settings. </p>"}, "CustomKeys": {"shape": "RateBasedStatementCustomKeys", "documentation": "<p>Specifies the aggregate keys to use in a rate-base rule. </p>"}}, "documentation": "<p>A rate-based rule counts incoming requests and rate limits requests when they are coming at too fast a rate. The rule categorizes requests according to your aggregation criteria, collects them into aggregation instances, and counts and rate limits the requests for each instance. </p> <p>You can specify individual aggregation keys, like IP address or HTTP method. You can also specify aggregation key combinations, like IP address and HTTP method, or HTTP method, query argument, and cookie. </p> <p>Each unique set of values for the aggregation keys that you specify is a separate aggregation instance, with the value from each key contributing to the aggregation instance definition. </p> <p>For example, assume the rule evaluates web requests with the following IP address and HTTP method values: </p> <ul> <li> <p>IP address ********, HTTP method POST</p> </li> <li> <p>IP address ********, HTTP method GET</p> </li> <li> <p>IP address *********, HTTP method POST</p> </li> <li> <p>IP address ********, HTTP method GET</p> </li> </ul> <p>The rule would create different aggregation instances according to your aggregation criteria, for example: </p> <ul> <li> <p>If the aggregation criteria is just the IP address, then each individual address is an aggregation instance, and WAF counts requests separately for each. The aggregation instances and request counts for our example would be the following: </p> <ul> <li> <p>IP address ********: count 3</p> </li> <li> <p>IP address *********: count 1</p> </li> </ul> </li> <li> <p>If the aggregation criteria is HTTP method, then each individual HTTP method is an aggregation instance. The aggregation instances and request counts for our example would be the following: </p> <ul> <li> <p>HTTP method POST: count 2</p> </li> <li> <p>HTTP method GET: count 2</p> </li> </ul> </li> <li> <p>If the aggregation criteria is IP address and HTTP method, then each IP address and each HTTP method would contribute to the combined aggregation instance. The aggregation instances and request counts for our example would be the following: </p> <ul> <li> <p>IP address ********, HTTP method POST: count 1</p> </li> <li> <p>IP address ********, HTTP method GET: count 2</p> </li> <li> <p>IP address *********, HTTP method POST: count 1</p> </li> </ul> </li> </ul> <p>For any n-tuple of aggregation keys, each unique combination of values for the keys defines a separate aggregation instance, which WAF counts and rate-limits individually. </p> <p>You can optionally nest another statement inside the rate-based statement, to narrow the scope of the rule so that it only counts and rate limits requests that match the nested statement. You can use this nested scope-down statement in conjunction with your aggregation key specifications or you can just count and rate limit all requests that match the scope-down statement, without additional aggregation. When you choose to just manage all requests that match a scope-down statement, the aggregation instance is singular for the rule. </p> <p>You cannot nest a <code>RateBasedStatement</code> inside another statement, for example inside a <code>NotStatement</code> or <code>OrStatement</code>. You can define a <code>RateBasedStatement</code> inside a web ACL and inside a rule group. </p> <p>For additional information about the options, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rate-based-rules.html\">Rate limiting web requests using rate-based rules</a> in the <i>WAF Developer Guide</i>. </p> <p>If you only aggregate on the individual IP address or forwarded IP address, you can retrieve the list of IP addresses that WAF is currently rate limiting for a rule through the API call <code>GetRateBasedStatementManagedKeys</code>. This option is not available for other aggregation configurations.</p> <p>WAF tracks and manages web requests separately for each instance of a rate-based rule that you use. For example, if you provide the same rate-based rule settings in two web ACLs, each of the two rule statements represents a separate instance of the rate-based rule and gets its own tracking and management by WAF. If you define a rate-based rule inside a rule group, and then use that rule group in multiple places, each use creates a separate instance of the rate-based rule that gets its own tracking and management by WAF. </p>"}, "RateBasedStatementAggregateKeyType": {"type": "string", "enum": ["IP", "FORWARDED_IP", "CUSTOM_KEYS", "CONSTANT"]}, "RateBasedStatementCustomKey": {"type": "structure", "members": {"Header": {"shape": "RateLimitHeader", "documentation": "<p>Use the value of a header in the request as an aggregate key. Each distinct value in the header contributes to the aggregation instance. If you use a single header as your custom key, then each value fully defines an aggregation instance. </p>"}, "Cookie": {"shape": "RateLimitCookie", "documentation": "<p>Use the value of a cookie in the request as an aggregate key. Each distinct value in the cookie contributes to the aggregation instance. If you use a single cookie as your custom key, then each value fully defines an aggregation instance. </p>"}, "QueryArgument": {"shape": "RateLimitQueryArgument", "documentation": "<p>Use the specified query argument as an aggregate key. Each distinct value for the named query argument contributes to the aggregation instance. If you use a single query argument as your custom key, then each value fully defines an aggregation instance. </p>"}, "QueryString": {"shape": "RateLimitQueryString", "documentation": "<p>Use the request's query string as an aggregate key. Each distinct string contributes to the aggregation instance. If you use just the query string as your custom key, then each string fully defines an aggregation instance. </p>"}, "HTTPMethod": {"shape": "RateLimitHTTPMethod", "documentation": "<p>Use the request's HTTP method as an aggregate key. Each distinct HTTP method contributes to the aggregation instance. If you use just the HTTP method as your custom key, then each method fully defines an aggregation instance. </p>"}, "ForwardedIP": {"shape": "RateLimitForwardedIP", "documentation": "<p>Use the first IP address in an HTTP header as an aggregate key. Each distinct forwarded IP address contributes to the aggregation instance.</p> <p>When you specify an IP or forwarded IP in the custom key settings, you must also specify at least one other key to use. You can aggregate on only the forwarded IP address by specifying <code>FORWARDED_IP</code> in your rate-based statement's <code>AggregateKeyType</code>. </p> <p>With this option, you must specify the header to use in the rate-based rule's <code>ForwardedIPConfig</code> property. </p>"}, "IP": {"shape": "RateLimitIP", "documentation": "<p>Use the request's originating IP address as an aggregate key. Each distinct IP address contributes to the aggregation instance.</p> <p>When you specify an IP or forwarded IP in the custom key settings, you must also specify at least one other key to use. You can aggregate on only the IP address by specifying <code>IP</code> in your rate-based statement's <code>AggregateKeyType</code>. </p>"}, "LabelNamespace": {"shape": "RateLimitLabelNamespace", "documentation": "<p>Use the specified label namespace as an aggregate key. Each distinct fully qualified label name that has the specified label namespace contributes to the aggregation instance. If you use just one label namespace as your custom key, then each label name fully defines an aggregation instance. </p> <p>This uses only labels that have been added to the request by rules that are evaluated before this rate-based rule in the web ACL. </p> <p>For information about label namespaces and names, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rule-label-requirements.html\">Label syntax and naming requirements</a> in the <i>WAF Developer Guide</i>.</p>"}, "UriPath": {"shape": "RateLimitUriPath", "documentation": "<p>Use the request's URI path as an aggregate key. Each distinct URI path contributes to the aggregation instance. If you use just the URI path as your custom key, then each URI path fully defines an aggregation instance. </p>"}}, "documentation": "<p>Specifies a single custom aggregate key for a rate-base rule. </p> <note> <p>Web requests that are missing any of the components specified in the aggregation keys are omitted from the rate-based rule evaluation and handling. </p> </note>"}, "RateBasedStatementCustomKeys": {"type": "list", "member": {"shape": "RateBasedStatementCustomKey"}, "max": 5, "min": 1}, "RateBasedStatementManagedKeysIPSet": {"type": "structure", "members": {"IPAddressVersion": {"shape": "IPAddressVersion", "documentation": "<p>The version of the IP addresses, either <code>IPV4</code> or <code>IPV6</code>. </p>"}, "Addresses": {"shape": "IPAddresses", "documentation": "<p>The IP addresses that are currently blocked.</p>"}}, "documentation": "<p>The set of IP addresses that are currently blocked for a <a>RateBasedStatement</a>. This is only available for rate-based rules that aggregate on just the IP address, with the <code>AggregateKeyType</code> set to <code>IP</code> or <code>FORWARDED_IP</code>.</p> <p>A rate-based rule applies its rule action to requests from IP addresses that are in the rule's managed keys list and that match the rule's scope-down statement. When a rule has no scope-down statement, it applies the action to all requests from the IP addresses that are in the list. The rule applies its rule action to rate limit the matching requests. The action is usually Block but it can be any valid rule action except for Allow. </p> <p>The maximum number of IP addresses that can be rate limited by a single rate-based rule instance is 10,000. If more than 10,000 addresses exceed the rate limit, WAF limits those with the highest rates. </p>"}, "RateLimit": {"type": "long", "max": 2000000000, "min": 100}, "RateLimitCookie": {"type": "structure", "required": ["Name", "TextTransformations"], "members": {"Name": {"shape": "FieldToMatchData", "documentation": "<p>The name of the cookie to use. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>Specifies a cookie as an aggregate key for a rate-based rule. Each distinct value in the cookie contributes to the aggregation instance. If you use a single cookie as your custom key, then each value fully defines an aggregation instance. </p>"}, "RateLimitForwardedIP": {"type": "structure", "members": {}, "documentation": "<p>Specifies the first IP address in an HTTP header as an aggregate key for a rate-based rule. Each distinct forwarded IP address contributes to the aggregation instance.</p> <p>This setting is used only in the <code>RateBasedStatementCustomKey</code> specification of a rate-based rule statement. When you specify an IP or forwarded IP in the custom key settings, you must also specify at least one other key to use. You can aggregate on only the forwarded IP address by specifying <code>FORWARDED_IP</code> in your rate-based statement's <code>AggregateKeyType</code>. </p> <p>This data type supports using the forwarded IP address in the web request aggregation for a rate-based rule, in <code>RateBasedStatementCustomKey</code>. The JSON specification for using the forwarded IP address doesn't explicitly use this data type. </p> <p>JSON specification: <code>\"ForwardedIP\": {}</code> </p> <p>When you use this specification, you must also configure the forwarded IP address in the rate-based statement's <code>ForwardedIPConfig</code>. </p>"}, "RateLimitHTTPMethod": {"type": "structure", "members": {}, "documentation": "<p>Specifies the request's HTTP method as an aggregate key for a rate-based rule. Each distinct HTTP method contributes to the aggregation instance. If you use just the HTTP method as your custom key, then each method fully defines an aggregation instance. </p> <p>JSON specification: <code>\"RateLimitHTTPMethod\": {}</code> </p>"}, "RateLimitHeader": {"type": "structure", "required": ["Name", "TextTransformations"], "members": {"Name": {"shape": "FieldToMatchData", "documentation": "<p>The name of the header to use. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>Specifies a header as an aggregate key for a rate-based rule. Each distinct value in the header contributes to the aggregation instance. If you use a single header as your custom key, then each value fully defines an aggregation instance. </p>"}, "RateLimitIP": {"type": "structure", "members": {}, "documentation": "<p>Specifies the IP address in the web request as an aggregate key for a rate-based rule. Each distinct IP address contributes to the aggregation instance. </p> <p>This setting is used only in the <code>RateBasedStatementCustomKey</code> specification of a rate-based rule statement. To use this in the custom key settings, you must specify at least one other key to use, along with the IP address. To aggregate on only the IP address, in your rate-based statement's <code>AggregateKeyType</code>, specify <code>IP</code>.</p> <p>JSON specification: <code>\"RateLimitIP\": {}</code> </p>"}, "RateLimitLabelNamespace": {"type": "structure", "required": ["Namespace"], "members": {"Namespace": {"shape": "LabelNamespace", "documentation": "<p>The namespace to use for aggregation. </p>"}}, "documentation": "<p>Specifies a label namespace to use as an aggregate key for a rate-based rule. Each distinct fully qualified label name that has the specified label namespace contributes to the aggregation instance. If you use just one label namespace as your custom key, then each label name fully defines an aggregation instance. </p> <p>This uses only labels that have been added to the request by rules that are evaluated before this rate-based rule in the web ACL. </p> <p>For information about label namespaces and names, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rule-label-requirements.html\">Label syntax and naming requirements</a> in the <i>WAF Developer Guide</i>.</p>"}, "RateLimitQueryArgument": {"type": "structure", "required": ["Name", "TextTransformations"], "members": {"Name": {"shape": "FieldToMatchData", "documentation": "<p>The name of the query argument to use. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>Specifies a query argument in the request as an aggregate key for a rate-based rule. Each distinct value for the named query argument contributes to the aggregation instance. If you use a single query argument as your custom key, then each value fully defines an aggregation instance. </p>"}, "RateLimitQueryString": {"type": "structure", "required": ["TextTransformations"], "members": {"TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>Specifies the request's query string as an aggregate key for a rate-based rule. Each distinct string contributes to the aggregation instance. If you use just the query string as your custom key, then each string fully defines an aggregation instance. </p>"}, "RateLimitUriPath": {"type": "structure", "required": ["TextTransformations"], "members": {"TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>Specifies the request's URI path as an aggregate key for a rate-based rule. Each distinct URI path contributes to the aggregation instance. If you use just the URI path as your custom key, then each URI path fully defines an aggregation instance. </p>"}, "RedactedFields": {"type": "list", "member": {"shape": "FieldToMatch"}, "max": 100}, "Regex": {"type": "structure", "members": {"RegexString": {"shape": "RegexPatternString", "documentation": "<p>The string representing the regular expression.</p>"}}, "documentation": "<p>A single regular expression. This is used in a <a>RegexPatternSet</a>.</p>"}, "RegexMatchStatement": {"type": "structure", "required": ["RegexString", "FieldToMatch", "TextTransformations"], "members": {"RegexString": {"shape": "RegexPatternString", "documentation": "<p>The string representing the regular expression.</p>"}, "FieldToMatch": {"shape": "FieldToMatch", "documentation": "<p>The part of the web request that you want WAF to inspect. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>A rule statement used to search web request components for a match against a single regular expression. </p>"}, "RegexPatternSet": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the set. You cannot change the name after you create the set.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the set that helps with identification. </p>"}, "RegularExpressionList": {"shape": "RegularExpressionList", "documentation": "<p>The regular expression patterns in the set.</p>"}}, "documentation": "<p>Contains one or more regular expressions. </p> <p>WAF assigns an ARN to each <code>RegexPatternSet</code> that you create. To use a set in a rule, you provide the ARN to the <a>Rule</a> statement <a>RegexPatternSetReferenceStatement</a>. </p>"}, "RegexPatternSetReferenceStatement": {"type": "structure", "required": ["ARN", "FieldToMatch", "TextTransformations"], "members": {"ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the <a>RegexPatternSet</a> that this statement references.</p>"}, "FieldToMatch": {"shape": "FieldToMatch", "documentation": "<p>The part of the web request that you want WAF to inspect. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>A rule statement used to search web request components for matches with regular expressions. To use this, create a <a>RegexPatternSet</a> that specifies the expressions that you want to detect, then use the ARN of that set in this statement. A web request matches the pattern set rule statement if the request component matches any of the patterns in the set. To create a regex pattern set, see <a>CreateRegexPatternSet</a>.</p> <p>Each regex pattern set rule statement references a regex pattern set. You create and maintain the set independent of your rules. This allows you to use the single set in multiple rules. When you update the referenced set, WAF automatically updates all rules that reference it.</p>"}, "RegexPatternSetSummaries": {"type": "list", "member": {"shape": "RegexPatternSetSummary"}}, "RegexPatternSetSummary": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the data type instance. You cannot change the name after you create the instance.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the set that helps with identification. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}}, "documentation": "<p>High-level information about a <a>RegexPatternSet</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage a <code>RegexPatternSet</code>, and the ARN, that you provide to the <a>RegexPatternSetReferenceStatement</a> to use the pattern set in a <a>Rule</a>.</p>"}, "RegexPatternString": {"type": "string", "max": 512, "min": 1, "pattern": ".*"}, "RegistrationPagePathString": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "RegularExpressionList": {"type": "list", "member": {"shape": "Regex"}}, "ReleaseNotes": {"type": "string"}, "ReleaseSummaries": {"type": "list", "member": {"shape": "ReleaseSummary"}}, "ReleaseSummary": {"type": "structure", "members": {"ReleaseVersion": {"shape": "VersionKeyString", "documentation": "<p>The release version. </p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of the release. </p>"}}, "documentation": "<p>High level information for an SDK release. </p>"}, "RequestBody": {"type": "map", "key": {"shape": "AssociatedResourceType"}, "value": {"shape": "RequestBodyAssociatedResourceTypeConfig"}}, "RequestBodyAssociatedResourceTypeConfig": {"type": "structure", "required": ["DefaultSizeInspectionLimit"], "members": {"DefaultSizeInspectionLimit": {"shape": "SizeInspectionLimit", "documentation": "<p>Specifies the maximum size of the web request body component that an associated CloudFront distribution should send to WAF for inspection. This applies to statements in the web ACL that inspect the body or JSON body. </p> <p>Default: <code>16 KB (16,384 bytes)</code> </p>"}}, "documentation": "<p>Customizes the maximum size of the request body that your protected CloudFront distributions forward to WAF for inspection. The default size is 16 KB (16,384 bytes). </p> <note> <p>You are charged additional fees when your protected resources forward body sizes that are larger than the default. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note> <p>This is used in the <code>AssociationConfig</code> of the web ACL. </p>"}, "RequestInspection": {"type": "structure", "required": ["PayloadType", "UsernameField", "PasswordField"], "members": {"PayloadType": {"shape": "PayloadType", "documentation": "<p>The payload type for your login endpoint, either JSON or form encoded.</p>"}, "UsernameField": {"shape": "UsernameField", "documentation": "<p>The name of the field in the request payload that contains your customer's username. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"username\": \"THE_USERNAME\" } }</code>, the username field specification is <code>/form/username</code>. </p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>username1</code>, the username field specification is <code>username1</code> </p> </li> </ul>"}, "PasswordField": {"shape": "PasswordField", "documentation": "<p>The name of the field in the request payload that contains your customer's password. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"password\": \"THE_PASSWORD\" } }</code>, the password field specification is <code>/form/password</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>password1</code>, the password field specification is <code>password1</code>.</p> </li> </ul>"}}, "documentation": "<p>The criteria for inspecting login requests, used by the ATP rule group to validate credentials usage. </p> <p>This is part of the <code>AWSManagedRulesATPRuleSet</code> configuration in <code>ManagedRuleGroupConfig</code>.</p> <p>In these settings, you specify how your application accepts login attempts by providing the request payload type and the names of the fields within the request body where the username and password are provided. </p>"}, "RequestInspectionACFP": {"type": "structure", "required": ["PayloadType"], "members": {"PayloadType": {"shape": "PayloadType", "documentation": "<p>The payload type for your account creation endpoint, either JSON or form encoded.</p>"}, "UsernameField": {"shape": "UsernameField", "documentation": "<p>The name of the field in the request payload that contains your customer's username. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"username\": \"THE_USERNAME\" } }</code>, the username field specification is <code>/form/username</code>. </p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>username1</code>, the username field specification is <code>username1</code> </p> </li> </ul>"}, "PasswordField": {"shape": "PasswordField", "documentation": "<p>The name of the field in the request payload that contains your customer's password. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"password\": \"THE_PASSWORD\" } }</code>, the password field specification is <code>/form/password</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>password1</code>, the password field specification is <code>password1</code>.</p> </li> </ul>"}, "EmailField": {"shape": "EmailField", "documentation": "<p>The name of the field in the request payload that contains your customer's email. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"email\": \"THE_EMAIL\" } }</code>, the email field specification is <code>/form/email</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>email1</code>, the email field specification is <code>email1</code>.</p> </li> </ul>"}, "PhoneNumberFields": {"shape": "PhoneNumberFields", "documentation": "<p>The names of the fields in the request payload that contain your customer's primary phone number. </p> <p>Order the phone number fields in the array exactly as they are ordered in the request payload. </p> <p>How you specify the phone number fields depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field identifiers in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"primaryphoneline1\": \"THE_PHONE1\", \"primaryphoneline2\": \"THE_PHONE2\", \"primaryphoneline3\": \"THE_PHONE3\" } }</code>, the phone number field identifiers are <code>/form/primaryphoneline1</code>, <code>/form/primaryphoneline2</code>, and <code>/form/primaryphoneline3</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with input elements named <code>primaryphoneline1</code>, <code>primaryphoneline2</code>, and <code>primaryphoneline3</code>, the phone number field identifiers are <code>primaryphoneline1</code>, <code>primaryphoneline2</code>, and <code>primaryphoneline3</code>. </p> </li> </ul>"}, "AddressFields": {"shape": "AddressFields", "documentation": "<p>The names of the fields in the request payload that contain your customer's primary physical address. </p> <p>Order the address fields in the array exactly as they are ordered in the request payload. </p> <p>How you specify the address fields depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field identifiers in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"primaryaddressline1\": \"THE_ADDRESS1\", \"primaryaddressline2\": \"THE_ADDRESS2\", \"primaryaddressline3\": \"THE_ADDRESS3\" } }</code>, the address field idenfiers are <code>/form/primaryaddressline1</code>, <code>/form/primaryaddressline2</code>, and <code>/form/primaryaddressline3</code>.</p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with input elements named <code>primaryaddressline1</code>, <code>primaryaddressline2</code>, and <code>primaryaddressline3</code>, the address fields identifiers are <code>primaryaddressline1</code>, <code>primaryaddressline2</code>, and <code>primaryaddressline3</code>. </p> </li> </ul>"}}, "documentation": "<p>The criteria for inspecting account creation requests, used by the ACFP rule group to validate and track account creation attempts. </p> <p>This is part of the <code>AWSManagedRulesACFPRuleSet</code> configuration in <code>ManagedRuleGroupConfig</code>.</p> <p>In these settings, you specify how your application accepts account creation attempts by providing the request payload type and the names of the fields within the request body where the username, password, email, and primary address and phone number fields are provided. </p>"}, "ResourceArn": {"type": "string", "max": 2048, "min": 20, "pattern": ".*\\S.*"}, "ResourceArns": {"type": "list", "member": {"shape": "ResourceArn"}}, "ResourceType": {"type": "string", "enum": ["APPLICATION_LOAD_BALANCER", "API_GATEWAY", "APPSYNC", "COGNITO_USER_POOL", "APP_RUNNER_SERVICE", "VERIFIED_ACCESS_INSTANCE"]}, "ResponseCode": {"type": "integer"}, "ResponseContent": {"type": "string", "max": 10240, "min": 1, "pattern": "[\\s\\S]*"}, "ResponseContentType": {"type": "string", "enum": ["TEXT_PLAIN", "TEXT_HTML", "APPLICATION_JSON"]}, "ResponseInspection": {"type": "structure", "members": {"StatusCode": {"shape": "ResponseInspectionStatusCode", "documentation": "<p>Configures inspection of the response status code for success and failure indicators. </p>"}, "Header": {"shape": "ResponseInspectionHeader", "documentation": "<p>Configures inspection of the response header for success and failure indicators. </p>"}, "BodyContains": {"shape": "ResponseInspectionBodyContains", "documentation": "<p>Configures inspection of the response body for success and failure indicators. WAF can inspect the first 65,536 bytes (64 KB) of the response body. </p>"}, "Json": {"shape": "ResponseInspectionJson", "documentation": "<p>Configures inspection of the response JSON for success and failure indicators. WAF can inspect the first 65,536 bytes (64 KB) of the response JSON. </p>"}}, "documentation": "<p>The criteria for inspecting responses to login requests and account creation requests, used by the ATP and ACFP rule groups to track login and account creation success and failure rates. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note> <p>The rule groups evaluates the responses that your protected resources send back to client login and account creation attempts, keeping count of successful and failed attempts from each IP address and client session. Using this information, the rule group labels and mitigates requests from client sessions and IP addresses with too much suspicious activity in a short amount of time. </p> <p>This is part of the <code>AWSManagedRulesATPRuleSet</code> and <code>AWSManagedRulesACFPRuleSet</code> configurations in <code>ManagedRuleGroupConfig</code>.</p> <p>Enable response inspection by configuring exactly one component of the response to inspect, for example, <code>Header</code> or <code>StatusCode</code>. You can't configure more than one component for inspection. If you don't configure any of the response inspection options, response inspection is disabled. </p>"}, "ResponseInspectionBodyContains": {"type": "structure", "required": ["SuccessStrings", "FailureStrings"], "members": {"SuccessStrings": {"shape": "ResponseInspectionBodyContainsSuccessStrings", "documentation": "<p>Strings in the body of the response that indicate a successful login or account creation attempt. To be counted as a success, the string can be anywhere in the body and must be an exact match, including case. Each string must be unique among the success and failure strings. </p> <p>JSON examples: <code>\"SuccessStrings\": [ \"Login successful\" ]</code> and <code>\"SuccessStrings\": [ \"Account creation successful\", \"Welcome to our site!\" ]</code> </p>"}, "FailureStrings": {"shape": "ResponseInspectionBodyContainsFailureStrings", "documentation": "<p>Strings in the body of the response that indicate a failed login or account creation attempt. To be counted as a failure, the string can be anywhere in the body and must be an exact match, including case. Each string must be unique among the success and failure strings. </p> <p>JSON example: <code>\"FailureStrings\": [ \"Request failed\" ]</code> </p>"}}, "documentation": "<p>Configures inspection of the response body. WAF can inspect the first 65,536 bytes (64 KB) of the response body. This is part of the <code>ResponseInspection</code> configuration for <code>AWSManagedRulesATPRuleSet</code> and <code>AWSManagedRulesACFPRuleSet</code>. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note>"}, "ResponseInspectionBodyContainsFailureStrings": {"type": "list", "member": {"shape": "FailureValue"}, "max": 5, "min": 1}, "ResponseInspectionBodyContainsSuccessStrings": {"type": "list", "member": {"shape": "SuccessValue"}, "max": 5, "min": 1}, "ResponseInspectionHeader": {"type": "structure", "required": ["Name", "SuccessValues", "FailureV<PERSON>ues"], "members": {"Name": {"shape": "ResponseInspectionHeaderName", "documentation": "<p>The name of the header to match against. The name must be an exact match, including case.</p> <p>JSON example: <code>\"Name\": [ \"RequestResult\" ]</code> </p>"}, "SuccessValues": {"shape": "ResponseInspectionHeaderSuccessValues", "documentation": "<p>Values in the response header with the specified name that indicate a successful login or account creation attempt. To be counted as a success, the value must be an exact match, including case. Each value must be unique among the success and failure values. </p> <p>JSON examples: <code>\"SuccessValues\": [ \"LoginPassed\", \"Successful login\" ]</code> and <code>\"SuccessValues\": [ \"AccountCreated\", \"Successful account creation\" ]</code> </p>"}, "FailureValues": {"shape": "ResponseInspectionHeaderFailureValues", "documentation": "<p>Values in the response header with the specified name that indicate a failed login or account creation attempt. To be counted as a failure, the value must be an exact match, including case. Each value must be unique among the success and failure values. </p> <p>JSON examples: <code>\"FailureValues\": [ \"LoginFailed\", \"Failed login\" ]</code> and <code>\"FailureValues\": [ \"AccountCreationFailed\" ]</code> </p>"}}, "documentation": "<p>Configures inspection of the response header. This is part of the <code>ResponseInspection</code> configuration for <code>AWSManagedRulesATPRuleSet</code> and <code>AWSManagedRulesACFPRuleSet</code>. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note>"}, "ResponseInspectionHeaderFailureValues": {"type": "list", "member": {"shape": "FailureValue"}, "max": 3, "min": 1}, "ResponseInspectionHeaderName": {"type": "string", "max": 200, "min": 1, "pattern": ".*\\S.*"}, "ResponseInspectionHeaderSuccessValues": {"type": "list", "member": {"shape": "SuccessValue"}, "max": 3, "min": 1}, "ResponseInspectionJson": {"type": "structure", "required": ["Identifier", "SuccessValues", "FailureV<PERSON>ues"], "members": {"Identifier": {"shape": "FieldIdentifier", "documentation": "<p>The identifier for the value to match against in the JSON. The identifier must be an exact match, including case.</p> <p>JSON examples: <code>\"Identifier\": [ \"/login/success\" ]</code> and <code>\"Identifier\": [ \"/sign-up/success\" ]</code> </p>"}, "SuccessValues": {"shape": "ResponseInspectionJsonSuccessValues", "documentation": "<p>Values for the specified identifier in the response JSON that indicate a successful login or account creation attempt. To be counted as a success, the value must be an exact match, including case. Each value must be unique among the success and failure values. </p> <p>JSON example: <code>\"SuccessValues\": [ \"True\", \"Succeeded\" ]</code> </p>"}, "FailureValues": {"shape": "ResponseInspectionJsonFailureValues", "documentation": "<p>Values for the specified identifier in the response JSON that indicate a failed login or account creation attempt. To be counted as a failure, the value must be an exact match, including case. Each value must be unique among the success and failure values. </p> <p>JSON example: <code>\"FailureValues\": [ \"False\", \"Failed\" ]</code> </p>"}}, "documentation": "<p>Configures inspection of the response JSON. WAF can inspect the first 65,536 bytes (64 KB) of the response JSON. This is part of the <code>ResponseInspection</code> configuration for <code>AWSManagedRulesATPRuleSet</code> and <code>AWSManagedRulesACFPRuleSet</code>. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note>"}, "ResponseInspectionJsonFailureValues": {"type": "list", "member": {"shape": "FailureValue"}, "max": 5, "min": 1}, "ResponseInspectionJsonSuccessValues": {"type": "list", "member": {"shape": "SuccessValue"}, "max": 5, "min": 1}, "ResponseInspectionStatusCode": {"type": "structure", "required": ["SuccessCodes", "FailureCodes"], "members": {"SuccessCodes": {"shape": "ResponseInspectionStatusCodeSuccessCodes", "documentation": "<p>Status codes in the response that indicate a successful login or account creation attempt. To be counted as a success, the response status code must match one of these. Each code must be unique among the success and failure status codes. </p> <p>JSON example: <code>\"SuccessCodes\": [ 200, 201 ]</code> </p>"}, "FailureCodes": {"shape": "ResponseInspectionStatusCodeFailureCodes", "documentation": "<p>Status codes in the response that indicate a failed login or account creation attempt. To be counted as a failure, the response status code must match one of these. Each code must be unique among the success and failure status codes. </p> <p>JSON example: <code>\"FailureCodes\": [ 400, 404 ]</code> </p>"}}, "documentation": "<p>Configures inspection of the response status code. This is part of the <code>ResponseInspection</code> configuration for <code>AWSManagedRulesATPRuleSet</code> and <code>AWSManagedRulesACFPRuleSet</code>. </p> <note> <p>Response inspection is available only in web ACLs that protect Amazon CloudFront distributions.</p> </note>"}, "ResponseInspectionStatusCodeFailureCodes": {"type": "list", "member": {"shape": "FailureCode"}, "max": 10, "min": 1}, "ResponseInspectionStatusCodeSuccessCodes": {"type": "list", "member": {"shape": "SuccessCode"}, "max": 10, "min": 1}, "ResponseStatusCode": {"type": "integer", "max": 599, "min": 200}, "Rule": {"type": "structure", "required": ["Name", "Priority", "Statement", "VisibilityConfig"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule. </p> <p>If you change the name of a <code>Rule</code> after you create it and you want the rule's metric name to reflect the change, update the metric name in the rule's <code>VisibilityConfig</code> settings. WAF doesn't automatically update the metric name when you update the rule name. </p>"}, "Priority": {"shape": "RulePriority", "documentation": "<p>If you define more than one <code>Rule</code> in a <code>WebACL</code>, WAF evaluates each request against the <code>Rules</code> in order based on the value of <code>Priority</code>. WAF processes rules with lower priority first. The priorities don't need to be consecutive, but they must all be different.</p>"}, "Statement": {"shape": "Statement", "documentation": "<p>The WAF processing statement for the rule, for example <a>ByteMatchStatement</a> or <a>SizeConstraintStatement</a>. </p>"}, "Action": {"shape": "RuleAction", "documentation": "<p>The action that WAF should take on a web request when it matches the rule statement. Settings at the web ACL level can override the rule action setting. </p> <p>This is used only for rules whose statements do not reference a rule group. Rule statements that reference a rule group include <code>RuleGroupReferenceStatement</code> and <code>ManagedRuleGroupStatement</code>. </p> <p>You must specify either this <code>Action</code> setting or the rule <code>OverrideAction</code> setting, but not both:</p> <ul> <li> <p>If the rule statement does not reference a rule group, use this rule action setting and not the rule override action setting. </p> </li> <li> <p>If the rule statement references a rule group, use the override action setting and not this action setting. </p> </li> </ul>"}, "OverrideAction": {"shape": "OverrideAction", "documentation": "<p>The action to use in the place of the action that results from the rule group evaluation. Set the override action to none to leave the result of the rule group alone. Set it to count to override the result to count only. </p> <p>You can only use this for rule statements that reference a rule group, like <code>RuleGroupReferenceStatement</code> and <code>ManagedRuleGroupStatement</code>. </p> <note> <p>This option is usually set to none. It does not affect how the rules in the rule group are evaluated. If you want the rules in the rule group to only count matches, do not use this and instead use the rule action override option, with <code>Count</code> action, in your rule group reference statement settings. </p> </note>"}, "RuleLabels": {"shape": "Labels", "documentation": "<p>Labels to apply to web requests that match the rule match statement. WAF applies fully qualified labels to matching web requests. A fully qualified label is the concatenation of a label namespace and a rule label. The rule's rule group or web ACL defines the label namespace. </p> <p>Rules that run after this rule in the web ACL can match against these labels using a <code>LabelMatchStatement</code>.</p> <p>For each label, provide a case-sensitive string containing optional namespaces and a label name, according to the following guidelines:</p> <ul> <li> <p>Separate each component of the label with a colon. </p> </li> <li> <p>Each namespace or name can have up to 128 characters.</p> </li> <li> <p>You can specify up to 5 namespaces in a label.</p> </li> <li> <p>Don't use the following reserved words in your label specification: <code>aws</code>, <code>waf</code>, <code>managed</code>, <code>rulegroup</code>, <code>webacl</code>, <code>regexpatternset</code>, or <code>ipset</code>.</p> </li> </ul> <p>For example, <code>myLabelName</code> or <code>nameSpace1:nameSpace2:myLabelName</code>. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p> <p>If you change the name of a <code>Rule</code> after you create it and you want the rule's metric name to reflect the change, update the metric name as well. WAF doesn't automatically update the metric name. </p>"}, "CaptchaConfig": {"shape": "CaptchaConfig", "documentation": "<p>Specifies how WAF should handle <code>CAPTCHA</code> evaluations. If you don't specify this, WAF uses the <code>CAPTCHA</code> configuration that's defined for the web ACL. </p>"}, "ChallengeConfig": {"shape": "ChallengeConfig", "documentation": "<p>Specifies how WAF should handle <code>Challenge</code> evaluations. If you don't specify this, WAF uses the challenge configuration that's defined for the web ACL. </p>"}}, "documentation": "<p>A single rule, which you can use in a <a>WebACL</a> or <a>RuleGroup</a> to identify web requests that you want to manage in some way. Each rule includes one top-level <a>Statement</a> that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "RuleAction": {"type": "structure", "members": {"Block": {"shape": "BlockAction", "documentation": "<p>Instructs WAF to block the web request.</p>"}, "Allow": {"shape": "AllowAction", "documentation": "<p>Instructs WAF to allow the web request.</p>"}, "Count": {"shape": "CountAction", "documentation": "<p>Instructs WAF to count the web request and then continue evaluating the request using the remaining rules in the web ACL.</p>"}, "Captcha": {"shape": "CaptchaAction", "documentation": "<p>Instructs WAF to run a <code>CAPTCHA</code> check against the web request.</p>"}, "Challenge": {"shape": "ChallengeAction", "documentation": "<p>Instructs WAF to run a <code>Challenge</code> check against the web request.</p>"}}, "documentation": "<p>The action that WAF should take on a web request when it matches a rule's statement. Settings at the web ACL level can override the rule action setting. </p>"}, "RuleActionOverride": {"type": "structure", "required": ["Name", "ActionToUse"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule to override.</p>"}, "ActionToUse": {"shape": "RuleAction", "documentation": "<p>The override action to use, in place of the configured action of the rule in the rule group. </p>"}}, "documentation": "<p>Action setting to use in the place of a rule action that is configured inside the rule group. You specify one override for each rule whose action you want to change. </p> <p>You can use overrides for testing, for example you can override all of rule actions to <code>Count</code> and then monitor the resulting count metrics to understand how the rule group would handle your web traffic. You can also permanently override some or all actions, to modify how the rule group manages your web traffic.</p>"}, "RuleActionOverrides": {"type": "list", "member": {"shape": "RuleActionOverride"}, "max": 100, "min": 1}, "RuleGroup": {"type": "structure", "required": ["Name", "Id", "Capacity", "ARN", "VisibilityConfig"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule group. You cannot change the name of a rule group after you create it.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the rule group. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Capacity": {"shape": "CapacityUnit", "documentation": "<p>The web ACL capacity units (WCUs) required for this rule group.</p> <p>When you create your own rule group, you define this, and you cannot change it after creation. When you add or modify the rules in a rule group, WAF enforces this limit. You can check the capacity for a set of rules using <a>CheckCapacity</a>.</p> <p>WAF uses WCUs to calculate and control the operating resources that are used to run your rules, rule groups, and web ACLs. WAF calculates capacity differently for each rule type, to reflect the relative cost of each rule. Simple rules that cost little to run use fewer WCUs than more complex rules that use more processing power. Rule group capacity is fixed at creation, which helps users plan their web ACL WCU usage when they use a rule group. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-waf-capacity-units.html\">WAF web ACL capacity units (WCU)</a> in the <i>WAF Developer Guide</i>. </p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the rule group that helps with identification. </p>"}, "Rules": {"shape": "Rules", "documentation": "<p>The <a>Rule</a> statements used to identify the web requests that you want to manage. Each rule includes one top-level statement that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "LabelNamespace": {"shape": "LabelName", "documentation": "<p>The label namespace prefix for this rule group. All labels added by rules in this rule group have this prefix. </p> <ul> <li> <p>The syntax for the label namespace prefix for your rule groups is the following: </p> <p> <code>awswaf:&lt;account ID&gt;:rulegroup:&lt;rule group name&gt;:</code> </p> </li> <li> <p>When a rule with a label matches a web request, WAF adds the fully qualified label to the request. A fully qualified label is made up of the label namespace from the rule group or web ACL where the rule is defined and the label from the rule, separated by a colon: </p> <p> <code>&lt;label namespace&gt;:&lt;label from rule&gt;</code> </p> </li> </ul>"}, "CustomResponseBodies": {"shape": "CustomResponseBodies", "documentation": "<p>A map of custom response keys and content bodies. When you create a rule with a block action, you can send a custom response to the web request. You define these for the rule group, and then use them in the rules that you define in the rule group. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}, "AvailableLabels": {"shape": "LabelSummaries", "documentation": "<p>The labels that one or more rules in this rule group add to matching web requests. These labels are defined in the <code>RuleLabels</code> for a <a>Rule</a>.</p>"}, "ConsumedLabels": {"shape": "LabelSummaries", "documentation": "<p>The labels that one or more rules in this rule group match against in label match statements. These labels are defined in a <code>LabelMatchStatement</code> specification, in the <a>Statement</a> definition of a rule. </p>"}}, "documentation": "<p> A rule group defines a collection of rules to inspect and control web requests that you can use in a <a>WebACL</a>. When you create a rule group, you define an immutable capacity limit. If you update a rule group, you must stay within the capacity. This allows others to reuse the rule group with confidence in its capacity requirements. </p>"}, "RuleGroupReferenceStatement": {"type": "structure", "required": ["ARN"], "members": {"ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}, "ExcludedRules": {"shape": "ExcludedRules", "documentation": "<p>Rules in the referenced rule group whose actions are set to <code>Count</code>. </p> <note> <p>Instead of this option, use <code>RuleActionOverrides</code>. It accepts any valid action setting, including <code>Count</code>.</p> </note>"}, "RuleActionOverrides": {"shape": "RuleActionOverrides", "documentation": "<p>Action settings to use in the place of the rule actions that are configured inside the rule group. You specify one override for each rule whose action you want to change. </p> <p>You can use overrides for testing, for example you can override all of rule actions to <code>Count</code> and then monitor the resulting count metrics to understand how the rule group would handle your web traffic. You can also permanently override some or all actions, to modify how the rule group manages your web traffic.</p>"}}, "documentation": "<p>A rule statement used to run the rules that are defined in a <a>RuleGroup</a>. To use this, create a rule group with your rules, then provide the ARN of the rule group in this statement.</p> <p>You cannot nest a <code>RuleGroupReferenceStatement</code>, for example for use inside a <code>NotStatement</code> or <code>OrStatement</code>. You cannot use a rule group reference statement inside another rule group. You can only reference a rule group as a top-level statement within a rule that you define in a web ACL.</p>"}, "RuleGroupSummaries": {"type": "list", "member": {"shape": "RuleGroupSummary"}}, "RuleGroupSummary": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the data type instance. You cannot change the name after you create the instance.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the rule group. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the rule group that helps with identification. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}}, "documentation": "<p>High-level information about a <a>RuleGroup</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage a <code>RuleGroup</code>, and the ARN, that you provide to the <a>RuleGroupReferenceStatement</a> to use the rule group in a <a>Rule</a>.</p>"}, "RulePriority": {"type": "integer", "min": 0}, "RuleSummaries": {"type": "list", "member": {"shape": "RuleSummary"}}, "RuleSummary": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule. </p>"}, "Action": {"shape": "RuleAction", "documentation": "<p>The action that WAF should take on a web request when it matches a rule's statement. Settings at the web ACL level can override the rule action setting. </p>"}}, "documentation": "<p>High-level information about a <a>Rule</a>, returned by operations like <a>DescribeManagedRuleGroup</a>. This provides information like the ID, that you can use to retrieve and manage a <code>RuleGroup</code>, and the ARN, that you provide to the <a>RuleGroupReferenceStatement</a> to use the rule group in a <a>Rule</a>.</p>"}, "Rules": {"type": "list", "member": {"shape": "Rule"}}, "SampleWeight": {"type": "long", "min": 0}, "SampledHTTPRequest": {"type": "structure", "required": ["Request", "Weight"], "members": {"Request": {"shape": "HTTPRequest", "documentation": "<p>A complex type that contains detailed information about the request.</p>"}, "Weight": {"shape": "SampleWeight", "documentation": "<p>A value that indicates how one result in the response relates proportionally to other results in the response. For example, a result that has a weight of <code>2</code> represents roughly twice as many web requests as a result that has a weight of <code>1</code>.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>The time at which WAF received the request from your Amazon Web Services resource, in Unix time format (in seconds).</p>"}, "Action": {"shape": "Action", "documentation": "<p>The action that WAF applied to the request.</p>"}, "RuleNameWithinRuleGroup": {"shape": "EntityName", "documentation": "<p>The name of the <code>Rule</code> that the request matched. For managed rule groups, the format for this name is <code>&lt;vendor name&gt;#&lt;managed rule group name&gt;#&lt;rule name&gt;</code>. For your own rule groups, the format for this name is <code>&lt;rule group name&gt;#&lt;rule name&gt;</code>. If the rule is not in a rule group, this field is absent. </p>"}, "RequestHeadersInserted": {"shape": "HTTPHeaders", "documentation": "<p>Custom request headers inserted by WAF into the request, according to the custom request configuration for the matching rule action.</p>"}, "ResponseCodeSent": {"shape": "ResponseStatusCode", "documentation": "<p>The response code that was sent for the request.</p>"}, "Labels": {"shape": "Labels", "documentation": "<p>Labels applied to the web request by matching rules. WAF applies fully qualified labels to matching web requests. A fully qualified label is the concatenation of a label namespace and a rule label. The rule's rule group or web ACL defines the label namespace. </p> <p>For example, <code>awswaf:111122223333:myRuleGroup:testRules:testNS1:testNS2:labelNameA</code> or <code>awswaf:managed:aws:managed-rule-set:header:encoding:utf8</code>. </p>"}, "CaptchaResponse": {"shape": "CaptchaResponse", "documentation": "<p>The <code>CAPTCHA</code> response for the request.</p>"}, "ChallengeResponse": {"shape": "ChallengeResponse", "documentation": "<p>The <code>Challenge</code> response for the request.</p>"}, "OverriddenAction": {"shape": "Action", "documentation": "<p>Used only for rule group rules that have a rule action override in place in the web ACL. This is the action that the rule group rule is configured for, and not the action that was applied to the request. The action that WAF applied is the <code>Action</code> value. </p>"}}, "documentation": "<p>Represents a single sampled web request. The response from <a>GetSampledRequests</a> includes a <code>SampledHTTPRequests</code> complex type that appears as <code>SampledRequests</code> in the response syntax. <code>SampledHTTPRequests</code> contains an array of <code>SampledHTTPRequest</code> objects.</p>"}, "SampledHTTPRequests": {"type": "list", "member": {"shape": "SampledHTTPRequest"}}, "Scope": {"type": "string", "enum": ["CLOUDFRONT", "REGIONAL"]}, "SearchString": {"type": "blob"}, "SensitivityLevel": {"type": "string", "enum": ["LOW", "HIGH"]}, "SingleCookieName": {"type": "string", "max": 60, "min": 1, "pattern": ".*\\S.*"}, "SingleHeader": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "FieldToMatchData", "documentation": "<p>The name of the query header to inspect.</p>"}}, "documentation": "<p>Inspect one of the headers in the web request, identified by name, for example, <code>User-Agent</code> or <code>Referer</code>. The name isn't case sensitive.</p> <p>You can filter and inspect all headers with the <code>FieldToMatch</code> setting <code>Headers</code>.</p> <p>This is used to indicate the web request component to inspect, in the <a>FieldToMatch</a> specification. </p> <p>Example JSON: <code>\"SingleHeader\": { \"Name\": \"haystack\" }</code> </p>"}, "SingleQueryArgument": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "FieldToMatchData", "documentation": "<p>The name of the query argument to inspect.</p>"}}, "documentation": "<p>Inspect one query argument in the web request, identified by name, for example <i>UserName</i> or <i>SalesRegion</i>. The name isn't case sensitive. </p> <p>This is used to indicate the web request component to inspect, in the <a>FieldToMatch</a> specification. </p> <p>Example JSON: <code>\"SingleQueryArgument\": { \"Name\": \"myArgument\" }</code> </p>"}, "Size": {"type": "long", "max": 21474836480, "min": 0}, "SizeConstraintStatement": {"type": "structure", "required": ["FieldToMatch", "ComparisonOperator", "Size", "TextTransformations"], "members": {"FieldToMatch": {"shape": "FieldToMatch", "documentation": "<p>The part of the web request that you want WAF to inspect. </p>"}, "ComparisonOperator": {"shape": "ComparisonOperator", "documentation": "<p>The operator to use to compare the request part to the size setting. </p>"}, "Size": {"shape": "Size", "documentation": "<p>The size, in byte, to compare to the request part, after any transformations.</p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>A rule statement that compares a number of bytes against the size of a request component, using a comparison operator, such as greater than (&gt;) or less than (&lt;). For example, you can use a size constraint statement to look for query strings that are longer than 100 bytes. </p> <p>If you configure WAF to inspect the request body, WAF inspects only the number of bytes of the body up to the limit for the web ACL. By default, for regional web ACLs, this limit is 8 KB (8,192 bytes) and for CloudFront web ACLs, this limit is 16 KB (16,384 bytes). For CloudFront web ACLs, you can increase the limit in the web ACL <code>AssociationConfig</code>, for additional fees. If you know that the request body for your web requests should never exceed the inspection limit, you could use a size constraint statement to block requests that have a larger request body size.</p> <p>If you choose URI for the value of Part of the request to filter on, the slash (/) in the URI counts as one character. For example, the URI <code>/logo.jpg</code> is nine characters long.</p>"}, "SizeInspectionLimit": {"type": "string", "enum": ["KB_16", "KB_32", "KB_48", "KB_64"]}, "SolveTimestamp": {"type": "long"}, "SqliMatchStatement": {"type": "structure", "required": ["FieldToMatch", "TextTransformations"], "members": {"FieldToMatch": {"shape": "FieldToMatch", "documentation": "<p>The part of the web request that you want WAF to inspect. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}, "SensitivityLevel": {"shape": "SensitivityLevel", "documentation": "<p>The sensitivity that you want WAF to use to inspect for SQL injection attacks. </p> <p> <code>HIGH</code> detects more attacks, but might generate more false positives, especially if your web requests frequently contain unusual strings. For information about identifying and mitigating false positives, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/web-acl-testing.html\">Testing and tuning</a> in the <i>WAF Developer Guide</i>.</p> <p> <code>LOW</code> is generally a better choice for resources that already have other protections against SQL injection attacks or that have a low tolerance for false positives. </p> <p>Default: <code>LOW</code> </p>"}}, "documentation": "<p>A rule statement that inspects for malicious SQL code. Attackers insert malicious SQL code into web requests to do things like modify your database or extract data from it. </p>"}, "Statement": {"type": "structure", "members": {"ByteMatchStatement": {"shape": "ByteMatchStatement", "documentation": "<p>A rule statement that defines a string match search for WAF to apply to web requests. The byte match statement provides the bytes to search for, the location in requests that you want WAF to search, and other settings. The bytes to search for are typically a string that corresponds with ASCII characters. In the WAF console and the developer guide, this is called a string match statement.</p>"}, "SqliMatchStatement": {"shape": "SqliMatchStatement", "documentation": "<p>A rule statement that inspects for malicious SQL code. Attackers insert malicious SQL code into web requests to do things like modify your database or extract data from it. </p>"}, "XssMatchStatement": {"shape": "XssMatchStatement", "documentation": "<p>A rule statement that inspects for cross-site scripting (XSS) attacks. In XSS attacks, the attacker uses vulnerabilities in a benign website as a vehicle to inject malicious client-site scripts into other legitimate web browsers. </p>"}, "SizeConstraintStatement": {"shape": "SizeConstraintStatement", "documentation": "<p>A rule statement that compares a number of bytes against the size of a request component, using a comparison operator, such as greater than (&gt;) or less than (&lt;). For example, you can use a size constraint statement to look for query strings that are longer than 100 bytes. </p> <p>If you configure WAF to inspect the request body, WAF inspects only the number of bytes of the body up to the limit for the web ACL. By default, for regional web ACLs, this limit is 8 KB (8,192 bytes) and for CloudFront web ACLs, this limit is 16 KB (16,384 bytes). For CloudFront web ACLs, you can increase the limit in the web ACL <code>AssociationConfig</code>, for additional fees. If you know that the request body for your web requests should never exceed the inspection limit, you could use a size constraint statement to block requests that have a larger request body size.</p> <p>If you choose URI for the value of Part of the request to filter on, the slash (/) in the URI counts as one character. For example, the URI <code>/logo.jpg</code> is nine characters long.</p>"}, "GeoMatchStatement": {"shape": "GeoMatchStatement", "documentation": "<p>A rule statement that labels web requests by country and region and that matches against web requests based on country code. A geo match rule labels every request that it inspects regardless of whether it finds a match.</p> <ul> <li> <p>To manage requests only by country, you can use this statement by itself and specify the countries that you want to match against in the <code>CountryCodes</code> array. </p> </li> <li> <p>Otherwise, configure your geo match rule with Count action so that it only labels requests. Then, add one or more label match rules to run after the geo match rule and configure them to match against the geographic labels and handle the requests as needed. </p> </li> </ul> <p>WAF labels requests using the alpha-2 country and region codes from the International Organization for Standardization (ISO) 3166 standard. WAF determines the codes using either the IP address in the web request origin or, if you specify it, the address in the geo match <code>ForwardedIPConfig</code>. </p> <p>If you use the web request origin, the label formats are <code>awswaf:clientip:geo:region:&lt;ISO country code&gt;-&lt;ISO region code&gt;</code> and <code>awswaf:clientip:geo:country:&lt;ISO country code&gt;</code>.</p> <p>If you use a forwarded IP address, the label formats are <code>awswaf:forwardedip:geo:region:&lt;ISO country code&gt;-&lt;ISO region code&gt;</code> and <code>awswaf:forwardedip:geo:country:&lt;ISO country code&gt;</code>.</p> <p>For additional details, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rule-statement-type-geo-match.html\">Geographic match rule statement</a> in the <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-chapter.html\">WAF Developer Guide</a>. </p>"}, "RuleGroupReferenceStatement": {"shape": "RuleGroupReferenceStatement", "documentation": "<p>A rule statement used to run the rules that are defined in a <a>RuleGroup</a>. To use this, create a rule group with your rules, then provide the ARN of the rule group in this statement.</p> <p>You cannot nest a <code>RuleGroupReferenceStatement</code>, for example for use inside a <code>NotStatement</code> or <code>OrStatement</code>. You cannot use a rule group reference statement inside another rule group. You can only reference a rule group as a top-level statement within a rule that you define in a web ACL.</p>"}, "IPSetReferenceStatement": {"shape": "IPSetReferenceStatement", "documentation": "<p>A rule statement used to detect web requests coming from particular IP addresses or address ranges. To use this, create an <a>IPSet</a> that specifies the addresses you want to detect, then use the ARN of that set in this statement. To create an IP set, see <a>CreateIPSet</a>.</p> <p>Each IP set rule statement references an IP set. You create and maintain the set independent of your rules. This allows you to use the single set in multiple rules. When you update the referenced set, WAF automatically updates all rules that reference it.</p>"}, "RegexPatternSetReferenceStatement": {"shape": "RegexPatternSetReferenceStatement", "documentation": "<p>A rule statement used to search web request components for matches with regular expressions. To use this, create a <a>RegexPatternSet</a> that specifies the expressions that you want to detect, then use the ARN of that set in this statement. A web request matches the pattern set rule statement if the request component matches any of the patterns in the set. To create a regex pattern set, see <a>CreateRegexPatternSet</a>.</p> <p>Each regex pattern set rule statement references a regex pattern set. You create and maintain the set independent of your rules. This allows you to use the single set in multiple rules. When you update the referenced set, WAF automatically updates all rules that reference it.</p>"}, "RateBasedStatement": {"shape": "RateBasedStatement", "documentation": "<p>A rate-based rule counts incoming requests and rate limits requests when they are coming at too fast a rate. The rule categorizes requests according to your aggregation criteria, collects them into aggregation instances, and counts and rate limits the requests for each instance. </p> <p>You can specify individual aggregation keys, like IP address or HTTP method. You can also specify aggregation key combinations, like IP address and HTTP method, or HTTP method, query argument, and cookie. </p> <p>Each unique set of values for the aggregation keys that you specify is a separate aggregation instance, with the value from each key contributing to the aggregation instance definition. </p> <p>For example, assume the rule evaluates web requests with the following IP address and HTTP method values: </p> <ul> <li> <p>IP address ********, HTTP method POST</p> </li> <li> <p>IP address ********, HTTP method GET</p> </li> <li> <p>IP address *********, HTTP method POST</p> </li> <li> <p>IP address ********, HTTP method GET</p> </li> </ul> <p>The rule would create different aggregation instances according to your aggregation criteria, for example: </p> <ul> <li> <p>If the aggregation criteria is just the IP address, then each individual address is an aggregation instance, and WAF counts requests separately for each. The aggregation instances and request counts for our example would be the following: </p> <ul> <li> <p>IP address ********: count 3</p> </li> <li> <p>IP address *********: count 1</p> </li> </ul> </li> <li> <p>If the aggregation criteria is HTTP method, then each individual HTTP method is an aggregation instance. The aggregation instances and request counts for our example would be the following: </p> <ul> <li> <p>HTTP method POST: count 2</p> </li> <li> <p>HTTP method GET: count 2</p> </li> </ul> </li> <li> <p>If the aggregation criteria is IP address and HTTP method, then each IP address and each HTTP method would contribute to the combined aggregation instance. The aggregation instances and request counts for our example would be the following: </p> <ul> <li> <p>IP address ********, HTTP method POST: count 1</p> </li> <li> <p>IP address ********, HTTP method GET: count 2</p> </li> <li> <p>IP address *********, HTTP method POST: count 1</p> </li> </ul> </li> </ul> <p>For any n-tuple of aggregation keys, each unique combination of values for the keys defines a separate aggregation instance, which WAF counts and rate-limits individually. </p> <p>You can optionally nest another statement inside the rate-based statement, to narrow the scope of the rule so that it only counts and rate limits requests that match the nested statement. You can use this nested scope-down statement in conjunction with your aggregation key specifications or you can just count and rate limit all requests that match the scope-down statement, without additional aggregation. When you choose to just manage all requests that match a scope-down statement, the aggregation instance is singular for the rule. </p> <p>You cannot nest a <code>RateBasedStatement</code> inside another statement, for example inside a <code>NotStatement</code> or <code>OrStatement</code>. You can define a <code>RateBasedStatement</code> inside a web ACL and inside a rule group. </p> <p>For additional information about the options, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rate-based-rules.html\">Rate limiting web requests using rate-based rules</a> in the <i>WAF Developer Guide</i>. </p> <p>If you only aggregate on the individual IP address or forwarded IP address, you can retrieve the list of IP addresses that WAF is currently rate limiting for a rule through the API call <code>GetRateBasedStatementManagedKeys</code>. This option is not available for other aggregation configurations.</p> <p>WAF tracks and manages web requests separately for each instance of a rate-based rule that you use. For example, if you provide the same rate-based rule settings in two web ACLs, each of the two rule statements represents a separate instance of the rate-based rule and gets its own tracking and management by WAF. If you define a rate-based rule inside a rule group, and then use that rule group in multiple places, each use creates a separate instance of the rate-based rule that gets its own tracking and management by WAF. </p>"}, "AndStatement": {"shape": "AndStatement", "documentation": "<p>A logical rule statement used to combine other rule statements with AND logic. You provide more than one <a>Statement</a> within the <code>AndStatement</code>. </p>"}, "OrStatement": {"shape": "OrStatement", "documentation": "<p>A logical rule statement used to combine other rule statements with OR logic. You provide more than one <a>Statement</a> within the <code>OrStatement</code>. </p>"}, "NotStatement": {"shape": "NotStatement", "documentation": "<p>A logical rule statement used to negate the results of another rule statement. You provide one <a>Statement</a> within the <code>NotStatement</code>.</p>"}, "ManagedRuleGroupStatement": {"shape": "ManagedRuleGroupStatement", "documentation": "<p>A rule statement used to run the rules that are defined in a managed rule group. To use this, provide the vendor name and the name of the rule group in this statement. You can retrieve the required names by calling <a>ListAvailableManagedRuleGroups</a>.</p> <p>You cannot nest a <code>ManagedRuleGroupStatement</code>, for example for use inside a <code>NotStatement</code> or <code>OrStatement</code>. You cannot use a managed rule group inside another rule group. You can only reference a managed rule group as a top-level statement within a rule that you define in a web ACL.</p> <note> <p>You are charged additional fees when you use the WAF Bot Control managed rule group <code>AWSManagedRulesBotControlRuleSet</code>, the WAF Fraud Control account takeover prevention (ATP) managed rule group <code>AWSManagedRulesATPRuleSet</code>, or the WAF Fraud Control account creation fraud prevention (ACFP) managed rule group <code>AWSManagedRulesACFPRuleSet</code>. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}, "LabelMatchStatement": {"shape": "LabelMatchStatement", "documentation": "<p>A rule statement to match against labels that have been added to the web request by rules that have already run in the web ACL. </p> <p>The label match statement provides the label or namespace string to search for. The label string can represent a part or all of the fully qualified label name that had been added to the web request. Fully qualified labels have a prefix, optional namespaces, and label name. The prefix identifies the rule group or web ACL context of the rule that added the label. If you do not provide the fully qualified name in your label match string, WAF performs the search for labels that were added in the same context as the label match statement. </p>"}, "RegexMatchStatement": {"shape": "RegexMatchStatement", "documentation": "<p>A rule statement used to search web request components for a match against a single regular expression. </p>"}}, "documentation": "<p>The processing guidance for a <a>Rule</a>, used by WAF to determine whether a web request matches the rule. </p> <p>For example specifications, see the examples section of <a>CreateWebACL</a>.</p>"}, "Statements": {"type": "list", "member": {"shape": "Statement"}}, "String": {"type": "string"}, "SuccessCode": {"type": "integer", "max": 999, "min": 0}, "SuccessValue": {"type": "string", "max": 100, "min": 1, "pattern": ".*\\S.*"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>Part of the key:value pair that defines a tag. You can use a tag key to describe a category of information, such as \"customer.\" Tag keys are case-sensitive.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>Part of the key:value pair that defines a tag. You can use a tag value to describe a specific value within a category, such as \"companyA\" or \"companyB.\" Tag values are case-sensitive.</p>"}}, "documentation": "<p>A tag associated with an Amazon Web Services resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing or other management. Typically, the tag key represents a category, such as \"environment\", and the tag value represents a specific value within that category, such as \"test,\" \"development,\" or \"production\". Or you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can tag the Amazon Web Services resources that you manage through WAF: web ACLs, rule groups, IP sets, and regex pattern sets. You can't manage or view tags through the WAF console. </p>"}, "TagInfoForResource": {"type": "structure", "members": {"ResourceARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "TagList": {"shape": "TagList", "documentation": "<p>The array of <a>Tag</a> objects defined for the resource. </p>"}}, "documentation": "<p>The collection of tagging definitions for an Amazon Web Services resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing or other management. Typically, the tag key represents a category, such as \"environment\", and the tag value represents a specific value within that category, such as \"test,\" \"development,\" or \"production\". Or you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can tag the Amazon Web Services resources that you manage through WAF: web ACLs, rule groups, IP sets, and regex pattern sets. You can't manage or view tags through the WAF console. </p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of key:value pairs to associate with the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TextTransformation": {"type": "structure", "required": ["Priority", "Type"], "members": {"Priority": {"shape": "TextTransformationPriority", "documentation": "<p>Sets the relative processing order for multiple transformations. WAF processes all transformations, from lowest priority to highest, before inspecting the transformed content. The priorities don't need to be consecutive, but they must all be different. </p>"}, "Type": {"shape": "TextTransformationType", "documentation": "<p>For detailed descriptions of each of the transformation types, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-rule-statement-transformation.html\">Text transformations</a> in the <i>WAF Developer Guide</i>.</p>"}}, "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. </p>"}, "TextTransformationPriority": {"type": "integer", "min": 0}, "TextTransformationType": {"type": "string", "enum": ["NONE", "COMPRESS_WHITE_SPACE", "HTML_ENTITY_DECODE", "LOWERCASE", "CMD_LINE", "URL_DECODE", "BASE64_DECODE", "HEX_DECODE", "MD5", "REPLACE_COMMENTS", "ESCAPE_SEQ_DECODE", "SQL_HEX_DECODE", "CSS_DECODE", "JS_DECODE", "NORMALIZE_PATH", "NORMALIZE_PATH_WIN", "REMOVE_NULLS", "REPLACE_NULLS", "BASE64_DECODE_EXT", "URL_DECODE_UNI", "UTF8_TO_UNICODE"]}, "TextTransformations": {"type": "list", "member": {"shape": "TextTransformation"}, "min": 1}, "TimeWindow": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The beginning of the time range from which you want <code>GetSampledRequests</code> to return a sample of the requests that your Amazon Web Services resource received. You must specify the times in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <code>Z</code>. For example, <code>\"2016-09-27T14:50Z\"</code>. You can specify any time range in the previous three hours.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end of the time range from which you want <code>GetSampledRequests</code> to return a sample of the requests that your Amazon Web Services resource received. You must specify the times in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <code>Z</code>. For example, <code>\"2016-09-27T14:50Z\"</code>. You can specify any time range in the previous three hours.</p>"}}, "documentation": "<p>In a <a>GetSampledRequests</a> request, the <code>StartTime</code> and <code>EndTime</code> objects specify the time range for which you want WAF to return a sample of web requests.</p> <p>You must specify the times in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <code>Z</code>. For example, <code>\"2016-09-27T14:50Z\"</code>. You can specify any time range in the previous three hours.</p> <p>In a <a>GetSampledRequests</a> response, the <code>StartTime</code> and <code>EndTime</code> objects specify the time range for which WAF actually returned a sample of web requests. WAF gets the specified number of requests from among the first 5,000 requests that your Amazon Web Services resource receives during the specified time period. If your resource receives more than 5,000 requests during that period, WAF stops sampling after the 5,000th request. In that case, <code>EndTime</code> is the time that WAF received the 5,000th request.</p>"}, "TimeWindowDay": {"type": "integer", "min": 1}, "TimeWindowSecond": {"type": "long", "max": 259200, "min": 60}, "Timestamp": {"type": "timestamp"}, "TokenDomain": {"type": "string", "max": 253, "min": 1, "pattern": "^[\\w\\.\\-/]+$"}, "TokenDomains": {"type": "list", "member": {"shape": "TokenDomain"}}, "URIString": {"type": "string"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>An array of keys identifying the tags to disassociate from the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateIPSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "Addresses", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the IP set. You cannot change the name of an <code>IPSet</code> after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the IP set that helps with identification. </p>"}, "Addresses": {"shape": "IPAddresses", "documentation": "<p>Contains an array of strings that specifies zero or more IP addresses or blocks of IP addresses that you want WAF to inspect for in incoming requests. All addresses must be specified using Classless Inter-Domain Routing (CIDR) notation. WAF supports all IPv4 and IPv6 CIDR ranges except for <code>/0</code>. </p> <p>Example address strings: </p> <ul> <li> <p>For requests that originated from the IP address **********, specify <code>**********/32</code>.</p> </li> <li> <p>For requests that originated from IP addresses from ********* to ***********, specify <code>*********/24</code>.</p> </li> <li> <p>For requests that originated from the IP address 1111:0000:0000:0000:0000:0000:0000:0111, specify <code>1111:0000:0000:0000:0000:0000:0000:0111/128</code>.</p> </li> <li> <p>For requests that originated from IP addresses 1111:0000:0000:0000:0000:0000:0000:0000 to 1111:0000:0000:0000:ffff:ffff:ffff:ffff, specify <code>1111:0000:0000:0000:0000:0000:0000:0000/64</code>.</p> </li> </ul> <p>For more information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>.</p> <p>Example JSON <code>Addresses</code> specifications: </p> <ul> <li> <p>Empty array: <code>\"Addresses\": []</code> </p> </li> <li> <p>Array with one address: <code>\"Addresses\": [\"**********/32\"]</code> </p> </li> <li> <p>Array with three addresses: <code>\"Addresses\": [\"**********/32\", \"*********/24\", \"*********/16\"]</code> </p> </li> <li> <p>INVALID specification: <code>\"Addresses\": [\"\"]</code> INVALID </p> </li> </ul>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "UpdateIPSetResponse": {"type": "structure", "members": {"NextLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns this token to your <code>update</code> requests. You use <code>NextLockToken</code> in the same manner as you use <code>LockToken</code>. </p>"}}}, "UpdateManagedRuleSetVersionExpiryDateRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "LockToken", "VersionToExpire", "ExpiryTimestamp"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the managed rule set. You use this, along with the rule set ID, to identify the rule set.</p> <p>This name is assigned to the corresponding managed rule group, which your customers can access and use. </p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the managed rule set. The ID is returned in the responses to commands like <code>list</code>. You provide it to operations like <code>get</code> and <code>update</code>.</p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "VersionToExpire": {"shape": "VersionKeyString", "documentation": "<p>The version that you want to remove from your list of offerings for the named managed rule group. </p>"}, "ExpiryTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that you want the version to expire.</p> <p>Times are in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <PERSON>. For example, \"2016-09-27T14:50Z\". </p>"}}}, "UpdateManagedRuleSetVersionExpiryDateResponse": {"type": "structure", "members": {"ExpiringVersion": {"shape": "VersionKeyString", "documentation": "<p>The version that is set to expire. </p>"}, "ExpiryTimestamp": {"shape": "Timestamp", "documentation": "<p>The time that the version will expire. </p> <p>Times are in Coordinated Universal Time (UTC) format. UTC format includes the special designator, <PERSON>. For example, \"2016-09-27T14:50Z\". </p>"}, "NextLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "UpdateRegexPatternSetRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "RegularExpressionList", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the set. You cannot change the name after you create the set.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the set. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the set that helps with identification. </p>"}, "RegularExpressionList": {"shape": "RegularExpressionList", "documentation": "<p/>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}}}, "UpdateRegexPatternSetResponse": {"type": "structure", "members": {"NextLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns this token to your <code>update</code> requests. You use <code>NextLockToken</code> in the same manner as you use <code>LockToken</code>. </p>"}}}, "UpdateRuleGroupRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "VisibilityConfig", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the rule group. You cannot change the name of a rule group after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the rule group. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the rule group that helps with identification. </p>"}, "Rules": {"shape": "Rules", "documentation": "<p>The <a>Rule</a> statements used to identify the web requests that you want to manage. Each rule includes one top-level statement that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "CustomResponseBodies": {"shape": "CustomResponseBodies", "documentation": "<p>A map of custom response keys and content bodies. When you create a rule with a block action, you can send a custom response to the web request. You define these for the rule group, and then use them in the rules that you define in the rule group. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}}}, "UpdateRuleGroupResponse": {"type": "structure", "members": {"NextLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns this token to your <code>update</code> requests. You use <code>NextLockToken</code> in the same manner as you use <code>LockToken</code>. </p>"}}}, "UpdateWebACLRequest": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Id", "DefaultAction", "VisibilityConfig", "LockToken"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies whether this is for an Amazon CloudFront distribution or for a regional application. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> <p>To work with CloudFront, you must also specify the Region US East (N. Virginia) as follows: </p> <ul> <li> <p>CLI - Specify the Region when you use the CloudFront scope: <code>--scope=CLOUDFRONT --region=us-east-1</code>. </p> </li> <li> <p>API and SDKs - For all calls, use the Region endpoint us-east-1. </p> </li> </ul>"}, "Id": {"shape": "EntityId", "documentation": "<p>The unique identifier for the web ACL. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "DefaultAction": {"shape": "DefaultAction", "documentation": "<p>The action to perform if none of the <code>Rules</code> contained in the <code>WebACL</code> match. </p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the web ACL that helps with identification. </p>"}, "Rules": {"shape": "Rules", "documentation": "<p>The <a>Rule</a> statements used to identify the web requests that you want to manage. Each rule includes one top-level statement that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "CustomResponseBodies": {"shape": "CustomResponseBodies", "documentation": "<p>A map of custom response keys and content bodies. When you create a rule with a block action, you can send a custom response to the web request. You define these for the web ACL, and then use them in the rules and default actions that you define in the web ACL. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}, "CaptchaConfig": {"shape": "CaptchaConfig", "documentation": "<p>Specifies how WAF should handle <code>CAPTCHA</code> evaluations for rules that don't have their own <code>CaptchaConfig</code> settings. If you don't specify this, WAF uses its default settings for <code>CaptchaConfig</code>. </p>"}, "ChallengeConfig": {"shape": "ChallengeConfig", "documentation": "<p>Specifies how WAF should handle challenge evaluations for rules that don't have their own <code>ChallengeConfig</code> settings. If you don't specify this, WAF uses its default settings for <code>ChallengeConfig</code>. </p>"}, "TokenDomains": {"shape": "TokenDomains", "documentation": "<p>Specifies the domains that WAF should accept in a web request token. This enables the use of tokens across multiple protected websites. When WAF provides a token, it uses the domain of the Amazon Web Services resource that the web ACL is protecting. If you don't specify a list of token domains, WAF accepts tokens only for the domain of the protected resource. With a token domain list, WAF accepts the resource's host domain plus all domains in the token domain list, including their prefixed subdomains.</p> <p>Example JSON: <code>\"TokenDomains\": { \"mywebsite.com\", \"myotherwebsite.com\" }</code> </p> <p>Public suffixes aren't allowed. For example, you can't use <code>usa.gov</code> or <code>co.uk</code> as token domains.</p>"}, "AssociationConfig": {"shape": "AssociationConfig", "documentation": "<p>Specifies custom configurations for the associations between the web ACL and protected resources. </p> <p>Use this to customize the maximum size of the request body that your protected CloudFront distributions forward to WAF for inspection. The default is 16 KB (16,384 bytes). </p> <note> <p>You are charged additional fees when your protected resources forward body sizes that are larger than the default. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}}}, "UpdateWebACLResponse": {"type": "structure", "members": {"NextLockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns this token to your <code>update</code> requests. You use <code>NextLockToken</code> in the same manner as you use <code>LockToken</code>. </p>"}}}, "UriPath": {"type": "structure", "members": {}, "documentation": "<p>Inspect the path component of the URI of the web request. This is the part of the web request that identifies a resource. For example, <code>/images/daily-ad.jpg</code>.</p> <p>This is used in the <a>FieldToMatch</a> specification for some web request component types. </p> <p>JSON specification: <code>\"UriPath\": {}</code> </p>"}, "UsernameField": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "FieldIdentifier", "documentation": "<p>The name of the username field. </p> <p>How you specify this depends on the request inspection payload type.</p> <ul> <li> <p>For JSON payloads, specify the field name in JSON pointer syntax. For information about the JSON Pointer syntax, see the Internet Engineering Task Force (IETF) documentation <a href=\"https://tools.ietf.org/html/rfc6901\">JavaScript Object Notation (JSON) Pointer</a>. </p> <p>For example, for the JSON payload <code>{ \"form\": { \"username\": \"THE_USERNAME\" } }</code>, the username field specification is <code>/form/username</code>. </p> </li> <li> <p>For form encoded payload types, use the HTML form names.</p> <p>For example, for an HTML form with the input element named <code>username1</code>, the username field specification is <code>username1</code> </p> </li> </ul>"}}, "documentation": "<p>The name of the field in the request payload that contains your customer's username. </p> <p>This data type is used in the <code>RequestInspection</code> and <code>RequestInspectionACFP</code> data types. </p>"}, "VendorName": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "VersionKeyString": {"type": "string", "max": 64, "min": 1, "pattern": "^[\\w#:\\.\\-/]+$"}, "VersionToPublish": {"type": "structure", "members": {"AssociatedRuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the vendor's rule group that's used in the published managed rule group version. </p>"}, "ForecastedLifetime": {"shape": "TimeWindowDay", "documentation": "<p>The amount of time the vendor expects this version of the managed rule group to last, in days. </p>"}}, "documentation": "<p>A version of the named managed rule group, that the rule group's vendor publishes for use by customers. </p> <note> <p>This is intended for use only by vendors of managed rule sets. Vendors are Amazon Web Services and Amazon Web Services Marketplace sellers. </p> <p>Vendors, you can use the managed rule set APIs to provide controlled rollout of your versioned managed rule group offerings for your customers. The APIs are <code>ListManagedRuleSets</code>, <code>GetManagedRuleSet</code>, <code>PutManagedRuleSetVersions</code>, and <code>UpdateManagedRuleSetVersionExpiryDate</code>.</p> </note>"}, "VersionsToPublish": {"type": "map", "key": {"shape": "VersionKeyString"}, "value": {"shape": "VersionToPublish"}}, "VisibilityConfig": {"type": "structure", "required": ["SampledRequestsEnabled", "CloudWatchMetricsEnabled", "MetricName"], "members": {"SampledRequestsEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether WAF should store a sampling of the web requests that match the rules. You can view the sampled requests through the WAF console. </p>"}, "CloudWatchMetricsEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the associated resource sends metrics to Amazon CloudWatch. For the list of available metrics, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/monitoring-cloudwatch.html#waf-metrics\">WAF Metrics</a> in the <i>WAF Developer Guide</i>.</p> <p>For web ACLs, the metrics are for web requests that have the web ACL default action applied. WAF applies the default action to web requests that pass the inspection of all rules in the web ACL without being either allowed or blocked. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/web-acl-default-action.html\">The web ACL default action</a> in the <i>WAF Developer Guide</i>.</p>"}, "MetricName": {"shape": "MetricName", "documentation": "<p>A name of the Amazon CloudWatch metric dimension. The name can contain only the characters: A-Z, a-z, 0-9, - (hyphen), and _ (underscore). The name can be from one to 128 characters long. It can't contain whitespace or metric names that are reserved for WAF, for example <code>All</code> and <code>Default_Action</code>. </p>"}}, "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "WAFAssociatedItemException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t perform the operation because your resource is being used by another resource or it’s associated with another resource. </p>", "exception": true}, "WAFConfigurationWarningException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because you are inspecting the web request body, headers, or cookies without specifying how to handle oversize components. Rules that inspect the body must either provide an <code>OversizeHandling</code> configuration or they must be preceded by a <code>SizeConstraintStatement</code> that blocks the body content from being too large. Rules that inspect the headers or cookies must provide an <code>OversizeHandling</code> configuration. </p> <p>Provide the handling configuration and retry your operation.</p> <p>Alternately, you can suppress this warning by adding the following tag to the resource that you provide to this operation: <code>Tag</code> (key:<code>WAF:OversizeFieldsHandlingConstraintOptOut</code>, value:<code>true</code>).</p>", "exception": true}, "WAFDuplicateItemException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t perform the operation because the resource that you tried to save is a duplicate of an existing one.</p>", "exception": true}, "WAFExpiredManagedRuleGroupVersionException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because the specified version for the managed rule group has expired. You can retrieve the available versions for the managed rule group by calling <a>ListAvailableManagedRuleGroupVersions</a>.</p>", "exception": true}, "WAFInternalErrorException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Your request is valid, but WAF couldn’t perform the operation because of a system problem. Retry your request. </p>", "exception": true, "fault": true}, "WAFInvalidOperationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation isn't valid. </p>", "exception": true}, "WAFInvalidParameterException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}, "Field": {"shape": "ParameterExceptionField", "documentation": "<p>The settings where the invalid parameter was found. </p>"}, "Parameter": {"shape": "ParameterExceptionParameter", "documentation": "<p>The invalid parameter that resulted in the exception. </p>"}, "Reason": {"shape": "ErrorReason", "documentation": "<p>Additional information about the exception.</p>"}}, "documentation": "<p>The operation failed because WAF didn't recognize a parameter in the request. For example: </p> <ul> <li> <p>You specified a parameter name or value that isn't valid.</p> </li> <li> <p>Your nested statement isn't valid. You might have tried to nest a statement that can’t be nested. </p> </li> <li> <p>You tried to update a <code>WebACL</code> with a <code>DefaultAction</code> that isn't among the types available at <a>DefaultAction</a>.</p> </li> <li> <p>Your request references an ARN that is malformed, or corresponds to a resource with which a web ACL can't be associated.</p> </li> </ul>", "exception": true}, "WAFInvalidPermissionPolicyException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because the specified policy isn't in the proper format. </p> <p>The policy specifications must conform to the following:</p> <ul> <li> <p>The policy must be composed using IAM Policy version 2012-10-17.</p> </li> <li> <p>The policy must include specifications for <code>Effect</code>, <code>Action</code>, and <code>Principal</code>.</p> </li> <li> <p> <code>Effect</code> must specify <code>Allow</code>.</p> </li> <li> <p> <code>Action</code> must specify <code>wafv2:CreateWebACL</code>, <code>wafv2:UpdateWebACL</code>, and <code>wafv2:PutFirewallManagerRuleGroups</code> and may optionally specify <code>wafv2:GetRuleGroup</code>. WAF rejects any extra actions or wildcard actions in the policy.</p> </li> <li> <p>The policy must not include a <code>Resource</code> parameter.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies.html\">IAM Policies</a>. </p>", "exception": true}, "WAFInvalidResourceException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t perform the operation because the resource that you requested isn’t valid. Check the resource, and try again.</p>", "exception": true}, "WAFLimitsExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t perform the operation because you exceeded your resource limit. For example, the maximum number of <code>WebACL</code> objects that you can create for an Amazon Web Services account. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>.</p>", "exception": true}, "WAFLogDestinationPermissionIssueException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because you don't have the permissions that your logging configuration requires. For information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging.html\">Logging web ACL traffic information</a> in the <i>WAF Developer Guide</i>.</p>", "exception": true}, "WAFNonexistentItemException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t perform the operation because your resource doesn't exist. If you've just created a resource that you're using in this operation, you might just need to wait a few minutes. It can take from a few seconds to a number of minutes for changes to propagate. </p>", "exception": true}, "WAFOptimisticLockException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t save your changes because you tried to update or delete a resource that has changed since you last retrieved it. Get the resource again, make any changes you need to make to the new copy, and retry your operation. </p>", "exception": true}, "WAFServiceLinkedRoleErrorException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF is not able to access the service linked role. This can be caused by a previous <code>PutLoggingConfiguration</code> request, which can lock the service linked role for about 20 seconds. Please try your request again. The service linked role can also be locked by a previous <code>DeleteServiceLinkedRole</code> request, which can lock the role for 15 minutes or more. If you recently made a call to <code>DeleteServiceLinkedRole</code>, wait at least 15 minutes and try the request again. If you receive this same exception again, you will have to wait additional time until the role is unlocked.</p>", "exception": true}, "WAFSubscriptionNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You tried to use a managed rule group that's available by subscription, but you aren't subscribed to it yet. </p>", "exception": true}, "WAFTagOperationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An error occurred during the tagging operation. Retry your request.</p>", "exception": true}, "WAFTagOperationInternalErrorException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t perform your tagging operation because of an internal error. Retry your request.</p>", "exception": true, "fault": true}, "WAFUnavailableEntityException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>WAF couldn’t retrieve a resource that you specified for this operation. If you've just created a resource that you're using in this operation, you might just need to wait a few minutes. It can take from a few seconds to a number of minutes for changes to propagate. Verify the resources that you are specifying in your request parameters and then retry the operation.</p>", "exception": true}, "WAFUnsupportedAggregateKeyTypeException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The rule that you've named doesn't aggregate solely on the IP address or solely on the forwarded IP address. This call is only available for rate-based rules with an <code>AggregateKeyType</code> setting of <code>IP</code> or <code>FORWARDED_IP</code>.</p>", "exception": true}, "WebACL": {"type": "structure", "required": ["Name", "Id", "ARN", "DefaultAction", "VisibilityConfig"], "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>A unique identifier for the <code>WebACL</code>. This ID is returned in the responses to create and list commands. You use this ID to do things like get, update, and delete a <code>WebACL</code>.</p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the web ACL that you want to associate with the resource.</p>"}, "DefaultAction": {"shape": "DefaultAction", "documentation": "<p>The action to perform if none of the <code>Rules</code> contained in the <code>WebACL</code> match. </p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the web ACL that helps with identification. </p>"}, "Rules": {"shape": "Rules", "documentation": "<p>The <a>Rule</a> statements used to identify the web requests that you want to manage. Each rule includes one top-level statement that WAF uses to identify matching web requests, and parameters that govern how WAF handles them. </p>"}, "VisibilityConfig": {"shape": "VisibilityConfig", "documentation": "<p>Defines and enables Amazon CloudWatch metrics and web request sample collection. </p>"}, "Capacity": {"shape": "ConsumedCapacity", "documentation": "<p>The web ACL capacity units (WCUs) currently being used by this web ACL. </p> <p>WAF uses WCUs to calculate and control the operating resources that are used to run your rules, rule groups, and web ACLs. WAF calculates capacity differently for each rule type, to reflect the relative cost of each rule. Simple rules that cost little to run use fewer WCUs than more complex rules that use more processing power. Rule group capacity is fixed at creation, which helps users plan their web ACL WCU usage when they use a rule group. For more information, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/aws-waf-capacity-units.html\">WAF web ACL capacity units (WCU)</a> in the <i>WAF Developer Guide</i>. </p>"}, "PreProcessFirewallManagerRuleGroups": {"shape": "FirewallManagerRuleGroups", "documentation": "<p>The first set of rules for WAF to process in the web ACL. This is defined in an Firewall Manager WAF policy and contains only rule group references. You can't alter these. Any rules and rule groups that you define for the web ACL are prioritized after these. </p> <p>In the Firewall Manager WAF policy, the Firewall Manager administrator can define a set of rule groups to run first in the web ACL and a set of rule groups to run last. Within each set, the administrator prioritizes the rule groups, to determine their relative processing order.</p>"}, "PostProcessFirewallManagerRuleGroups": {"shape": "FirewallManagerRuleGroups", "documentation": "<p>The last set of rules for WAF to process in the web ACL. This is defined in an Firewall Manager WAF policy and contains only rule group references. You can't alter these. Any rules and rule groups that you define for the web ACL are prioritized before these. </p> <p>In the Firewall Manager WAF policy, the Firewall Manager administrator can define a set of rule groups to run first in the web ACL and a set of rule groups to run last. Within each set, the administrator prioritizes the rule groups, to determine their relative processing order.</p>"}, "ManagedByFirewallManager": {"shape": "Boolean", "documentation": "<p>Indicates whether this web ACL is managed by Firewall Manager. If true, then only Firewall Manager can delete the web ACL or any Firewall Manager rule groups in the web ACL. </p>"}, "LabelNamespace": {"shape": "LabelName", "documentation": "<p>The label namespace prefix for this web ACL. All labels added by rules in this web ACL have this prefix. </p> <ul> <li> <p>The syntax for the label namespace prefix for a web ACL is the following: </p> <p> <code>awswaf:&lt;account ID&gt;:webacl:&lt;web ACL name&gt;:</code> </p> </li> <li> <p>When a rule with a label matches a web request, WAF adds the fully qualified label to the request. A fully qualified label is made up of the label namespace from the rule group or web ACL where the rule is defined and the label from the rule, separated by a colon: </p> <p> <code>&lt;label namespace&gt;:&lt;label from rule&gt;</code> </p> </li> </ul>"}, "CustomResponseBodies": {"shape": "CustomResponseBodies", "documentation": "<p>A map of custom response keys and content bodies. When you create a rule with a block action, you can send a custom response to the web request. You define these for the web ACL, and then use them in the rules and default actions that you define in the web ACL. </p> <p>For information about customizing web requests and responses, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-custom-request-response.html\">Customizing web requests and responses in WAF</a> in the <i>WAF Developer Guide</i>. </p> <p>For information about the limits on count and size for custom request and response settings, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/limits.html\">WAF quotas</a> in the <i>WAF Developer Guide</i>. </p>"}, "CaptchaConfig": {"shape": "CaptchaConfig", "documentation": "<p>Specifies how WAF should handle <code>CAPTCHA</code> evaluations for rules that don't have their own <code>CaptchaConfig</code> settings. If you don't specify this, WAF uses its default settings for <code>CaptchaConfig</code>. </p>"}, "ChallengeConfig": {"shape": "ChallengeConfig", "documentation": "<p>Specifies how WAF should handle challenge evaluations for rules that don't have their own <code>ChallengeConfig</code> settings. If you don't specify this, WAF uses its default settings for <code>ChallengeConfig</code>. </p>"}, "TokenDomains": {"shape": "TokenDomains", "documentation": "<p>Specifies the domains that WAF should accept in a web request token. This enables the use of tokens across multiple protected websites. When WAF provides a token, it uses the domain of the Amazon Web Services resource that the web ACL is protecting. If you don't specify a list of token domains, WAF accepts tokens only for the domain of the protected resource. With a token domain list, WAF accepts the resource's host domain plus all domains in the token domain list, including their prefixed subdomains.</p>"}, "AssociationConfig": {"shape": "AssociationConfig", "documentation": "<p>Specifies custom configurations for the associations between the web ACL and protected resources. </p> <p>Use this to customize the maximum size of the request body that your protected CloudFront distributions forward to WAF for inspection. The default is 16 KB (16,384 bytes). </p> <note> <p>You are charged additional fees when your protected resources forward body sizes that are larger than the default. For more information, see <a href=\"http://aws.amazon.com/waf/pricing/\">WAF Pricing</a>.</p> </note>"}}, "documentation": "<p> A web ACL defines a collection of rules to use to inspect and control web requests. Each rule has a statement that defines what to look for in web requests and an action that WAF applies to requests that match the statement. In the web ACL, you assign a default action to take (allow, block) for any request that does not match any of the rules. The rules in a web ACL can be a combination of the types <a>Rule</a>, <a>RuleGroup</a>, and managed rule group. You can associate a web ACL with one or more Amazon Web Services resources to protect. The resources can be an Amazon CloudFront distribution, an Amazon API Gateway REST API, an Application Load Balancer, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p>"}, "WebACLSummaries": {"type": "list", "member": {"shape": "WebACLSummary"}}, "WebACLSummary": {"type": "structure", "members": {"Name": {"shape": "EntityName", "documentation": "<p>The name of the web ACL. You cannot change the name of a web ACL after you create it.</p>"}, "Id": {"shape": "EntityId", "documentation": "<p>The unique identifier for the web ACL. This ID is returned in the responses to create and list commands. You provide it to operations like update and delete.</p>"}, "Description": {"shape": "EntityDescription", "documentation": "<p>A description of the web ACL that helps with identification. </p>"}, "LockToken": {"shape": "LockToken", "documentation": "<p>A token used for optimistic locking. WAF returns a token to your <code>get</code> and <code>list</code> requests, to mark the state of the entity at the time of the request. To make changes to the entity associated with the token, you provide the token to operations like <code>update</code> and <code>delete</code>. WAF uses the token to ensure that no changes have been made to the entity since you last retrieved it. If a change has been made, the update fails with a <code>WAFOptimisticLockException</code>. If this happens, perform another <code>get</code>, and use the new token returned by that operation. </p>"}, "ARN": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity.</p>"}}, "documentation": "<p>High-level information about a <a>WebACL</a>, returned by operations like create and list. This provides information like the ID, that you can use to retrieve and manage a <code>WebACL</code>, and the ARN, that you provide to operations like <a>AssociateWebACL</a>.</p>"}, "XssMatchStatement": {"type": "structure", "required": ["FieldToMatch", "TextTransformations"], "members": {"FieldToMatch": {"shape": "FieldToMatch", "documentation": "<p>The part of the web request that you want WAF to inspect. </p>"}, "TextTransformations": {"shape": "TextTransformations", "documentation": "<p>Text transformations eliminate some of the unusual formatting that attackers use in web requests in an effort to bypass detection. Text transformations are used in rule match statements, to transform the <code>FieldToMatch</code> request component before inspecting it, and they're used in rate-based rule statements, to transform request components before using them as custom aggregation keys. If you specify one or more transformations to apply, WAF performs all transformations on the specified content, starting from the lowest priority setting, and then uses the transformed component contents. </p>"}}, "documentation": "<p>A rule statement that inspects for cross-site scripting (XSS) attacks. In XSS attacks, the attacker uses vulnerabilities in a benign website as a vehicle to inject malicious client-site scripts into other legitimate web browsers. </p>"}}, "documentation": "<fullname>WAF</fullname> <note> <p>This is the latest version of the <b>WAF</b> API, released in November, 2019. The names of the entities that you use to access this API, like endpoints and namespaces, all have the versioning information added, like \"V2\" or \"v2\", to distinguish from the prior version. We recommend migrating your resources to this version, because it has a number of significant improvements.</p> <p>If you used WAF prior to this release, you can't use this WAFV2 API to access any WAF resources that you created before. You can access your old rules, web ACLs, and other WAF resources only through the WAF Classic APIs. The WAF Classic APIs have retained the prior names, endpoints, and namespaces. </p> <p>For information, including how to migrate your WAF resources to this version, see the <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/waf-chapter.html\">WAF Developer Guide</a>. </p> </note> <p>WAF is a web application firewall that lets you monitor the HTTP and HTTPS requests that are forwarded to an Amazon CloudFront distribution, Amazon API Gateway REST API, Application Load Balancer, AppSync GraphQL API, Amazon Cognito user pool, App Runner service, or Amazon Web Services Verified Access instance. WAF also lets you control access to your content, to protect the Amazon Web Services resource that WAF is monitoring. Based on conditions that you specify, such as the IP addresses that requests originate from or the values of query strings, the protected resource responds to requests with either the requested content, an HTTP 403 status code (Forbidden), or with a custom response. </p> <p>This API guide is for developers who need detailed information about WAF API actions, data types, and errors. For detailed information about WAF features and guidance for configuring and using WAF, see the <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/what-is-aws-waf.html\">WAF Developer Guide</a>.</p> <p>You can make calls using the endpoints listed in <a href=\"https://docs.aws.amazon.com/general/latest/gr/waf.html\">WAF endpoints and quotas</a>. </p> <ul> <li> <p>For regional applications, you can use any of the endpoints in the list. A regional application can be an Application Load Balancer (ALB), an Amazon API Gateway REST API, an AppSync GraphQL API, an Amazon Cognito user pool, an App Runner service, or an Amazon Web Services Verified Access instance. </p> </li> <li> <p>For Amazon CloudFront applications, you must use the API endpoint listed for US East (N. Virginia): us-east-1.</p> </li> </ul> <p>Alternatively, you can use one of the Amazon Web Services SDKs to access an API that's tailored to the programming language or platform that you're using. For more information, see <a href=\"http://aws.amazon.com/tools/#SDKs\">Amazon Web Services SDKs</a>.</p> <p>We currently provide two versions of the WAF API: this API and the prior versions, the classic WAF APIs. This new API provides the same functionality as the older versions, with the following major improvements:</p> <ul> <li> <p>You use one API for both global and regional applications. Where you need to distinguish the scope, you specify a <code>Scope</code> parameter and set it to <code>CLOUDFRONT</code> or <code>REGIONAL</code>. </p> </li> <li> <p>You can define a web ACL or rule group with a single call, and update it with a single call. You define all rule specifications in JSON format, and pass them to your rule group or web ACL calls.</p> </li> <li> <p>The limits WAF places on the use of rules more closely reflects the cost of running each type of rule. Rule groups include capacity settings, so you know the maximum cost of a rule group when you use it.</p> </li> </ul>"}