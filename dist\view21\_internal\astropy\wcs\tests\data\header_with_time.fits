SIMPLE  =                    F / Conforms to FITS standard?  NO!                BITPIX  =                  -32 / IEEE single precision floating point           NAXIS   =                    0 / No image data                                                                                                                  CRPIX1A =                513.0 / Pixel coordinate of reference point            CRPIX2A =                513.0 / Pixel coordinate of reference point            CRPIX3A =               1025.0 / Pixel coordinate of reference point            CRPIX4A =                  1.0 / Pixel coordinate of reference point            PC1_1A  =          0.866025404 / Linear transformation matrix element           PC1_2A  =          0.500000000 / Linear transformation matrix element           PC2_1A  =         -0.500000000 / Linear transformation matrix element           PC2_2A  =          0.866025404 / Linear transformation matrix element                                                                                           CDELT1A =                -0.10 / [deg] x-scale                                  CUNIT1A = 'deg'                / Degree units are required                      CTYPE1A = 'RA---SZP'           / Right ascension in slant zenithal projection   CRVAL1A =                150.0 / [deg] Right ascension at the reference point   CNAME1A = 'Right ascension (J2000)' / Axis name for labelling purposes          CDELT2A =                 0.10 / [deg] y-scale                                  CUNIT2A = 'deg'                / Degree units are required                      CTYPE2A = 'DEC--SZP'           / Declination in a slant zenithal projection     CRVAL2A =                -30.0 / [deg] Declination at the reference point       CNAME2A = 'Declination (J2000)' / Axis name for labelling purposes              PV1_1A  =                  0.0 / [deg] Native longitude of the reference point  PV1_2A  =                 90.0 / [deg] Native latitude  of the reference point  PV1_3A  =                195.0 / [deg] LONPOLEa by another name (precedence)    PV1_4A  =                999.0 / [deg] LATPOLEa by another name (precedence)    PV2_1A  =                  0.0 / SZP distance, in spherical radii               PV2_2A  =                180.0 / [deg] SZP P-longitude                          PV2_3A  =                 45.0 / [deg] SZP P-latitude                           LONPOLEA=                195.0 / [deg] Native longitude of the NCP              LATPOLEA=                999.0 / [deg] Native latitude of the NCP               RADESYSA= 'FK5'                / Mean equatorial coordinates, IAU 1984 system   EQUINOXA=               2000.0 / [yr] Equinox of equatorial coordinates                                                                                         CDELT3A =      -9.635265432E-6 / [m] Wavelength scale                           CUNIT3A = 'm'                  / Wavelength units                               CTYPE3A = 'WAVE-F2W'           / Frequency axis expressed as wavelength         CRVAL3A =          0.214982042 / [m] Reference wavelength                       CNAME3A = 'Wavelength'         / Axis name for labelling purposes               CRDER3A =              1.0E-11 / [m] Wavelength calibration, random error       CSYER3A =              1.0E-12 / [m] Wavelength calibration, systematic error   RESTFRQA=         1.42040575E9 / [Hz] HI rest frequency                         RESTWAVA=          0.211061141 / [m] HI rest wavelength                         SPECSYSA= 'BARYCENT'           / Reference frame of spectral coordinates        SSYSOBSA= 'TOPOCENT'           / Reference frame of observation                 VELOSYSA=               1500.0 / [m/s] Bary-topo velocity towards the source    SSYSSRCA= 'LSRK'               / Reference frame of source redshift             ZSOURCEA=               0.0025 / Redshift of the source                                                                                                         CDELT4A =                  1.0 / [s] Time scale                                 CUNIT4A = 's'                  / Time units                                     CTYPE4A = 'TIME    '           / String value and comment containing quotes (') CRVAL4A =                 -2E3 / [s] Time at the reference point                CNAME4A = 'Time offset'        / Axis name for labelling purposes               PS4_0A  = 'UTC'                / Time measurement system                                                                                                        UNDEF   =                      / Undefined keyvalue                             TRUE    =                    T / Logical                                        FALSE   =                    F / Logical                                        INT32   =          00000012345 / Not a 64-bit integer                           INT32   =     -000000123456789 / Not a 64-bit integer                           INT32   =          -2147483648 / Not a 64-bit integer (INT_MIN)                 INT32   =           2147483647 / Not a 64-bit integer (INT_MAX)                 INT32   =    0000000000000000000000000000000000012345 / Not a very long integer INT32   =       -000000000000000000000000000123456789 / Not a very long integer INT64   =          -2147483649 / 64-bit integer (INT_MIN - 1)                   INT64   =          +2147483648 / 64-bit integer (INT_MAX + 1)                   INT64   =  +100000000000000000 / 64-bit integer                                 INT64   =  -876543210987654321 / 64-bit integer                                 INT64   = -9223372036854775808 / Not a very long integer (LONG_MIN)             INT64   = +9223372036854775807 / Not a very long integer (LONG_MAX)             INT64   = -000000000000000000000000000000876543210987654321 / 64-bit integer    INTVL   = -9223372036854775809 / Very long integer (LONG_MIN - 1)               INTVL   = +9223372036854775808 / Very long integer (LONG_MAX + 1)               INTVL   = -100000000000000000000000000000876543210987654321 / Very-long integer INTVL   = +123456789012345678901234567890123456789012345678901234567890123456789INTVL   = 1234567890123456789012345678901234567890123456789012345678901234567890FLOAT   =        3.14159265358 / Floating point                                 FLOAT   =      1.602176565E-19 / Floating point, lower-case exp allowed         FLOAT   =      2.99792458E8    / Floating point                                 FLOAT   =       6.62606957D-34 / Floating point, lower-case exp allowed         FLOAT   =        6.02214129D23 / Floating point                                 COMPLEX =            (137, -1) / An integer complex keyvalue                    COMPLEX =         (10E5, -0.1) / A floating point complex keyvalue              GOODSTR =     '"G''DAY"  '     / A valid string keyvalue                        BLANKS  =   '              '   / An all-blank string equals a single blank      LONGSTR = 'The loooooongest possible non-continued string value, 68 characters.'END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             