{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["![image](testimage.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.4.3"}}, "nbformat": 4, "nbformat_minor": 0}