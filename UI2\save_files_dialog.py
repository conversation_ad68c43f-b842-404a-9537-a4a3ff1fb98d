#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QListWidgetItem,
    QComboBox, QPushButton, QFileDialog, QMessageBox, QProgressBar, QGroupBox,
    QCheckBox, QLineEdit, QFormLayout, QSpinBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont
from language_manager import get_text

# 导入文件读取模块
try:
    from spectrum_file_reader import SpectrumFileReader
    from nod_file_serializer import NodFileSerializer
    NOD_MODULES_AVAILABLE = True
except ImportError:
    NOD_MODULES_AVAILABLE = False

class FileSaveWorker(QThread):
    """文件保存工作线程"""
    progress_updated = pyqtSignal(int)
    file_saved = pyqtSignal(str, str)  # 文件路径, 状态
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)
    
    def __init__(self, files_to_save: List[Dict], save_format: str, output_dir: str):
        super().__init__()
        self.files_to_save = files_to_save
        self.save_format = save_format
        self.output_dir = output_dir
        self.is_cancelled = False
        
    def run(self):
        """执行文件保存任务"""
        try:
            total_files = len(self.files_to_save)
            
            for i, file_info in enumerate(self.files_to_save):
                if self.is_cancelled:
                    break
                    
                try:
                    file_path = file_info['file_path']
                    spectrum_data = file_info['spectrum_data']
                    
                    # 生成输出文件名
                    base_name = os.path.splitext(os.path.basename(file_path))[0]
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_filename = f"{base_name}_{timestamp}.{self.save_format}"
                    output_path = os.path.join(self.output_dir, output_filename)
                    
                    # 根据格式保存文件
                    if self.save_format == 'csv':
                        self._save_as_csv(spectrum_data, output_path, file_info.get('file_info', {}))
                    elif self.save_format == 'txt':
                        self._save_as_txt(spectrum_data, output_path, file_info.get('file_info', {}))
                    elif self.save_format == 'nod':
                        self._save_as_nod(spectrum_data, output_path, file_info)
                    
                    self.file_saved.emit(output_path, "成功")
                    
                except Exception as e:
                    self.file_saved.emit(file_path, f"失败: {str(e)}")
                
                # 更新进度
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
                
        except Exception as e:
            self.error_occurred.emit(str(e))
        finally:
            self.finished.emit()
    
    def _save_as_csv(self, spectrum_data: np.ndarray, output_path: str, file_info: Dict = None):
        """保存为CSV格式，使用integrated_measurement_logic的保存格式"""
        try:
            # 导入integrated_measurement_logic
            from integrated_measurement_logic import IntegratedMeasurementLogic
            
            # 创建临时实例
            logic = IntegratedMeasurementLogic()
            
            # 构造compatible数据格式
            data = self._convert_to_measurement_data_format(spectrum_data, file_info, output_path)
            
            # 使用integrated_measurement_logic的save_as_csv方法
            logic.save_as_csv(data, output_path)
                
        except Exception as e:
            raise Exception(f"保存CSV文件失败: {e}")
    
    def _save_as_txt(self, spectrum_data: np.ndarray, output_path: str, file_info: Dict = None):
        """保存为TXT格式，使用integrated_measurement_logic的保存格式"""
        try:
            # 导入integrated_measurement_logic
            from integrated_measurement_logic import IntegratedMeasurementLogic
            
            # 创建临时实例
            logic = IntegratedMeasurementLogic()
            
            # 构造compatible数据格式
            data = self._convert_to_measurement_data_format(spectrum_data, file_info, output_path)
            
            # 使用integrated_measurement_logic的save_as_txt方法
            logic.save_as_txt(data, output_path)
                    
        except Exception as e:
            raise Exception(f"保存TXT文件失败: {e}")
    
    def _save_as_nod(self, spectrum_data: np.ndarray, output_path: str, file_info: Dict):
        """保存为NOD格式，使用integrated_measurement_logic的保存格式"""
        try:
            # 导入integrated_measurement_logic
            from integrated_measurement_logic import IntegratedMeasurementLogic
            
            # 创建临时实例
            logic = IntegratedMeasurementLogic()
            
            # 构造compatible数据格式
            data = self._convert_to_measurement_data_format(spectrum_data, file_info, output_path)
            
            # 使用integrated_measurement_logic的save_as_nod方法
            logic.save_as_nod(data, output_path)
            
        except Exception as e:
            raise Exception(f"保存NOD文件失败: {e}")
    
    def _convert_to_measurement_data_format(self, spectrum_data: np.ndarray, file_info: Dict, output_path: str) -> Dict:
        """将光谱数据转换为integrated_measurement_logic兼容的数据格式"""
        try:
            # 提取基础文件名（不包含扩展名）
            base_filename = os.path.splitext(os.path.basename(output_path))[0]
            
            # 构造兼容的数据格式
            data = {
                # 固定信息
                'data_generated_by': 'Neolily',
                'file_version': '4',
                'description': '保存文件对话框导出',
                'acquisition_mode': '精确采集',
                'sampling_interval': '0',
                
                # 文件信息
                'filename': base_filename,
                'original_filename': file_info.get('文件名', base_filename) if file_info else base_filename,
                'datetime_str': datetime.now().strftime("%Y%m%d_%H%M%S"),
                'operator': file_info.get('操作员', '未知操作员') if file_info else '未知操作员',
                'scan_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                
                # 参数信息
                'integration_time': file_info.get('积分时间', '1000') if file_info else '1000',
                'laser_power': file_info.get('激光功率', '100') if file_info else '100',
                'average_count': file_info.get('平均次数', '1') if file_info else '1',
                'current_measurement': '1',
                'device_model': file_info.get('设备型号', '未知设备型号') if file_info else '未知设备型号',
                'device_serial': file_info.get('设备序列号', '未知序列号') if file_info else '未知序列号',
                'pixel_count': len(spectrum_data),
                'hole_id': file_info.get('孔位', '') if file_info else '',
                
                # 光谱数据
                'raman_shift': spectrum_data[:, 0],
                'intensity': spectrum_data[:, 1]
            }
            
            return data
            
        except Exception as e:
            raise Exception(f"数据格式转换失败: {e}")
    
    def cancel(self):
        """取消保存任务"""
        self.is_cancelled = True


class SaveFilesDialog(QDialog):
    """文件保存对话框"""
    
    def __init__(self, available_files: List[Dict], parent=None):
        super().__init__(parent)
        self.available_files = available_files
        self.selected_files = []
        self.save_worker = None
        
        # 从父窗口获取编辑器实例，用于读取文件数据
        self.editor = None
        if parent:
            self.editor = getattr(parent, 'editor', None)
        
        self.setWindowTitle("保存文件")
        self.setModal(True)
        self.resize(600, 500)
        
        self.setup_ui()
        self.setup_connections()
        self.populate_file_list()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 文件选择组
        file_group = QGroupBox(get_text("select_files_to_save"))
        file_layout = QVBoxLayout(file_group)
        
        # 全选/取消全选按钮
        select_layout = QHBoxLayout()
        self.select_all_btn = QPushButton(get_text("select_all"))
        self.deselect_all_btn = QPushButton(get_text("deselect_all"))
        select_layout.addWidget(self.select_all_btn)
        select_layout.addWidget(self.deselect_all_btn)
        select_layout.addStretch()
        file_layout.addLayout(select_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
        
        # 保存设置组
        settings_group = QGroupBox(get_text("save_settings"))
        settings_layout = QFormLayout(settings_group)
        
        # 保存格式选择
        self.format_combo = QComboBox()
        self.format_combo.addItems(["CSV", "TXT", "NOD"])
        settings_layout.addRow(get_text("save_format"), self.format_combo)
        
        # 输出目录选择
        dir_layout = QHBoxLayout()
        self.dir_edit = QLineEdit()
        self.dir_edit.setPlaceholderText(get_text("select_output_directory"))
        self.browse_btn = QPushButton(get_text("browse"))
        dir_layout.addWidget(self.dir_edit)
        dir_layout.addWidget(self.browse_btn)
        settings_layout.addRow(get_text("output_directory"), dir_layout)
        
        # 文件名前缀
        self.prefix_edit = QLineEdit()
        self.prefix_edit.setPlaceholderText(get_text("optional_filename_prefix"))
        settings_layout.addRow(get_text("filename_prefix"), self.prefix_edit)
        
        layout.addWidget(settings_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 按钮组
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton(get_text("start_saving"))
        self.cancel_btn = QPushButton(get_text("cancel"))
        self.close_btn = QPushButton(get_text("close"))
        self.close_btn.setVisible(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        self.select_all_btn.clicked.connect(self.select_all_files)
        self.deselect_all_btn.clicked.connect(self.deselect_all_files)
        self.browse_btn.clicked.connect(self.browse_output_directory)
        self.save_btn.clicked.connect(self.start_saving)
        self.cancel_btn.clicked.connect(self.cancel_saving)
        self.close_btn.clicked.connect(self.accept)
        
    def populate_file_list(self):
        """填充文件列表"""
        self.file_list.clear()
        
        # 从编辑器中获取勾选的文件路径
        if self.editor and hasattr(self.editor, 'file_root_item'):
            checked_files = []
            for i in range(self.editor.file_root_item.childCount()):
                item = self.editor.file_root_item.child(i)
                if item.checkState(0) == Qt.CheckState.Checked:
                    file_path = item.data(0, Qt.ItemDataRole.UserRole)
                    checked_files.append(file_path)
            
            for file_path in checked_files:
                file_name = os.path.basename(str(file_path))
                
                # 创建列表项
                item = QListWidgetItem(file_name)
                item.setData(Qt.ItemDataRole.UserRole, file_path)
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                item.setCheckState(Qt.CheckState.Checked)  # 默认选中
                
                self.file_list.addItem(item)
        else:
            # 如果没有编辑器，使用传入的文件信息
            for file_info in self.available_files:
                file_name = file_info.get('文件名', '未知文件')
                
                # 创建列表项
                item = QListWidgetItem(file_name)
                item.setData(Qt.ItemDataRole.UserRole, file_info)
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                item.setCheckState(Qt.CheckState.Unchecked)
                
                self.file_list.addItem(item)
    
    def select_all_files(self):
        """全选文件"""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            item.setCheckState(Qt.CheckState.Checked)
    
    def deselect_all_files(self):
        """取消全选文件"""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            item.setCheckState(Qt.CheckState.Unchecked)
    
    def browse_output_directory(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", 
            self.dir_edit.text() or os.getcwd()
        )
        if dir_path:
            self.dir_edit.setText(dir_path)
    
    def get_selected_files(self) -> List[Dict]:
        """获取选中的文件"""
        selected_files = []
        
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                file_data = item.data(Qt.ItemDataRole.UserRole)
                
                # 如果是文件路径，需要读取文件数据
                if isinstance(file_data, str):
                    try:
                        from spectrum_file_reader import SpectrumFileReader
                        reader = SpectrumFileReader()
                        file_info, spectrum_data = reader.read_file(file_data)
                        
                        selected_files.append({
                            'file_path': file_data,
                            'spectrum_data': spectrum_data,
                            'file_info': file_info
                        })
                    except Exception as e:
                        print(f"读取文件失败: {file_data}, {e}")
                else:
                    # 如果已经是文件信息字典，直接使用
                    selected_files.append(file_data)
        
        return selected_files
    
    def validate_inputs(self) -> bool:
        """验证输入"""
        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.warning(self, "警告", "请选择至少一个文件进行保存")
            return False
        
        output_dir = self.dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return False
        
        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "警告", "输出目录不存在")
            return False
        
        return True
    
    def start_saving(self):
        """开始保存文件"""
        if not self.validate_inputs():
            return
        
        selected_files = self.get_selected_files()
        save_format = self.format_combo.currentText().lower()
        output_dir = self.dir_edit.text().strip()
        
        # 更新UI状态
        self.save_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建并启动保存线程
        self.save_worker = FileSaveWorker(selected_files, save_format, output_dir)
        self.save_worker.progress_updated.connect(self.progress_bar.setValue)
        self.save_worker.file_saved.connect(self.on_file_saved)
        self.save_worker.finished.connect(self.on_save_finished)
        self.save_worker.error_occurred.connect(self.on_save_error)
        
        self.save_worker.start()
    
    def cancel_saving(self):
        """取消保存"""
        if self.save_worker and self.save_worker.isRunning():
            self.save_worker.cancel()
            self.save_worker.wait()
        
        self.reset_ui_state()
    
    def on_file_saved(self, file_path: str, status: str):
        """文件保存完成回调"""
        file_name = os.path.basename(file_path)
        if "成功" in status:
            print(f"文件保存成功: {file_name}")
        else:
            print(f"文件保存失败: {file_name} - {status}")
    
    def on_save_finished(self):
        """保存完成回调"""
        QMessageBox.information(self, "完成", "文件保存完成！")
        self.reset_ui_state()
        self.close_btn.setVisible(True)
        self.save_btn.setVisible(False)
        self.cancel_btn.setVisible(False)
    
    def on_save_error(self, error_msg: str):
        """保存错误回调"""
        QMessageBox.critical(self, "错误", f"保存过程中发生错误:\n{error_msg}")
        self.reset_ui_state()
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.save_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        if self.save_worker:
            self.save_worker.deleteLater()
            self.save_worker = None
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.save_worker and self.save_worker.isRunning():
            reply = QMessageBox.question(
                self, "确认", "文件正在保存中，确定要取消吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.cancel_saving()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept() 