{"metadata": {"apiVersion": "2020-01-01", "endpointPrefix": "macie2", "signingName": "macie2", "serviceFullName": "Amazon Macie 2", "serviceId": "Macie2", "protocol": "rest-json", "jsonVersion": "1.1", "uid": "macie2-2020-01-01", "signatureVersion": "v4"}, "operations": {"AcceptInvitation": {"name": "AcceptInvitation", "http": {"method": "POST", "requestUri": "/invitations/accept", "responseCode": 200}, "input": {"shape": "AcceptInvitationRequest"}, "output": {"shape": "AcceptInvitationResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Accepts an Amazon Macie membership invitation that was received from a specific account.</p>"}, "BatchGetCustomDataIdentifiers": {"name": "BatchGetCustomDataIdentifiers", "http": {"method": "POST", "requestUri": "/custom-data-identifiers/get", "responseCode": 200}, "input": {"shape": "BatchGetCustomDataIdentifiersRequest"}, "output": {"shape": "BatchGetCustomDataIdentifiersResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves information about one or more custom data identifiers.</p>"}, "CreateAllowList": {"name": "CreateAllowList", "http": {"method": "POST", "requestUri": "/allow-lists", "responseCode": 200}, "input": {"shape": "CreateAllowListRequest"}, "output": {"shape": "CreateAllowListResponse", "documentation": "<p>The request succeeded. The specified allow list was created.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Creates and defines the settings for an allow list.</p>"}, "CreateClassificationJob": {"name": "CreateClassificationJob", "http": {"method": "POST", "requestUri": "/jobs", "responseCode": 200}, "input": {"shape": "CreateClassificationJobRequest"}, "output": {"shape": "CreateClassificationJobResponse", "documentation": "<p>The request succeeded. The specified job was created.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Creates and defines the settings for a classification job.</p>"}, "CreateCustomDataIdentifier": {"name": "CreateCustomDataIdentifier", "http": {"method": "POST", "requestUri": "/custom-data-identifiers", "responseCode": 200}, "input": {"shape": "CreateCustomDataIdentifierRequest"}, "output": {"shape": "CreateCustomDataIdentifierResponse", "documentation": "<p>The request succeeded. The specified custom data identifier was created.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Creates and defines the criteria and other settings for a custom data identifier.</p>"}, "CreateFindingsFilter": {"name": "C<PERSON><PERSON><PERSON>ingsFilt<PERSON>", "http": {"method": "POST", "requestUri": "/findingsfilters", "responseCode": 200}, "input": {"shape": "CreateFindingsFilterRequest"}, "output": {"shape": "CreateFindingsFilterResponse"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Creates and defines the criteria and other settings for a findings filter.</p>"}, "CreateInvitations": {"name": "CreateInvitations", "http": {"method": "POST", "requestUri": "/invitations", "responseCode": 200}, "input": {"shape": "CreateInvitationsRequest"}, "output": {"shape": "CreateInvitationsResponse", "documentation": "<p>The request succeeded. Processing might not be complete.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Sends an Amazon Macie membership invitation to one or more accounts.</p>"}, "CreateMember": {"name": "CreateMember", "http": {"method": "POST", "requestUri": "/members", "responseCode": 200}, "input": {"shape": "CreateMemberRequest"}, "output": {"shape": "CreateMemberResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Associates an account with an Amazon Macie administrator account.</p>"}, "CreateSampleFindings": {"name": "CreateSampleFindings", "http": {"method": "POST", "requestUri": "/findings/sample", "responseCode": 200}, "input": {"shape": "CreateSampleFindingsRequest"}, "output": {"shape": "CreateSampleFindingsResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Creates sample findings.</p>"}, "DeclineInvitations": {"name": "DeclineInvitations", "http": {"method": "POST", "requestUri": "/invitations/decline", "responseCode": 200}, "input": {"shape": "DeclineInvitationsRequest"}, "output": {"shape": "DeclineInvitationsResponse", "documentation": "<p>The request succeeded. Processing might not be complete.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Declines Amazon Macie membership invitations that were received from specific accounts.</p>"}, "DeleteAllowList": {"name": "DeleteAllowList", "http": {"method": "DELETE", "requestUri": "/allow-lists/{id}", "responseCode": 200}, "input": {"shape": "DeleteAllowListRequest"}, "output": {"shape": "DeleteAllowListResponse", "documentation": "<p>The request succeeded. The allow list was deleted and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Deletes an allow list.</p>"}, "DeleteCustomDataIdentifier": {"name": "DeleteCustomDataIdentifier", "http": {"method": "DELETE", "requestUri": "/custom-data-identifiers/{id}", "responseCode": 200}, "input": {"shape": "DeleteCustomDataIdentifierRequest"}, "output": {"shape": "DeleteCustomDataIdentifierResponse", "documentation": "<p>The request succeeded. The specified custom data identifier was deleted and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Soft deletes a custom data identifier.</p>"}, "DeleteFindingsFilter": {"name": "DeleteFindingsFilter", "http": {"method": "DELETE", "requestUri": "/findingsfilters/{id}", "responseCode": 200}, "input": {"shape": "DeleteFindingsFilterRequest"}, "output": {"shape": "DeleteFindingsFilterResponse", "documentation": "<p>The request succeeded. The specified findings filter was deleted and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Deletes a findings filter.</p>"}, "DeleteInvitations": {"name": "DeleteInvitations", "http": {"method": "POST", "requestUri": "/invitations/delete", "responseCode": 200}, "input": {"shape": "DeleteInvitationsRequest"}, "output": {"shape": "DeleteInvitationsResponse", "documentation": "<p>The request succeeded. Processing might not be complete.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Deletes Amazon Macie membership invitations that were received from specific accounts.</p>"}, "DeleteMember": {"name": "DeleteMember", "http": {"method": "DELETE", "requestUri": "/members/{id}", "responseCode": 200}, "input": {"shape": "DeleteMemberRequest"}, "output": {"shape": "DeleteMemberResponse", "documentation": "<p>The request succeeded. The association was deleted and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Deletes the association between an Amazon Macie administrator account and an account.</p>"}, "DescribeBuckets": {"name": "DescribeBuckets", "http": {"method": "POST", "requestUri": "/datasources/s3", "responseCode": 200}, "input": {"shape": "DescribeBucketsRequest"}, "output": {"shape": "DescribeBucketsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves (queries) statistical data and other information about one or more S3 buckets that Amazon Macie monitors and analyzes for an account.</p>"}, "DescribeClassificationJob": {"name": "DescribeClassificationJob", "http": {"method": "GET", "requestUri": "/jobs/{jobId}", "responseCode": 200}, "input": {"shape": "DescribeClassificationJobRequest"}, "output": {"shape": "DescribeClassificationJobResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the status and settings for a classification job.</p>"}, "DescribeOrganizationConfiguration": {"name": "DescribeOrganizationConfiguration", "http": {"method": "GET", "requestUri": "/admin/configuration", "responseCode": 200}, "input": {"shape": "DescribeOrganizationConfigurationRequest"}, "output": {"shape": "DescribeOrganizationConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the Amazon Macie configuration settings for an organization in Organizations.</p>"}, "DisableMacie": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "DELETE", "requestUri": "/macie", "responseCode": 200}, "input": {"shape": "DisableMacieRequest"}, "output": {"shape": "DisableMacieResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Disables Amazon Macie and deletes all settings and resources for a Macie account.</p>"}, "DisableOrganizationAdminAccount": {"name": "DisableOrganizationAdminAccount", "http": {"method": "DELETE", "requestUri": "/admin", "responseCode": 200}, "input": {"shape": "DisableOrganizationAdminAccountRequest"}, "output": {"shape": "DisableOrganizationAdminAccountResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Disables an account as the delegated Amazon Macie administrator account for an organization in Organizations.</p>"}, "DisassociateFromAdministratorAccount": {"name": "DisassociateFromAdministratorAccount", "http": {"method": "POST", "requestUri": "/administrator/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateFromAdministratorAccountRequest"}, "output": {"shape": "DisassociateFromAdministratorAccountResponse"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Disassociates a member account from its Amazon Macie administrator account.</p>"}, "DisassociateFromMasterAccount": {"name": "DisassociateFromMasterAccount", "http": {"method": "POST", "requestUri": "/master/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateFromMasterAccountRequest"}, "output": {"shape": "DisassociateFromMasterAccountResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>(Deprecated) Disassociates a member account from its Amazon Macie administrator account. This operation has been replaced by the <link  linkend=\"DisassociateFromAdministratorAccount\">DisassociateFromAdministratorAccount</link> operation.</p>"}, "DisassociateMember": {"name": "DisassociateMember", "http": {"method": "POST", "requestUri": "/members/disassociate/{id}", "responseCode": 200}, "input": {"shape": "DisassociateMemberRequest"}, "output": {"shape": "DisassociateMemberResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Disassociates an Amazon Macie administrator account from a member account.</p>"}, "EnableMacie": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/macie", "responseCode": 200}, "input": {"shape": "EnableMacieRequest"}, "output": {"shape": "EnableMacieResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Enables Amazon Macie and specifies the configuration settings for a Macie account.</p>"}, "EnableOrganizationAdminAccount": {"name": "EnableOrganizationAdminAccount", "http": {"method": "POST", "requestUri": "/admin", "responseCode": 200}, "input": {"shape": "EnableOrganizationAdminAccountRequest"}, "output": {"shape": "EnableOrganizationAdminAccountResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Designates an account as the delegated Amazon Macie administrator account for an organization in Organizations.</p>"}, "GetAdministratorAccount": {"name": "GetAdministratorAccount", "http": {"method": "GET", "requestUri": "/administrator", "responseCode": 200}, "input": {"shape": "GetAdministratorAccountRequest"}, "output": {"shape": "GetAdministratorAccountResponse"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves information about the Amazon Macie administrator account for an account.</p>"}, "GetAllowList": {"name": "GetAllowList", "http": {"method": "GET", "requestUri": "/allow-lists/{id}", "responseCode": 200}, "input": {"shape": "GetAllowListRequest"}, "output": {"shape": "GetAllowListResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves the settings and status of an allow list.</p>"}, "GetAutomatedDiscoveryConfiguration": {"name": "GetAutomatedDiscoveryConfiguration", "http": {"method": "GET", "requestUri": "/automated-discovery/configuration", "responseCode": 200}, "input": {"shape": "GetAutomatedDiscoveryConfigurationRequest"}, "output": {"shape": "GetAutomatedDiscoveryConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves the configuration settings and status of automated sensitive data discovery for an account.</p>"}, "GetBucketStatistics": {"name": "GetBucketStatistics", "http": {"method": "POST", "requestUri": "/datasources/s3/statistics", "responseCode": 200}, "input": {"shape": "GetBucketStatisticsRequest"}, "output": {"shape": "GetBucketStatisticsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves (queries) aggregated statistical data about all the S3 buckets that Amazon Macie monitors and analyzes for an account.</p>"}, "GetClassificationExportConfiguration": {"name": "GetClassificationExportConfiguration", "http": {"method": "GET", "requestUri": "/classification-export-configuration", "responseCode": 200}, "input": {"shape": "GetClassificationExportConfigurationRequest"}, "output": {"shape": "GetClassificationExportConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the configuration settings for storing data classification results.</p>"}, "GetClassificationScope": {"name": "GetClassificationScope", "http": {"method": "GET", "requestUri": "/classification-scopes/{id}", "responseCode": 200}, "input": {"shape": "GetClassificationScopeRequest"}, "output": {"shape": "GetClassificationScopeResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves the classification scope settings for an account.</p>"}, "GetCustomDataIdentifier": {"name": "GetCustomDataIdentifier", "http": {"method": "GET", "requestUri": "/custom-data-identifiers/{id}", "responseCode": 200}, "input": {"shape": "GetCustomDataIdentifierRequest"}, "output": {"shape": "GetCustomDataIdentifierResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the criteria and other settings for a custom data identifier.</p>"}, "GetFindingStatistics": {"name": "GetFindingStatistics", "http": {"method": "POST", "requestUri": "/findings/statistics", "responseCode": 200}, "input": {"shape": "GetFindingStatisticsRequest"}, "output": {"shape": "GetFindingStatisticsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves (queries) aggregated statistical data about findings.</p>"}, "GetFindings": {"name": "GetFindings", "http": {"method": "POST", "requestUri": "/findings/describe", "responseCode": 200}, "input": {"shape": "GetFindingsRequest"}, "output": {"shape": "GetFindingsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the details of one or more findings.</p>"}, "GetFindingsFilter": {"name": "GetFindingsFilter", "http": {"method": "GET", "requestUri": "/findingsfilters/{id}", "responseCode": 200}, "input": {"shape": "GetFindingsFilterRequest"}, "output": {"shape": "GetFindingsFilterResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the criteria and other settings for a findings filter.</p>"}, "GetFindingsPublicationConfiguration": {"name": "GetFindingsPublicationConfiguration", "http": {"method": "GET", "requestUri": "/findings-publication-configuration", "responseCode": 200}, "input": {"shape": "GetFindingsPublicationConfigurationRequest"}, "output": {"shape": "GetFindingsPublicationConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the configuration settings for publishing findings to Security Hub.</p>"}, "GetInvitationsCount": {"name": "GetInvitationsCount", "http": {"method": "GET", "requestUri": "/invitations/count", "responseCode": 200}, "input": {"shape": "GetInvitationsCountRequest"}, "output": {"shape": "GetInvitationsCountResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the count of Amazon Macie membership invitations that were received by an account.</p>"}, "GetMacieSession": {"name": "GetMacieSession", "http": {"method": "GET", "requestUri": "/macie", "responseCode": 200}, "input": {"shape": "GetMacieSessionRequest"}, "output": {"shape": "GetMacieSessionResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves the status and configuration settings for an Amazon Macie account.</p>"}, "GetMasterAccount": {"name": "GetMasterAccount", "http": {"method": "GET", "requestUri": "/master", "responseCode": 200}, "input": {"shape": "GetMasterAccountRequest"}, "output": {"shape": "GetMasterAccountResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>(Deprecated) Retrieves information about the Amazon Macie administrator account for an account. This operation has been replaced by the <link  linkend=\"GetAdministratorAccount\">GetAdministratorAccount</link> operation.</p>"}, "GetMember": {"name": "GetMember", "http": {"method": "GET", "requestUri": "/members/{id}", "responseCode": 200}, "input": {"shape": "GetMemberRequest"}, "output": {"shape": "GetMemberResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves information about an account that's associated with an Amazon Macie administrator account.</p>"}, "GetResourceProfile": {"name": "GetResourceProfile", "http": {"method": "GET", "requestUri": "/resource-profiles", "responseCode": 200}, "input": {"shape": "GetResourceProfileRequest"}, "output": {"shape": "GetResourceProfileResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}], "documentation": "<p>Retrieves (queries) sensitive data discovery statistics and the sensitivity score for an S3 bucket.</p>"}, "GetRevealConfiguration": {"name": "GetRevealConfiguration", "http": {"method": "GET", "requestUri": "/reveal-configuration", "responseCode": 200}, "input": {"shape": "GetRevealConfigurationRequest"}, "output": {"shape": "GetRevealConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves the status and configuration settings for retrieving occurrences of sensitive data reported by findings.</p>"}, "GetSensitiveDataOccurrences": {"name": "GetSensitiveDataOccurrences", "http": {"method": "GET", "requestUri": "/findings/{findingId}/reveal", "responseCode": 200}, "input": {"shape": "GetSensitiveDataOccurrencesRequest"}, "output": {"shape": "GetSensitiveDataOccurrencesResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "UnprocessableEntityException", "documentation": "<p>The request failed because it contains instructions that Amazon Macie can't process (Unprocessable Entity).</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}], "documentation": "<p>Retrieves occurrences of sensitive data reported by a finding.</p>"}, "GetSensitiveDataOccurrencesAvailability": {"name": "GetSensitiveDataOccurrencesAvailability", "http": {"method": "GET", "requestUri": "/findings/{findingId}/reveal/availability", "responseCode": 200}, "input": {"shape": "GetSensitiveDataOccurrencesAvailabilityRequest"}, "output": {"shape": "GetSensitiveDataOccurrencesAvailabilityResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Checks whether occurrences of sensitive data can be retrieved for a finding.</p>"}, "GetSensitivityInspectionTemplate": {"name": "GetSensitivityInspectionTemplate", "http": {"method": "GET", "requestUri": "/templates/sensitivity-inspections/{id}", "responseCode": 200}, "input": {"shape": "GetSensitivityInspectionTemplateRequest"}, "output": {"shape": "GetSensitivityInspectionTemplateResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": " <p>Retrieves the settings for the sensitivity inspection template for an account.</p>"}, "GetUsageStatistics": {"name": "GetUsageStatistics", "http": {"method": "POST", "requestUri": "/usage/statistics", "responseCode": 200}, "input": {"shape": "GetUsageStatisticsRequest"}, "output": {"shape": "GetUsageStatisticsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves (queries) quotas and aggregated usage data for one or more accounts.</p>"}, "GetUsageTotals": {"name": "GetUsageTotals", "http": {"method": "GET", "requestUri": "/usage", "responseCode": 200}, "input": {"shape": "GetUsageTotalsRequest"}, "output": {"shape": "GetUsageTotalsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves (queries) aggregated usage data for an account.</p>"}, "ListAllowLists": {"name": "ListAllowLists", "http": {"method": "GET", "requestUri": "/allow-lists", "responseCode": 200}, "input": {"shape": "ListAllowListsRequest"}, "output": {"shape": "ListAllowListsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves a subset of information about all the allow lists for an account.</p>"}, "ListClassificationJobs": {"name": "ListClassificationJobs", "http": {"method": "POST", "requestUri": "/jobs/list", "responseCode": 200}, "input": {"shape": "ListClassificationJobsRequest"}, "output": {"shape": "ListClassificationJobsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves a subset of information about one or more classification jobs.</p>"}, "ListClassificationScopes": {"name": "ListClassificationScopes", "http": {"method": "GET", "requestUri": "/classification-scopes", "responseCode": 200}, "input": {"shape": "ListClassificationScopesRequest"}, "output": {"shape": "ListClassificationScopesResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves a subset of information about the classification scope for an account.</p>"}, "ListCustomDataIdentifiers": {"name": "ListCustomDataIdentifiers", "http": {"method": "POST", "requestUri": "/custom-data-identifiers/list", "responseCode": 200}, "input": {"shape": "ListCustomDataIdentifiersRequest"}, "output": {"shape": "ListCustomDataIdentifiersResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves a subset of information about all the custom data identifiers for an account.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/findings", "responseCode": 200}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves a subset of information about one or more findings.</p>"}, "ListFindingsFilters": {"name": "ListFindingsFilters", "http": {"method": "GET", "requestUri": "/findingsfilters", "responseCode": 200}, "input": {"shape": "ListFindingsFiltersRequest"}, "output": {"shape": "ListFindingsFiltersResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves a subset of information about all the findings filters for an account.</p>"}, "ListInvitations": {"name": "ListInvitations", "http": {"method": "GET", "requestUri": "/invitations", "responseCode": 200}, "input": {"shape": "ListInvitationsRequest"}, "output": {"shape": "ListInvitationsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves information about the Amazon Macie membership invitations that were received by an account.</p>"}, "ListManagedDataIdentifiers": {"name": "ListManagedDataIdentifiers", "http": {"method": "POST", "requestUri": "/managed-data-identifiers/list", "responseCode": 200}, "input": {"shape": "ListManagedDataIdentifiersRequest"}, "output": {"shape": "ListManagedDataIdentifiersResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [], "documentation": "<p>Retrieves information about all the managed data identifiers that Amazon Macie currently provides.</p>"}, "ListMembers": {"name": "ListMembers", "http": {"method": "GET", "requestUri": "/members", "responseCode": 200}, "input": {"shape": "ListMembersRequest"}, "output": {"shape": "ListMembersResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves information about the accounts that are associated with an Amazon Macie administrator account.</p>"}, "ListOrganizationAdminAccounts": {"name": "ListOrganizationAdminAccounts", "http": {"method": "GET", "requestUri": "/admin", "responseCode": 200}, "input": {"shape": "ListOrganizationAdminAccountsRequest"}, "output": {"shape": "ListOrganizationAdminAccountsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves information about the delegated Amazon Macie administrator account for an organization in Organizations.</p>"}, "ListResourceProfileArtifacts": {"name": "ListResourceProfileArtifacts", "http": {"method": "GET", "requestUri": "/resource-profiles/artifacts", "responseCode": 200}, "input": {"shape": "ListResourceProfileArtifactsRequest"}, "output": {"shape": "ListResourceProfileArtifactsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Retrieves information about objects that were selected from an S3 bucket for automated sensitive data discovery.</p>"}, "ListResourceProfileDetections": {"name": "ListResourceProfileDetections", "http": {"method": "GET", "requestUri": "/resource-profiles/detections", "responseCode": 200}, "input": {"shape": "ListResourceProfileDetectionsRequest"}, "output": {"shape": "ListResourceProfileDetectionsResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}], "documentation": "<p>Retrieves information about the types and amount of sensitive data that Amazon Macie found in an S3 bucket.</p>"}, "ListSensitivityInspectionTemplates": {"name": "ListSensitivityInspectionTemplates", "http": {"method": "GET", "requestUri": "/templates/sensitivity-inspections", "responseCode": 200}, "input": {"shape": "ListSensitivityInspectionTemplatesRequest"}, "output": {"shape": "ListSensitivityInspectionTemplatesResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": " <p>Retrieves a subset of information about the sensitivity inspection template for an account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [], "documentation": "<p>Retrieves the tags (keys and values) that are associated with an Amazon Macie resource.</p>"}, "PutClassificationExportConfiguration": {"name": "PutClassificationExportConfiguration", "http": {"method": "PUT", "requestUri": "/classification-export-configuration", "responseCode": 200}, "input": {"shape": "PutClassificationExportConfigurationRequest"}, "output": {"shape": "PutClassificationExportConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Creates or updates the configuration settings for storing data classification results.</p>"}, "PutFindingsPublicationConfiguration": {"name": "PutFindingsPublicationConfiguration", "http": {"method": "PUT", "requestUri": "/findings-publication-configuration", "responseCode": 200}, "input": {"shape": "PutFindingsPublicationConfigurationRequest"}, "output": {"shape": "PutFindingsPublicationConfigurationResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Updates the configuration settings for publishing findings to Security Hub.</p>"}, "SearchResources": {"name": "SearchResources", "http": {"method": "POST", "requestUri": "/datasources/search-resources", "responseCode": 200}, "input": {"shape": "SearchResourcesRequest"}, "output": {"shape": "SearchResourcesResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Retrieves (queries) statistical data and other information about Amazon Web Services resources that Amazon Macie monitors and analyzes.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [], "documentation": "<p>Adds or updates one or more tags (keys and values) that are associated with an Amazon Macie resource.</p>"}, "TestCustomDataIdentifier": {"name": "TestCustomDataIdentifier", "http": {"method": "POST", "requestUri": "/custom-data-identifiers/test", "responseCode": 200}, "input": {"shape": "TestCustomDataIdentifierRequest"}, "output": {"shape": "TestCustomDataIdentifierResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Tests a custom data identifier.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [], "documentation": "<p>Removes one or more tags (keys and values) from an Amazon Macie resource.</p>"}, "UpdateAllowList": {"name": "UpdateAllowList", "http": {"method": "PUT", "requestUri": "/allow-lists/{id}", "responseCode": 200}, "input": {"shape": "UpdateAllowListRequest"}, "output": {"shape": "UpdateAllowListResponse", "documentation": "<p>The request succeeded. The settings for the allow list were updated.</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Updates the settings for an allow list.</p>"}, "UpdateAutomatedDiscoveryConfiguration": {"name": "UpdateAutomatedDiscoveryConfiguration", "http": {"method": "PUT", "requestUri": "/automated-discovery/configuration", "responseCode": 200}, "input": {"shape": "UpdateAutomatedDiscoveryConfigurationRequest"}, "output": {"shape": "UpdateAutomatedDiscoveryConfigurationResponse", "documentation": "<p>The request succeeded. The status of the automated sensitive data discovery configuration for the account was updated and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Enables or disables automated sensitive data discovery for an account.</p>"}, "UpdateClassificationJob": {"name": "UpdateClassificationJob", "http": {"method": "PATCH", "requestUri": "/jobs/{jobId}", "responseCode": 200}, "input": {"shape": "UpdateClassificationJobRequest"}, "output": {"shape": "UpdateClassificationJobResponse", "documentation": "<p>The request succeeded. The job's status was changed and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Changes the status of a classification job.</p>"}, "UpdateClassificationScope": {"name": "UpdateClassificationScope", "http": {"method": "PATCH", "requestUri": "/classification-scopes/{id}", "responseCode": 200}, "input": {"shape": "UpdateClassificationScopeRequest"}, "output": {"shape": "UpdateClassificationScopeResponse", "documentation": "<p>The request succeeded. The specified settings were updated and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Updates the classification scope settings for an account.</p>"}, "UpdateFindingsFilter": {"name": "UpdateFindingsFilter", "http": {"method": "PATCH", "requestUri": "/findingsfilters/{id}", "responseCode": 200}, "input": {"shape": "UpdateFindingsFilterRequest"}, "output": {"shape": "UpdateFindingsFilterResponse", "documentation": "<p>The request succeeded. The specified findings filter was updated.</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Updates the criteria and other settings for a findings filter.</p>"}, "UpdateMacieSession": {"name": "UpdateMacieSession", "http": {"method": "PATCH", "requestUri": "/macie", "responseCode": 200}, "input": {"shape": "UpdateMacieSessionRequest"}, "output": {"shape": "UpdateMacieSessionResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Suspends or re-enables Amazon Macie, or updates the configuration settings for a Macie account.</p>"}, "UpdateMemberSession": {"name": "UpdateMemberSession", "http": {"method": "PATCH", "requestUri": "/macie/members/{id}", "responseCode": 200}, "input": {"shape": "UpdateMemberSessionRequest"}, "output": {"shape": "UpdateMemberSessionResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Enables an Amazon Macie administrator to suspend or re-enable <PERSON><PERSON> for a member account.</p>"}, "UpdateOrganizationConfiguration": {"name": "UpdateOrganizationConfiguration", "http": {"method": "PATCH", "requestUri": "/admin/configuration", "responseCode": 200}, "input": {"shape": "UpdateOrganizationConfigurationRequest"}, "output": {"shape": "UpdateOrganizationConfigurationResponse", "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ConflictException", "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>"}], "documentation": "<p>Updates the Amazon Macie configuration settings for an organization in Organizations.</p>"}, "UpdateResourceProfile": {"name": "UpdateResourceProfile", "http": {"method": "PATCH", "requestUri": "/resource-profiles", "responseCode": 200}, "input": {"shape": "UpdateResourceProfileRequest"}, "output": {"shape": "UpdateResourceProfileResponse", "documentation": "<p>The request succeeded. The S3 bucket's sensitivity score was updated and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}], "documentation": "<p>Updates the sensitivity score for an S3 bucket.</p>"}, "UpdateResourceProfileDetections": {"name": "UpdateResourceProfileDetections", "http": {"method": "PATCH", "requestUri": "/resource-profiles/detections", "responseCode": 200}, "input": {"shape": "UpdateResourceProfileDetectionsRequest"}, "output": {"shape": "UpdateResourceProfileDetectionsResponse", "documentation": "<p>The request succeeded. The settings were updated and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "ServiceQuotaExceededException", "documentation": "<p>The request failed because fulfilling the request would exceed one or more service quotas for your account.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}, {"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}], "documentation": "<p>Updates the sensitivity scoring settings for an S3 bucket.</p>"}, "UpdateRevealConfiguration": {"name": "UpdateRevealConfiguration", "http": {"method": "PUT", "requestUri": "/reveal-configuration", "responseCode": 200}, "input": {"shape": "UpdateRevealConfigurationRequest"}, "output": {"shape": "UpdateRevealConfigurationResponse", "documentation": "<p>The request succeeded.</p>"}, "errors": [{"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": "<p>Updates the status and configuration settings for retrieving occurrences of sensitive data reported by findings.</p>"}, "UpdateSensitivityInspectionTemplate": {"name": "UpdateSensitivityInspectionTemplate", "http": {"method": "PUT", "requestUri": "/templates/sensitivity-inspections/{id}", "responseCode": 200}, "input": {"shape": "UpdateSensitivityInspectionTemplateRequest"}, "output": {"shape": "UpdateSensitivityInspectionTemplateResponse", "documentation": "<p>The request succeeded. The template's settings were updated and there isn't any content to include in the body of the response (No Content).</p>"}, "errors": [{"shape": "ResourceNotFoundException", "documentation": "<p>The request failed because the specified resource wasn't found.</p>"}, {"shape": "ThrottlingException", "documentation": "<p>The request failed because you sent too many requests during a certain amount of time.</p>"}, {"shape": "ValidationException", "documentation": "<p>The request failed because the input doesn't satisfy the constraints specified by the service.</p>"}, {"shape": "InternalServerException", "documentation": "<p>The request failed due to an unknown internal server error, exception, or failure.</p>"}, {"shape": "AccessDeniedException", "documentation": "<p>The request was denied because you don't have sufficient access to the specified resource.</p>"}], "documentation": " <p>Updates the settings for the sensitivity inspection template for an account.</p>"}}, "shapes": {"AcceptInvitationRequest": {"type": "structure", "members": {"administratorAccountId": {"shape": "__string", "locationName": "administratorAccountId", "documentation": "<p>The Amazon Web Services account ID for the account that sent the invitation.</p>"}, "invitationId": {"shape": "__string", "locationName": "invitationId", "documentation": "<p>The unique identifier for the invitation to accept.</p>"}, "masterAccount": {"shape": "__string", "locationName": "masterAccount", "documentation": "<p>(Deprecated) The Amazon Web Services account ID for the account that sent the invitation. This property has been replaced by the administratorAccountId property and is retained only for backward compatibility.</p>"}}, "required": ["invitationId"]}, "AcceptInvitationResponse": {"type": "structure", "members": {}}, "AccessControlList": {"type": "structure", "members": {"allowsPublicReadAccess": {"shape": "__boolean", "locationName": "allowsPublicReadAccess", "documentation": "<p>Specifies whether the ACL grants the general public with read access permissions for the bucket.</p>"}, "allowsPublicWriteAccess": {"shape": "__boolean", "locationName": "allowsPublicWriteAccess", "documentation": "<p>Specifies whether the ACL grants the general public with write access permissions for the bucket.</p>"}}, "documentation": "<p>Provides information about the permissions settings of the bucket-level access control list (ACL) for an S3 bucket.</p>"}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred due to insufficient access to a specified resource.</p>", "exception": true, "error": {"httpStatusCode": 403}}, "AccountDetail": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The Amazon Web Services account ID for the account.</p>"}, "email": {"shape": "__string", "locationName": "email", "documentation": "<p>The email address for the account.</p>"}}, "documentation": "<p>Specifies the details of an account to associate with an Amazon Macie administrator account.</p>", "required": ["email", "accountId"]}, "AccountLevelPermissions": {"type": "structure", "members": {"blockPublicAccess": {"shape": "BlockPublicAccess", "locationName": "blockPublicAccess", "documentation": "<p>The block public access settings for the Amazon Web Services account that owns the bucket.</p>"}}, "documentation": "<p>Provides information about the account-level permissions settings that apply to an S3 bucket.</p>"}, "AdminAccount": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The Amazon Web Services account ID for the account.</p>"}, "status": {"shape": "AdminStatus", "locationName": "status", "documentation": "<p>The current status of the account as the delegated Amazon Macie administrator account for the organization.</p>"}}, "documentation": "<p>Provides information about the delegated Amazon Macie administrator account for an organization in Organizations.</p>"}, "AdminStatus": {"type": "string", "documentation": "<p>The current status of an account as the delegated Amazon Macie administrator account for an organization in Organizations. Possible values are:</p>", "enum": ["ENABLED", "DISABLING_IN_PROGRESS"]}, "AllowListCriteria": {"type": "structure", "members": {"regex": {"shape": "__stringMin1Max512PatternSS", "locationName": "regex", "documentation": "<p>The regular expression (<i>regex</i>) that defines the text pattern to ignore. The expression can contain as many as 512 characters.</p>"}, "s3WordsList": {"shape": "S3WordsList", "locationName": "s3WordsList", "documentation": "<p>The location and name of the S3 object that lists specific text to ignore.</p>"}}, "documentation": "<p>Specifies the criteria for an allow list. The criteria must specify a regular expression (regex) or an S3 object (s3WordsList). It can't specify both.</p>"}, "AllowListStatus": {"type": "structure", "members": {"code": {"shape": "AllowListStatusCode", "locationName": "code", "documentation": "<p>The current status of the allow list. If the list's criteria specify a regular expression (regex), this value is typically OK. Amazon Macie can compile the expression.</p> <p>If the list's criteria specify an S3 object, possible values are:</p> <ul><li><p>OK - <PERSON><PERSON> can retrieve and parse the contents of the object.</p></li> <li><p>S3_OBJECT_ACCESS_DENIED - <PERSON><PERSON> isn't allowed to access the object or the object is encrypted with a customer managed KMS key that <PERSON><PERSON> isn't allowed to use. Check the bucket policy and other permissions settings for the bucket and the object. If the object is encrypted, also ensure that it's encrypted with a key that <PERSON><PERSON> is allowed to use.</p></li> <li><p>S3_OBJECT_EMPTY - <PERSON><PERSON> can retrieve the object but the object doesn't contain any content. Ensure that the object contains the correct entries. Also ensure that the list's criteria specify the correct bucket and object names.</p></li> <li><p>S3_OBJECT_NOT_FOUND - The object doesn't exist in Amazon S3. Ensure that the list's criteria specify the correct bucket and object names.</p></li> <li><p>S3_OBJECT_OVERSIZE - <PERSON><PERSON> can retrieve the object. However, the object contains too many entries or its storage size exceeds the quota for an allow list. Try breaking the list into multiple files and ensure that each file doesn't exceed any quotas. Then configure list settings in Macie for each file.</p></li> <li><p>S3_THROTTLED - Amazon S3 throttled the request to retrieve the object. Wait a few minutes and then try again.</p></li> <li><p>S3_USER_ACCESS_DENIED - Amazon S3 denied the request to retrieve the object. If the specified object exists, you're not allowed to access it or it's encrypted with an KMS key that you're not allowed to use. Work with your Amazon Web Services administrator to ensure that the list's criteria specify the correct bucket and object names, and you have read access to the bucket and the object. If the object is encrypted, also ensure that it's encrypted with a key that you're allowed to use.</p></li> <li><p>UNKNOWN_ERROR - A transient or internal error occurred when Macie attempted to retrieve or parse the object. Wait a few minutes and then try again. A list can also have this status if it's encrypted with a key that Amazon S3 and Macie can't access or use.</p></li></ul>"}, "description": {"shape": "__stringMin1Max1024PatternSS", "locationName": "description", "documentation": "<p>A brief description of the status of the allow list. Amazon Macie uses this value to provide additional information about an error that occurred when <PERSON><PERSON> tried to access and use the list's criteria.</p>"}}, "documentation": "<p>Provides information about the current status of an allow list, which indicates whether Amazon Macie can access and use the list's criteria.</p>", "required": ["code"]}, "AllowListStatusCode": {"type": "string", "documentation": "<p>Indicates the current status of an allow list. Depending on the type of criteria that the list specifies, possible values are:</p>", "enum": ["OK", "S3_OBJECT_NOT_FOUND", "S3_USER_ACCESS_DENIED", "S3_OBJECT_ACCESS_DENIED", "S3_THROTTLED", "S3_OBJECT_OVERSIZE", "S3_OBJECT_EMPTY", "UNKNOWN_ERROR"]}, "AllowListSummary": {"type": "structure", "members": {"arn": {"shape": "__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the allow list.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the allow list was created in Amazon Macie.</p>"}, "description": {"shape": "__stringMin1Max512PatternSS", "locationName": "description", "documentation": "<p>The custom description of the allow list.</p>"}, "id": {"shape": "__stringMin22Max22PatternAZ0922", "locationName": "id", "documentation": "<p>The unique identifier for the allow list.</p>"}, "name": {"shape": "__stringMin1Max128Pattern", "locationName": "name", "documentation": "<p>The custom name of the allow list.</p>"}, "updatedAt": {"shape": "__timestampIso8601", "locationName": "updatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the allow list's settings were most recently changed in Amazon Macie.</p>"}}, "documentation": "<p>Provides a subset of information about an allow list.</p>"}, "AllowsUnencryptedObjectUploads": {"type": "string", "enum": ["TRUE", "FALSE", "UNKNOWN"]}, "ApiCallDetails": {"type": "structure", "members": {"api": {"shape": "__string", "locationName": "api", "documentation": "<p>The name of the operation that was invoked most recently and produced the finding.</p>"}, "apiServiceName": {"shape": "__string", "locationName": "apiServiceName", "documentation": "<p>The URL of the Amazon Web Service that provides the operation, for example: s3.amazonaws.com.</p>"}, "firstSeen": {"shape": "__timestampIso8601", "locationName": "firstSeen", "documentation": "<p>The first date and time, in UTC and extended ISO 8601 format, when any operation was invoked and produced the finding.</p>"}, "lastSeen": {"shape": "__timestampIso8601", "locationName": "lastSeen", "documentation": "<p>The most recent date and time, in UTC and extended ISO 8601 format, when the specified operation (api) was invoked and produced the finding.</p>"}}, "documentation": "<p>Provides information about an API operation that an entity invoked for an affected resource.</p>"}, "AssumedRole": {"type": "structure", "members": {"accessKeyId": {"shape": "__string", "locationName": "accessKeyId", "documentation": "<p>The Amazon Web Services access key ID that identifies the credentials.</p>"}, "accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that owns the entity that was used to get the credentials.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity that was used to get the credentials.</p>"}, "principalId": {"shape": "__string", "locationName": "principalId", "documentation": "<p>The unique identifier for the entity that was used to get the credentials.</p>"}, "sessionContext": {"shape": "SessionContext", "locationName": "sessionContext", "documentation": "<p>The details of the session that was created for the credentials, including the entity that issued the session.</p>"}}, "documentation": "<p>Provides information about an identity that performed an action on an affected resource by using temporary security credentials. The credentials were obtained using the AssumeRole operation of the Security Token Service (STS) API.</p>"}, "AutomatedDiscoveryStatus": {"type": "string", "documentation": "<p>The status of the automated sensitive data discovery configuration for an Amazon Macie account. Valid values are:</p>", "enum": ["ENABLED", "DISABLED"]}, "AvailabilityCode": {"type": "string", "documentation": "<p>Specifies whether occurrences of sensitive data can be retrieved for a finding. Possible values are:</p>", "enum": ["AVAILABLE", "UNAVAILABLE"]}, "AwsAccount": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account.</p>"}, "principalId": {"shape": "__string", "locationName": "principalId", "documentation": "<p>The unique identifier for the entity that performed the action.</p>"}}, "documentation": "<p>Provides information about an Amazon Web Services account and entity that performed an action on an affected resource. The action was performed using the credentials for an Amazon Web Services account other than your own account.</p>"}, "AwsService": {"type": "structure", "members": {"invokedBy": {"shape": "__string", "locationName": "invokedBy", "documentation": "<p>The name of the Amazon Web Service that performed the action.</p>"}}, "documentation": "<p>Provides information about an Amazon Web Service that performed an action on an affected resource.</p>"}, "BatchGetCustomDataIdentifierSummary": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the custom data identifier.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the custom data identifier was created.</p>"}, "deleted": {"shape": "__boolean", "locationName": "deleted", "documentation": "<p>Specifies whether the custom data identifier was deleted. If you delete a custom data identifier, Amazon Macie doesn't delete it permanently. Instead, it soft deletes the identifier.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>The custom description of the custom data identifier.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the custom data identifier.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the custom data identifier.</p>"}}, "documentation": "<p>Provides information about a custom data identifier.</p>"}, "BatchGetCustomDataIdentifiersRequest": {"type": "structure", "members": {"ids": {"shape": "__listOf__string", "locationName": "ids", "documentation": "<p>An array of custom data identifier IDs, one for each custom data identifier to retrieve information about.</p>"}}}, "BatchGetCustomDataIdentifiersResponse": {"type": "structure", "members": {"customDataIdentifiers": {"shape": "__listOfBatchGetCustomDataIdentifierSummary", "locationName": "customDataIdentifiers", "documentation": "<p>An array of objects, one for each custom data identifier that matches the criteria specified in the request.</p>"}, "notFoundIdentifierIds": {"shape": "__listOf__string", "locationName": "notFoundIdentifierIds", "documentation": "<p>An array of custom data identifier IDs, one for each custom data identifier that was specified in the request but doesn't correlate to an existing custom data identifier.</p>"}}}, "BlockPublicAccess": {"type": "structure", "members": {"blockPublicAcls": {"shape": "__boolean", "locationName": "blockPublicAcls", "documentation": "<p>Specifies whether Amazon S3 blocks public access control lists (ACLs) for the bucket and objects in the bucket.</p>"}, "blockPublicPolicy": {"shape": "__boolean", "locationName": "blockPublicPolicy", "documentation": "<p>Specifies whether Amazon S3 blocks public bucket policies for the bucket.</p>"}, "ignorePublicAcls": {"shape": "__boolean", "locationName": "ignorePublicAcls", "documentation": "<p>Specifies whether Amazon S3 ignores public ACLs for the bucket and objects in the bucket.</p>"}, "restrictPublicBuckets": {"shape": "__boolean", "locationName": "restrictPublicBuckets", "documentation": "<p>Specifies whether Amazon S3 restricts public bucket policies for the bucket.</p>"}}, "documentation": "<p>Provides information about the block public access settings for an S3 bucket. These settings can apply to a bucket at the account or bucket level. For detailed information about each setting, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-control-block-public-access.html\">Blocking public access to your Amazon S3 storage</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>"}, "BucketCountByEffectivePermission": {"type": "structure", "members": {"publiclyAccessible": {"shape": "__long", "locationName": "publiclyAccessible", "documentation": "<p>The total number of buckets that allow the general public to have read or write access to the bucket.</p>"}, "publiclyReadable": {"shape": "__long", "locationName": "publiclyReadable", "documentation": "<p>The total number of buckets that allow the general public to have read access to the bucket.</p>"}, "publiclyWritable": {"shape": "__long", "locationName": "publiclyWritable", "documentation": "<p>The total number of buckets that allow the general public to have write access to the bucket.</p>"}, "unknown": {"shape": "__long", "locationName": "unknown", "documentation": "<p>The total number of buckets that Amazon Macie wasn't able to evaluate permissions settings for. <PERSON><PERSON> can't determine whether these buckets are publicly accessible.</p>"}}, "documentation": "<p>Provides information about the number of S3 buckets that are publicly accessible due to a combination of permissions settings for each bucket.</p>"}, "BucketCountByEncryptionType": {"type": "structure", "members": {"kmsManaged": {"shape": "__long", "locationName": "kmsManaged", "documentation": " <p>The total number of buckets whose default encryption settings are configured to encrypt new objects with an Amazon Web Services managed KMS key or a customer managed KMS key. By default, these buckets encrypt new objects automatically using SSE-KMS encryption.</p>"}, "s3Managed": {"shape": "__long", "locationName": "s3Managed", "documentation": "<p>The total number of buckets whose default encryption settings are configured to encrypt new objects with an Amazon S3 managed key. By default, these buckets encrypt new objects automatically using SSE-S3 encryption.</p>"}, "unencrypted": {"shape": "__long", "locationName": "unencrypted", "documentation": "<p>The total number of buckets that don't specify default server-side encryption behavior for new objects. Default encryption settings aren't configured for these buckets.</p>"}, "unknown": {"shape": "__long", "locationName": "unknown", "documentation": "<p>The total number of buckets that Amazon Macie doesn't have current encryption metadata for. <PERSON><PERSON> can't provide current data about the default encryption settings for these buckets.</p>"}}, "documentation": "<p>Provides information about the number of S3 buckets whose settings do or don't specify default server-side encryption behavior for objects that are added to the buckets. For detailed information about these settings, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-encryption.html\">Setting default server-side encryption behavior for Amazon S3 buckets</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>"}, "BucketCountBySharedAccessType": {"type": "structure", "members": {"external": {"shape": "__long", "locationName": "external", "documentation": "<p>The total number of buckets that are shared with one or more of the following or any combination of the following: an Amazon CloudFront OAI, a CloudFront OAC, or an Amazon Web Services account that isn't in the same Amazon Macie organization.</p>"}, "internal": {"shape": "__long", "locationName": "internal", "documentation": "<p>The total number of buckets that are shared with one or more Amazon Web Services accounts in the same Amazon Macie organization. These buckets aren't shared with Amazon CloudFront OAIs or OACs.</p>"}, "notShared": {"shape": "__long", "locationName": "notShared", "documentation": "<p>The total number of buckets that aren't shared with other Amazon Web Services accounts, Amazon CloudFront OAIs, or CloudFront OACs.</p>"}, "unknown": {"shape": "__long", "locationName": "unknown", "documentation": "<p>The total number of buckets that Amazon Macie wasn't able to evaluate shared access settings for. <PERSON><PERSON> can't determine whether these buckets are shared with other Amazon Web Services accounts, Amazon CloudFront OAIs, or CloudFront OACs.</p>"}}, "documentation": "<p>Provides information about the number of S3 buckets that are or aren't shared with other Amazon Web Services accounts, Amazon CloudFront origin access identities (OAIs), or CloudFront origin access controls (OACs). In this data, an <i>Amazon Macie organization</i> is defined as a set of Macie accounts that are centrally managed as a group of related accounts through Organizations or by Macie invitation.</p>"}, "BucketCountPolicyAllowsUnencryptedObjectUploads": {"type": "structure", "members": {"allowsUnencryptedObjectUploads": {"shape": "__long", "locationName": "allowsUnencryptedObjectUploads", "documentation": "<p>The total number of buckets that don't have a bucket policy or have a bucket policy that doesn't require server-side encryption of new objects. If a bucket policy exists, the policy doesn't require PutObject requests to include a valid server-side encryption header: the x-amz-server-side-encryption header with a value of AES256 or aws:kms, or the x-amz-server-side-encryption-customer-algorithm header with a value of AES256.</p>"}, "deniesUnencryptedObjectUploads": {"shape": "__long", "locationName": "deniesUnencryptedObjectUploads", "documentation": "<p>The total number of buckets whose bucket policies require server-side encryption of new objects. PutObject requests for these buckets must include a valid server-side encryption header: the x-amz-server-side-encryption header with a value of AES256 or aws:kms, or the x-amz-server-side-encryption-customer-algorithm header with a value of AES256.</p>"}, "unknown": {"shape": "__long", "locationName": "unknown", "documentation": "<p>The total number of buckets that Amazon Macie wasn't able to evaluate server-side encryption requirements for. <PERSON><PERSON> can't determine whether the bucket policies for these buckets require server-side encryption of new objects.</p>"}}, "documentation": "<p>Provides information about the number of S3 buckets whose bucket policies do or don't require server-side encryption of objects when objects are added to the buckets.</p>"}, "BucketCriteria": {"type": "map", "documentation": "<p>Specifies, as a map, one or more property-based conditions that filter the results of a query for information about S3 buckets.</p>", "key": {"shape": "__string"}, "value": {"shape": "BucketCriteriaAdditionalProperties"}}, "BucketCriteriaAdditionalProperties": {"type": "structure", "members": {"eq": {"shape": "__listOf__string", "locationName": "eq", "documentation": "<p>The value for the property matches (equals) the specified value. If you specify multiple values, Amazon Macie uses OR logic to join the values.</p>"}, "gt": {"shape": "__long", "locationName": "gt", "documentation": "<p>The value for the property is greater than the specified value.</p>"}, "gte": {"shape": "__long", "locationName": "gte", "documentation": "<p>The value for the property is greater than or equal to the specified value.</p>"}, "lt": {"shape": "__long", "locationName": "lt", "documentation": "<p>The value for the property is less than the specified value.</p>"}, "lte": {"shape": "__long", "locationName": "lte", "documentation": "<p>The value for the property is less than or equal to the specified value.</p>"}, "neq": {"shape": "__listOf__string", "locationName": "neq", "documentation": "<p>The value for the property doesn't match (doesn't equal) the specified value. If you specify multiple values, Amazon Macie uses OR logic to join the values.</p>"}, "prefix": {"shape": "__string", "locationName": "prefix", "documentation": "<p>The name of the bucket begins with the specified value.</p>"}}, "documentation": "<p>Specifies the operator to use in a property-based condition that filters the results of a query for information about S3 buckets.</p>"}, "BucketLevelPermissions": {"type": "structure", "members": {"accessControlList": {"shape": "AccessControlList", "locationName": "accessControlList", "documentation": "<p>The permissions settings of the access control list (ACL) for the bucket. This value is null if an ACL hasn't been defined for the bucket.</p>"}, "blockPublicAccess": {"shape": "BlockPublicAccess", "locationName": "blockPublicAccess", "documentation": "<p>The block public access settings for the bucket.</p>"}, "bucketPolicy": {"shape": "BucketPolicy", "locationName": "bucketPolicy", "documentation": "<p>The permissions settings of the bucket policy for the bucket. This value is null if a bucket policy hasn't been defined for the bucket.</p>"}}, "documentation": "<p>Provides information about the bucket-level permissions settings for an S3 bucket.</p>"}, "BucketMetadata": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that owns the bucket.</p>"}, "allowsUnencryptedObjectUploads": {"shape": "AllowsUnencryptedObjectUploads", "locationName": "allowsUnencryptedObjectUploads", "documentation": "<p>Specifies whether the bucket policy for the bucket requires server-side encryption of objects when objects are added to the bucket. Possible values are:</p> <ul><li><p>FALSE - The bucket policy requires server-side encryption of new objects. PutObject requests must include a valid server-side encryption header.</p></li> <li><p>TRUE - The bucket doesn't have a bucket policy or it has a bucket policy that doesn't require server-side encryption of new objects. If a bucket policy exists, it doesn't require PutObject requests to include a valid server-side encryption header.</p></li> <li><p>UNKNOWN - Amazon Macie can't determine whether the bucket policy requires server-side encryption of new objects.</p></li></ul> <p>Valid server-side encryption headers are: x-amz-server-side-encryption with a value of AES256 or aws:kms, and x-amz-server-side-encryption-customer-algorithm with a value of AES256.</p>"}, "bucketArn": {"shape": "__string", "locationName": "bucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket.</p>"}, "bucketCreatedAt": {"shape": "__timestampIso8601", "locationName": "bucketCreatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the bucket was created. This value can also indicate when changes such as edits to the bucket's policy were most recently made to the bucket.</p>"}, "bucketName": {"shape": "__string", "locationName": "bucketName", "documentation": "<p>The name of the bucket.</p>"}, "classifiableObjectCount": {"shape": "__long", "locationName": "classifiableObjectCount", "documentation": "<p>The total number of objects that Amazon Macie can analyze in the bucket. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p>"}, "classifiableSizeInBytes": {"shape": "__long", "locationName": "classifiableSizeInBytes", "documentation": "<p>The total storage size, in bytes, of the objects that Amazon Macie can analyze in the bucket. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p> <p>If versioning is enabled for the bucket, <PERSON><PERSON> calculates this value based on the size of the latest version of each applicable object in the bucket. This value doesn't reflect the storage size of all versions of each applicable object in the bucket.</p>"}, "errorCode": {"shape": "BucketMetadataErrorCode", "locationName": "errorCode", "documentation": "<p>The error code for an error that prevented Amazon Macie from retrieving and processing information about the bucket and the bucket's objects. If this value is ACCESS_DENIED, <PERSON><PERSON> doesn't have permission to retrieve the information. For example, the bucket has a restrictive bucket policy and Amazon S3 denied the request. If this value is null, <PERSON><PERSON> was able to retrieve and process the information.</p>"}, "errorMessage": {"shape": "__string", "locationName": "errorMessage", "documentation": "<p>A brief description of the error (errorCode) that prevented Amazon Macie from retrieving and processing information about the bucket and the bucket's objects. This value is null if <PERSON><PERSON> was able to retrieve and process the information.</p>"}, "jobDetails": {"shape": "JobDetails", "locationName": "jobDetails", "documentation": "<p>Specifies whether any one-time or recurring classification jobs are configured to analyze data in the bucket, and, if so, the details of the job that ran most recently.</p>"}, "lastAutomatedDiscoveryTime": {"shape": "__timestampIso8601", "locationName": "lastAutomatedDiscoveryTime", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie most recently analyzed data in the bucket while performing automated sensitive data discovery for your account. This value is null if automated sensitive data discovery is currently disabled for your account.</p>"}, "lastUpdated": {"shape": "__timestampIso8601", "locationName": "lastUpdated", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie most recently retrieved bucket or object metadata from Amazon S3 for the bucket.</p>"}, "objectCount": {"shape": "__long", "locationName": "objectCount", "documentation": "<p>The total number of objects in the bucket.</p>"}, "objectCountByEncryptionType": {"shape": "ObjectCountByEncryptionType", "locationName": "objectCountByEncryptionType", "documentation": "<p>The total number of objects in the bucket, grouped by server-side encryption type. This includes a grouping that reports the total number of objects that aren't encrypted or use client-side encryption.</p>"}, "publicAccess": {"shape": "BucketPublicAccess", "locationName": "publicAccess", "documentation": "<p>Specifies whether the bucket is publicly accessible due to the combination of permissions settings that apply to the bucket, and provides information about those settings.</p>"}, "region": {"shape": "__string", "locationName": "region", "documentation": "<p>The Amazon Web Services Region that hosts the bucket.</p>"}, "replicationDetails": {"shape": "ReplicationDetails", "locationName": "replicationDetails", "documentation": "<p>Specifies whether the bucket is configured to replicate one or more objects to buckets for other Amazon Web Services accounts and, if so, which accounts.</p>"}, "sensitivityScore": {"shape": "__integer", "locationName": "sensitivityScore", "documentation": "<p>The sensitivity score for the bucket, ranging from -1 (classification error) to 100 (sensitive). This value is null if automated sensitive data discovery is currently disabled for your account.</p>"}, "serverSideEncryption": {"shape": "BucketServerSideEncryption", "locationName": "serverSideEncryption", "documentation": "<p>The default server-side encryption settings for the bucket.</p>"}, "sharedAccess": {"shape": "SharedAccess", "locationName": "sharedAccess", "documentation": "<p>Specifies whether the bucket is shared with another Amazon Web Services account, an Amazon CloudFront origin access identity (OAI), or a CloudFront origin access control (OAC). Possible values are:</p> <ul><li><p>EXTERNAL - The bucket is shared with one or more of the following or any combination of the following: a CloudFront OAI, a CloudFront OAC, or an Amazon Web Services account that isn't part of your Amazon Macie organization.</p></li> <li><p>INTERNAL - The bucket is shared with one or more Amazon Web Services accounts that are part of your Amazon Macie organization. It isn't shared with a CloudFront OAI or OAC.</p></li> <li><p>NOT_SHARED - The bucket isn't shared with another Amazon Web Services account, a CloudFront OAI, or a CloudFront OAC.</p></li> <li><p>UNKNOWN - Amazon Macie wasn't able to evaluate the shared access settings for the bucket.</p></li></ul> <p>An <i>Amazon Macie organization</i> is a set of Macie accounts that are centrally managed as a group of related accounts through Organizations or by <PERSON>ie invitation.</p>"}, "sizeInBytes": {"shape": "__long", "locationName": "sizeInBytes", "documentation": "<p>The total storage size, in bytes, of the bucket.</p> <p>If versioning is enabled for the bucket, Amazon Macie calculates this value based on the size of the latest version of each object in the bucket. This value doesn't reflect the storage size of all versions of each object in the bucket.</p>"}, "sizeInBytesCompressed": {"shape": "__long", "locationName": "sizeInBytesCompressed", "documentation": "<p>The total storage size, in bytes, of the objects that are compressed (.gz, .gzip, .zip) files in the bucket.</p> <p>If versioning is enabled for the bucket, Amazon Macie calculates this value based on the size of the latest version of each applicable object in the bucket. This value doesn't reflect the storage size of all versions of each applicable object in the bucket.</p>"}, "tags": {"shape": "__listOfKeyValuePair", "locationName": "tags", "documentation": "<p>An array that specifies the tags (keys and values) that are associated with the bucket.</p>"}, "unclassifiableObjectCount": {"shape": "ObjectLevelStatistics", "locationName": "unclassifiableObjectCount", "documentation": "<p>The total number of objects that <PERSON> Macie can't analyze in the bucket. These objects don't use a supported storage class or don't have a file name extension for a supported file or storage format.</p>"}, "unclassifiableObjectSizeInBytes": {"shape": "ObjectLevelStatistics", "locationName": "unclassifiableObjectSizeInBytes", "documentation": "<p>The total storage size, in bytes, of the objects that Amazon Macie can't analyze in the bucket. These objects don't use a supported storage class or don't have a file name extension for a supported file or storage format.</p>"}, "versioning": {"shape": "__boolean", "locationName": "versioning", "documentation": "<p>Specifies whether versioning is enabled for the bucket.</p>"}}, "documentation": "<p>Provides statistical data and other information about an S3 bucket that Amazon Macie monitors and analyzes for your account. By default, object count and storage size values include data for object parts that are the result of incomplete multipart uploads. For more information, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/monitoring-s3-how-it-works.html\">How <PERSON><PERSON> monitors Amazon S3 data security</a> in the <i>Amazon Macie User Guide</i>.</p> <p>If an error occurs when <PERSON><PERSON> attempts to retrieve and process metadata from Amazon S3 for the bucket or the bucket's objects, the value for the versioning property is false and the value for most other properties is null. Key exceptions are accountId, bucketArn, bucketCreatedAt, bucketName, lastUpdated, and region. To identify the cause of the error, refer to the errorCode and errorMessage values.</p>"}, "BucketMetadataErrorCode": {"type": "string", "documentation": "<p>The error code for an error that prevented Amazon Macie from retrieving and processing metadata from Amazon S3 for an S3 bucket and the bucket's objects.</p>", "enum": ["ACCESS_DENIED"]}, "BucketPermissionConfiguration": {"type": "structure", "members": {"accountLevelPermissions": {"shape": "AccountLevelPermissions", "locationName": "accountLevelPermissions", "documentation": "<p>The account-level permissions settings that apply to the bucket.</p>"}, "bucketLevelPermissions": {"shape": "BucketLevelPermissions", "locationName": "bucketLevelPermissions", "documentation": "<p>The bucket-level permissions settings for the bucket.</p>"}}, "documentation": "<p>Provides information about the account-level and bucket-level permissions settings for an S3 bucket.</p>"}, "BucketPolicy": {"type": "structure", "members": {"allowsPublicReadAccess": {"shape": "__boolean", "locationName": "allowsPublicReadAccess", "documentation": "<p>Specifies whether the bucket policy allows the general public to have read access to the bucket.</p>"}, "allowsPublicWriteAccess": {"shape": "__boolean", "locationName": "allowsPublicWriteAccess", "documentation": "<p>Specifies whether the bucket policy allows the general public to have write access to the bucket.</p>"}}, "documentation": "<p>Provides information about the permissions settings of the bucket policy for an S3 bucket.</p>"}, "BucketPublicAccess": {"type": "structure", "members": {"effectivePermission": {"shape": "EffectivePermission", "locationName": "effectivePermission", "documentation": " <p>Specifies whether the bucket is publicly accessible due to the combination of permissions settings that apply to the bucket. Possible values are:</p> <ul><li><p>NOT_PUBLIC - The bucket isn't publicly accessible.</p></li> <li><p>PUBLIC - The bucket is publicly accessible.</p></li> <li><p>UNKNOWN - Amazon Macie can't determine whether the bucket is publicly accessible.</p></li></ul>"}, "permissionConfiguration": {"shape": "BucketPermissionConfiguration", "locationName": "permissionConfiguration", "documentation": "<p>The account-level and bucket-level permissions settings for the bucket.</p>"}}, "documentation": "<p>Provides information about the permissions settings that determine whether an S3 bucket is publicly accessible.</p>"}, "BucketServerSideEncryption": {"type": "structure", "members": {"kmsMasterKeyId": {"shape": "__string", "locationName": "kmsMasterKeyId", "documentation": "<p>The Amazon Resource Name (ARN) or unique identifier (key ID) for the KMS key that's used by default to encrypt objects that are added to the bucket. This value is null if the bucket is configured to use an Amazon S3 managed key to encrypt new objects.</p>"}, "type": {"shape": "Type", "locationName": "type", "documentation": "<p>The server-side encryption algorithm that's used by default to encrypt objects that are added to the bucket. Possible values are:</p> <ul><li><p>AES256 - New objects are encrypted with an Amazon S3 managed key. They use SSE-S3 encryption.</p></li> <li><p>aws:kms - New objects are encrypted with an KMS key (kmsMasterKeyId), either an Amazon Web Services managed key or a customer managed key. They use SSE-KMS encryption.</p></li> <li><p>NONE - The bucket's default encryption settings don't specify server-side encryption behavior for new objects.</p></li></ul>"}}, "documentation": "<p>Provides information about the default server-side encryption settings for an S3 bucket. For detailed information about these settings, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-encryption.html\">Setting default server-side encryption behavior for Amazon S3 buckets</a> in the <i>Amazon Simple Storage Service User Guide</i>.</p>"}, "BucketSortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "__string", "locationName": "attributeName", "documentation": "<p>The name of the bucket property to sort the results by. This value can be one of the following properties that Amazon Macie defines as bucket metadata: accountId, bucketName, classifiableObjectCount, classifiableSizeInBytes, objectCount, sensitivityScore, or sizeInBytes.</p>"}, "orderBy": {"shape": "OrderBy", "locationName": "orderBy", "documentation": "<p>The sort order to apply to the results, based on the value specified by the attributeName property. Valid values are: ASC, sort the results in ascending order; and, DESC, sort the results in descending order.</p>"}}, "documentation": "<p>Specifies criteria for sorting the results of a query for information about S3 buckets.</p>"}, "BucketStatisticsBySensitivity": {"type": "structure", "members": {"classificationError": {"shape": "SensitivityAggregations", "locationName": "classificationError", "documentation": "<p>The aggregated statistical data for all buckets that have a sensitivity score of -1.</p>"}, "notClassified": {"shape": "SensitivityAggregations", "locationName": "notClassified", "documentation": "<p>The aggregated statistical data for all buckets that have a sensitivity score of 50.</p>"}, "notSensitive": {"shape": "SensitivityAggregations", "locationName": "notSensitive", "documentation": "<p>The aggregated statistical data for all buckets that have a sensitivity score of 1-49.</p>"}, "sensitive": {"shape": "SensitivityAggregations", "locationName": "sensitive", "documentation": "<p>The aggregated statistical data for all buckets that have a sensitivity score of 51-100.</p>"}}, "documentation": "<p>Provides aggregated statistical data for sensitive data discovery metrics that apply to S3 buckets, grouped by bucket sensitivity score (sensitivityScore). If automated sensitive data discovery is currently disabled for your account, the value for each metric is 0.</p>"}, "Cell": {"type": "structure", "members": {"cellReference": {"shape": "__string", "locationName": "cellReference", "documentation": "<p>The location of the cell, as an absolute cell reference, that contains the sensitive data, for example Sheet2!C5 for cell C5 on Sheet2 in a Microsoft Excel workbook. This value is null for CSV and TSV files.</p>"}, "column": {"shape": "__long", "locationName": "column", "documentation": "<p>The column number of the column that contains the sensitive data. For a Microsoft Excel workbook, this value correlates to the alphabetical character(s) for a column identifier, for example: 1 for column A, 2 for column B, and so on.</p>"}, "columnName": {"shape": "__string", "locationName": "columnName", "documentation": "<p>The name of the column that contains the sensitive data, if available.</p>"}, "row": {"shape": "__long", "locationName": "row", "documentation": "<p>The row number of the row that contains the sensitive data.</p>"}}, "documentation": "<p>Specifies the location of an occurrence of sensitive data in a Microsoft Excel workbook, CSV file, or TSV file.</p>"}, "Cells": {"type": "list", "documentation": "<p>Specifies the location of occurrences of sensitive data in a Microsoft Excel workbook, CSV file, or TSV file.</p>", "member": {"shape": "Cell"}}, "ClassificationDetails": {"type": "structure", "members": {"detailedResultsLocation": {"shape": "__string", "locationName": "detailedResultsLocation", "documentation": "<p>The path to the folder or file in Amazon S3 that contains the corresponding sensitive data discovery result for the finding. If a finding applies to a large archive or compressed file, this value is the path to a folder. Otherwise, this value is the path to a file.</p>"}, "jobArn": {"shape": "__string", "locationName": "jobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the classification job that produced the finding. This value is null if the origin of the finding (originType) is AUTOMATED_SENSITIVE_DATA_DISCOVERY.</p>"}, "jobId": {"shape": "__string", "locationName": "jobId", "documentation": "<p>The unique identifier for the classification job that produced the finding. This value is null if the origin of the finding (originType) is AUTOMATED_SENSITIVE_DATA_DISCOVERY.</p>"}, "originType": {"shape": "OriginType", "locationName": "originType", "documentation": "<p>Specifies how <PERSON> Macie found the sensitive data that produced the finding. Possible values are: SENSITIVE_DATA_DISCOVERY_JOB, for a classification job; and, AUTOMATED_SENSITIVE_DATA_DISCOVERY, for automated sensitive data discovery.</p>"}, "result": {"shape": "ClassificationResult", "locationName": "result", "documentation": "<p>The status and other details of the finding.</p>"}}, "documentation": "<p>Provides information about a sensitive data finding and the details of the finding.</p>"}, "ClassificationExportConfiguration": {"type": "structure", "members": {"s3Destination": {"shape": "S3Destination", "locationName": "s3Destination", "documentation": "<p>The S3 bucket to store data classification results in, and the encryption settings to use when storing results in that bucket.</p>"}}, "documentation": "<p>Specifies where to store data classification results, and the encryption settings to use when storing results in that location. The location must be an S3 bucket.</p>"}, "ClassificationResult": {"type": "structure", "members": {"additionalOccurrences": {"shape": "__boolean", "locationName": "additionalOccurrences", "documentation": "<p>Specifies whether Amazon Macie detected additional occurrences of sensitive data in the S3 object. A finding includes location data for a maximum of 15 occurrences of sensitive data.</p> <p>This value can help you determine whether to investigate additional occurrences of sensitive data in an object. You can do this by referring to the corresponding sensitive data discovery result for the finding (classificationDetails.detailedResultsLocation).</p>"}, "customDataIdentifiers": {"shape": "CustomDataIdentifiers", "locationName": "customDataIdentifiers", "documentation": "<p>The custom data identifiers that detected the sensitive data and the number of occurrences of the data that they detected.</p>"}, "mimeType": {"shape": "__string", "locationName": "mimeType", "documentation": "<p>The type of content, as a MIME type, that the finding applies to. For example, application/gzip, for a GNU Gzip compressed archive file, or application/pdf, for an Adobe Portable Document Format file.</p>"}, "sensitiveData": {"shape": "SensitiveData", "locationName": "sensitiveData", "documentation": "<p>The category, types, and number of occurrences of the sensitive data that produced the finding.</p>"}, "sizeClassified": {"shape": "__long", "locationName": "sizeClassified", "documentation": "<p>The total size, in bytes, of the data that the finding applies to.</p>"}, "status": {"shape": "ClassificationResultStatus", "locationName": "status", "documentation": "<p>The status of the finding.</p>"}}, "documentation": "<p>Provides the details of a sensitive data finding, including the types, number of occurrences, and locations of the sensitive data that was detected.</p>"}, "ClassificationResultStatus": {"type": "structure", "members": {"code": {"shape": "__string", "locationName": "code", "documentation": " <p>The status of the finding. Possible values are:</p> <ul><li><p>COMPLETE - Amazon Macie successfully completed its analysis of the S3 object that the finding applies to.</p></li> <li><p>PARTIAL - <PERSON><PERSON> analyzed only a subset of the data in the S3 object that the finding applies to. For example, the object is an archive file that contains files in an unsupported format.</p></li> <li><p>SKIPPED - <PERSON><PERSON> wasn't able to analyze the S3 object that the finding applies to. For example, the object is a file that uses an unsupported format.</p></li></ul>"}, "reason": {"shape": "__string", "locationName": "reason", "documentation": "<p>A brief description of the status of the finding. This value is null if the status (code) of the finding is COMPLETE.</p> <p>Amazon Macie uses this value to notify you of any errors, warnings, or considerations that might impact your analysis of the finding and the affected S3 object. Possible values are:</p> <ul><li><p>ARCHIVE_CONTAINS_UNPROCESSED_FILES - The object is an archive file and <PERSON><PERSON> extracted and analyzed only some or none of the files in the archive. To determine which files <PERSON><PERSON> analyzed, if any, refer to the corresponding sensitive data discovery result for the finding (classificationDetails.detailedResultsLocation).</p></li> <li><p>ARCHIVE_EXCEEDS_SIZE_LIMIT - The object is an archive file whose total storage size exceeds the size quota for this type of archive.</p></li> <li><p>ARCHIVE_NESTING_LEVEL_OVER_LIMIT - The object is an archive file whose nested depth exceeds the quota for the maximum number of nested levels that <PERSON><PERSON> analyzes for this type of archive.</p></li> <li><p>ARCHIVE_TOTAL_BYTES_EXTRACTED_OVER_LIMIT - The object is an archive file that exceeds the quota for the maximum amount of data that <PERSON><PERSON> extracts and analyzes for this type of archive.</p></li> <li><p>ARCHIVE_TOTAL_DOCUMENTS_PROCESSED_OVER_LIMIT - The object is an archive file that contains more than the maximum number of files that Macie extracts and analyzes for this type of archive.</p></li> <li><p>FILE_EXCEEDS_SIZE_LIMIT - The storage size of the object exceeds the size quota for this type of file.</p></li> <li><p>INVALID_ENCRYPTION - The object is encrypted using server-side encryption but Macie isn't allowed to use the key. Macie can't decrypt and analyze the object.</p></li> <li><p>INVALID_KMS_KEY - The object is encrypted with an KMS key that was disabled or is being deleted. Macie can't decrypt and analyze the object.</p></li> <li><p>INVALID_OBJECT_STATE - The object doesn't use a supported Amazon S3 storage class.</p></li> <li><p>JSON_NESTING_LEVEL_OVER_LIMIT - The object contains JSON data and the nested depth of the data exceeds the quota for the number of nested levels that Macie analyzes for this type of file.</p></li> <li><p>MALFORMED_FILE - The object is a malformed or corrupted file. An error occurred when Macie attempted to detect the file's type or extract data from the file.</p></li> <li><p>MALFORMED_OR_FILE_SIZE_EXCEEDS_LIMIT - The object is a Microsoft Office file that is malformed or exceeds the size quota for this type of file. If the file is malformed, an error occurred when Macie attempted to extract data from the file.</p></li> <li><p>NO_SUCH_BUCKET_AVAILABLE - The object was in a bucket that was deleted shortly before or when Macie attempted to analyze the object.</p></li> <li><p>OBJECT_VERSION_MISMATCH - The object was changed while Macie was analyzing it.</p></li> <li><p>OOXML_UNCOMPRESSED_RATIO_EXCEEDS_LIMIT - The object is an Office Open XML file whose compression ratio exceeds the compression quota for this type of file.</p></li> <li><p>OOXML_UNCOMPRESSED_SIZE_EXCEEDS_LIMIT - The object is an Office Open XML file that exceeds the size quota for this type of file.</p></li> <li><p>PERMISSION_DENIED - Macie isn't allowed to access the object. The object's permissions settings prevent Macie from analyzing the object.</p></li> <li><p>SOURCE_OBJECT_NO_LONGER_AVAILABLE - The object was deleted shortly before or when Macie attempted to analyze it.</p></li> <li><p>TIME_CUT_OFF_REACHED - Macie started analyzing the object but additional analysis would exceed the time quota for analyzing an object.</p></li> <li><p>UNABLE_TO_PARSE_FILE - The object is a file that contains structured data and an error occurred when Macie attempted to parse the data.</p></li> <li><p>UNSUPPORTED_FILE_TYPE_EXCEPTION - The object is a file that uses an unsupported file or storage format.</p></li></ul> <p>For information about quotas, supported storage classes, and supported file and storage formats, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/macie-quotas.html\">Quotas</a> and <a href=\"https://docs.aws.amazon.com/macie/latest/user/discovery-supported-storage.html\">Supported storage classes and formats</a> in the <i>Amazon Macie User Guide</i>.</p>"}}, "documentation": "<p>Provides information about the status of a sensitive data finding.</p>"}, "ClassificationScopeId": {"type": "string", "documentation": "<p>The unique identifier the classification scope.</p>", "pattern": "^[0-9a-z]*$"}, "ClassificationScopeName": {"type": "string", "documentation": "<p>The name of the classification scope.</p>", "pattern": "^[0-9a-zA-Z_\\\\-]*$"}, "ClassificationScopeSummary": {"type": "structure", "members": {"id": {"shape": "ClassificationScopeId", "locationName": "id", "documentation": "<p>The unique identifier for the classification scope.</p>"}, "name": {"shape": "ClassificationScopeName", "locationName": "name", "documentation": "<p>The name of the classification scope: automated-sensitive-data-discovery.</p>"}}, "documentation": "<p>Provides information about the classification scope for an Amazon Macie account. Macie uses the scope's settings when it performs automated sensitive data discovery for the account.</p>"}, "ClassificationScopeUpdateOperation": {"type": "string", "documentation": "<p>Specifies how to apply changes to the S3 bucket exclusion list defined by the classification scope for an Amazon Macie account. Valid values are:</p>", "enum": ["ADD", "REPLACE", "REMOVE"]}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred due to a versioning conflict for a specified resource.</p>", "exception": true, "error": {"httpStatusCode": 409}}, "CreateAllowListRequest": {"type": "structure", "members": {"clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "criteria": {"shape": "AllowListCriteria", "locationName": "criteria", "documentation": "<p>The criteria that specify the text or text pattern to ignore. The criteria can be the location and name of an S3 object that lists specific text to ignore (s3WordsList), or a regular expression (regex) that defines a text pattern to ignore.</p>"}, "description": {"shape": "__stringMin1Max512PatternSS", "locationName": "description", "documentation": "<p>A custom description of the allow list. The description can contain as many as 512 characters.</p>"}, "name": {"shape": "__stringMin1Max128Pattern", "locationName": "name", "documentation": "<p>A custom name for the allow list. The name can contain as many as 128 characters.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies the tags to associate with the allow list.</p> <p>An allow list can have a maximum of 50 tags. Each tag consists of a tag key and an associated tag value. The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}, "required": ["criteria", "clientToken", "name"]}, "CreateAllowListResponse": {"type": "structure", "members": {"arn": {"shape": "__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the allow list.</p>"}, "id": {"shape": "__stringMin22Max22PatternAZ0922", "locationName": "id", "documentation": "<p>The unique identifier for the allow list.</p>"}}}, "CreateClassificationJobRequest": {"type": "structure", "members": {"allowListIds": {"shape": "__listOf__string", "locationName": "allowListIds", "documentation": "<p>An array of unique identifiers, one for each allow list for the job to use when it analyzes data.</p>"}, "clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "customDataIdentifierIds": {"shape": "__listOf__string", "locationName": "customDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each custom data identifier for the job to use when it analyzes data. To use only managed data identifiers, don't specify a value for this property and specify a value other than NONE for the managedDataIdentifierSelector property.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>A custom description of the job. The description can contain as many as 200 characters.</p>"}, "initialRun": {"shape": "__boolean", "locationName": "initialRun", "documentation": "<p>For a recurring job, specifies whether to analyze all existing, eligible objects immediately after the job is created (true). To analyze only those objects that are created or changed after you create the job and before the job's first scheduled run, set this value to false.</p> <p>If you configure the job to run only once, don't specify a value for this property.</p>"}, "jobType": {"shape": "JobType", "locationName": "jobType", "documentation": "<p>The schedule for running the job. Valid values are:</p> <ul><li><p>ONE_TIME - Run the job only once. If you specify this value, don't specify a value for the scheduleFrequency property.</p></li> <li><p>SCHEDULED - Run the job on a daily, weekly, or monthly basis. If you specify this value, use the scheduleFrequency property to define the recurrence pattern for the job.</p></li></ul>"}, "managedDataIdentifierIds": {"shape": "__listOf__string", "locationName": "managedDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each managed data identifier for the job to include (use) or exclude (not use) when it analyzes data. Inclusion or exclusion depends on the managed data identifier selection type that you specify for the job (managedDataIdentifierSelector).</p> <p>To retrieve a list of valid values for this property, use the ListManagedDataIdentifiers operation.</p>"}, "managedDataIdentifierSelector": {"shape": "ManagedDataIdentifierSelector", "locationName": "managedDataIdentifierSelector", "documentation": "<p>The selection type to apply when determining which managed data identifiers the job uses to analyze data. Valid values are:</p> <ul><li><p>ALL - Use all managed data identifiers. If you specify this value, don't specify any values for the managedDataIdentifierIds property.</p></li> <li><p>EXCLUDE - Use all managed data identifiers except the ones specified by the managedDataIdentifierIds property.</p></li> <li><p>INCLUDE - Use only the managed data identifiers specified by the managedDataIdentifierIds property.</p></li> <li><p>NONE - Don't use any managed data identifiers. If you specify this value, specify at least one value for the customDataIdentifierIds property and don't specify any values for the managedDataIdentifierIds property.</p></li> <li><p>RECOMMENDED (default) - Use the recommended set of managed data identifiers. If you specify this value, don't specify any values for the managedDataIdentifierIds property.</p></li></ul> <p>If you don't specify a value for this property, the job uses the recommended set of managed data identifiers.</p> <p>If the job is a recurring job and you specify ALL or EXCLUDE, each job run automatically uses new managed data identifiers that are released. If you specify RECOMMENDED for a recurring job, each job run automatically uses all the managed data identifiers that are in the recommended set when the run starts.</p> <p>For information about individual managed data identifiers or to determine which ones are in the recommended set, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/managed-data-identifiers.html\">Using managed data identifiers</a> and <a href=\"https://docs.aws.amazon.com/macie/latest/user/discovery-jobs-mdis-recommended.html\">Recommended managed data identifiers</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>A custom name for the job. The name can contain as many as 500 characters.</p>"}, "s3JobDefinition": {"shape": "S3JobDefinition", "locationName": "s3JobDefinition", "documentation": "<p>The S3 buckets that contain the objects to analyze, and the scope of that analysis.</p>"}, "samplingPercentage": {"shape": "__integer", "locationName": "samplingPercentage", "documentation": "<p>The sampling depth, as a percentage, for the job to apply when processing objects. This value determines the percentage of eligible objects that the job analyzes. If this value is less than 100, Amazon Macie selects the objects to analyze at random, up to the specified percentage, and analyzes all the data in those objects.</p>"}, "scheduleFrequency": {"shape": "JobScheduleFrequency", "locationName": "scheduleFrequency", "documentation": "<p>The recurrence pattern for running the job. To run the job only once, don't specify a value for this property and set the value for the jobType property to ONE_TIME.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies the tags to associate with the job.</p> <p>A job can have a maximum of 50 tags. Each tag consists of a tag key and an associated tag value. The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}, "required": ["s3JobDefinition", "jobType", "clientToken", "name"]}, "CreateClassificationJobResponse": {"type": "structure", "members": {"jobArn": {"shape": "__string", "locationName": "jobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the job.</p>"}, "jobId": {"shape": "__string", "locationName": "jobId", "documentation": "<p>The unique identifier for the job.</p>"}}}, "CreateCustomDataIdentifierRequest": {"type": "structure", "members": {"clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>A custom description of the custom data identifier. The description can contain as many as 512 characters.</p> <p>We strongly recommend that you avoid including any sensitive data in the description of a custom data identifier. Other users of your account might be able to see this description, depending on the actions that they're allowed to perform in Amazon Macie.</p>"}, "ignoreWords": {"shape": "__listOf__string", "locationName": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An array that lists specific character sequences (<i>ignore words</i>) to exclude from the results. If the text matched by the regular expression contains any string in this array, Amazon Macie ignores it. The array can contain as many as 10 ignore words. Each ignore word can contain 4-90 UTF-8 characters. Ignore words are case sensitive.</p>"}, "keywords": {"shape": "__listOf__string", "locationName": "keywords", "documentation": "<p>An array that lists specific character sequences (<i>keywords</i>), one of which must precede and be within proximity (maximumMatchDistance) of the regular expression to match. The array can contain as many as 50 keywords. Each keyword can contain 3-90 UTF-8 characters. Keywords aren't case sensitive.</p>"}, "maximumMatchDistance": {"shape": "__integer", "locationName": "maximumMatchDistance", "documentation": "<p>The maximum number of characters that can exist between the end of at least one complete character sequence specified by the keywords array and the end of the text that matches the regex pattern. If a complete keyword precedes all the text that matches the pattern and the keyword is within the specified distance, Amazon Macie includes the result. The distance can be 1-300 characters. The default value is 50.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>A custom name for the custom data identifier. The name can contain as many as 128 characters.</p> <p>We strongly recommend that you avoid including any sensitive data in the name of a custom data identifier. Other users of your account might be able to see this name, depending on the actions that they're allowed to perform in Amazon Macie.</p>"}, "regex": {"shape": "__string", "locationName": "regex", "documentation": "<p>The regular expression (<i>regex</i>) that defines the pattern to match. The expression can contain as many as 512 characters.</p>"}, "severityLevels": {"shape": "SeverityLevelList", "locationName": "severityLevels", "documentation": "<p>The severity to assign to findings that the custom data identifier produces, based on the number of occurrences of text that match the custom data identifier's detection criteria. You can specify as many as three SeverityLevel objects in this array, one for each severity: LOW, MEDIUM, or HIGH. If you specify more than one, the occurrences thresholds must be in ascending order by severity, moving from LOW to HIGH. For example, 1 for LOW, 50 for MEDIUM, and 100 for HIGH. If an S3 object contains fewer occurrences than the lowest specified threshold, Amazon Macie doesn't create a finding.</p> <p>If you don't specify any values for this array, <PERSON><PERSON> creates findings for S3 objects that contain at least one occurrence of text that matches the detection criteria, and <PERSON><PERSON> assigns the MEDIUM severity to those findings.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies the tags to associate with the custom data identifier.</p> <p>A custom data identifier can have a maximum of 50 tags. Each tag consists of a tag key and an associated tag value. The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}, "required": ["regex", "name"]}, "CreateCustomDataIdentifierResponse": {"type": "structure", "members": {"customDataIdentifierId": {"shape": "__string", "locationName": "customDataIdentifierId", "documentation": "<p>The unique identifier for the custom data identifier that was created.</p>"}}}, "CreateFindingsFilterRequest": {"type": "structure", "members": {"action": {"shape": "FindingsFilterAction", "locationName": "action", "documentation": "<p>The action to perform on findings that match the filter criteria (findingCriteria). Valid values are: ARCHIVE, suppress (automatically archive) the findings; and, NOOP, don't perform any action on the findings.</p>"}, "clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>A custom description of the filter. The description can contain as many as 512 characters.</p> <p>We strongly recommend that you avoid including any sensitive data in the description of a filter. Other users of your account might be able to see this description, depending on the actions that they're allowed to perform in Amazon Macie.</p>"}, "findingCriteria": {"shape": "FindingCriteria", "locationName": "findingCriteria", "documentation": "<p>The criteria to use to filter findings.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>A custom name for the filter. The name must contain at least 3 characters and can contain as many as 64 characters.</p> <p>We strongly recommend that you avoid including any sensitive data in the name of a filter. Other users of your account might be able to see this name, depending on the actions that they're allowed to perform in Amazon Macie.</p>"}, "position": {"shape": "__integer", "locationName": "position", "documentation": "<p>The position of the filter in the list of saved filters on the Amazon Macie console. This value also determines the order in which the filter is applied to findings, relative to other filters that are also applied to the findings.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies the tags to associate with the filter.</p> <p>A findings filter can have a maximum of 50 tags. Each tag consists of a tag key and an associated tag value. The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}, "required": ["action", "findingCriteria", "name"]}, "CreateFindingsFilterResponse": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the filter that was created.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the filter that was created.</p>"}}}, "CreateInvitationsRequest": {"type": "structure", "members": {"accountIds": {"shape": "__listOf__string", "locationName": "accountIds", "documentation": "<p>An array that lists Amazon Web Services account IDs, one for each account to send the invitation to.</p>"}, "disableEmailNotification": {"shape": "__boolean", "locationName": "disableEmailNotification", "documentation": "<p>Specifies whether to send the invitation as an email message. If this value is false, Amazon Macie sends the invitation (as an email message) to the email address that you specified for the recipient's account when you associated the account with your account. The default value is false.</p>"}, "message": {"shape": "__string", "locationName": "message", "documentation": "<p>Custom text to include in the email message that contains the invitation. The text can contain as many as 80 alphanumeric characters.</p>"}}, "required": ["accountIds"]}, "CreateInvitationsResponse": {"type": "structure", "members": {"unprocessedAccounts": {"shape": "__listOfUnprocessedAccount", "locationName": "unprocessedAccounts", "documentation": "<p>An array of objects, one for each account whose invitation hasn't been processed. Each object identifies the account and explains why the invitation hasn't been processed for the account.</p>"}}}, "CreateMemberRequest": {"type": "structure", "members": {"account": {"shape": "AccountDetail", "locationName": "account", "documentation": "<p>The details of the account to associate with the administrator account.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies the tags to associate with the account in Amazon Macie.</p> <p>An account can have a maximum of 50 tags. Each tag consists of a tag key and an associated tag value. The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}, "required": ["account"]}, "CreateMemberResponse": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the account that was associated with the administrator account.</p>"}}}, "CreateSampleFindingsRequest": {"type": "structure", "members": {"findingTypes": {"shape": "__listOfFindingType", "locationName": "findingTypes", "documentation": "<p>An array of finding types, one for each type of sample finding to create. To create a sample of every type of finding that Amazon Macie supports, don't include this array in your request.</p>"}}}, "CreateSampleFindingsResponse": {"type": "structure", "members": {}}, "CriteriaBlockForJob": {"type": "structure", "members": {"and": {"shape": "__listOfCriteriaForJob", "locationName": "and", "documentation": "<p>An array of conditions, one for each condition that determines which buckets to include or exclude from the job. If you specify more than one condition, Amazon Macie uses AND logic to join the conditions.</p>"}}, "documentation": "<p>Specifies one or more property- and tag-based conditions that define criteria for including or excluding S3 buckets from a classification job.</p>"}, "CriteriaForJob": {"type": "structure", "members": {"simpleCriterion": {"shape": "SimpleCriterionForJob", "locationName": "simpleCriterion", "documentation": "<p>A property-based condition that defines a property, operator, and one or more values for including or excluding buckets from the job.</p>"}, "tagCriterion": {"shape": "TagCriterionForJob", "locationName": "tagCriterion", "documentation": "<p>A tag-based condition that defines an operator and tag keys, tag values, or tag key and value pairs for including or excluding buckets from the job.</p>"}}, "documentation": "<p>Specifies a property- or tag-based condition that defines criteria for including or excluding S3 buckets from a classification job.</p>"}, "Criterion": {"type": "map", "documentation": "<p>Specifies a condition that defines a property, operator, and one or more values to filter the results of a query for findings. The number of values depends on the property and operator specified by the condition. For information about defining filter conditions, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/findings-filter-basics.html\">Fundamentals of filtering findings</a> in the <i>Amazon Macie User Guide</i>.</p>", "key": {"shape": "__string"}, "value": {"shape": "CriterionAdditionalProperties"}}, "CriterionAdditionalProperties": {"type": "structure", "members": {"eq": {"shape": "__listOf__string", "locationName": "eq", "documentation": "<p>The value for the property matches (equals) the specified value. If you specify multiple values, <PERSON><PERSON> uses OR logic to join the values.</p>"}, "eqExactMatch": {"shape": "__listOf__string", "locationName": "eqExactMatch", "documentation": "<p>The value for the property exclusively matches (equals an exact match for) all the specified values. If you specify multiple values, Amazon Macie uses AND logic to join the values.</p> <p>You can use this operator with the following properties: customDataIdentifiers.detections.arn, customDataIdentifiers.detections.name, resourcesAffected.s3Bucket.tags.key, resourcesAffected.s3Bucket.tags.value, resourcesAffected.s3Object.tags.key, resourcesAffected.s3Object.tags.value, sensitiveData.category, and sensitiveData.detections.type.</p>"}, "gt": {"shape": "__long", "locationName": "gt", "documentation": "<p>The value for the property is greater than the specified value.</p>"}, "gte": {"shape": "__long", "locationName": "gte", "documentation": "<p>The value for the property is greater than or equal to the specified value.</p>"}, "lt": {"shape": "__long", "locationName": "lt", "documentation": "<p>The value for the property is less than the specified value.</p>"}, "lte": {"shape": "__long", "locationName": "lte", "documentation": "<p>The value for the property is less than or equal to the specified value.</p>"}, "neq": {"shape": "__listOf__string", "locationName": "neq", "documentation": "<p>The value for the property doesn't match (doesn't equal) the specified value. If you specify multiple values, <PERSON><PERSON> uses OR logic to join the values.</p>"}}, "documentation": "<p>Specifies the operator to use in a property-based condition that filters the results of a query for findings. For detailed information and examples of each operator, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/findings-filter-basics.html\">Fundamentals of filtering findings</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "Currency": {"type": "string", "documentation": "<p>The type of currency that the data for an Amazon Macie usage metric is reported in. Possible values are:</p>", "enum": ["USD"]}, "CustomDataIdentifierSummary": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the custom data identifier.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the custom data identifier was created.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>The custom description of the custom data identifier.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the custom data identifier.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the custom data identifier.</p>"}}, "documentation": "<p>Provides information about a custom data identifier.</p>"}, "CustomDataIdentifiers": {"type": "structure", "members": {"detections": {"shape": "CustomDetections", "locationName": "detections", "documentation": "<p>The custom data identifiers that detected the data, and the number of occurrences of the data that each identifier detected.</p>"}, "totalCount": {"shape": "__long", "locationName": "totalCount", "documentation": "<p>The total number of occurrences of the data that was detected by the custom data identifiers and produced the finding.</p>"}}, "documentation": "<p>Provides information about custom data identifiers that produced a sensitive data finding, and the number of occurrences of the data that they detected for the finding.</p>"}, "CustomDetection": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The unique identifier for the custom data identifier.</p>"}, "count": {"shape": "__long", "locationName": "count", "documentation": "<p>The total number of occurrences of the sensitive data that the custom data identifier detected.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the custom data identifier.</p>"}, "occurrences": {"shape": "Occurrences", "locationName": "occurrences", "documentation": "<p>The location of 1-15 occurrences of the sensitive data that the custom data identifier detected. A finding includes location data for a maximum of 15 occurrences of sensitive data.</p>"}}, "documentation": "<p>Provides information about a custom data identifier that produced a sensitive data finding, and the sensitive data that it detected for the finding.</p>"}, "CustomDetections": {"type": "list", "documentation": "<p>Provides information about custom data identifiers that produced a sensitive data finding, and the number of occurrences of the data that each identifier detected.</p>", "member": {"shape": "CustomDetection"}}, "DailySchedule": {"type": "structure", "members": {}, "documentation": "<p>Specifies that a classification job runs once a day, every day. This is an empty object.</p>"}, "DataIdentifierSeverity": {"type": "string", "documentation": "<p>The severity of a finding, ranging from LOW, for least severe, to HIGH, for most severe. Valid values are:</p>", "enum": ["LOW", "MEDIUM", "HIGH"]}, "DataIdentifierType": {"type": "string", "documentation": "<p>The type of data identifier that detected a specific type of sensitive data in an S3 bucket. Possible values are:</p>", "enum": ["CUSTOM", "MANAGED"]}, "DayOfWeek": {"type": "string", "enum": ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"]}, "DeclineInvitationsRequest": {"type": "structure", "members": {"accountIds": {"shape": "__listOf__string", "locationName": "accountIds", "documentation": "<p>An array that lists Amazon Web Services account IDs, one for each account that sent an invitation to decline.</p>"}}, "required": ["accountIds"]}, "DeclineInvitationsResponse": {"type": "structure", "members": {"unprocessedAccounts": {"shape": "__listOfUnprocessedAccount", "locationName": "unprocessedAccounts", "documentation": "<p>An array of objects, one for each account whose invitation hasn't been declined. Each object identifies the account and explains why the request hasn't been processed for that account.</p>"}}}, "DefaultDetection": {"type": "structure", "members": {"count": {"shape": "__long", "locationName": "count", "documentation": "<p>The total number of occurrences of the type of sensitive data that was detected.</p>"}, "occurrences": {"shape": "Occurrences", "locationName": "occurrences", "documentation": "<p>The location of 1-15 occurrences of the sensitive data that was detected. A finding includes location data for a maximum of 15 occurrences of sensitive data.</p>"}, "type": {"shape": "__string", "locationName": "type", "documentation": "<p>The type of sensitive data that was detected. For example, AWS_CREDENTIALS, PHONE_NUMBER, or ADDRESS.</p>"}}, "documentation": "<p>Provides information about a type of sensitive data that was detected by a managed data identifier and produced a sensitive data finding.</p>"}, "DefaultDetections": {"type": "list", "documentation": "<p>Provides information about sensitive data that was detected by managed data identifiers and produced a sensitive data finding, and the number of occurrences of each type of sensitive data that was detected.</p>", "member": {"shape": "DefaultDetection"}}, "DeleteAllowListRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}, "ignoreJobChecks": {"shape": "__string", "location": "querystring", "locationName": "ignoreJobChecks", "documentation": "<p>Specifies whether to force deletion of the allow list, even if active classification jobs are configured to use the list.</p> <p>When you try to delete an allow list, Amazon Macie checks for classification jobs that use the list and have a status other than COMPLETE or CANCELLED. By default, <PERSON><PERSON> rejects your request if any jobs meet these criteria. To skip these checks and delete the list, set this value to true. To delete the list only if no active jobs are configured to use it, set this value to false.</p>"}}, "required": ["id"]}, "DeleteAllowListResponse": {"type": "structure", "members": {}}, "DeleteCustomDataIdentifierRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "DeleteCustomDataIdentifierResponse": {"type": "structure", "members": {}}, "DeleteFindingsFilterRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "DeleteFindingsFilterResponse": {"type": "structure", "members": {}}, "DeleteInvitationsRequest": {"type": "structure", "members": {"accountIds": {"shape": "__listOf__string", "locationName": "accountIds", "documentation": "<p>An array that lists Amazon Web Services account IDs, one for each account that sent an invitation to delete.</p>"}}, "required": ["accountIds"]}, "DeleteInvitationsResponse": {"type": "structure", "members": {"unprocessedAccounts": {"shape": "__listOfUnprocessedAccount", "locationName": "unprocessedAccounts", "documentation": "<p>An array of objects, one for each account whose invitation hasn't been deleted. Each object identifies the account and explains why the request hasn't been processed for that account.</p>"}}}, "DeleteMemberRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "DeleteMemberResponse": {"type": "structure", "members": {}}, "DescribeBucketsRequest": {"type": "structure", "members": {"criteria": {"shape": "BucketCriteria", "locationName": "criteria", "documentation": "<p>The criteria to use to filter the query results.</p>"}, "maxResults": {"shape": "__integer", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of the response. The default value is 50.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "sortCriteria": {"shape": "BucketSortCriteria", "locationName": "sortCriteria", "documentation": "<p>The criteria to use to sort the query results.</p>"}}}, "DescribeBucketsResponse": {"type": "structure", "members": {"buckets": {"shape": "__listOfBucketMetadata", "locationName": "buckets", "documentation": "<p>An array of objects, one for each bucket that matches the filter criteria specified in the request.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "DescribeClassificationJobRequest": {"type": "structure", "members": {"jobId": {"shape": "__string", "location": "uri", "locationName": "jobId", "documentation": "<p>The unique identifier for the classification job.</p>"}}, "required": ["jobId"]}, "DescribeClassificationJobResponse": {"type": "structure", "members": {"allowListIds": {"shape": "__listOf__string", "locationName": "allowListIds", "documentation": "<p>An array of unique identifiers, one for each allow list that the job uses when it analyzes data.</p>"}, "clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>The token that was provided to ensure the idempotency of the request to create the job.</p>", "idempotencyToken": true}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the job was created.</p>"}, "customDataIdentifierIds": {"shape": "__listOf__string", "locationName": "customDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each custom data identifier that the job uses when it analyzes data. This value is null if the job uses only managed data identifiers to analyze data.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>The custom description of the job.</p>"}, "initialRun": {"shape": "__boolean", "locationName": "initialRun", "documentation": "<p>For a recurring job, specifies whether you configured the job to analyze all existing, eligible objects immediately after the job was created (true). If you configured the job to analyze only those objects that were created or changed after the job was created and before the job's first scheduled run, this value is false. This value is also false for a one-time job.</p>"}, "jobArn": {"shape": "__string", "locationName": "jobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the job.</p>"}, "jobId": {"shape": "__string", "locationName": "jobId", "documentation": "<p>The unique identifier for the job.</p>"}, "jobStatus": {"shape": "JobStatus", "locationName": "jobStatus", "documentation": "<p>The current status of the job. Possible values are:</p> <ul><li><p>CANCELLED - You cancelled the job or, if it's a one-time job, you paused the job and didn't resume it within 30 days.</p></li> <li><p>COMPLETE - For a one-time job, <PERSON> Macie finished processing the data specified for the job. This value doesn't apply to recurring jobs.</p></li> <li><p>IDLE - For a recurring job, the previous scheduled run is complete and the next scheduled run is pending. This value doesn't apply to one-time jobs.</p></li> <li><p>PAUSED - <PERSON><PERSON> started running the job but additional processing would exceed the monthly sensitive data discovery quota for your account or one or more member accounts that the job analyzes data for.</p></li> <li><p>RUNNING - For a one-time job, the job is in progress. For a recurring job, a scheduled run is in progress.</p></li> <li><p>USER_PAUSED - You paused the job. If you paused the job while it had a status of RUNNING and you don't resume it within 30 days of pausing it, the job or job run will expire and be cancelled, depending on the job's type. To check the expiration date, refer to the UserPausedDetails.jobExpiresAt property.</p></li></ul>"}, "jobType": {"shape": "JobType", "locationName": "jobType", "documentation": "<p>The schedule for running the job. Possible values are:</p> <ul><li><p>ONE_TIME - The job runs only once.</p></li> <li><p>SCHEDULED - The job runs on a daily, weekly, or monthly basis. The scheduleFrequency property indicates the recurrence pattern for the job.</p></li></ul>"}, "lastRunErrorStatus": {"shape": "LastRunErrorStatus", "locationName": "lastRunErrorStatus", "documentation": "<p>Specifies whether any account- or bucket-level access errors occurred when the job ran. For a recurring job, this value indicates the error status of the job's most recent run.</p>"}, "lastRunTime": {"shape": "__timestampIso8601", "locationName": "lastRunTime", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the job started. If the job is a recurring job, this value indicates when the most recent run started or, if the job hasn't run yet, when the job was created.</p>"}, "managedDataIdentifierIds": {"shape": "__listOf__string", "locationName": "managedDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each managed data identifier that the job is explicitly configured to include (use) or exclude (not use) when it analyzes data. Inclusion or exclusion depends on the managed data identifier selection type specified for the job (managedDataIdentifierSelector).</p><p>This value is null if the job's managed data identifier selection type is ALL, NONE, or RECOMMENDED.</p>"}, "managedDataIdentifierSelector": {"shape": "ManagedDataIdentifierSelector", "locationName": "managedDataIdentifierSelector", "documentation": "<p>The selection type that determines which managed data identifiers the job uses when it analyzes data. Possible values are:</p> <ul><li><p>ALL - Use all managed data identifiers.</p></li> <li><p>EXCLUDE - Use all managed data identifiers except the ones specified by the managedDataIdentifierIds property.</p></li> <li><p>INCLUDE - Use only the managed data identifiers specified by the managedDataIdentifierIds property.</p></li> <li><p>NONE - Don't use any managed data identifiers. Use only custom data identifiers (customDataIdentifierIds).</p></li> <li><p>RECOMMENDED (default) - Use the recommended set of managed data identifiers.</p></li></ul> <p>If this value is null, the job uses the recommended set of managed data identifiers.</p> <p>If the job is a recurring job and this value is ALL or EXCLUDE, each job run automatically uses new managed data identifiers that are released. If this value is null or RECOMMENDED for a recurring job, each job run uses all the managed data identifiers that are in the recommended set when the run starts.</p> <p>For information about individual managed data identifiers or to determine which ones are in the recommended set, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/managed-data-identifiers.html\">Using managed data identifiers</a> and <a href=\"https://docs.aws.amazon.com/macie/latest/user/discovery-jobs-mdis-recommended.html\">Recommended managed data identifiers</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the job.</p>"}, "s3JobDefinition": {"shape": "S3JobDefinition", "locationName": "s3JobDefinition", "documentation": "<p>The S3 buckets that contain the objects to analyze, and the scope of that analysis.</p>"}, "samplingPercentage": {"shape": "__integer", "locationName": "samplingPercentage", "documentation": "<p>The sampling depth, as a percentage, that determines the percentage of eligible objects that the job analyzes.</p>"}, "scheduleFrequency": {"shape": "JobScheduleFrequency", "locationName": "scheduleFrequency", "documentation": "<p>The recurrence pattern for running the job. This value is null if the job is configured to run only once.</p>"}, "statistics": {"shape": "Statistics", "locationName": "statistics", "documentation": "<p>The number of times that the job has run and processing statistics for the job's current run.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the classification job.</p>"}, "userPausedDetails": {"shape": "UserPausedDetails", "locationName": "userPausedDetails", "documentation": "<p>If the current status of the job is USER_PAUSED, specifies when the job was paused and when the job or job run will expire and be cancelled if it isn't resumed. This value is present only if the value for jobStatus is USER_PAUSED.</p>"}}}, "DescribeOrganizationConfigurationRequest": {"type": "structure", "members": {}}, "DescribeOrganizationConfigurationResponse": {"type": "structure", "members": {"autoEnable": {"shape": "__boolean", "locationName": "autoEnable", "documentation": "<p>Specifies whether Amazon Macie is enabled automatically for accounts that are added to the organization.</p>"}, "maxAccountLimitReached": {"shape": "__boolean", "locationName": "maxAccountLimitReached", "documentation": "<p>Specifies whether the maximum number of Amazon Macie member accounts are part of the organization.</p>"}}}, "DetectedDataDetails": {"type": "structure", "members": {"value": {"shape": "__stringMin1Max128", "locationName": "value", "documentation": "<p>An occurrence of the specified type of sensitive data. Each occurrence can contain 1-128 characters.</p>"}}, "documentation": "<p>Specifies 1-10 occurrences of a specific type of sensitive data reported by a finding.</p>", "required": ["value"]}, "Detection": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>If the sensitive data was detected by a custom data identifier, the Amazon Resource Name (ARN) of the custom data identifier that detected the data. Otherwise, this value is null.</p>"}, "count": {"shape": "__long", "locationName": "count", "documentation": "<p>The total number of occurrences of the sensitive data.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the custom data identifier or managed data identifier that detected the sensitive data. For additional details about a specified managed data identifier, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/managed-data-identifiers.html\">Using managed data identifiers</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the custom data identifier or managed data identifier that detected the sensitive data. For a managed data identifier, this value is the same as the unique identifier (id).</p>"}, "suppressed": {"shape": "__boolean", "locationName": "suppressed", "documentation": "<p>Specifies whether occurrences of this type of sensitive data are excluded (true) or included (false) in the bucket's sensitivity score.</p>"}, "type": {"shape": "DataIdentifierType", "locationName": "type", "documentation": "<p>The type of data identifier that detected the sensitive data. Possible values are: CUSTOM, for a custom data identifier; and, MANAGED, for a managed data identifier.</p>"}}, "documentation": "<p>Provides information about a type of sensitive data that <PERSON> Macie found in an S3 bucket while performing automated sensitive data discovery for the bucket. The information also specifies the custom data identifier or managed data identifier that detected the data. This information is available only if automated sensitive data discovery is currently enabled for your account.</p>"}, "DisableMacieRequest": {"type": "structure", "members": {}}, "DisableMacieResponse": {"type": "structure", "members": {}}, "DisableOrganizationAdminAccountRequest": {"type": "structure", "members": {"adminAccountId": {"shape": "__string", "location": "querystring", "locationName": "adminAccountId", "documentation": "<p>The Amazon Web Services account ID of the delegated Amazon Macie administrator account.</p>"}}, "required": ["adminAccountId"]}, "DisableOrganizationAdminAccountResponse": {"type": "structure", "members": {}}, "DisassociateFromAdministratorAccountRequest": {"type": "structure", "members": {}}, "DisassociateFromAdministratorAccountResponse": {"type": "structure", "members": {}}, "DisassociateFromMasterAccountRequest": {"type": "structure", "members": {}}, "DisassociateFromMasterAccountResponse": {"type": "structure", "members": {}}, "DisassociateMemberRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "DisassociateMemberResponse": {"type": "structure", "members": {}}, "DomainDetails": {"type": "structure", "members": {"domainName": {"shape": "__string", "locationName": "domainName", "documentation": "<p>The name of the domain.</p>"}}, "documentation": "<p>Provides information about the domain name of the device that an entity used to perform an action on an affected resource.</p>"}, "EffectivePermission": {"type": "string", "enum": ["PUBLIC", "NOT_PUBLIC", "UNKNOWN"]}, "Empty": {"type": "structure", "members": {}, "documentation": "<p>The request succeeded and there isn't any content to include in the body of the response (No Content).</p>"}, "EnableMacieRequest": {"type": "structure", "members": {"clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "findingPublishingFrequency": {"shape": "FindingPublishingFrequency", "locationName": "findingPublishingFrequency", "documentation": "<p>Specifies how often to publish updates to policy findings for the account. This includes publishing updates to Security Hub and Amazon EventBridge (formerly Amazon CloudWatch Events).</p>"}, "status": {"shape": "<PERSON>ie<PERSON><PERSON><PERSON>", "locationName": "status", "documentation": "<p>Specifies the new status for the account. To enable Amazon Macie and start all Macie activities for the account, set this value to ENABLED.</p>"}}}, "EnableMacieResponse": {"type": "structure", "members": {}}, "EnableOrganizationAdminAccountRequest": {"type": "structure", "members": {"adminAccountId": {"shape": "__string", "locationName": "adminAccountId", "documentation": "<p>The Amazon Web Services account ID for the account to designate as the delegated Amazon Macie administrator account for the organization.</p>"}, "clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}}, "required": ["adminAccountId"]}, "EnableOrganizationAdminAccountResponse": {"type": "structure", "members": {}}, "EncryptionType": {"type": "string", "documentation": "<p>The server-side encryption algorithm that was used to encrypt an S3 object or is used by default to encrypt objects that are added to an S3 bucket. Possible values are:</p>", "enum": ["NONE", "AES256", "aws:kms", "UNKNOWN"]}, "ErrorCode": {"type": "string", "documentation": "<p>The source of an issue or delay. Possible values are:</p>", "enum": ["ClientError", "InternalError"]}, "FederatedUser": {"type": "structure", "members": {"accessKeyId": {"shape": "__string", "locationName": "accessKeyId", "documentation": "<p>The Amazon Web Services access key ID that identifies the credentials.</p>"}, "accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that owns the entity that was used to get the credentials.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the entity that was used to get the credentials.</p>"}, "principalId": {"shape": "__string", "locationName": "principalId", "documentation": "<p>The unique identifier for the entity that was used to get the credentials.</p>"}, "sessionContext": {"shape": "SessionContext", "locationName": "sessionContext", "documentation": "<p>The details of the session that was created for the credentials, including the entity that issued the session.</p>"}}, "documentation": "<p>Provides information about an identity that performed an action on an affected resource by using temporary security credentials. The credentials were obtained using the GetFederationToken operation of the Security Token Service (STS) API.</p>"}, "Finding": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that the finding applies to. This is typically the account that owns the affected resource.</p>"}, "archived": {"shape": "__boolean", "locationName": "archived", "documentation": "<p>Specifies whether the finding is archived (suppressed).</p>"}, "category": {"shape": "FindingCategory", "locationName": "category", "documentation": "<p>The category of the finding. Possible values are: CLASSIFICATION, for a sensitive data finding; and, POLICY, for a policy finding.</p>"}, "classificationDetails": {"shape": "ClassificationDetails", "locationName": "classificationDetails", "documentation": "<p>The details of a sensitive data finding. This value is null for a policy finding.</p>"}, "count": {"shape": "__long", "locationName": "count", "documentation": "<p>The total number of occurrences of the finding. For sensitive data findings, this value is always 1. All sensitive data findings are considered unique.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie created the finding.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>The description of the finding.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the finding. This is a random string that Amazon Macie generates and assigns to a finding when it creates the finding.</p>"}, "partition": {"shape": "__string", "locationName": "partition", "documentation": "<p>The Amazon Web Services partition that Amazon Macie created the finding in.</p>"}, "policyDetails": {"shape": "PolicyDetails", "locationName": "policyDetails", "documentation": "<p>The details of a policy finding. This value is null for a sensitive data finding.</p>"}, "region": {"shape": "__string", "locationName": "region", "documentation": "<p>The Amazon Web Services Region that Amazon Macie created the finding in.</p>"}, "resourcesAffected": {"shape": "ResourcesAffected", "locationName": "resourcesAffected", "documentation": "<p>The resources that the finding applies to.</p>"}, "sample": {"shape": "__boolean", "locationName": "sample", "documentation": "<p>Specifies whether the finding is a sample finding. A <i>sample finding</i> is a finding that uses example data to demonstrate what a finding might contain.</p>"}, "schemaVersion": {"shape": "__string", "locationName": "schemaVersion", "documentation": "<p>The version of the schema that was used to define the data structures in the finding.</p>"}, "severity": {"shape": "Severity", "locationName": "severity", "documentation": "<p>The severity level and score for the finding.</p>"}, "title": {"shape": "__string", "locationName": "title", "documentation": "<p>The brief description of the finding.</p>"}, "type": {"shape": "FindingType", "locationName": "type", "documentation": "<p>The type of the finding.</p>"}, "updatedAt": {"shape": "__timestampIso8601", "locationName": "updatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie last updated the finding. For sensitive data findings, this value is the same as the value for the createdAt property. All sensitive data findings are considered new.</p>"}}, "documentation": "<p>Provides the details of a finding.</p>"}, "FindingAction": {"type": "structure", "members": {"actionType": {"shape": "FindingActionType", "locationName": "actionType", "documentation": "<p>The type of action that occurred for the affected resource. This value is typically AWS_API_CALL, which indicates that an entity invoked an API operation for the resource.</p>"}, "apiCallDetails": {"shape": "ApiCallDetails", "locationName": "apiCallDetails", "documentation": "<p>The invocation details of the API operation that an entity invoked for the affected resource, if the value for the actionType property is AWS_API_CALL.</p>"}}, "documentation": "<p>Provides information about an action that occurred for a resource and produced a policy finding.</p>"}, "FindingActionType": {"type": "string", "documentation": "<p>The type of action that occurred for the resource and produced the policy finding:</p>", "enum": ["AWS_API_CALL"]}, "FindingActor": {"type": "structure", "members": {"domainDetails": {"shape": "DomainDetails", "locationName": "domainDetails", "documentation": "<p>The domain name of the device that the entity used to perform the action on the affected resource.</p>"}, "ipAddressDetails": {"shape": "IpAddressDetails", "locationName": "ipAddressDetails", "documentation": "<p>The IP address of the device that the entity used to perform the action on the affected resource. This object also provides information such as the owner and geographic location for the IP address.</p>"}, "userIdentity": {"shape": "UserIdentity", "locationName": "userIdentity", "documentation": "<p>The type and other characteristics of the entity that performed the action on the affected resource.</p>"}}, "documentation": "<p>Provides information about an entity that performed an action that produced a policy finding for a resource.</p>"}, "FindingCategory": {"type": "string", "documentation": "<p>The category of the finding. Possible values are:</p>", "enum": ["CLASSIFICATION", "POLICY"]}, "FindingCriteria": {"type": "structure", "members": {"criterion": {"shape": "Criterion", "locationName": "criterion", "documentation": "<p>A condition that specifies the property, operator, and one or more values to use to filter the results.</p>"}}, "documentation": "<p>Specifies, as a map, one or more property-based conditions that filter the results of a query for findings.</p>"}, "FindingPublishingFrequency": {"type": "string", "documentation": "<p>The frequency with which Amazon Macie publishes updates to policy findings for an account. This includes publishing updates to Security Hub and Amazon EventBridge (formerly Amazon CloudWatch Events). For more information, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/findings-monitor.html\">Monitoring and processing findings</a> in the <i>Amazon Macie User Guide</i>. Valid values are:</p>", "enum": ["FIFTEEN_MINUTES", "ONE_HOUR", "SIX_HOURS"]}, "FindingStatisticsSortAttributeName": {"type": "string", "documentation": "<p>The grouping to sort the results by. Valid values are:</p>", "enum": ["groupKey", "count"]}, "FindingStatisticsSortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "FindingStatisticsSortAttributeName", "locationName": "attributeName", "documentation": "<p>The grouping to sort the results by. Valid values are: count, sort the results by the number of findings in each group of results; and, groupKey, sort the results by the name of each group of results.</p>"}, "orderBy": {"shape": "OrderBy", "locationName": "orderBy", "documentation": "<p>The sort order to apply to the results, based on the value for the property specified by the attributeName property. Valid values are: ASC, sort the results in ascending order; and, DESC, sort the results in descending order.</p>"}}, "documentation": "<p>Specifies criteria for sorting the results of a query that retrieves aggregated statistical data about findings.</p>"}, "FindingType": {"type": "string", "documentation": "<p>The type of finding. For details about each type, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/findings-types.html\">Types of Amazon Macie findings</a> in the <i>Amazon Macie User Guide</i>. Possible values are:</p>", "enum": ["SensitiveData:S3Object/Multiple", "SensitiveData:S3Object/Financial", "SensitiveData:S3Object/Personal", "SensitiveData:S3Object/Credentials", "SensitiveData:S3Object/CustomIdentifier", "Policy:IAMUser/S3BucketPublic", "Policy:IAMUser/S3BucketSharedExternally", "Policy:IAMUser/S3BucketReplicatedExternally", "Policy:IAMUser/S3BucketEncryptionDisabled", "Policy:IAMUser/S3BlockPublicAccessDisabled", "Policy:IAMUser/S3BucketSharedWithCloudFront"]}, "FindingsFilterAction": {"type": "string", "documentation": "<p>The action to perform on findings that match the filter criteria. To suppress (automatically archive) findings that match the criteria, set this value to ARCHIVE. Valid values are:</p>", "enum": ["ARCHIVE", "NOOP"]}, "FindingsFilterListItem": {"type": "structure", "members": {"action": {"shape": "FindingsFilterAction", "locationName": "action", "documentation": "<p>The action that's performed on findings that match the filter criteria. Possible values are: ARCHIVE, suppress (automatically archive) the findings; and, NOOP, don't perform any action on the findings.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the filter.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the filter.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the filter.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the filter.</p>"}}, "documentation": "<p>Provides information about a findings filter.</p>"}, "GetAdministratorAccountRequest": {"type": "structure", "members": {}}, "GetAdministratorAccountResponse": {"type": "structure", "members": {"administrator": {"shape": "Invitation", "locationName": "administrator", "documentation": "<p>The Amazon Web Services account ID for the administrator account. If the accounts are associated by an Amazon Macie membership invitation, this object also provides details about the invitation that was sent to establish the relationship between the accounts.</p>"}}}, "GetAllowListRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "GetAllowListResponse": {"type": "structure", "members": {"arn": {"shape": "__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the allow list.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the allow list was created in Amazon Macie.</p>"}, "criteria": {"shape": "AllowListCriteria", "locationName": "criteria", "documentation": "<p>The criteria that specify the text or text pattern to ignore. The criteria can be the location and name of an S3 object that lists specific text to ignore (s3WordsList), or a regular expression (regex) that defines a text pattern to ignore.</p>"}, "description": {"shape": "__stringMin1Max512PatternSS", "locationName": "description", "documentation": "<p>The custom description of the allow list.</p>"}, "id": {"shape": "__stringMin22Max22PatternAZ0922", "locationName": "id", "documentation": "<p>The unique identifier for the allow list.</p>"}, "name": {"shape": "__stringMin1Max128Pattern", "locationName": "name", "documentation": "<p>The custom name of the allow list.</p>"}, "status": {"shape": "AllowListStatus", "locationName": "status", "documentation": "<p>The current status of the allow list, which indicates whether Amazon Macie can access and use the list's criteria.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the allow list.</p>"}, "updatedAt": {"shape": "__timestampIso8601", "locationName": "updatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the allow list's settings were most recently changed in Amazon Macie.</p>"}}}, "GetAutomatedDiscoveryConfigurationRequest": {"type": "structure", "members": {}}, "GetAutomatedDiscoveryConfigurationResponse": {"type": "structure", "members": {"classificationScopeId": {"shape": "ClassificationScopeId", "locationName": "classificationScopeId", "documentation": "<p>The unique identifier for the classification scope that's used when performing automated sensitive data discovery for the account. The classification scope specifies S3 buckets to exclude from automated sensitive data discovery.</p>"}, "disabledAt": {"shape": "Timestamp", "locationName": "disabledAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when automated sensitive data discovery was most recently disabled for the account. This value is null if automated sensitive data discovery wasn't enabled and subsequently disabled for the account.</p>"}, "firstEnabledAt": {"shape": "Timestamp", "locationName": "firstEnabledAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when automated sensitive data discovery was initially enabled for the account. This value is null if automated sensitive data discovery has never been enabled for the account.</p>"}, "lastUpdatedAt": {"shape": "Timestamp", "locationName": "lastUpdatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when automated sensitive data discovery was most recently enabled or disabled for the account.</p>"}, "sensitivityInspectionTemplateId": {"shape": "SensitivityInspectionTemplateId", "locationName": "sensitivityInspectionTemplateId", "documentation": "<p>The unique identifier for the sensitivity inspection template that's used when performing automated sensitive data discovery for the account. The template specifies which allow lists, custom data identifiers, and managed data identifiers to use when analyzing data.</p>"}, "status": {"shape": "AutomatedDiscoveryStatus", "locationName": "status", "documentation": "<p>The current status of the automated sensitive data discovery configuration for the account. Possible values are: ENABLED, use the specified settings to perform automated sensitive data discovery activities for the account; and, DISABLED, don't perform automated sensitive data discovery activities for the account.</p>"}}}, "GetBucketStatisticsRequest": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account.</p>"}}}, "GetBucketStatisticsResponse": {"type": "structure", "members": {"bucketCount": {"shape": "__long", "locationName": "bucketCount", "documentation": "<p>The total number of buckets.</p>"}, "bucketCountByEffectivePermission": {"shape": "BucketCountByEffectivePermission", "locationName": "bucketCountByEffectivePermission", "documentation": "<p>The total number of buckets that are publicly accessible due to a combination of permissions settings for each bucket.</p>"}, "bucketCountByEncryptionType": {"shape": "BucketCountByEncryptionType", "locationName": "bucketCountByEncryptionType", "documentation": "<p>The total number of buckets whose settings do or don't specify default server-side encryption behavior for objects that are added to the buckets.</p>"}, "bucketCountByObjectEncryptionRequirement": {"shape": "BucketCountPolicyAllowsUnencryptedObjectUploads", "locationName": "bucketCountByObjectEncryptionRequirement", "documentation": "<p>The total number of buckets whose bucket policies do or don't require server-side encryption of objects when objects are added to the buckets.</p>"}, "bucketCountBySharedAccessType": {"shape": "BucketCountBySharedAccessType", "locationName": "bucketCountBySharedAccessType", "documentation": "<p>The total number of buckets that are or aren't shared with other Amazon Web Services accounts, Amazon CloudFront origin access identities (OAIs), or CloudFront origin access controls (OACs).</p>"}, "bucketStatisticsBySensitivity": {"shape": "BucketStatisticsBySensitivity", "locationName": "bucketStatisticsBySensitivity", "documentation": "<p>The aggregated sensitive data discovery statistics for the buckets. If automated sensitive data discovery is currently disabled for your account, the value for each statistic is 0.</p>"}, "classifiableObjectCount": {"shape": "__long", "locationName": "classifiableObjectCount", "documentation": "<p>The total number of objects that Amazon Macie can analyze in the buckets. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p>"}, "classifiableSizeInBytes": {"shape": "__long", "locationName": "classifiableSizeInBytes", "documentation": "<p>The total storage size, in bytes, of all the objects that Amazon Macie can analyze in the buckets. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p> <p>If versioning is enabled for any of the buckets, this value is based on the size of the latest version of each applicable object in the buckets. This value doesn't reflect the storage size of all versions of all applicable objects in the buckets.</p>"}, "lastUpdated": {"shape": "__timestampIso8601", "locationName": "lastUpdated", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie most recently retrieved bucket or object metadata from Amazon S3 for the buckets.</p>"}, "objectCount": {"shape": "__long", "locationName": "objectCount", "documentation": "<p>The total number of objects in the buckets.</p>"}, "sizeInBytes": {"shape": "__long", "locationName": "sizeInBytes", "documentation": "<p>The total storage size, in bytes, of the buckets.</p> <p>If versioning is enabled for any of the buckets, this value is based on the size of the latest version of each object in the buckets. This value doesn't reflect the storage size of all versions of the objects in the buckets.</p>"}, "sizeInBytesCompressed": {"shape": "__long", "locationName": "sizeInBytesCompressed", "documentation": "<p>The total storage size, in bytes, of the objects that are compressed (.gz, .gzip, .zip) files in the buckets.</p> <p>If versioning is enabled for any of the buckets, this value is based on the size of the latest version of each applicable object in the buckets. This value doesn't reflect the storage size of all versions of the applicable objects in the buckets.</p>"}, "unclassifiableObjectCount": {"shape": "ObjectLevelStatistics", "locationName": "unclassifiableObjectCount", "documentation": "<p>The total number of objects that Amazon Macie can't analyze in the buckets. These objects don't use a supported storage class or don't have a file name extension for a supported file or storage format.</p>"}, "unclassifiableObjectSizeInBytes": {"shape": "ObjectLevelStatistics", "locationName": "unclassifiableObjectSizeInBytes", "documentation": "<p>The total storage size, in bytes, of the objects that Amazon Macie can't analyze in the buckets. These objects don't use a supported storage class or don't have a file name extension for a supported file or storage format.</p>"}}}, "GetClassificationExportConfigurationRequest": {"type": "structure", "members": {}}, "GetClassificationExportConfigurationResponse": {"type": "structure", "members": {"configuration": {"shape": "ClassificationExportConfiguration", "locationName": "configuration", "documentation": "<p>The location where data classification results are stored, and the encryption settings that are used when storing results in that location.</p>"}}}, "GetClassificationScopeRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "GetClassificationScopeResponse": {"type": "structure", "members": {"id": {"shape": "ClassificationScopeId", "locationName": "id", "documentation": "<p>The unique identifier for the classification scope.</p>"}, "name": {"shape": "ClassificationScopeName", "locationName": "name", "documentation": "<p>The name of the classification scope: automated-sensitive-data-discovery.</p>"}, "s3": {"shape": "S3ClassificationScope", "locationName": "s3", "documentation": "<p>The S3 buckets that are excluded from automated sensitive data discovery.</p>"}}}, "GetCustomDataIdentifierRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "GetCustomDataIdentifierResponse": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the custom data identifier.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the custom data identifier was created.</p>"}, "deleted": {"shape": "__boolean", "locationName": "deleted", "documentation": "<p>Specifies whether the custom data identifier was deleted. If you delete a custom data identifier, Amazon Macie doesn't delete it permanently. Instead, it soft deletes the identifier.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>The custom description of the custom data identifier.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the custom data identifier.</p>"}, "ignoreWords": {"shape": "__listOf__string", "locationName": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An array that lists specific character sequences (<i>ignore words</i>) to exclude from the results. If the text matched by the regular expression contains any string in this array, <PERSON> Macie ignores it. Ignore words are case sensitive.</p>"}, "keywords": {"shape": "__listOf__string", "locationName": "keywords", "documentation": "<p>An array that lists specific character sequences (<i>keywords</i>), one of which must precede and be within proximity (maximumMatchDistance) of the regular expression to match. Keywords aren't case sensitive.</p>"}, "maximumMatchDistance": {"shape": "__integer", "locationName": "maximumMatchDistance", "documentation": "<p>The maximum number of characters that can exist between the end of at least one complete character sequence specified by the keywords array and the end of the text that matches the regex pattern. If a complete keyword precedes all the text that matches the pattern and the keyword is within the specified distance, Amazon Macie includes the result. Otherwise, <PERSON><PERSON> excludes the result.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the custom data identifier.</p>"}, "regex": {"shape": "__string", "locationName": "regex", "documentation": "<p>The regular expression (<i>regex</i>) that defines the pattern to match.</p>"}, "severityLevels": {"shape": "SeverityLevelList", "locationName": "severityLevels", "documentation": "<p>Specifies the severity that's assigned to findings that the custom data identifier produces, based on the number of occurrences of text that match the custom data identifier's detection criteria. By default, Amazon Macie creates findings for S3 objects that contain at least one occurrence of text that matches the detection criteria, and <PERSON><PERSON> assigns the MEDIUM severity to those findings.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that identifies the tags (keys and values) that are associated with the custom data identifier.</p>"}}}, "GetFindingStatisticsRequest": {"type": "structure", "members": {"findingCriteria": {"shape": "FindingCriteria", "locationName": "findingCriteria", "documentation": "<p>The criteria to use to filter the query results.</p>"}, "groupBy": {"shape": "GroupBy", "locationName": "groupBy", "documentation": "<p>The finding property to use to group the query results. Valid values are:</p> <ul><li><p>classificationDetails.jobId - The unique identifier for the classification job that produced the finding.</p></li> <li><p>resourcesAffected.s3Bucket.name - The name of the S3 bucket that the finding applies to.</p></li> <li><p>severity.description - The severity level of the finding, such as High or Medium.</p></li> <li><p>type - The type of finding, such as Policy:IAMUser/S3BucketPublic and SensitiveData:S3Object/Personal.</p></li></ul>"}, "size": {"shape": "__integer", "locationName": "size", "documentation": "<p>The maximum number of items to include in each page of the response.</p>"}, "sortCriteria": {"shape": "FindingStatisticsSortCriteria", "locationName": "sortCriteria", "documentation": "<p>The criteria to use to sort the query results.</p>"}}, "required": ["groupBy"]}, "GetFindingStatisticsResponse": {"type": "structure", "members": {"countsByGroup": {"shape": "__listOfGroupCount", "locationName": "countsByGroup", "documentation": "<p>An array of objects, one for each group of findings that matches the filter criteria specified in the request.</p>"}}}, "GetFindingsFilterRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "GetFindingsFilterResponse": {"type": "structure", "members": {"action": {"shape": "FindingsFilterAction", "locationName": "action", "documentation": "<p>The action that's performed on findings that match the filter criteria (findingCriteria). Possible values are: ARCHIVE, suppress (automatically archive) the findings; and, NOOP, don't perform any action on the findings.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the filter.</p>"}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>The custom description of the filter.</p>"}, "findingCriteria": {"shape": "FindingCriteria", "locationName": "findingCriteria", "documentation": "<p>The criteria that's used to filter findings.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the filter.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the filter.</p>"}, "position": {"shape": "__integer", "locationName": "position", "documentation": "<p>The position of the filter in the list of saved filters on the Amazon Macie console. This value also determines the order in which the filter is applied to findings, relative to other filters that are also applied to the findings.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the filter.</p>"}}}, "GetFindingsPublicationConfigurationRequest": {"type": "structure", "members": {}}, "GetFindingsPublicationConfigurationResponse": {"type": "structure", "members": {"securityHubConfiguration": {"shape": "SecurityHubConfiguration", "locationName": "securityHubConfiguration", "documentation": "<p>The configuration settings that determine which findings are published to Security Hub.</p>"}}}, "GetFindingsRequest": {"type": "structure", "members": {"findingIds": {"shape": "__listOf__string", "locationName": "findingIds", "documentation": "<p>An array of strings that lists the unique identifiers for the findings to retrieve. You can specify as many as 50 unique identifiers in this array.</p>"}, "sortCriteria": {"shape": "SortCriteria", "locationName": "sortCriteria", "documentation": "<p>The criteria for sorting the results of the request.</p>"}}, "required": ["findingIds"]}, "GetFindingsResponse": {"type": "structure", "members": {"findings": {"shape": "__listOfFinding", "locationName": "findings", "documentation": "<p>An array of objects, one for each finding that matches the criteria specified in the request.</p>"}}}, "GetInvitationsCountRequest": {"type": "structure", "members": {}}, "GetInvitationsCountResponse": {"type": "structure", "members": {"invitationsCount": {"shape": "__long", "locationName": "invitationsCount", "documentation": "<p>The total number of invitations that were received by the account, not including the currently accepted invitation.</p>"}}}, "GetMacieSessionRequest": {"type": "structure", "members": {}}, "GetMacieSessionResponse": {"type": "structure", "members": {"createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the Amazon Macie account was created.</p>"}, "findingPublishingFrequency": {"shape": "FindingPublishingFrequency", "locationName": "findingPublishingFrequency", "documentation": "<p>The frequency with which Amazon Macie publishes updates to policy findings for the account. This includes publishing updates to Security Hub and Amazon EventBridge (formerly Amazon CloudWatch Events).</p>"}, "serviceRole": {"shape": "__string", "locationName": "serviceRole", "documentation": "<p>The Amazon Resource Name (ARN) of the service-linked role that allows Amazon Macie to monitor and analyze data in Amazon Web Services resources for the account.</p>"}, "status": {"shape": "<PERSON>ie<PERSON><PERSON><PERSON>", "locationName": "status", "documentation": "<p>The current status of the Amazon Macie account. Possible values are: PAUSED, the account is enabled but all Macie activities are suspended (paused) for the account; and, ENABLED, the account is enabled and all Macie activities are enabled for the account.</p>"}, "updatedAt": {"shape": "__timestampIso8601", "locationName": "updatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, of the most recent change to the status of the Amazon Macie account.</p>"}}}, "GetMasterAccountRequest": {"type": "structure", "members": {}}, "GetMasterAccountResponse": {"type": "structure", "members": {"master": {"shape": "Invitation", "locationName": "master", "documentation": "<p>(Deprecated) The Amazon Web Services account ID for the administrator account. If the accounts are associated by a Macie membership invitation, this object also provides details about the invitation that was sent to establish the relationship between the accounts.</p>"}}}, "GetMemberRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "GetMemberResponse": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The Amazon Web Services account ID for the account.</p>"}, "administratorAccountId": {"shape": "__string", "locationName": "administratorAccountId", "documentation": "<p>The Amazon Web Services account ID for the administrator account.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the account.</p>"}, "email": {"shape": "__string", "locationName": "email", "documentation": "<p>The email address for the account. This value is null if the account is associated with the administrator account through Organizations.</p>"}, "invitedAt": {"shape": "__timestampIso8601", "locationName": "invitedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when an Amazon Macie membership invitation was last sent to the account. This value is null if a Macie membership invitation hasn't been sent to the account.</p>"}, "masterAccountId": {"shape": "__string", "locationName": "masterAccountId", "documentation": "<p>(Deprecated) The Amazon Web Services account ID for the administrator account. This property has been replaced by the administratorAccountId property and is retained only for backward compatibility.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "locationName": "relationshipStatus", "documentation": "<p>The current status of the relationship between the account and the administrator account.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the account in Amazon Macie.</p>"}, "updatedAt": {"shape": "__timestampIso8601", "locationName": "updatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, of the most recent change to the status of the relationship between the account and the administrator account.</p>"}}}, "GetResourceProfileRequest": {"type": "structure", "members": {"resourceArn": {"shape": "__string", "location": "querystring", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket that the request applies to.</p>"}}, "required": ["resourceArn"]}, "GetResourceProfileResponse": {"type": "structure", "members": {"profileUpdatedAt": {"shape": "__timestampIso8601", "locationName": "profileUpdatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie most recently recalculated sensitive data discovery statistics and details for the bucket. If the bucket's sensitivity score is calculated automatically, this includes the score.</p>"}, "sensitivityScore": {"shape": "__integer", "locationName": "sensitivityScore", "documentation": "<p>The current sensitivity score for the bucket, ranging from -1 (classification error) to 100 (sensitive). By default, this score is calculated automatically based on the amount of data that <PERSON> Macie has analyzed in the bucket and the amount of sensitive data that <PERSON><PERSON> has found in the bucket.</p>"}, "sensitivityScoreOverridden": {"shape": "__boolean", "locationName": "sensitivityScoreOverridden", "documentation": "<p>Specifies whether the bucket's current sensitivity score was set manually. If this value is true, the score was manually changed to 100. If this value is false, the score was calculated automatically by Amazon Macie.</p>"}, "statistics": {"shape": "ResourceStatistics", "locationName": "statistics", "documentation": "<p>The sensitive data discovery statistics for the bucket. The statistics capture the results of automated sensitive data discovery activities that Amazon Macie has performed for the bucket.</p>"}}}, "GetRevealConfigurationRequest": {"type": "structure", "members": {}}, "GetRevealConfigurationResponse": {"type": "structure", "members": {"configuration": {"shape": "RevealConfiguration", "locationName": "configuration", "documentation": "<p>The current configuration settings and the status of the configuration for the account.</p>"}}}, "GetSensitiveDataOccurrencesAvailabilityRequest": {"type": "structure", "members": {"findingId": {"shape": "__string", "location": "uri", "locationName": "findingId", "documentation": "<p>The unique identifier for the finding.</p>"}}, "required": ["findingId"]}, "GetSensitiveDataOccurrencesAvailabilityResponse": {"type": "structure", "members": {"code": {"shape": "AvailabilityCode", "locationName": "code", "documentation": "<p>Specifies whether occurrences of sensitive data can be retrieved for the finding. Possible values are: AVAILABLE, the sensitive data can be retrieved; and, UNAVAILABLE, the sensitive data can't be retrieved. If this value is UNAVAILABLE, the reasons array indicates why the data can't be retrieved.</p>"}, "reasons": {"shape": "__listOfUnavailabilityReasonCode", "locationName": "reasons", "documentation": "<p>Specifies why occurrences of sensitive data can't be retrieved for the finding. Possible values are:</p> <ul><li><p>INVALID_CLASSIFICATION_RESULT - <PERSON> <PERSON>ie can't verify the location of the sensitive data to retrieve. There isn't a corresponding sensitive data discovery result for the finding. Or the sensitive data discovery result specified by the classificationDetails.detailedResultsLocation field of the finding isn't available, is malformed or corrupted, or uses an unsupported storage format.</p></li> <li><p>OBJECT_EXCEEDS_SIZE_QUOTA - The storage size of the affected S3 object exceeds the size quota for retrieving occurrences of sensitive data.</p></li> <li><p>OBJECT_UNAVAILABLE - The affected S3 object isn't available. The object might have been renamed, moved, or deleted. Or the object was changed after <PERSON><PERSON> created the finding.</p></li> <li><p>UNSUPPORTED_FINDING_TYPE - The specified finding isn't a sensitive data finding.</p></li> <li><p>UNSUPPORTED_OBJECT_TYPE - The affected S3 object uses a file or storage format that <PERSON><PERSON> doesn't support for retrieving occurrences of sensitive data.</p></li></ul> <p>This value is null if sensitive data can be retrieved for the finding.</p>"}}}, "GetSensitiveDataOccurrencesRequest": {"type": "structure", "members": {"findingId": {"shape": "__string", "location": "uri", "locationName": "findingId", "documentation": "<p>The unique identifier for the finding.</p>"}}, "required": ["findingId"]}, "GetSensitiveDataOccurrencesResponse": {"type": "structure", "members": {"error": {"shape": "__string", "locationName": "error", "documentation": "<p>If an error occurred when Amazon Macie attempted to retrieve occurrences of sensitive data reported by the finding, a description of the error that occurred. This value is null if the status (status) of the request is PROCESSING or SUCCESS.</p>"}, "sensitiveDataOccurrences": {"shape": "SensitiveDataOccurrences", "locationName": "sensitiveDataOccurrences", "documentation": "<p>A map that specifies 1-100 types of sensitive data reported by the finding and, for each type, 1-10 occurrences of sensitive data.</p>"}, "status": {"shape": "RevealRequestStatus", "locationName": "status", "documentation": "<p>The status of the request to retrieve occurrences of sensitive data reported by the finding. Possible values are:</p> <ul><li><p>ERROR - An error occurred when Amazon Macie attempted to locate, retrieve, or encrypt the sensitive data. The error value indicates the nature of the error that occurred.</p></li> <li><p>PROCESSING - <PERSON><PERSON> is processing the request.</p></li> <li><p>SUCCESS - <PERSON><PERSON> successfully located, retrieved, and encrypted the sensitive data.</p></li></ul>"}}}, "GetSensitivityInspectionTemplateRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}}, "required": ["id"]}, "GetSensitivityInspectionTemplateResponse": {"type": "structure", "members": {"description": {"shape": "__string", "locationName": "description", "documentation": "<p>The custom description of the template.</p>"}, "excludes": {"shape": "SensitivityInspectionTemplateExcludes", "locationName": "excludes", "documentation": " <p>The managed data identifiers that are explicitly excluded (not used) when analyzing data.</p>"}, "includes": {"shape": "SensitivityInspectionTemplateIncludes", "locationName": "includes", "documentation": "<p>The allow lists, custom data identifiers, and managed data identifiers that are included (used) when analyzing data.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the template: automated-sensitive-data-discovery.</p>"}, "sensitivityInspectionTemplateId": {"shape": "SensitivityInspectionTemplateId", "locationName": "sensitivityInspectionTemplateId", "documentation": "<p>The unique identifier for the template.</p>"}}}, "GetUsageStatisticsRequest": {"type": "structure", "members": {"filterBy": {"shape": "__listOfUsageStatisticsFilter", "locationName": "filterBy", "documentation": "<p>An array of objects, one for each condition to use to filter the query results. If you specify more than one condition, Amazon Macie uses an AND operator to join the conditions.</p>"}, "maxResults": {"shape": "__integer", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of the response.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "sortBy": {"shape": "UsageStatisticsSortBy", "locationName": "sortBy", "documentation": "<p>The criteria to use to sort the query results.</p>"}, "timeRange": {"shape": "TimeRange", "locationName": "timeRange", "documentation": "<p>The inclusive time period to query usage data for. Valid values are: MONTH_TO_DATE, for the current calendar month to date; and, PAST_30_DAYS, for the preceding 30 days. If you don't specify a value, Amazon Macie provides usage data for the preceding 30 days.</p>"}}}, "GetUsageStatisticsResponse": {"type": "structure", "members": {"nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}, "records": {"shape": "__listOfUsageRecord", "locationName": "records", "documentation": "<p>An array of objects that contains the results of the query. Each object contains the data for an account that matches the filter criteria specified in the request.</p>"}, "timeRange": {"shape": "TimeRange", "locationName": "timeRange", "documentation": "<p>The inclusive time period that the usage data applies to. Possible values are: MONTH_TO_DATE, for the current calendar month to date; and, PAST_30_DAYS, for the preceding 30 days.</p>"}}}, "GetUsageTotalsRequest": {"type": "structure", "members": {"timeRange": {"shape": "__string", "location": "querystring", "locationName": "timeRange", "documentation": "<p>The inclusive time period to retrieve the data for. Valid values are: MONTH_TO_DATE, for the current calendar month to date; and, PAST_30_DAYS, for the preceding 30 days. If you don't specify a value for this parameter, Amazon Macie provides aggregated usage data for the preceding 30 days.</p>"}}}, "GetUsageTotalsResponse": {"type": "structure", "members": {"timeRange": {"shape": "TimeRange", "locationName": "timeRange", "documentation": "<p>The inclusive time period that the usage data applies to. Possible values are: MONTH_TO_DATE, for the current calendar month to date; and, PAST_30_DAYS, for the preceding 30 days.</p>"}, "usageTotals": {"shape": "__listOfUsageTotal", "locationName": "usageTotals", "documentation": "<p>An array of objects that contains the results of the query. Each object contains the data for a specific usage metric.</p>"}}}, "GroupBy": {"type": "string", "enum": ["resourcesAffected.s3Bucket.name", "type", "classificationDetails.jobId", "severity.description"]}, "GroupCount": {"type": "structure", "members": {"count": {"shape": "__long", "locationName": "count", "documentation": "<p>The total number of findings in the group of query results.</p>"}, "groupKey": {"shape": "__string", "locationName": "groupKey", "documentation": "<p>The name of the property that defines the group in the query results, as specified by the groupBy property in the query request.</p>"}}, "documentation": "<p>Provides a group of results for a query that retrieved aggregated statistical data about findings.</p>"}, "IamUser": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that's associated with the IAM user who performed the action.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the principal that performed the action. The last section of the ARN contains the name of the user who performed the action.</p>"}, "principalId": {"shape": "__string", "locationName": "principalId", "documentation": "<p>The unique identifier for the IAM user who performed the action.</p>"}, "userName": {"shape": "__string", "locationName": "userName", "documentation": "<p>The username of the IAM user who performed the action.</p>"}}, "documentation": "<p>Provides information about an Identity and Access Management (IAM) user who performed an action on an affected resource.</p>"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred due to an unknown internal server error, exception, or failure.</p>", "exception": true, "error": {"httpStatusCode": 500}}, "Invitation": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The Amazon Web Services account ID for the account that sent the invitation.</p>"}, "invitationId": {"shape": "__string", "locationName": "invitationId", "documentation": "<p>The unique identifier for the invitation.</p>"}, "invitedAt": {"shape": "__timestampIso8601", "locationName": "invitedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the invitation was sent.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "locationName": "relationshipStatus", "documentation": "<p>The status of the relationship between the account that sent the invitation and the account that received the invitation.</p>"}}, "documentation": "<p>Provides information about an Amazon Macie membership invitation.</p>"}, "IpAddressDetails": {"type": "structure", "members": {"ipAddressV4": {"shape": "__string", "locationName": "ipAddressV4", "documentation": "<p>The Internet Protocol version 4 (IPv4) address of the device.</p>"}, "ipCity": {"shape": "IpCity", "locationName": "ipCity", "documentation": "<p>The city that the IP address originated from.</p>"}, "ipCountry": {"shape": "IpCountry", "locationName": "ipCountry", "documentation": "<p>The country that the IP address originated from.</p>"}, "ipGeoLocation": {"shape": "IpGeoLocation", "locationName": "ipGeoLocation", "documentation": "<p>The geographic coordinates of the location that the IP address originated from.</p>"}, "ipOwner": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locationName": "ip<PERSON><PERSON><PERSON>", "documentation": "<p>The registered owner of the IP address.</p>"}}, "documentation": "<p>Provides information about the IP address of the device that an entity used to perform an action on an affected resource.</p>"}, "IpCity": {"type": "structure", "members": {"name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the city.</p>"}}, "documentation": "<p>Provides information about the city that an IP address originated from.</p>"}, "IpCountry": {"type": "structure", "members": {"code": {"shape": "__string", "locationName": "code", "documentation": "<p>The two-character code, in ISO 3166-1 alpha-2 format, for the country that the IP address originated from. For example, US for the United States.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the country that the IP address originated from.</p>"}}, "documentation": "<p>Provides information about the country that an IP address originated from.</p>"}, "IpGeoLocation": {"type": "structure", "members": {"lat": {"shape": "__double", "locationName": "lat", "documentation": "<p>The latitude coordinate of the location, rounded to four decimal places.</p>"}, "lon": {"shape": "__double", "locationName": "lon", "documentation": "<p>The longitude coordinate of the location, rounded to four decimal places.</p>"}}, "documentation": "<p>Provides geographic coordinates that indicate where a specified IP address originated from.</p>"}, "IpOwner": {"type": "structure", "members": {"asn": {"shape": "__string", "locationName": "asn", "documentation": "<p>The autonomous system number (ASN) for the autonomous system that included the IP address.</p>"}, "asnOrg": {"shape": "__string", "locationName": "asnOrg", "documentation": "<p>The organization identifier that's associated with the autonomous system number (ASN) for the autonomous system that included the IP address.</p>"}, "isp": {"shape": "__string", "locationName": "isp", "documentation": "<p>The name of the internet service provider (ISP) that owned the IP address.</p>"}, "org": {"shape": "__string", "locationName": "org", "documentation": "<p>The name of the organization that owned the IP address.</p>"}}, "documentation": "<p>Provides information about the registered owner of an IP address.</p>"}, "IsDefinedInJob": {"type": "string", "enum": ["TRUE", "FALSE", "UNKNOWN"]}, "IsMonitoredByJob": {"type": "string", "enum": ["TRUE", "FALSE", "UNKNOWN"]}, "JobComparator": {"type": "string", "documentation": "<p>The operator to use in a condition. Depending on the type of condition, possible values are:</p>", "enum": ["EQ", "GT", "GTE", "LT", "LTE", "NE", "CONTAINS", "STARTS_WITH"]}, "JobDetails": {"type": "structure", "members": {"isDefinedInJob": {"shape": "IsDefinedInJob", "locationName": "isDefinedInJob", "documentation": "<p>Specifies whether any one-time or recurring jobs are configured to analyze data in the bucket. Possible values are:</p> <ul><li><p>TRUE - The bucket is explicitly included in the bucket definition (S3BucketDefinitionForJob) for one or more jobs and at least one of those jobs has a status other than CANCELLED. Or the bucket matched the bucket criteria (S3BucketCriteriaForJob) for at least one job that previously ran.</p></li> <li><p>FALSE - The bucket isn't explicitly included in the bucket definition (S3BucketDefinitionForJob) for any jobs, all the jobs that explicitly include the bucket in their bucket definitions have a status of CANCELLED, or the bucket didn't match the bucket criteria (S3BucketCriteriaForJob) for any jobs that previously ran.</p></li> <li><p>UNKNOWN - An exception occurred when Amazon Macie attempted to retrieve job data for the bucket.</p></li></ul>"}, "isMonitoredByJob": {"shape": "IsMonitoredByJob", "locationName": "isMonitoredByJob", "documentation": "<p>Specifies whether any recurring jobs are configured to analyze data in the bucket. Possible values are:</p> <ul><li><p>TRUE - The bucket is explicitly included in the bucket definition (S3BucketDefinitionForJob) for one or more recurring jobs or the bucket matches the bucket criteria (S3BucketCriteriaForJob) for one or more recurring jobs. At least one of those jobs has a status other than CANCELLED.</p></li> <li><p>FALSE - The bucket isn't explicitly included in the bucket definition (S3BucketDefinitionForJob) for any recurring jobs, the bucket doesn't match the bucket criteria (S3BucketCriteriaForJob) for any recurring jobs, or all the recurring jobs that are configured to analyze data in the bucket have a status of CANCELLED.</p></li> <li><p>UNKNOWN - An exception occurred when Amazon Macie attempted to retrieve job data for the bucket.</p></li></ul>"}, "lastJobId": {"shape": "__string", "locationName": "lastJobId", "documentation": "<p>The unique identifier for the job that ran most recently and is configured to analyze data in the bucket, either the latest run of a recurring job or the only run of a one-time job.</p> <p>This value is typically null if the value for the isDefinedInJob property is FALSE or UNKNOWN.</p>"}, "lastJobRunTime": {"shape": "__timestampIso8601", "locationName": "lastJobRunTime", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the job (lastJobId) started. If the job is a recurring job, this value indicates when the most recent run started.</p> <p>This value is typically null if the value for the isDefinedInJob property is FALSE or UNKNOWN.</p>"}}, "documentation": "<p>Specifies whether any one-time or recurring classification jobs are configured to analyze data in an S3 bucket, and, if so, the details of the job that ran most recently.</p>"}, "JobScheduleFrequency": {"type": "structure", "members": {"dailySchedule": {"shape": "DailySchedule", "locationName": "dailySchedule", "documentation": "<p>Specifies a daily recurrence pattern for running the job.</p>"}, "monthlySchedule": {"shape": "MonthlySchedule", "locationName": "monthlySchedule", "documentation": "<p>Specifies a monthly recurrence pattern for running the job.</p>"}, "weeklySchedule": {"shape": "WeeklySchedule", "locationName": "weeklySchedule", "documentation": "<p>Specifies a weekly recurrence pattern for running the job.</p>"}}, "documentation": "<p>Specifies the recurrence pattern for running a classification job.</p>"}, "JobScopeTerm": {"type": "structure", "members": {"simpleScopeTerm": {"shape": "SimpleScopeTerm", "locationName": "simpleScopeTerm", "documentation": "<p>A property-based condition that defines a property, operator, and one or more values for including or excluding objects from the job.</p>"}, "tagScopeTerm": {"shape": "TagScopeTerm", "locationName": "tagScopeTerm", "documentation": "<p>A tag-based condition that defines the operator and tag keys or tag key and value pairs for including or excluding objects from the job.</p>"}}, "documentation": "<p>Specifies a property- or tag-based condition that defines criteria for including or excluding S3 objects from a classification job. A JobScopeTerm object can contain only one simpleScopeTerm object or one tagScopeTerm object.</p>"}, "JobScopingBlock": {"type": "structure", "members": {"and": {"shape": "__listOfJobScopeTerm", "locationName": "and", "documentation": "<p>An array of conditions, one for each property- or tag-based condition that determines which objects to include or exclude from the job. If you specify more than one condition, <PERSON> Macie uses AND logic to join the conditions.</p>"}}, "documentation": "<p>Specifies one or more property- and tag-based conditions that define criteria for including or excluding S3 objects from a classification job.</p>"}, "JobStatus": {"type": "string", "documentation": "<p>The status of a classification job. Possible values are:</p>", "enum": ["RUNNING", "PAUSED", "CANCELLED", "COMPLETE", "IDLE", "USER_PAUSED"]}, "JobSummary": {"type": "structure", "members": {"bucketCriteria": {"shape": "S3BucketCriteriaForJob", "locationName": "bucketCriteria", "documentation": "<p>The property- and tag-based conditions that determine which S3 buckets are included or excluded from the job's analysis. Each time the job runs, the job uses these criteria to determine which buckets to analyze. A job's definition can contain a bucketCriteria object or a bucketDefinitions array, not both.</p>"}, "bucketDefinitions": {"shape": "__listOfS3BucketDefinitionForJob", "locationName": "bucketDefinitions", "documentation": "<p>An array of objects, one for each Amazon Web Services account that owns specific S3 buckets for the job to analyze. Each object specifies the account ID for an account and one or more buckets to analyze for that account. A job's definition can contain a bucketDefinitions array or a bucketCriteria object, not both.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the job was created.</p>"}, "jobId": {"shape": "__string", "locationName": "jobId", "documentation": "<p>The unique identifier for the job.</p>"}, "jobStatus": {"shape": "JobStatus", "locationName": "jobStatus", "documentation": "<p>The current status of the job. Possible values are:</p> <ul><li><p>CANCELLED - You cancelled the job or, if it's a one-time job, you paused the job and didn't resume it within 30 days.</p></li> <li><p>COMPLETE - For a one-time job, <PERSON> Macie finished processing the data specified for the job. This value doesn't apply to recurring jobs.</p></li> <li><p>IDLE - For a recurring job, the previous scheduled run is complete and the next scheduled run is pending. This value doesn't apply to one-time jobs.</p></li> <li><p>PAUSED - <PERSON><PERSON> started running the job but additional processing would exceed the monthly sensitive data discovery quota for your account or one or more member accounts that the job analyzes data for.</p></li> <li><p>RUNNING - For a one-time job, the job is in progress. For a recurring job, a scheduled run is in progress.</p></li> <li><p>USER_PAUSED - You paused the job. If you paused the job while it had a status of RUNNING and you don't resume it within 30 days of pausing it, the job or job run will expire and be cancelled, depending on the job's type. To check the expiration date, refer to the UserPausedDetails.jobExpiresAt property.</p></li></ul>"}, "jobType": {"shape": "JobType", "locationName": "jobType", "documentation": "<p>The schedule for running the job. Possible values are:</p> <ul><li><p>ONE_TIME - The job runs only once.</p></li> <li><p>SCHEDULED - The job runs on a daily, weekly, or monthly basis.</p></li></ul>"}, "lastRunErrorStatus": {"shape": "LastRunErrorStatus", "locationName": "lastRunErrorStatus", "documentation": "<p>Specifies whether any account- or bucket-level access errors occurred when the job ran. For a recurring job, this value indicates the error status of the job's most recent run.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The custom name of the job.</p>"}, "userPausedDetails": {"shape": "UserPausedDetails", "locationName": "userPausedDetails", "documentation": "<p>If the current status of the job is USER_PAUSED, specifies when the job was paused and when the job or job run will expire and be cancelled if it isn't resumed. This value is present only if the value for jobStatus is USER_PAUSED.</p>"}}, "documentation": "<p>Provides information about a classification job, including the current status of the job.</p>"}, "JobType": {"type": "string", "documentation": "<p>The schedule for running a classification job. Valid values are:</p>", "enum": ["ONE_TIME", "SCHEDULED"]}, "KeyValuePair": {"type": "structure", "members": {"key": {"shape": "__string", "locationName": "key", "documentation": "<p>One part of a key-value pair that comprises a tag. A tag key is a general label that acts as a category for more specific tag values.</p>"}, "value": {"shape": "__string", "locationName": "value", "documentation": "<p>One part of a key-value pair that comprises a tag. A tag value acts as a descriptor for a tag key. A tag value can be an empty string.</p>"}}, "documentation": "<p>Provides information about the tags that are associated with an S3 bucket or object. Each tag consists of a required tag key and an associated tag value.</p>"}, "KeyValuePairList": {"type": "list", "documentation": "<p>Provides information about the tags that are associated with an S3 bucket or object. Each tag consists of a required tag key and an associated tag value.</p>", "member": {"shape": "KeyValuePair"}}, "LastRunErrorStatus": {"type": "structure", "members": {"code": {"shape": "LastRunErrorStatusCode", "locationName": "code", "documentation": "<p>Specifies whether any account- or bucket-level access errors occurred when the job ran. For a recurring job, this value indicates the error status of the job's most recent run. Possible values are:</p> <ul><li><p>ERROR - One or more errors occurred. Amazon Macie didn't process all the data specified for the job.</p></li> <li><p>NONE - No errors occurred. <PERSON><PERSON> processed all the data specified for the job.</p></li></ul>"}}, "documentation": "<p>Specifies whether any account- or bucket-level access errors occurred when a classification job ran. For information about using logging data to investigate these errors, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/discovery-jobs-monitor-cw-logs.html\">Monitoring sensitive data discovery jobs</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "LastRunErrorStatusCode": {"type": "string", "documentation": "<p>Specifies whether any account- or bucket-level access errors occurred during the run of a one-time classification job or the most recent run of a recurring classification job. Possible values are:</p>", "enum": ["NONE", "ERROR"]}, "ListAllowListsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListAllowListsResponse": {"type": "structure", "members": {"allowLists": {"shape": "__listOfAllowListSummary", "locationName": "allowLists", "documentation": "<p>An array of objects, one for each allow list.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListClassificationJobsRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "ListJobsFilterCriteria", "locationName": "filterCriteria", "documentation": "<p>The criteria to use to filter the results.</p>"}, "maxResults": {"shape": "__integer", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of the response.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "sortCriteria": {"shape": "ListJobsSortCriteria", "locationName": "sortCriteria", "documentation": "<p>The criteria to use to sort the results.</p>"}}}, "ListClassificationJobsResponse": {"type": "structure", "members": {"items": {"shape": "__listOfJobSummary", "locationName": "items", "documentation": "<p>An array of objects, one for each job that matches the filter criteria specified in the request.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListClassificationScopesRequest": {"type": "structure", "members": {"name": {"shape": "__string", "location": "querystring", "locationName": "name", "documentation": "<p>The name of the classification scope to retrieve the unique identifier for.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListClassificationScopesResponse": {"type": "structure", "members": {"classificationScopes": {"shape": "__listOfClassificationScopeSummary", "locationName": "classificationScopes", "documentation": "<p>An array that specifies the unique identifier and name of the classification scope for the account.</p>"}, "nextToken": {"shape": "NextToken", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListCustomDataIdentifiersRequest": {"type": "structure", "members": {"maxResults": {"shape": "__integer", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of the response.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListCustomDataIdentifiersResponse": {"type": "structure", "members": {"items": {"shape": "__listOfCustomDataIdentifierSummary", "locationName": "items", "documentation": "<p>An array of objects, one for each custom data identifier.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListFindingsFiltersRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListFindingsFiltersResponse": {"type": "structure", "members": {"findingsFilterListItems": {"shape": "__listOfFindingsFilterListItem", "locationName": "findingsFilterListItems", "documentation": "<p>An array of objects, one for each filter that's associated with the account.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListFindingsRequest": {"type": "structure", "members": {"findingCriteria": {"shape": "FindingCriteria", "locationName": "findingCriteria", "documentation": "<p>The criteria to use to filter the results.</p>"}, "maxResults": {"shape": "__integer", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of the response.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "sortCriteria": {"shape": "SortCriteria", "locationName": "sortCriteria", "documentation": "<p>The criteria to use to sort the results.</p>"}}}, "ListFindingsResponse": {"type": "structure", "members": {"findingIds": {"shape": "__listOf__string", "locationName": "findingIds", "documentation": "<p>An array of strings, where each string is the unique identifier for a finding that matches the filter criteria specified in the request.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListInvitationsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListInvitationsResponse": {"type": "structure", "members": {"invitations": {"shape": "__listOfInvitation", "locationName": "invitations", "documentation": "<p>An array of objects, one for each invitation that was received by the account.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListJobsFilterCriteria": {"type": "structure", "members": {"excludes": {"shape": "__listOfListJobsFilterTerm", "locationName": "excludes", "documentation": "<p>An array of objects, one for each condition that determines which jobs to exclude from the results.</p>"}, "includes": {"shape": "__listOfListJobsFilterTerm", "locationName": "includes", "documentation": "<p>An array of objects, one for each condition that determines which jobs to include in the results.</p>"}}, "documentation": "<p>Specifies criteria for filtering the results of a request for information about classification jobs.</p>"}, "ListJobsFilterKey": {"type": "string", "documentation": "<p>The property to use to filter the results. Valid values are:</p>", "enum": ["jobType", "jobStatus", "createdAt", "name"]}, "ListJobsFilterTerm": {"type": "structure", "members": {"comparator": {"shape": "JobComparator", "locationName": "comparator", "documentation": "<p>The operator to use to filter the results.</p>"}, "key": {"shape": "ListJobs<PERSON>ilter<PERSON>ey", "locationName": "key", "documentation": "<p>The property to use to filter the results.</p>"}, "values": {"shape": "__listOf__string", "locationName": "values", "documentation": "<p>An array that lists one or more values to use to filter the results.</p>"}}, "documentation": "<p>Specifies a condition that filters the results of a request for information about classification jobs. Each condition consists of a property, an operator, and one or more values.</p>"}, "ListJobsSortAttributeName": {"type": "string", "documentation": "<p>The property to sort the results by. Valid values are:</p>", "enum": ["createdAt", "jobStatus", "name", "jobType"]}, "ListJobsSortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "ListJobsSortAttributeName", "locationName": "attributeName", "documentation": "<p>The property to sort the results by.</p>"}, "orderBy": {"shape": "OrderBy", "locationName": "orderBy", "documentation": "<p>The sort order to apply to the results, based on the value for the property specified by the attributeName property. Valid values are: ASC, sort the results in ascending order; and, DESC, sort the results in descending order.</p>"}}, "documentation": "<p>Specifies criteria for sorting the results of a request for information about classification jobs.</p>"}, "ListManagedDataIdentifiersRequest": {"type": "structure", "members": {"nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListManagedDataIdentifiersResponse": {"type": "structure", "members": {"items": {"shape": "__listOfManagedDataIdentifierSummary", "locationName": "items", "documentation": "<p>An array of objects, one for each managed data identifier.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListMembersRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "onlyAssociated": {"shape": "__string", "location": "querystring", "locationName": "onlyAssociated", "documentation": "<p>Specifies which accounts to include in the response, based on the status of an account's relationship with the administrator account. By default, the response includes only current member accounts. To include all accounts, set this value to false.</p>"}}}, "ListMembersResponse": {"type": "structure", "members": {"members": {"shape": "__listOfMember", "locationName": "members", "documentation": "<p>An array of objects, one for each account that's associated with the administrator account and matches the criteria specified in the request.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListOrganizationAdminAccountsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListOrganizationAdminAccountsResponse": {"type": "structure", "members": {"adminAccounts": {"shape": "__listOfAdminAccount", "locationName": "adminAccounts", "documentation": "<p>An array of objects, one for each delegated Amazon Macie administrator account for the organization. Only one of these accounts can have a status of ENABLED.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListResourceProfileArtifactsRequest": {"type": "structure", "members": {"nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "resourceArn": {"shape": "__string", "location": "querystring", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket that the request applies to.</p>"}}, "required": ["resourceArn"]}, "ListResourceProfileArtifactsResponse": {"type": "structure", "members": {"artifacts": {"shape": "__listOfResourceProfileArtifact", "locationName": "artifacts", "documentation": "<p>An array of objects, one for each of 1-100 S3 objects that <PERSON> Macie selected for analysis.</p> <p>If <PERSON><PERSON> has analyzed more than 100 objects in the bucket, <PERSON><PERSON> populates the array based on the value for the ResourceProfileArtifact.sensitive field for an object: true (sensitive), followed by false (not sensitive). <PERSON><PERSON> then populates any remaining items in the array with information about objects where the value for the ResourceProfileArtifact.classificationResultStatus field is SKIPPED.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListResourceProfileDetectionsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "resourceArn": {"shape": "__string", "location": "querystring", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket that the request applies to.</p>"}}, "required": ["resourceArn"]}, "ListResourceProfileDetectionsResponse": {"type": "structure", "members": {"detections": {"shape": "__listOfDetection", "locationName": "detections", "documentation": "<p>An array of objects, one for each type of sensitive data that Amazon Macie found in the bucket. Each object reports the number of occurrences of the specified type and provides information about the custom data identifier or managed data identifier that detected the data.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "ListSensitivityInspectionTemplatesRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of a paginated response.</p>"}, "nextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}}}, "ListSensitivityInspectionTemplatesResponse": {"type": "structure", "members": {"nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}, "sensitivityInspectionTemplates": {"shape": "__listOfSensitivityInspectionTemplatesEntry", "locationName": "sensitivityInspectionTemplates", "documentation": "<p>An array that specifies the unique identifier and name of the sensitivity inspection template for the account.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "members": {"resourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}, "required": ["resourceArn"]}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the resource.</p>"}}}, "MacieStatus": {"type": "string", "documentation": "<p>The status of an Amazon Macie account. Valid values are:</p>", "enum": ["PAUSED", "ENABLED"]}, "ManagedDataIdentifierSelector": {"type": "string", "documentation": "<p>The selection type that determines which managed data identifiers a classification job uses to analyze data. Valid values are:</p>", "enum": ["ALL", "EXCLUDE", "INCLUDE", "NONE", "RECOMMENDED"]}, "ManagedDataIdentifierSummary": {"type": "structure", "members": {"category": {"shape": "SensitiveDataItemCategory", "locationName": "category", "documentation": "<p>The category of sensitive data that the managed data identifier detects: CREDENTIALS, for credentials data such as private keys or Amazon Web Services secret access keys; FINANCIAL_INFORMATION, for financial data such as credit card numbers; or, PERSONAL_INFORMATION, for personal health information, such as health insurance identification numbers, or personally identifiable information, such as passport numbers.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the managed data identifier. This is a string that describes the type of sensitive data that the managed data identifier detects. For example: OPENSSH_PRIVATE_KEY for OpenSSH private keys, CREDIT_CARD_NUMBER for credit card numbers, or USA_PASSPORT_NUMBER for US passport numbers.</p>"}}, "documentation": "<p>Provides information about a managed data identifier. For additional information, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/managed-data-identifiers.html\">Using managed data identifiers</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "MatchingBucket": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that owns the bucket.</p>"}, "bucketName": {"shape": "__string", "locationName": "bucketName", "documentation": "<p>The name of the bucket.</p>"}, "classifiableObjectCount": {"shape": "__long", "locationName": "classifiableObjectCount", "documentation": "<p>The total number of objects that Amazon Macie can analyze in the bucket. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p>"}, "classifiableSizeInBytes": {"shape": "__long", "locationName": "classifiableSizeInBytes", "documentation": "<p>The total storage size, in bytes, of the objects that Amazon Macie can analyze in the bucket. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p> <p>If versioning is enabled for the bucket, <PERSON><PERSON> calculates this value based on the size of the latest version of each applicable object in the bucket. This value doesn't reflect the storage size of all versions of each applicable object in the bucket.</p>"}, "errorCode": {"shape": "BucketMetadataErrorCode", "locationName": "errorCode", "documentation": "<p>The error code for an error that prevented Amazon Macie from retrieving and processing information about the bucket and the bucket's objects. If this value is ACCESS_DENIED, <PERSON><PERSON> doesn't have permission to retrieve the information. For example, the bucket has a restrictive bucket policy and Amazon S3 denied the request. If this value is null, <PERSON><PERSON> was able to retrieve and process the information.</p>"}, "errorMessage": {"shape": "__string", "locationName": "errorMessage", "documentation": "<p>A brief description of the error (errorCode) that prevented Amazon Macie from retrieving and processing information about the bucket and the bucket's objects. This value is null if <PERSON><PERSON> was able to retrieve and process the information.</p>"}, "jobDetails": {"shape": "JobDetails", "locationName": "jobDetails", "documentation": "<p>Specifies whether any one-time or recurring classification jobs are configured to analyze objects in the bucket, and, if so, the details of the job that ran most recently.</p>"}, "lastAutomatedDiscoveryTime": {"shape": "__timestampIso8601", "locationName": "lastAutomatedDiscoveryTime", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when Amazon Macie most recently analyzed data in the bucket while performing automated sensitive data discovery for your account. This value is null if automated sensitive data discovery is currently disabled for your account.</p>"}, "objectCount": {"shape": "__long", "locationName": "objectCount", "documentation": "<p>The total number of objects in the bucket.</p>"}, "objectCountByEncryptionType": {"shape": "ObjectCountByEncryptionType", "locationName": "objectCountByEncryptionType", "documentation": "<p>The total number of objects in the bucket, grouped by server-side encryption type. This includes a grouping that reports the total number of objects that aren't encrypted or use client-side encryption.</p>"}, "sensitivityScore": {"shape": "__integer", "locationName": "sensitivityScore", "documentation": "<p>The current sensitivity score for the bucket, ranging from -1 (classification error) to 100 (sensitive). This value is null if automated sensitive data discovery is currently disabled for your account.</p>"}, "sizeInBytes": {"shape": "__long", "locationName": "sizeInBytes", "documentation": "<p>The total storage size, in bytes, of the bucket.</p> <p>If versioning is enabled for the bucket, Amazon Macie calculates this value based on the size of the latest version of each object in the bucket. This value doesn't reflect the storage size of all versions of each object in the bucket.</p>"}, "sizeInBytesCompressed": {"shape": "__long", "locationName": "sizeInBytesCompressed", "documentation": "<p>The total storage size, in bytes, of the objects that are compressed (.gz, .gzip, .zip) files in the bucket.</p> <p>If versioning is enabled for the bucket, Amazon Macie calculates this value based on the size of the latest version of each applicable object in the bucket. This value doesn't reflect the storage size of all versions of each applicable object in the bucket.</p>"}, "unclassifiableObjectCount": {"shape": "ObjectLevelStatistics", "locationName": "unclassifiableObjectCount", "documentation": "<p>The total number of objects that <PERSON> Macie can't analyze in the bucket. These objects don't use a supported storage class or don't have a file name extension for a supported file or storage format.</p>"}, "unclassifiableObjectSizeInBytes": {"shape": "ObjectLevelStatistics", "locationName": "unclassifiableObjectSizeInBytes", "documentation": "<p>The total storage size, in bytes, of the objects that Amazon Macie can't analyze in the bucket. These objects don't use a supported storage class or don't have a file name extension for a supported file or storage format.</p>"}}, "documentation": "<p>Provides statistical data and other information about an S3 bucket that Amazon Macie monitors and analyzes for your account. By default, object count and storage size values include data for object parts that are the result of incomplete multipart uploads. For more information, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/monitoring-s3-how-it-works.html\">How <PERSON><PERSON> monitors Amazon S3 data security</a> in the <i>Amazon Macie User Guide</i>.</p> <p>If an error occurs when <PERSON><PERSON> attempts to retrieve and process information about the bucket or the bucket's objects, the value for most of these properties is null. Key exceptions are accountId and bucketName. To identify the cause of the error, refer to the errorCode and errorMessage values.</p>"}, "MatchingResource": {"type": "structure", "members": {"matchingBucket": {"shape": "MatchingBucket", "locationName": "matchingBucket", "documentation": "<p>The details of an S3 bucket that Amazon Macie monitors and analyzes.</p>"}}, "documentation": "<p>Provides statistical data and other information about an Amazon Web Services resource that Amazon Macie monitors and analyzes for your account.</p>"}, "MaxResults": {"type": "integer", "min": 1, "max": 25}, "Member": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The Amazon Web Services account ID for the account.</p>"}, "administratorAccountId": {"shape": "__string", "locationName": "administratorAccountId", "documentation": "<p>The Amazon Web Services account ID for the administrator account.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the account.</p>"}, "email": {"shape": "__string", "locationName": "email", "documentation": "<p>The email address for the account. This value is null if the account is associated with the administrator account through Organizations.</p>"}, "invitedAt": {"shape": "__timestampIso8601", "locationName": "invitedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when an Amazon Macie membership invitation was last sent to the account. This value is null if a Macie membership invitation hasn't been sent to the account.</p>"}, "masterAccountId": {"shape": "__string", "locationName": "masterAccountId", "documentation": "<p>(Deprecated) The Amazon Web Services account ID for the administrator account. This property has been replaced by the administratorAccountId property and is retained only for backward compatibility.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "locationName": "relationshipStatus", "documentation": "<p>The current status of the relationship between the account and the administrator account.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies which tags (keys and values) are associated with the account in Amazon Macie.</p>"}, "updatedAt": {"shape": "__timestampIso8601", "locationName": "updatedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, of the most recent change to the status of the relationship between the account and the administrator account.</p>"}}, "documentation": "<p>Provides information about an account that's associated with an Amazon Macie administrator account.</p>"}, "MonthlySchedule": {"type": "structure", "members": {"dayOfMonth": {"shape": "__integer", "locationName": "dayOfMonth", "documentation": "<p>The numeric day of the month when <PERSON> Macie runs the job. This value can be an integer from 1 through 31.</p> <p>If this value exceeds the number of days in a certain month, <PERSON><PERSON> doesn't run the job that month. <PERSON><PERSON> runs the job only during months that have the specified day. For example, if this value is 31 and a month has only 30 days, <PERSON><PERSON> doesn't run the job that month. To run the job every month, specify a value that's less than 29.</p>"}}, "documentation": "<p>Specifies a monthly recurrence pattern for running a classification job.</p>"}, "NextToken": {"type": "string", "documentation": "<p>Specifies which page of results to return in a paginated response.</p>", "pattern": "^.*$"}, "ObjectCountByEncryptionType": {"type": "structure", "members": {"customerManaged": {"shape": "__long", "locationName": "customerManaged", "documentation": "<p>The total number of objects that are encrypted with a customer-provided key. The objects use customer-provided server-side encryption (SSE-C).</p>"}, "kmsManaged": {"shape": "__long", "locationName": "kmsManaged", "documentation": "<p>The total number of objects that are encrypted with an KMS key, either an Amazon Web Services managed key or a customer managed key. The objects use KMS encryption (SSE-KMS).</p>"}, "s3Managed": {"shape": "__long", "locationName": "s3Managed", "documentation": "<p>The total number of objects that are encrypted with an Amazon S3 managed key. The objects use Amazon S3 managed encryption (SSE-S3).</p>"}, "unencrypted": {"shape": "__long", "locationName": "unencrypted", "documentation": "<p>The total number of objects that use client-side encryption or aren't encrypted.</p>"}, "unknown": {"shape": "__long", "locationName": "unknown", "documentation": "<p>The total number of objects that Amazon Macie doesn't have current encryption metadata for. <PERSON><PERSON> can't provide current data about the encryption settings for these objects.</p>"}}, "documentation": "<p>Provides information about the number of objects that are in an S3 bucket and use certain types of server-side encryption, use client-side encryption, or aren't encrypted.</p>"}, "ObjectLevelStatistics": {"type": "structure", "members": {"fileType": {"shape": "__long", "locationName": "fileType", "documentation": "<p>The total storage size (in bytes) or number of objects that Amazon Macie can't analyze because the objects don't have a file name extension for a supported file or storage format.</p>"}, "storageClass": {"shape": "__long", "locationName": "storageClass", "documentation": "<p>The total storage size (in bytes) or number of objects that Amazon Macie can't analyze because the objects use an unsupported storage class.</p>"}, "total": {"shape": "__long", "locationName": "total", "documentation": "<p>The total storage size (in bytes) or number of objects that Amazon Macie can't analyze because the objects use an unsupported storage class or don't have a file name extension for a supported file or storage format.</p>"}}, "documentation": "<p>Provides information about the total storage size (in bytes) or number of objects that Amazon Macie can't analyze in one or more S3 buckets. In a BucketMetadata or MatchingBucket object, this data is for a specific bucket. In a GetBucketStatisticsResponse object, this data is aggregated for all the buckets in the query results. If versioning is enabled for a bucket, storage size values are based on the size of the latest version of each applicable object in the bucket.</p>"}, "Occurrences": {"type": "structure", "members": {"cells": {"shape": "Cells", "locationName": "cells", "documentation": "<p>An array of objects, one for each occurrence of sensitive data in a Microsoft Excel workbook, CSV file, or TSV file. This value is null for all other types of files.</p> <p>Each Cell object specifies a cell or field that contains the sensitive data.</p>"}, "lineRanges": {"shape": "Ranges", "locationName": "lineRanges", "documentation": "<p>An array of objects, one for each occurrence of sensitive data in an email message or a non-binary text file such as an HTML, TXT, or XML file. Each Range object specifies a line or inclusive range of lines that contains the sensitive data, and the position of the data on the specified line or lines.</p> <p>This value is often null for file types that are supported by Cell, Page, or Record objects. Exceptions are the location of sensitive data in: unstructured sections of an otherwise structured file, such as a comment in a file; a malformed file that Amazon Macie analyzes as plain text; and, a CSV or TSV file that has any column names that contain sensitive data.</p>"}, "offsetRanges": {"shape": "Ranges", "locationName": "offsetRanges", "documentation": " <p>Reserved for future use.</p>"}, "pages": {"shape": "Pages", "locationName": "pages", "documentation": "<p>An array of objects, one for each occurrence of sensitive data in an Adobe Portable Document Format file. This value is null for all other types of files.</p> <p>Each Page object specifies a page that contains the sensitive data.</p>"}, "records": {"shape": "Records", "locationName": "records", "documentation": "<p>An array of objects, one for each occurrence of sensitive data in an Apache Avro object container, Apache Parquet file, JSON file, or JSON Lines file. This value is null for all other types of files.</p> <p>For an Avro object container or Parquet file, each Record object specifies a record index and the path to a field in a record that contains the sensitive data. For a JSON or JSON Lines file, each Record object specifies the path to a field or array that contains the sensitive data. For a JSON Lines file, it also specifies the index of the line that contains the data.</p>"}}, "documentation": "<p>Specifies the location of 1-15 occurrences of sensitive data that was detected by a managed data identifier or a custom data identifier and produced a sensitive data finding.</p>"}, "OrderBy": {"type": "string", "enum": ["ASC", "DESC"]}, "OriginType": {"type": "string", "documentation": "<p>Specifies how Amazon Macie found the sensitive data that produced a finding. Possible values are:</p>", "enum": ["SENSITIVE_DATA_DISCOVERY_JOB", "AUTOMATED_SENSITIVE_DATA_DISCOVERY"]}, "Page": {"type": "structure", "members": {"lineRange": {"shape": "Range", "locationName": "lineRange", "documentation": " <p>Reserved for future use.</p>"}, "offsetRange": {"shape": "Range", "locationName": "offsetRange", "documentation": " <p>Reserved for future use.</p>"}, "pageNumber": {"shape": "__long", "locationName": "pageNumber", "documentation": "<p>The page number of the page that contains the sensitive data.</p>"}}, "documentation": "<p>Specifies the location of an occurrence of sensitive data in an Adobe Portable Document Format file.</p>"}, "Pages": {"type": "list", "documentation": "<p>Specifies the location of occurrences of sensitive data in an Adobe Portable Document Format file.</p>", "member": {"shape": "Page"}}, "PolicyDetails": {"type": "structure", "members": {"action": {"shape": "FindingAction", "locationName": "action", "documentation": "<p>The action that produced the finding.</p>"}, "actor": {"shape": "FindingActor", "locationName": "actor", "documentation": "<p>The entity that performed the action that produced the finding.</p>"}}, "documentation": "<p>Provides the details of a policy finding.</p>"}, "PutClassificationExportConfigurationRequest": {"type": "structure", "members": {"configuration": {"shape": "ClassificationExportConfiguration", "locationName": "configuration", "documentation": "<p>The location to store data classification results in, and the encryption settings to use when storing results in that location.</p>"}}, "required": ["configuration"]}, "PutClassificationExportConfigurationResponse": {"type": "structure", "members": {"configuration": {"shape": "ClassificationExportConfiguration", "locationName": "configuration", "documentation": "<p>The location where the data classification results are stored, and the encryption settings that are used when storing results in that location.</p>"}}}, "PutFindingsPublicationConfigurationRequest": {"type": "structure", "members": {"clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "securityHubConfiguration": {"shape": "SecurityHubConfiguration", "locationName": "securityHubConfiguration", "documentation": "<p>The configuration settings that determine which findings to publish to Security Hub.</p>"}}}, "PutFindingsPublicationConfigurationResponse": {"type": "structure", "members": {}}, "Range": {"type": "structure", "members": {"end": {"shape": "__long", "locationName": "end", "documentation": "<p>The number of lines from the beginning of the file to the end of the sensitive data.</p> "}, "start": {"shape": "__long", "locationName": "start", "documentation": "<p>The number of lines from the beginning of the file to the beginning of the sensitive data.</p> "}, "startColumn": {"shape": "__long", "locationName": "startColumn", "documentation": "<p>The number of characters, with spaces and starting from 1, from the beginning of the first line that contains the sensitive data (start) to the beginning of the sensitive data.</p>"}}, "documentation": "<p>Specifies the location of an occurrence of sensitive data in an email message or a non-binary text file such as an HTML, TXT, or XML file.</p>"}, "Ranges": {"type": "list", "documentation": "<p>Specifies the locations of occurrences of sensitive data in a non-binary text file.</p>", "member": {"shape": "Range"}}, "Record": {"type": "structure", "members": {"jsonPath": {"shape": "__string", "locationName": "jsonPath", "documentation": "<p>The path, as a JSONPath expression, to the sensitive data. For an Avro object container or Parquet file, this is the path to the field in the record (recordIndex) that contains the data. For a JSON or JSON Lines file, this is the path to the field or array that contains the data. If the data is a value in an array, the path also indicates which value contains the data.</p> <p>If Amazon Macie detects sensitive data in the name of any element in the path, <PERSON><PERSON> omits this field. If the name of an element exceeds 20 characters, <PERSON><PERSON> truncates the name by removing characters from the beginning of the name. If the resulting full path exceeds 250 characters, <PERSON><PERSON> also truncates the path, starting with the first element in the path, until the path contains 250 or fewer characters.</p>"}, "recordIndex": {"shape": "__long", "locationName": "recordIndex", "documentation": "<p>For an Avro object container or Parquet file, the record index, starting from 0, for the record that contains the sensitive data. For a JSON Lines file, the line index, starting from 0, for the line that contains the sensitive data. This value is always 0 for JSON files.</p>"}}, "documentation": "<p>Specifies the location of an occurrence of sensitive data in an Apache Avro object container, Apache Parquet file, JSON file, or JSON Lines file.</p>"}, "Records": {"type": "list", "documentation": "<p>Specifies the locations of occurrences of sensitive data in an Apache Avro object container or a structured data file.</p>", "member": {"shape": "Record"}}, "RelationshipStatus": {"type": "string", "documentation": "<p>The current status of the relationship between an account and an associated Amazon Macie administrator account. Possible values are:</p>", "enum": ["Enabled", "Paused", "Invited", "Created", "Removed", "Resigned", "EmailVerificationInProgress", "EmailVerificationFailed", "RegionDisabled", "AccountSuspended"]}, "ReplicationDetails": {"type": "structure", "members": {"replicated": {"shape": "__boolean", "locationName": "replicated", "documentation": "<p>Specifies whether the bucket is configured to replicate one or more objects to any destination.</p>"}, "replicatedExternally": {"shape": "__boolean", "locationName": "replicatedExternally", "documentation": "<p>Specifies whether the bucket is configured to replicate one or more objects to a bucket for an Amazon Web Services account that isn't part of your Amazon Macie organization. An <i>Amazon Macie organization</i> is a set of Macie accounts that are centrally managed as a group of related accounts through Organizations or by Macie invitation.</p>"}, "replicationAccounts": {"shape": "__listOf__string", "locationName": "replicationAccounts", "documentation": "<p>An array of Amazon Web Services account IDs, one for each Amazon Web Services account that owns a bucket that the bucket is configured to replicate one or more objects to.</p>"}}, "documentation": "<p>Provides information about settings that define whether one or more objects in an S3 bucket are replicated to S3 buckets for other Amazon Web Services accounts and, if so, which accounts.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred because a specified resource wasn't found.</p>", "exception": true, "error": {"httpStatusCode": 404}}, "ResourceProfileArtifact": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the object.</p>"}, "classificationResultStatus": {"shape": "__string", "locationName": "classificationResultStatus", "documentation": "<p>The status of the analysis. Possible values are:</p> <ul><li><p>COMPLETE - Amazon Macie successfully completed its analysis of the object.</p></li> <li><p>PARTIAL - <PERSON><PERSON> analyzed only a subset of data in the object. For example, the object is an archive file that contains files in an unsupported format.</p></li> <li><p>SKIPPED - <PERSON><PERSON> wasn't able to analyze the object. For example, the object is a malformed file.</p></li></ul>"}, "sensitive": {"shape": "__boolean", "locationName": "sensitive", "documentation": "<p>Specifies whether Amazon Macie found sensitive data in the object.</p>"}}, "documentation": "<p>Provides information about an S3 object that Amazon Macie selected for analysis while performing automated sensitive data discovery for an S3 bucket, and the status and results of the analysis. This information is available only if automated sensitive data discovery is currently enabled for your account.</p>", "required": ["classificationResultStatus", "arn"]}, "ResourceStatistics": {"type": "structure", "members": {"totalBytesClassified": {"shape": "__long", "locationName": "totalBytesClassified", "documentation": "<p>The total amount of data, in bytes, that Amazon Macie has analyzed in the bucket.</p>"}, "totalDetections": {"shape": "__long", "locationName": "totalDetections", "documentation": "<p>The total number of occurrences of sensitive data that Amazon Macie has found in the bucket's objects. This includes occurrences that are currently suppressed by the sensitivity scoring settings for the bucket (totalDetectionsSuppressed).</p>"}, "totalDetectionsSuppressed": {"shape": "__long", "locationName": "totalDetectionsSuppressed", "documentation": "<p>The total number of occurrences of sensitive data that are currently suppressed by the sensitivity scoring settings for the bucket. These represent occurrences of sensitive data that Amazon Macie found in the bucket's objects, but the occurrences were manually suppressed. By default, suppressed occurrences are excluded from the bucket's sensitivity score.</p>"}, "totalItemsClassified": {"shape": "__long", "locationName": "totalItemsClassified", "documentation": "<p>The total number of objects that <PERSON> Macie has analyzed in the bucket.</p>"}, "totalItemsSensitive": {"shape": "__long", "locationName": "totalItemsSensitive", "documentation": "<p>The total number of the bucket's objects that Amazon Macie has found sensitive data in.</p>"}, "totalItemsSkipped": {"shape": "__long", "locationName": "totalItemsSkipped", "documentation": "<p>The total number of objects that <PERSON> Macie wasn't able to analyze in the bucket due to an object-level issue or error. For example, the object is a malformed file. This value includes objects that <PERSON><PERSON> wasn't able to analyze for reasons reported by other statistics in the ResourceStatistics object.</p>"}, "totalItemsSkippedInvalidEncryption": {"shape": "__long", "locationName": "totalItemsSkippedInvalidEncryption", "documentation": "<p>The total number of objects that <PERSON> Macie wasn't able to analyze in the bucket because the objects are encrypted with a key that <PERSON><PERSON> can't access. The objects use server-side encryption with customer-provided keys (SSE-C).</p>"}, "totalItemsSkippedInvalidKms": {"shape": "__long", "locationName": "totalItemsSkippedInvalidKms", "documentation": "<p>The total number of objects that <PERSON> Macie wasn't able to analyze in the bucket because the objects are encrypted with KMS keys that were disabled, are scheduled for deletion, or were deleted.</p>"}, "totalItemsSkippedPermissionDenied": {"shape": "__long", "locationName": "totalItemsSkippedPermissionDenied", "documentation": "<p>The total number of objects that <PERSON> Macie wasn't able to analyze in the bucket due to the permissions settings for the objects or the permissions settings for the keys that were used to encrypt the objects.</p>"}}, "documentation": "<p>Provides statistical data for sensitive data discovery metrics that apply to an S3 bucket that Amazon Macie monitors and analyzes for your account. The statistics capture the results of automated sensitive data discovery activities that <PERSON><PERSON> has performed for the bucket. The data is available only if automated sensitive data discovery is currently enabled for your account.</p>"}, "ResourcesAffected": {"type": "structure", "members": {"s3Bucket": {"shape": "S3Bucket", "locationName": "s3Bucket", "documentation": "<p>The details of the S3 bucket that the finding applies to.</p>"}, "s3Object": {"shape": "S3Object", "locationName": "s3Object", "documentation": "<p>The details of the S3 object that the finding applies to.</p>"}}, "documentation": "<p>Provides information about the resources that a finding applies to.</p>"}, "RevealConfiguration": {"type": "structure", "members": {"kmsKeyId": {"shape": "__stringMin1Max2048", "locationName": "kmsKeyId", "documentation": "<p>The Amazon Resource Name (ARN), ID, or alias of the KMS key to use to encrypt sensitive data that's retrieved. The key must be an existing, customer managed, symmetric encryption key that's in the same Amazon Web Services Region as the Amazon Macie account.</p> <p>If this value specifies an alias, it must include the following prefix: alias/. If this value specifies a key that's owned by another Amazon Web Services account, it must specify the ARN of the key or the ARN of the key's alias.</p>"}, "status": {"shape": "RevealStatus", "locationName": "status", "documentation": "<p>The status of the configuration for the Amazon Macie account. In a request, valid values are: ENABLED, enable the configuration for the account; and, DISABLED, disable the configuration for the account. In a response, possible values are: ENABLED, the configuration is currently enabled for the account; and, DISABLED, the configuration is currently disabled for the account.</p>"}}, "documentation": "<p>Specifies the configuration settings for retrieving occurrences of sensitive data reported by findings, and the status of the configuration for an Amazon Macie account. When you enable the configuration for the first time, your request must specify an Key Management Service (KMS) key. Otherwise, an error occurs. <PERSON><PERSON> uses the specified key to encrypt the sensitive data that you retrieve.</p>", "required": ["status"]}, "RevealRequestStatus": {"type": "string", "documentation": "<p>The status of a request to retrieve occurrences of sensitive data reported by a finding. Possible values are:</p>", "enum": ["SUCCESS", "PROCESSING", "ERROR"]}, "RevealStatus": {"type": "string", "documentation": "<p>The status of the configuration for retrieving occurrences of sensitive data reported by findings. Valid values are:</p>", "enum": ["ENABLED", "DISABLED"]}, "S3Bucket": {"type": "structure", "members": {"allowsUnencryptedObjectUploads": {"shape": "AllowsUnencryptedObjectUploads", "locationName": "allowsUnencryptedObjectUploads", "documentation": "<p>Specifies whether the bucket policy for the bucket requires server-side encryption of objects when objects are added to the bucket. Possible values are:</p> <ul><li><p>FALSE - The bucket policy requires server-side encryption of new objects. PutObject requests must include a valid server-side encryption header.</p></li> <li><p>TRUE - The bucket doesn't have a bucket policy or it has a bucket policy that doesn't require server-side encryption of new objects. If a bucket policy exists, it doesn't require PutObject requests to include a valid server-side encryption header.</p></li> <li><p>UNKNOWN - Amazon Macie can't determine whether the bucket policy requires server-side encryption of new objects.</p></li></ul> <p>Valid server-side encryption headers are: x-amz-server-side-encryption with a value of AES256 or aws:kms, and x-amz-server-side-encryption-customer-algorithm with a value of AES256.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket.</p>"}, "createdAt": {"shape": "__timestampIso8601", "locationName": "createdAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the bucket was created. This value can also indicate when changes such as edits to the bucket's policy were most recently made to the bucket, relative to when the finding was created or last updated.</p>"}, "defaultServerSideEncryption": {"shape": "ServerSideEncryption", "locationName": "defaultServerSideEncryption", "documentation": "<p>The default server-side encryption settings for the bucket.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the bucket.</p>"}, "owner": {"shape": "S3BucketOwner", "locationName": "owner", "documentation": "<p>The display name and canonical user ID for the Amazon Web Services account that owns the bucket.</p>"}, "publicAccess": {"shape": "BucketPublicAccess", "locationName": "publicAccess", "documentation": "<p>The permissions settings that determine whether the bucket is publicly accessible.</p>"}, "tags": {"shape": "KeyValuePairList", "locationName": "tags", "documentation": "<p>The tags that are associated with the bucket.</p>"}}, "documentation": "<p>Provides information about the S3 bucket that a finding applies to.</p>"}, "S3BucketCriteriaForJob": {"type": "structure", "members": {"excludes": {"shape": "CriteriaBlockForJob", "locationName": "excludes", "documentation": "<p>The property- and tag-based conditions that determine which buckets to exclude from the job.</p>"}, "includes": {"shape": "CriteriaBlockForJob", "locationName": "includes", "documentation": "<p>The property- and tag-based conditions that determine which buckets to include in the job.</p>"}}, "documentation": "<p>Specifies property- and tag-based conditions that define criteria for including or excluding S3 buckets from a classification job. Exclude conditions take precedence over include conditions.</p>"}, "S3BucketDefinitionForJob": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that owns the buckets.</p>"}, "buckets": {"shape": "__listOf__string", "locationName": "buckets", "documentation": "<p>An array that lists the names of the buckets.</p>"}}, "documentation": "<p>Specifies an Amazon Web Services account that owns S3 buckets for a classification job to analyze, and one or more specific buckets to analyze for that account.</p>", "required": ["accountId", "buckets"]}, "S3BucketName": {"type": "string", "documentation": "<p>The name of an S3 bucket.</p>", "pattern": "^[A-Za-z0-9.\\-_]{3,255}$"}, "S3BucketOwner": {"type": "structure", "members": {"displayName": {"shape": "__string", "locationName": "displayName", "documentation": "<p>The display name of the account that owns the bucket.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The canonical user ID for the account that owns the bucket.</p>"}}, "documentation": "<p>Provides information about the Amazon Web Services account that owns an S3 bucket.</p>"}, "S3ClassificationScope": {"type": "structure", "members": {"excludes": {"shape": "S3ClassificationScopeExclusion", "locationName": "excludes", "documentation": "<p>The S3 buckets that are excluded.</p>"}}, "documentation": "<p>Specifies the S3 buckets that are excluded from automated sensitive data discovery for an Amazon Macie account.</p>", "required": ["excludes"]}, "S3ClassificationScopeExclusion": {"type": "structure", "members": {"bucketNames": {"shape": "__listOfS3BucketName", "locationName": "bucketNames", "documentation": "<p>An array of strings, one for each S3 bucket that is excluded. Each string is the full name of an excluded bucket.</p>"}}, "documentation": "<p>Specifies the names of the S3 buckets that are excluded from automated sensitive data discovery.</p>", "required": ["bucketNames"]}, "S3ClassificationScopeExclusionUpdate": {"type": "structure", "members": {"bucketNames": {"shape": "__listOfS3BucketName", "locationName": "bucketNames", "documentation": "<p>Depending on the value specified for the update operation (ClassificationScopeUpdateOperation), an array of strings that: lists the names of buckets to add or remove from the list, or specifies a new set of bucket names that overwrites all existing names in the list. Each string must be the full name of an S3 bucket. Values are case sensitive.</p>"}, "operation": {"shape": "ClassificationScopeUpdateOperation", "locationName": "operation", "documentation": "<p>Specifies how to apply the changes to the exclusion list. Valid values are:</p> <ul><li><p>ADD - Append the specified bucket names to the current list.</p></li> <li><p>REMOVE - Remove the specified bucket names from the current list.</p></li> <li><p>REPLACE - Overwrite the current list with the specified list of bucket names. If you specify this value, Amazon Macie removes all existing names from the list and adds all the specified names to the list.</p></li></ul>"}}, "documentation": "<p>Specifies S3 buckets to add or remove from the exclusion list defined by the classification scope for an Amazon Macie account.</p>", "required": ["bucketNames", "operation"]}, "S3ClassificationScopeUpdate": {"type": "structure", "members": {"excludes": {"shape": "S3ClassificationScopeExclusionUpdate", "locationName": "excludes", "documentation": "<p>The names of the S3 buckets to add or remove from the list.</p>"}}, "documentation": "<p>Specifies changes to the list of S3 buckets that are excluded from automated sensitive data discovery for an Amazon Macie account.</p>", "required": ["excludes"]}, "S3Destination": {"type": "structure", "members": {"bucketName": {"shape": "__string", "locationName": "bucketName", "documentation": "<p>The name of the bucket.</p>"}, "keyPrefix": {"shape": "__string", "locationName": "keyPrefix", "documentation": "<p>The path prefix to use in the path to the location in the bucket. This prefix specifies where to store classification results in the bucket.</p>"}, "kmsKeyArn": {"shape": "__string", "locationName": "kmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the customer managed KMS key to use for encryption of the results. This must be the ARN of an existing, symmetric encryption KMS key that's in the same Amazon Web Services Region as the bucket.</p>"}}, "documentation": "<p>Specifies an S3 bucket to store data classification results in, and the encryption settings to use when storing results in that bucket.</p>", "required": ["bucketName", "kmsKeyArn"]}, "S3JobDefinition": {"type": "structure", "members": {"bucketCriteria": {"shape": "S3BucketCriteriaForJob", "locationName": "bucketCriteria", "documentation": "<p>The property- and tag-based conditions that determine which S3 buckets to include or exclude from the analysis. Each time the job runs, the job uses these criteria to determine which buckets contain objects to analyze. A job's definition can contain a bucketCriteria object or a bucketDefinitions array, not both.</p>"}, "bucketDefinitions": {"shape": "__listOfS3BucketDefinitionForJob", "locationName": "bucketDefinitions", "documentation": "<p>An array of objects, one for each Amazon Web Services account that owns specific S3 buckets to analyze. Each object specifies the account ID for an account and one or more buckets to analyze for that account. A job's definition can contain a bucketDefinitions array or a bucketCriteria object, not both.</p>"}, "scoping": {"shape": "<PERSON><PERSON>", "locationName": "scoping", "documentation": "<p>The property- and tag-based conditions that determine which S3 objects to include or exclude from the analysis. Each time the job runs, the job uses these criteria to determine which objects to analyze.</p>"}}, "documentation": "<p>Specifies which S3 buckets contain the objects that a classification job analyzes, and the scope of that analysis. The bucket specification can be static (bucketDefinitions) or dynamic (bucketCriteria). If it's static, the job analyzes objects in the same predefined set of buckets each time the job runs. If it's dynamic, the job analyzes objects in any buckets that match the specified criteria each time the job starts to run.</p>"}, "S3Object": {"type": "structure", "members": {"bucketArn": {"shape": "__string", "locationName": "bucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the bucket that contains the object.</p>"}, "eTag": {"shape": "__string", "locationName": "eTag", "documentation": "<p>The entity tag (ETag) that identifies the affected version of the object. If the object was overwritten or changed after Amazon Macie produced the finding, this value might be different from the current ETag for the object.</p>"}, "extension": {"shape": "__string", "locationName": "extension", "documentation": "<p>The file name extension of the object. If the object doesn't have a file name extension, this value is \"\".</p>"}, "key": {"shape": "__string", "locationName": "key", "documentation": "<p>The full name (<i>key</i>) of the object, including the object's prefix if applicable.</p>"}, "lastModified": {"shape": "__timestampIso8601", "locationName": "lastModified", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the object was last modified.</p>"}, "path": {"shape": "__string", "locationName": "path", "documentation": "<p>The full path to the affected object, including the name of the affected bucket and the object's name (key).</p>"}, "publicAccess": {"shape": "__boolean", "locationName": "publicAccess", "documentation": "<p>Specifies whether the object is publicly accessible due to the combination of permissions settings that apply to the object.</p>"}, "serverSideEncryption": {"shape": "ServerSideEncryption", "locationName": "serverSideEncryption", "documentation": "<p>The type of server-side encryption that was used to encrypt the object.</p>"}, "size": {"shape": "__long", "locationName": "size", "documentation": "<p>The total storage size, in bytes, of the object.</p>"}, "storageClass": {"shape": "StorageClass", "locationName": "storageClass", "documentation": "<p>The storage class of the object.</p>"}, "tags": {"shape": "KeyValuePairList", "locationName": "tags", "documentation": "<p>The tags that are associated with the object.</p>"}, "versionId": {"shape": "__string", "locationName": "versionId", "documentation": "<p>The identifier for the affected version of the object.</p>"}}, "documentation": "<p>Provides information about the S3 object that a finding applies to.</p>"}, "S3WordsList": {"type": "structure", "members": {"bucketName": {"shape": "__stringMin3Max255PatternAZaZ093255", "locationName": "bucketName", "documentation": "<p>The full name of the S3 bucket that contains the object.</p>"}, "objectKey": {"shape": "__stringMin1Max1024PatternSS", "locationName": "object<PERSON>ey", "documentation": "<p>The full name (key) of the object.</p>"}}, "documentation": "<p>Provides information about an S3 object that lists specific text to ignore.</p>", "required": ["bucketName", "object<PERSON>ey"]}, "ScopeFilterKey": {"type": "string", "documentation": "<p>The property to use in a condition that determines whether an S3 object is included or excluded from a classification job. Valid values are:</p>", "enum": ["OBJECT_EXTENSION", "OBJECT_LAST_MODIFIED_DATE", "OBJECT_SIZE", "OBJECT_KEY"]}, "Scoping": {"type": "structure", "members": {"excludes": {"shape": "JobScopingBlock", "locationName": "excludes", "documentation": "<p>The property- and tag-based conditions that determine which objects to exclude from the analysis.</p>"}, "includes": {"shape": "JobScopingBlock", "locationName": "includes", "documentation": "<p>The property- and tag-based conditions that determine which objects to include in the analysis.</p>"}}, "documentation": "<p>Specifies one or more property- and tag-based conditions that define criteria for including or excluding S3 objects from a classification job. Exclude conditions take precedence over include conditions.</p>"}, "SearchResourcesBucketCriteria": {"type": "structure", "members": {"excludes": {"shape": "SearchResourcesCriteriaBlock", "locationName": "excludes", "documentation": "<p>The property- and tag-based conditions that determine which buckets to exclude from the results.</p>"}, "includes": {"shape": "SearchResourcesCriteriaBlock", "locationName": "includes", "documentation": "<p>The property- and tag-based conditions that determine which buckets to include in the results.</p>"}}, "documentation": "<p>Specifies property- and tag-based conditions that define filter criteria for including or excluding S3 buckets from the query results. Exclude conditions take precedence over include conditions.</p>"}, "SearchResourcesComparator": {"type": "string", "documentation": "<p>The operator to use in a condition that filters the results of a query. Valid values are:</p>", "enum": ["EQ", "NE"]}, "SearchResourcesCriteria": {"type": "structure", "members": {"simpleCriterion": {"shape": "SearchResourcesSimpleCriterion", "locationName": "simpleCriterion", "documentation": "<p>A property-based condition that defines a property, operator, and one or more values for including or excluding resources from the results.</p>"}, "tagCriterion": {"shape": "SearchResourcesTagCriterion", "locationName": "tagCriterion", "documentation": "<p>A tag-based condition that defines an operator and tag keys, tag values, or tag key and value pairs for including or excluding resources from the results.</p>"}}, "documentation": "<p>Specifies a property- or tag-based filter condition for including or excluding Amazon Web Services resources from the query results.</p>"}, "SearchResourcesCriteriaBlock": {"type": "structure", "members": {"and": {"shape": "__listOfSearchResourcesCriteria", "locationName": "and", "documentation": "<p>An array of objects, one for each property- or tag-based condition that includes or excludes resources from the query results. If you specify more than one condition, Amazon Macie uses AND logic to join the conditions.</p>"}}, "documentation": "<p>Specifies property- and tag-based conditions that define filter criteria for including or excluding Amazon Web Services resources from the query results.</p>"}, "SearchResourcesRequest": {"type": "structure", "members": {"bucketCriteria": {"shape": "SearchResourcesBucketCriteria", "locationName": "bucketCriteria", "documentation": "<p>The filter conditions that determine which S3 buckets to include or exclude from the query results.</p>"}, "maxResults": {"shape": "__integer", "locationName": "maxResults", "documentation": "<p>The maximum number of items to include in each page of the response. The default value is 50.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The nextToken string that specifies which page of results to return in a paginated response.</p>"}, "sortCriteria": {"shape": "SearchResourcesSortCriteria", "locationName": "sortCriteria", "documentation": "<p>The criteria to use to sort the results.</p>"}}}, "SearchResourcesResponse": {"type": "structure", "members": {"matchingResources": {"shape": "__listOfMatchingResource", "locationName": "matchingResources", "documentation": "<p>An array of objects, one for each resource that matches the filter criteria specified in the request.</p>"}, "nextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>The string to use in a subsequent request to get the next page of results in a paginated response. This value is null if there are no additional pages.</p>"}}}, "SearchResourcesSimpleCriterion": {"type": "structure", "members": {"comparator": {"shape": "SearchResourcesComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. Valid values are EQ (equals) and NE (not equals).</p>"}, "key": {"shape": "SearchResourcesSimpleCriterionKey", "locationName": "key", "documentation": "<p>The property to use in the condition.</p>"}, "values": {"shape": "__listOf__string", "locationName": "values", "documentation": "<p>An array that lists one or more values to use in the condition. If you specify multiple values, Amazon Macie uses OR logic to join the values. Valid values for each supported property (key) are:</p> <ul><li><p>ACCOUNT_ID - A string that represents the unique identifier for the Amazon Web Services account that owns the resource.</p></li> <li><p>S3_BUCKET_EFFECTIVE_PERMISSION - A string that represents an enumerated value that <PERSON><PERSON> defines for the <a href=\"https://docs.aws.amazon.com/macie/latest/APIReference/datasources-s3.html#datasources-s3-prop-bucketpublicaccess-effectivepermission\">BucketPublicAccess.effectivePermission</a> property of an S3 bucket.</p></li> <li><p>S3_BUCKET_NAME - A string that represents the name of an S3 bucket.</p></li> <li><p>S3_BUCKET_SHARED_ACCESS - A string that represents an enumerated value that <PERSON><PERSON> defines for the <a href=\"https://docs.aws.amazon.com/macie/latest/APIReference/datasources-s3.html#datasources-s3-prop-bucketmetadata-sharedaccess\">BucketMetadata.sharedAccess</a> property of an S3 bucket.</p></li></ul> <p>Values are case sensitive. Also, Macie doesn't support use of partial values or wildcard characters in values.</p>"}}, "documentation": "<p>Specifies a property-based filter condition that determines which Amazon Web Services resources are included or excluded from the query results.</p>"}, "SearchResourcesSimpleCriterionKey": {"type": "string", "documentation": "<p>The property to use in a condition that filters the query results. Valid values are:</p>", "enum": ["ACCOUNT_ID", "S3_BUCKET_NAME", "S3_BUCKET_EFFECTIVE_PERMISSION", "S3_BUCKET_SHARED_ACCESS"]}, "SearchResourcesSortAttributeName": {"type": "string", "documentation": "<p>The property to sort the query results by. Valid values are:</p>", "enum": ["ACCOUNT_ID", "RESOURCE_NAME", "S3_CLASSIFIABLE_OBJECT_COUNT", "S3_CLASSIFIABLE_SIZE_IN_BYTES"]}, "SearchResourcesSortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "SearchResourcesSortAttributeName", "locationName": "attributeName", "documentation": "<p>The property to sort the results by.</p>"}, "orderBy": {"shape": "OrderBy", "locationName": "orderBy", "documentation": "<p>The sort order to apply to the results, based on the value for the property specified by the attributeName property. Valid values are: ASC, sort the results in ascending order; and, DESC, sort the results in descending order.</p>"}}, "documentation": "<p>Specifies criteria for sorting the results of a query for information about Amazon Web Services resources that Amazon Macie monitors and analyzes.</p>"}, "SearchResourcesTagCriterion": {"type": "structure", "members": {"comparator": {"shape": "SearchResourcesComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. Valid values are EQ (equals) and NE (not equals).</p>"}, "tagValues": {"shape": "__listOfSearchResourcesTagCriterionPair", "locationName": "tagValues", "documentation": "<p>The tag keys, tag values, or tag key and value pairs to use in the condition.</p>"}}, "documentation": "<p>Specifies a tag-based filter condition that determines which Amazon Web Services resources are included or excluded from the query results.</p>"}, "SearchResourcesTagCriterionPair": {"type": "structure", "members": {"key": {"shape": "__string", "locationName": "key", "documentation": "<p>The value for the tag key to use in the condition.</p>"}, "value": {"shape": "__string", "locationName": "value", "documentation": "<p>The tag value to use in the condition.</p>"}}, "documentation": "<p>Specifies a tag key, a tag value, or a tag key and value (as a pair) to use in a tag-based filter condition for a query. Tag keys and values are case sensitive. Also, Amazon Macie doesn't support use of partial values or wildcard characters in tag-based filter conditions.</p>"}, "SecurityHubConfiguration": {"type": "structure", "members": {"publishClassificationFindings": {"shape": "__boolean", "locationName": "publishClassificationFindings", "documentation": "<p>Specifies whether to publish sensitive data findings to Security Hub. If you set this value to true, Amazon Macie automatically publishes all sensitive data findings that weren't suppressed by a findings filter. The default value is false.</p>"}, "publishPolicyFindings": {"shape": "__boolean", "locationName": "publishPolicyFindings", "documentation": "<p>Specifies whether to publish policy findings to Security Hub. If you set this value to true, Amazon Macie automatically publishes all new and updated policy findings that weren't suppressed by a findings filter. The default value is true.</p>"}}, "documentation": "<p>Specifies configuration settings that determine which findings are published to Security Hub automatically. For information about how <PERSON><PERSON> publishes findings to Security Hub, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/securityhub-integration.html\">Amazon Macie integration with Security Hub</a> in the <i>Amazon Macie User Guide</i>.</p>", "required": ["publishPolicyFindings", "publishClassificationFindings"]}, "SensitiveData": {"type": "list", "documentation": "<p>Provides information about the category and number of occurrences of sensitive data that produced a finding.</p>", "member": {"shape": "SensitiveDataItem"}}, "SensitiveDataItem": {"type": "structure", "members": {"category": {"shape": "SensitiveDataItemCategory", "locationName": "category", "documentation": "<p>The category of sensitive data that was detected. For example: CREDENTIALS, for credentials data such as private keys or Amazon Web Services secret access keys; FINANCIAL_INFORMATION, for financial data such as credit card numbers; or, PERSONAL_INFORMATION, for personal health information, such as health insurance identification numbers, or personally identifiable information, such as passport numbers.</p>"}, "detections": {"shape": "DefaultDetections", "locationName": "detections", "documentation": "<p>An array of objects, one for each type of sensitive data that was detected. Each object reports the number of occurrences of a specific type of sensitive data that was detected, and the location of up to 15 of those occurrences.</p>"}, "totalCount": {"shape": "__long", "locationName": "totalCount", "documentation": "<p>The total number of occurrences of the sensitive data that was detected.</p>"}}, "documentation": "<p>Provides information about the category, types, and occurrences of sensitive data that produced a sensitive data finding.</p>"}, "SensitiveDataItemCategory": {"type": "string", "documentation": "<p>For a finding, the category of sensitive data that was detected and produced the finding. For a managed data identifier, the category of sensitive data that the managed data identifier detects. Possible values are:</p>", "enum": ["FINANCIAL_INFORMATION", "PERSONAL_INFORMATION", "CREDENTIALS", "CUSTOM_IDENTIFIER"]}, "SensitiveDataOccurrences": {"type": "map", "documentation": "<p>Specifies a type of sensitive data reported by a finding and provides occurrences of the specified type of sensitive data.</p>", "key": {"shape": "__string"}, "value": {"shape": "__listOfDetectedDataDetails"}}, "SensitivityAggregations": {"type": "structure", "members": {"classifiableSizeInBytes": {"shape": "__long", "locationName": "classifiableSizeInBytes", "documentation": "<p>The total storage size, in bytes, of all the objects that Amazon Macie can analyze in the buckets. These objects use a supported storage class and have a file name extension for a supported file or storage format.</p> <p>If versioning is enabled for any of the buckets, this value is based on the size of the latest version of each applicable object in the buckets. This value doesn't reflect the storage size of all versions of all applicable objects in the buckets.</p>"}, "publiclyAccessibleCount": {"shape": "__long", "locationName": "publiclyAccessibleCount", "documentation": "<p>The total number of buckets that are publicly accessible due to a combination of permissions settings for each bucket.</p>"}, "totalCount": {"shape": "__long", "locationName": "totalCount", "documentation": "<p>The total number of buckets.</p>"}, "totalSizeInBytes": {"shape": "__long", "locationName": "totalSizeInBytes", "documentation": "<p>The total storage size, in bytes, of the buckets.</p> <p>If versioning is enabled for any of the buckets, this value is based on the size of the latest version of each object in the buckets. This value doesn't reflect the storage size of all versions of the objects in the buckets.</p>"}}, "documentation": "<p>Provides aggregated statistical data for sensitive data discovery metrics that apply to S3 buckets. Each field contains aggregated data for all the buckets that have a sensitivity score (sensitivityScore) of a specified value or within a specified range (BucketStatisticsBySensitivity). If automated sensitive data discovery is currently disabled for your account, the value for each field is 0.</p>"}, "SensitivityInspectionTemplateExcludes": {"type": "structure", "members": {"managedDataIdentifierIds": {"shape": "__listOf__string", "locationName": "managedDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each managed data identifier to exclude. To retrieve a list of valid values, use the ListManagedDataIdentifiers operation.</p>"}}, "documentation": "<p>Specifies managed data identifiers to exclude (not use) when performing automated sensitive data discovery for an Amazon Macie account. For information about the managed data identifiers that Amazon Macie currently provides, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/managed-data-identifiers.html\">Using managed data identifiers</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "SensitivityInspectionTemplateId": {"type": "string", "documentation": "<p>The unique identifier for the sensitivity inspection template.</p>"}, "SensitivityInspectionTemplateIncludes": {"type": "structure", "members": {"allowListIds": {"shape": "__listOf__string", "locationName": "allowListIds", "documentation": "<p>An array of unique identifiers, one for each allow list to include.</p>"}, "customDataIdentifierIds": {"shape": "__listOf__string", "locationName": "customDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each custom data identifier to include.</p>"}, "managedDataIdentifierIds": {"shape": "__listOf__string", "locationName": "managedDataIdentifierIds", "documentation": "<p>An array of unique identifiers, one for each managed data identifier to include.</p> <p>Amazon Macie uses these managed data identifiers in addition to managed data identifiers that are subsequently released and recommended for automated sensitive data discovery. To retrieve a list of valid values for the managed data identifiers that are currently available, use the ListManagedDataIdentifiers operation.</p> <para/>"}}, "documentation": "<p>Specifies the allow lists, custom data identifiers, and managed data identifiers to include (use) when performing automated sensitive data discovery for an Amazon Macie account. The configuration must specify at least one custom data identifier or managed data identifier. For information about the managed data identifiers that Amazon Macie currently provides, see <a href=\"https://docs.aws.amazon.com/macie/latest/user/managed-data-identifiers.html\">Using managed data identifiers</a> in the <i>Amazon Macie User Guide</i>.</p>"}, "SensitivityInspectionTemplatesEntry": {"type": "structure", "members": {"id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the sensitivity inspection template.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>The name of the sensitivity inspection template: automated-sensitive-data-discovery.</p>"}}, "documentation": "<p>Provides information about the sensitivity inspection template for an Amazon Macie account. <PERSON>ie uses the template's settings when it performs automated sensitive data discovery for the account.</p>"}, "ServerSideEncryption": {"type": "structure", "members": {"encryptionType": {"shape": "EncryptionType", "locationName": "encryptionType", "documentation": "<p>The server-side encryption algorithm that's used when storing data in the bucket or object. If default encryption settings aren't configured for the bucket or the object isn't encrypted using server-side encryption, this value is NONE.</p>"}, "kmsMasterKeyId": {"shape": "__string", "locationName": "kmsMasterKeyId", "documentation": "<p>The Amazon Resource Name (ARN) or unique identifier (key ID) for the KMS key that's used to encrypt data in the bucket or the object. This value is null if an KMS key isn't used to encrypt the data.</p>"}}, "documentation": "<p>Provides information about the default server-side encryption settings for an S3 bucket or the encryption settings for an S3 object.</p>"}, "ServiceLimit": {"type": "structure", "members": {"isServiceLimited": {"shape": "__boolean", "locationName": "isServiceLimited", "documentation": "<p>Specifies whether the account has met the quota that corresponds to the metric specified by the UsageByAccount.type field in the response.</p>"}, "unit": {"shape": "Unit", "locationName": "unit", "documentation": "<p>The unit of measurement for the value specified by the value field.</p>"}, "value": {"shape": "__long", "locationName": "value", "documentation": "<p>The value for the metric specified by the UsageByAccount.type field in the response.</p>"}}, "documentation": "<p>Specifies a current quota for an Amazon Macie account.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred due to one or more service quotas for an account.</p>", "exception": true, "error": {"httpStatusCode": 402}}, "SessionContext": {"type": "structure", "members": {"attributes": {"shape": "SessionContextAttributes", "locationName": "attributes", "documentation": "<p>The date and time when the credentials were issued, and whether the credentials were authenticated with a multi-factor authentication (MFA) device.</p>"}, "sessionIssuer": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "locationName": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The source and type of credentials that were issued to the entity.</p>"}}, "documentation": "<p>Provides information about a session that was created for an entity that performed an action by using temporary security credentials.</p>"}, "SessionContextAttributes": {"type": "structure", "members": {"creationDate": {"shape": "__timestampIso8601", "locationName": "creationDate", "documentation": "<p>The date and time, in UTC and ISO 8601 format, when the credentials were issued.</p>"}, "mfaAuthenticated": {"shape": "__boolean", "locationName": "mfaAuthenticated", "documentation": "<p>Specifies whether the credentials were authenticated with a multi-factor authentication (MFA) device.</p>"}}, "documentation": "<p>Provides information about the context in which temporary security credentials were issued to an entity.</p>"}, "SessionIssuer": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that owns the entity that was used to get the credentials.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the source account, Identity and Access Management (IAM) user, or role that was used to get the credentials.</p>"}, "principalId": {"shape": "__string", "locationName": "principalId", "documentation": "<p>The unique identifier for the entity that was used to get the credentials.</p>"}, "type": {"shape": "__string", "locationName": "type", "documentation": "<p>The source of the temporary security credentials, such as Root, IAMUser, or Role.</p>"}, "userName": {"shape": "__string", "locationName": "userName", "documentation": "<p>The name or alias of the user or role that issued the session. This value is null if the credentials were obtained from a root account that doesn't have an alias.</p>"}}, "documentation": "<p>Provides information about the source and type of temporary security credentials that were issued to an entity.</p>"}, "Severity": {"type": "structure", "members": {"description": {"shape": "SeverityDescription", "locationName": "description", "documentation": "<p>The qualitative representation of the finding's severity, ranging from Low (least severe) to High (most severe).</p>"}, "score": {"shape": "__long", "locationName": "score", "documentation": "<p>The numerical representation of the finding's severity, ranging from 1 (least severe) to 3 (most severe).</p>"}}, "documentation": "<p>Provides the numerical and qualitative representations of a finding's severity.</p>"}, "SeverityDescription": {"type": "string", "documentation": "<p>The qualitative representation of the finding's severity. Possible values are:</p>", "enum": ["Low", "Medium", "High"]}, "SeverityLevel": {"type": "structure", "members": {"occurrencesThreshold": {"shape": "__long", "locationName": "occurrencesT<PERSON><PERSON>old", "documentation": "<p>The minimum number of occurrences of text that must match the custom data identifier's detection criteria in order to produce a finding with the specified severity (severity).</p>"}, "severity": {"shape": "DataIdentifierSeverity", "locationName": "severity", "documentation": "<p>The severity to assign to a finding: if the number of occurrences is greater than or equal to the specified threshold (occurrencesThreshold); and, if applicable, the number of occurrences is less than the threshold for the next consecutive severity level for the custom data identifier, moving from LOW to HIGH.</p>"}}, "documentation": "<p>Specifies a severity level for findings that a custom data identifier produces. A severity level determines which severity is assigned to the findings, based on the number of occurrences of text that match the custom data identifier's detection criteria.</p>", "required": ["occurrencesT<PERSON><PERSON>old", "severity"]}, "SeverityLevelList": {"type": "list", "documentation": "<p>The severity to assign to findings that the custom data identifier produces, based on the number of occurrences of text that matches the custom data identifier's detection criteria. You can specify as many as three SeverityLevel objects in this array, one for each severity: LOW, MEDIUM, or HIGH. If you specify more than one, the occurrences thresholds must be in ascending order by severity, moving from LOW to HIGH. For example, 1 for LOW, 50 for MEDIUM, and 100 for HIGH. If an S3 object contains fewer occurrences than the lowest specified threshold, Amazon Macie doesn't create a finding.</p> <p>If you don't specify any values for this array, <PERSON><PERSON> creates findings for S3 objects that contain at least one occurrence of text that matches the detection criteria, and <PERSON><PERSON> automatically assigns the MEDIUM severity to those findings.</p>", "member": {"shape": "SeverityLevel"}}, "SharedAccess": {"type": "string", "enum": ["EXTERNAL", "INTERNAL", "NOT_SHARED", "UNKNOWN"]}, "SimpleCriterionForJob": {"type": "structure", "members": {"comparator": {"shape": "JobComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. Valid values are EQ (equals) and NE (not equals).</p>"}, "key": {"shape": "SimpleCriterionKeyForJob", "locationName": "key", "documentation": "<p>The property to use in the condition.</p>"}, "values": {"shape": "__listOf__string", "locationName": "values", "documentation": "<p>An array that lists one or more values to use in the condition. If you specify multiple values, Amazon Macie uses OR logic to join the values. Valid values for each supported property (key) are:</p> <ul><li><p>ACCOUNT_ID - A string that represents the unique identifier for the Amazon Web Services account that owns the bucket.</p></li> <li><p>S3_BUCKET_EFFECTIVE_PERMISSION - A string that represents an enumerated value that <PERSON><PERSON> defines for the <a href=\"https://docs.aws.amazon.com/macie/latest/APIReference/datasources-s3.html#datasources-s3-prop-bucketpublicaccess-effectivepermission\">BucketPublicAccess.effectivePermission</a> property of a bucket.</p></li> <li><p>S3_BUCKET_NAME - A string that represents the name of a bucket.</p></li> <li><p>S3_BUCKET_SHARED_ACCESS - A string that represents an enumerated value that <PERSON><PERSON> defines for the <a href=\"https://docs.aws.amazon.com/macie/latest/APIReference/datasources-s3.html#datasources-s3-prop-bucketmetadata-sharedaccess\">BucketMetadata.sharedAccess</a> property of a bucket.</p></li></ul> <p>Values are case sensitive. Also, Macie doesn't support use of partial values or wildcard characters in these values.</p>"}}, "documentation": "<p>Specifies a property-based condition that determines whether an S3 bucket is included or excluded from a classification job.</p>"}, "SimpleCriterionKeyForJob": {"type": "string", "documentation": "<p>The property to use in a condition that determines whether an S3 bucket is included or excluded from a classification job. Valid values are:</p>", "enum": ["ACCOUNT_ID", "S3_BUCKET_NAME", "S3_BUCKET_EFFECTIVE_PERMISSION", "S3_BUCKET_SHARED_ACCESS"]}, "SimpleScopeTerm": {"type": "structure", "members": {"comparator": {"shape": "JobComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. Valid values for each supported property (key) are:</p> <ul><li><p>OBJECT_EXTENSION - EQ (equals) or NE (not equals)</p></li> <li><p>OBJECT_KEY - STARTS_WITH</p></li> <li><p>OBJECT_LAST_MODIFIED_DATE - Any operator except CONTAINS</p></li> <li><p>OBJECT_SIZE - Any operator except CONTAINS</p></li></ul>"}, "key": {"shape": "ScopeFilter<PERSON>ey", "locationName": "key", "documentation": "<p>The object property to use in the condition.</p>"}, "values": {"shape": "__listOf__string", "locationName": "values", "documentation": "<p>An array that lists the values to use in the condition. If the value for the key property is OBJECT_EXTENSION or OBJECT_KEY, this array can specify multiple values and Amazon Macie uses OR logic to join the values. Otherwise, this array can specify only one value.</p> <p>Valid values for each supported property (key) are:</p> <ul><li><p>OBJECT_EXTENSION - A string that represents the file name extension of an object. For example: docx or pdf</p></li> <li><p>OBJECT_KEY - A string that represents the key prefix (folder name or path) of an object. For example: logs or awslogs/eventlogs. This value applies a condition to objects whose keys (names) begin with the specified value.</p></li> <li><p>OBJECT_LAST_MODIFIED_DATE - The date and time (in UTC and extended ISO 8601 format) when an object was created or last changed, whichever is latest. For example: 2020-09-28T14:31:13Z</p></li> <li><p>OBJECT_SIZE - An integer that represents the storage size (in bytes) of an object.</p></li></ul> <p><PERSON><PERSON> doesn't support use of wildcard characters in these values. Also, string values are case sensitive.</p>"}}, "documentation": "<p>Specifies a property-based condition that determines whether an S3 object is included or excluded from a classification job.</p>"}, "SortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "__string", "locationName": "attributeName", "documentation": "<p>The name of the property to sort the results by. Valid values are: count, createdAt, policyDetails.action.apiCallDetails.firstSeen, policyDetails.action.apiCallDetails.lastSeen, resourcesAffected, severity.score, type, and updatedAt.</p>"}, "orderBy": {"shape": "OrderBy", "locationName": "orderBy", "documentation": "<p>The sort order to apply to the results, based on the value for the property specified by the attributeName property. Valid values are: ASC, sort the results in ascending order; and, DESC, sort the results in descending order.</p>"}}, "documentation": "<p>Specifies criteria for sorting the results of a request for findings.</p>"}, "Statistics": {"type": "structure", "members": {"approximateNumberOfObjectsToProcess": {"shape": "__double", "locationName": "approximateNumberOfObjectsToProcess", "documentation": "<p>The approximate number of objects that the job has yet to process during its current run.</p>"}, "numberOfRuns": {"shape": "__double", "locationName": "numberOfRuns", "documentation": "<p>The number of times that the job has run.</p>"}}, "documentation": "<p>Provides processing statistics for a classification job.</p>"}, "StorageClass": {"type": "string", "documentation": "<p>The storage class of the S3 object. Possible values are:</p>", "enum": ["STANDARD", "REDUCED_REDUNDANCY", "STANDARD_IA", "INTELLIGENT_TIERING", "DEEP_ARCHIVE", "ONEZONE_IA", "GLACIER", "GLACIER_IR", "OUTPOSTS"]}, "SuppressDataIdentifier": {"type": "structure", "members": {"id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the custom data identifier or managed data identifier that detected the type of sensitive data to exclude or include in the score.</p>"}, "type": {"shape": "DataIdentifierType", "locationName": "type", "documentation": "<p>The type of data identifier that detected the sensitive data. Possible values are: CUSTOM, for a custom data identifier; and, MANAGED, for a managed data identifier.</p>"}}, "documentation": "<p>Specifies a custom data identifier or managed data identifier that detected a type of sensitive data to start excluding or including in an S3 bucket's sensitivity score.</p>"}, "TagCriterionForJob": {"type": "structure", "members": {"comparator": {"shape": "JobComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. Valid values are EQ (equals) and NE (not equals).</p>"}, "tagValues": {"shape": "__listOfTagCriterionPairForJob", "locationName": "tagValues", "documentation": "<p>The tag keys, tag values, or tag key and value pairs to use in the condition.</p>"}}, "documentation": "<p>Specifies a tag-based condition that determines whether an S3 bucket is included or excluded from a classification job.</p>"}, "TagCriterionPairForJob": {"type": "structure", "members": {"key": {"shape": "__string", "locationName": "key", "documentation": "<p>The value for the tag key to use in the condition.</p>"}, "value": {"shape": "__string", "locationName": "value", "documentation": "<p>The tag value to use in the condition.</p>"}}, "documentation": " <p>Specifies a tag key, a tag value, or a tag key and value (as a pair) to use in a tag-based condition that determines whether an S3 bucket is included or excluded from a classification job. Tag keys and values are case sensitive. Also, Amazon Macie doesn't support use of partial values or wildcard characters in tag-based conditions.</p>"}, "TagMap": {"type": "map", "documentation": "<p>A string-to-string map of key-value pairs that specifies the tags (keys and values) for an Amazon Macie resource.</p>", "key": {"shape": "__string"}, "value": {"shape": "__string"}}, "TagResourceRequest": {"type": "structure", "members": {"resourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "tags": {"shape": "TagMap", "locationName": "tags", "documentation": "<p>A map of key-value pairs that specifies the tags to associate with the resource.</p> <p>A resource can have a maximum of 50 tags. Each tag consists of a tag key and an associated tag value. The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}, "required": ["resourceArn", "tags"]}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagScopeTerm": {"type": "structure", "members": {"comparator": {"shape": "JobComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. Valid values are EQ (equals) or NE (not equals).</p>"}, "key": {"shape": "__string", "locationName": "key", "documentation": "<p>The object property to use in the condition. The only valid value is TAG.</p>"}, "tagValues": {"shape": "__listOfTagValuePair", "locationName": "tagValues", "documentation": "<p>The tag keys or tag key and value pairs to use in the condition. To specify only tag keys in a condition, specify the keys in this array and set the value for each associated tag value to an empty string.</p>"}, "target": {"shape": "TagTarget", "locationName": "target", "documentation": "<p>The type of object to apply the condition to.</p>"}}, "documentation": "<p>Specifies a tag-based condition that determines whether an S3 object is included or excluded from a classification job.</p>"}, "TagTarget": {"type": "string", "documentation": "<p>The type of object to apply a tag-based condition to. Valid values are:</p>", "enum": ["S3_OBJECT"]}, "TagValuePair": {"type": "structure", "members": {"key": {"shape": "__string", "locationName": "key", "documentation": "<p>The value for the tag key to use in the condition.</p>"}, "value": {"shape": "__string", "locationName": "value", "documentation": "<p>The tag value, associated with the specified tag key (key), to use in the condition. To specify only a tag key for a condition, specify the tag key for the key property and set this value to an empty string.</p>"}}, "documentation": "<p>Specifies a tag key or tag key and value pair to use in a tag-based condition that determines whether an S3 object is included or excluded from a classification job. Tag keys and values are case sensitive. Also, Amazon Macie doesn't support use of partial values or wildcard characters in tag-based conditions.</p>"}, "TestCustomDataIdentifierRequest": {"type": "structure", "members": {"ignoreWords": {"shape": "__listOf__string", "locationName": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An array that lists specific character sequences (<i>ignore words</i>) to exclude from the results. If the text matched by the regular expression contains any string in this array, Amazon Macie ignores it. The array can contain as many as 10 ignore words. Each ignore word can contain 4-90 UTF-8 characters. Ignore words are case sensitive.</p>"}, "keywords": {"shape": "__listOf__string", "locationName": "keywords", "documentation": "<p>An array that lists specific character sequences (<i>keywords</i>), one of which must precede and be within proximity (maximumMatchDistance) of the regular expression to match. The array can contain as many as 50 keywords. Each keyword can contain 3-90 UTF-8 characters. Keywords aren't case sensitive.</p>"}, "maximumMatchDistance": {"shape": "__integer", "locationName": "maximumMatchDistance", "documentation": "<p>The maximum number of characters that can exist between the end of at least one complete character sequence specified by the keywords array and the end of the text that matches the regex pattern. If a complete keyword precedes all the text that matches the pattern and the keyword is within the specified distance, Amazon Macie includes the result. The distance can be 1-300 characters. The default value is 50.</p>"}, "regex": {"shape": "__string", "locationName": "regex", "documentation": "<p>The regular expression (<i>regex</i>) that defines the pattern to match. The expression can contain as many as 512 characters.</p>"}, "sampleText": {"shape": "__string", "locationName": "sampleText", "documentation": "<p>The sample text to inspect by using the custom data identifier. The text can contain as many as 1,000 characters.</p>"}}, "required": ["regex", "sampleText"]}, "TestCustomDataIdentifierResponse": {"type": "structure", "members": {"matchCount": {"shape": "__integer", "locationName": "matchCount", "documentation": "<p>The number of occurrences of sample text that matched the criteria specified by the custom data identifier.</p>"}}}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred because too many requests were sent during a certain amount of time.</p>", "exception": true, "error": {"httpStatusCode": 429}}, "TimeRange": {"type": "string", "documentation": "<p>An inclusive time period that Amazon Macie usage data applies to. Possible values are:</p>", "enum": ["MONTH_TO_DATE", "PAST_30_DAYS"]}, "Timestamp": {"type": "timestamp", "documentation": "<p>Specifies a date and time in UTC and extended ISO 8601 format.</p>", "timestampFormat": "iso8601"}, "Type": {"type": "string", "enum": ["NONE", "AES256", "aws:kms"]}, "UnavailabilityReasonCode": {"type": "string", "documentation": "<p>Specifies why occurrences of sensitive data can't be retrieved for a finding. Possible values are:</p>", "enum": ["OBJECT_EXCEEDS_SIZE_QUOTA", "UNSUPPORTED_OBJECT_TYPE", "UNSUPPORTED_FINDING_TYPE", "INVALID_CLASSIFICATION_RESULT", "OBJECT_UNAVAILABLE"]}, "Unit": {"type": "string", "enum": ["TERABYTES"]}, "UnprocessableEntityException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The type of error that occurred and prevented Amazon Macie from retrieving occurrences of sensitive data reported by the finding. Possible values are:</p> <ul><li><p>INVALID_CLASSIFICATION_RESULT - Amazon Macie can't verify the location of the sensitive data to retrieve. There isn't a corresponding sensitive data discovery result for the finding. Or the sensitive data discovery result specified by the classificationDetails.detailedResultsLocation field of the finding isn't available, is malformed or corrupted, or uses an unsupported storage format.</p></li> <li><p>OBJECT_EXCEEDS_SIZE_QUOTA - The storage size of the affected S3 object exceeds the size quota for retrieving occurrences of sensitive data.</p></li> <li><p>OBJECT_UNAVAILABLE - The affected S3 object isn't available. The object might have been renamed, moved, or deleted. Or the object was changed after <PERSON><PERSON> created the finding.</p></li> <li><p>UNSUPPORTED_FINDING_TYPE - The specified finding isn't a sensitive data finding.</p></li> <li><p>UNSUPPORTED_OBJECT_TYPE - The affected S3 object uses a file or storage format that <PERSON><PERSON> doesn't support for retrieving occurrences of sensitive data.</p></li></ul>"}}, "documentation": "<p>Provides information about an error that occurred due to an unprocessable entity.</p>", "required": ["message"], "exception": true, "error": {"httpStatusCode": 422}}, "UnprocessedAccount": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The Amazon Web Services account ID for the account that the request applies to.</p>"}, "errorCode": {"shape": "ErrorCode", "locationName": "errorCode", "documentation": "<p>The source of the issue or delay in processing the request.</p>"}, "errorMessage": {"shape": "__string", "locationName": "errorMessage", "documentation": "<p>The reason why the request hasn't been processed.</p>"}}, "documentation": " <p>Provides information about an account-related request that hasn't been processed.</p>"}, "UntagResourceRequest": {"type": "structure", "members": {"resourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "tagKeys": {"shape": "__listOf__string", "location": "querystring", "locationName": "tagKeys", "documentation": "<p>One or more tags (keys) to remove from the resource. In an HTTP request to remove multiple tags, append the tagKeys parameter and argument for each tag to remove, separated by an ampersand (&amp;).</p>"}}, "required": ["tagKeys", "resourceArn"]}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAllowListRequest": {"type": "structure", "members": {"criteria": {"shape": "AllowListCriteria", "locationName": "criteria", "documentation": "<p>The criteria that specify the text or text pattern to ignore. The criteria can be the location and name of an S3 object that lists specific text to ignore (s3WordsList), or a regular expression that defines a text pattern to ignore (regex).</p> <p>You can change a list's underlying criteria, such as the name of the S3 object or the regular expression to use. However, you can't change the type from s3WordsList to regex or the other way around.</p>"}, "description": {"shape": "__stringMin1Max512PatternSS", "locationName": "description", "documentation": "<p>A custom description of the allow list. The description can contain as many as 512 characters.</p>"}, "id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}, "name": {"shape": "__stringMin1Max128Pattern", "locationName": "name", "documentation": "<p>A custom name for the allow list. The name can contain as many as 128 characters.</p>"}}, "required": ["id", "criteria", "name"]}, "UpdateAllowListResponse": {"type": "structure", "members": {"arn": {"shape": "__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the allow list.</p>"}, "id": {"shape": "__stringMin22Max22PatternAZ0922", "locationName": "id", "documentation": "<p>The unique identifier for the allow list.</p>"}}}, "UpdateAutomatedDiscoveryConfigurationRequest": {"type": "structure", "members": {"status": {"shape": "AutomatedDiscoveryStatus", "locationName": "status", "documentation": "<p>The new status of automated sensitive data discovery for the account. Valid values are: ENABLED, start or resume automated sensitive data discovery activities for the account; and, DISABLED, stop performing automated sensitive data discovery activities for the account.</p> <p>When you enable automated sensitive data discovery for the first time, Amazon Macie uses default configuration settings to determine which data sources to analyze and which managed data identifiers to use. To change these settings, use the UpdateClassificationScope and UpdateSensitivityInspectionTemplate operations, respectively. If you change the settings and subsequently disable the configuration, Amazon Macie retains your changes.</p>"}}, "required": ["status"]}, "UpdateAutomatedDiscoveryConfigurationResponse": {"type": "structure", "members": {}}, "UpdateClassificationJobRequest": {"type": "structure", "members": {"jobId": {"shape": "__string", "location": "uri", "locationName": "jobId", "documentation": "<p>The unique identifier for the classification job.</p>"}, "jobStatus": {"shape": "JobStatus", "locationName": "jobStatus", "documentation": "<p>The new status for the job. Valid values are:</p> <ul><li><p><PERSON>NCELLED - Stops the job permanently and cancels it. This value is valid only if the job's current status is IDLE, PAUSED, RUNNING, or USER_PAUSED.</p> <p>If you specify this value and the job's current status is RUNNING, Amazon Macie immediately begins to stop all processing tasks for the job. You can't resume or restart a job after you cancel it.</p></li> <li><p>RUNNING - Resumes the job. This value is valid only if the job's current status is USER_PAUSED.</p> <p>If you paused the job while it was actively running and you specify this value less than 30 days after you paused the job, <PERSON><PERSON> immediately resumes processing from the point where you paused the job. Otherwise, <PERSON><PERSON> resumes the job according to the schedule and other settings for the job.</p></li> <li><p>USER_PAUSED - Pauses the job temporarily. This value is valid only if the job's current status is IDLE, PAUSED, or RUNNING. If you specify this value and the job's current status is RUNNING, <PERSON><PERSON> immediately begins to pause all processing tasks for the job.</p> <p>If you pause a one-time job and you don't resume it within 30 days, the job expires and <PERSON><PERSON> cancels the job. If you pause a recurring job when its status is RUNNING and you don't resume it within 30 days, the job run expires and <PERSON>ie cancels the run. To check the expiration date, refer to the UserPausedDetails.jobExpiresAt property.</p></li></ul>"}}, "required": ["jobId", "jobStatus"]}, "UpdateClassificationJobResponse": {"type": "structure", "members": {}}, "UpdateClassificationScopeRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}, "s3": {"shape": "S3ClassificationScopeUpdate", "locationName": "s3", "documentation": "<p>The S3 buckets to add or remove from the exclusion list defined by the classification scope.</p>"}}, "required": ["id"]}, "UpdateClassificationScopeResponse": {"type": "structure", "members": {}}, "UpdateFindingsFilterRequest": {"type": "structure", "members": {"action": {"shape": "FindingsFilterAction", "locationName": "action", "documentation": "<p>The action to perform on findings that match the filter criteria (findingCriteria). Valid values are: ARCHIVE, suppress (automatically archive) the findings; and, NOOP, don't perform any action on the findings.</p>"}, "clientToken": {"shape": "__string", "locationName": "clientToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "description": {"shape": "__string", "locationName": "description", "documentation": "<p>A custom description of the filter. The description can contain as many as 512 characters.</p> <p>We strongly recommend that you avoid including any sensitive data in the description of a filter. Other users of your account might be able to see this description, depending on the actions that they're allowed to perform in Amazon Macie.</p>"}, "findingCriteria": {"shape": "FindingCriteria", "locationName": "findingCriteria", "documentation": "<p>The criteria to use to filter findings.</p>"}, "id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}, "name": {"shape": "__string", "locationName": "name", "documentation": "<p>A custom name for the filter. The name must contain at least 3 characters and can contain as many as 64 characters.</p> <p>We strongly recommend that you avoid including any sensitive data in the name of a filter. Other users of your account might be able to see this name, depending on the actions that they're allowed to perform in Amazon Macie.</p>"}, "position": {"shape": "__integer", "locationName": "position", "documentation": "<p>The position of the filter in the list of saved filters on the Amazon Macie console. This value also determines the order in which the filter is applied to findings, relative to other filters that are also applied to the findings.</p>"}}, "required": ["id"]}, "UpdateFindingsFilterResponse": {"type": "structure", "members": {"arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the filter that was updated.</p>"}, "id": {"shape": "__string", "locationName": "id", "documentation": "<p>The unique identifier for the filter that was updated.</p>"}}}, "UpdateMacieSessionRequest": {"type": "structure", "members": {"findingPublishingFrequency": {"shape": "FindingPublishingFrequency", "locationName": "findingPublishingFrequency", "documentation": "<p>Specifies how often to publish updates to policy findings for the account. This includes publishing updates to Security Hub and Amazon EventBridge (formerly Amazon CloudWatch Events).</p>"}, "status": {"shape": "<PERSON>ie<PERSON><PERSON><PERSON>", "locationName": "status", "documentation": "<p>Specifies a new status for the account. Valid values are: ENABLED, resume all Amazon Macie activities for the account; and, PAUSED, suspend all Macie activities for the account.</p>"}}}, "UpdateMacieSessionResponse": {"type": "structure", "members": {}}, "UpdateMemberSessionRequest": {"type": "structure", "members": {"id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}, "status": {"shape": "<PERSON>ie<PERSON><PERSON><PERSON>", "locationName": "status", "documentation": "<p>Specifies the new status for the account. Valid values are: ENABLED, resume all Amazon Macie activities for the account; and, PAUSED, suspend all Macie activities for the account.</p>"}}, "required": ["id", "status"]}, "UpdateMemberSessionResponse": {"type": "structure", "members": {}}, "UpdateOrganizationConfigurationRequest": {"type": "structure", "members": {"autoEnable": {"shape": "__boolean", "locationName": "autoEnable", "documentation": "<p>Specifies whether to enable Amazon Macie automatically for an account when the account is added to the organization in Organizations.</p>"}}, "required": ["autoEnable"]}, "UpdateOrganizationConfigurationResponse": {"type": "structure", "members": {}}, "UpdateResourceProfileDetectionsRequest": {"type": "structure", "members": {"resourceArn": {"shape": "__string", "location": "querystring", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket that the request applies to.</p>"}, "suppressDataIdentifiers": {"shape": "__listOfSuppressDataIdentifier", "locationName": "suppressDataIdentifiers", "documentation": "<p>An array of objects, one for each custom data identifier or managed data identifier that detected the type of sensitive data to start excluding or including in the bucket's score. To start including all sensitive data types in the score, don't specify any values for this array.</p>"}}, "required": ["resourceArn"]}, "UpdateResourceProfileDetectionsResponse": {"type": "structure", "members": {}}, "UpdateResourceProfileRequest": {"type": "structure", "members": {"resourceArn": {"shape": "__string", "location": "querystring", "locationName": "resourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket that the request applies to.</p>"}, "sensitivityScoreOverride": {"shape": "__integer", "locationName": "sensitivityScoreOverride", "documentation": "<p>The new sensitivity score for the bucket. Valid values are: 100, assign the maximum score and apply the <i>Sensitive</i> label to the bucket; and, null (empty), assign a score that Amazon Macie calculates automatically after you submit the request.</p>"}}, "required": ["resourceArn"]}, "UpdateResourceProfileResponse": {"type": "structure", "members": {}}, "UpdateRevealConfigurationRequest": {"type": "structure", "members": {"configuration": {"shape": "RevealConfiguration", "locationName": "configuration", "documentation": "<p>The new configuration settings and the status of the configuration for the account.</p>"}}, "required": ["configuration"]}, "UpdateRevealConfigurationResponse": {"type": "structure", "members": {"configuration": {"shape": "RevealConfiguration", "locationName": "configuration", "documentation": "<p>The new configuration settings and the status of the configuration for the account.</p>"}}}, "UpdateSensitivityInspectionTemplateRequest": {"type": "structure", "members": {"description": {"shape": "__string", "locationName": "description", "documentation": "<p>A custom description of the template. The description can contain as many as 200 characters.</p>"}, "excludes": {"shape": "SensitivityInspectionTemplateExcludes", "locationName": "excludes", "documentation": " <p>The managed data identifiers to explicitly exclude (not use) when analyzing data.</p> <p>To exclude an allow list or custom data identifier that's currently included by the template, update the values for the SensitivityInspectionTemplateIncludes.allowListIds and SensitivityInspectionTemplateIncludes.customDataIdentifierIds properties, respectively.</p>"}, "id": {"shape": "__string", "location": "uri", "locationName": "id", "documentation": "<p>The unique identifier for the Amazon Macie resource that the request applies to.</p>"}, "includes": {"shape": "SensitivityInspectionTemplateIncludes", "locationName": "includes", "documentation": "<p>The allow lists, custom data identifiers, and managed data identifiers to include (use) when analyzing data.</p>"}}, "required": ["id"]}, "UpdateSensitivityInspectionTemplateResponse": {"type": "structure", "members": {}}, "UsageByAccount": {"type": "structure", "members": {"currency": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "locationName": "currency", "documentation": "<p>The type of currency that the value for the metric (estimatedCost) is reported in.</p>"}, "estimatedCost": {"shape": "__string", "locationName": "estimatedCost", "documentation": "<p>The estimated value for the metric.</p>"}, "serviceLimit": {"shape": "ServiceLimit", "locationName": "serviceLimit", "documentation": "<p>The current value for the quota that corresponds to the metric specified by the type field.</p>"}, "type": {"shape": "UsageType", "locationName": "type", "documentation": "<p>The name of the metric. Possible values are: AUTOMATED_OBJECT_MONITORING, to monitor S3 objects for automated sensitive data discovery; AUTOMATED_SENSITIVE_DATA_DISCOVERY, to analyze S3 objects for automated sensitive data discovery; DATA_INVENTORY_EVALUATION, to monitor S3 buckets; and, SENSITIVE_DATA_DISCOVERY, to run classification jobs.</p>"}}, "documentation": "<p>Provides data for a specific usage metric and the corresponding quota for an Amazon Macie account.</p>"}, "UsageRecord": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account that the data applies to.</p>"}, "automatedDiscoveryFreeTrialStartDate": {"shape": "__timestampIso8601", "locationName": "automatedDiscoveryFreeTrialStartDate", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the free trial of automated sensitive data discovery started for the account. If the account is a member account in an organization, this value is the same as the value for the organization's Amazon Macie administrator account.</p>"}, "freeTrialStartDate": {"shape": "__timestampIso8601", "locationName": "freeTrialStartDate", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the Amazon Macie free trial started for the account.</p>"}, "usage": {"shape": "__listOfUsageByAccount", "locationName": "usage", "documentation": "<p>An array of objects that contains usage data and quotas for the account. Each object contains the data for a specific usage metric and the corresponding quota.</p>"}}, "documentation": "<p>Provides quota and aggregated usage data for an Amazon Macie account.</p>"}, "UsageStatisticsFilter": {"type": "structure", "members": {"comparator": {"shape": "UsageStatisticsFilterComparator", "locationName": "comparator", "documentation": "<p>The operator to use in the condition. If the value for the key property is accountId, this value must be CONTAINS. If the value for the key property is any other supported field, this value can be EQ, GT, GTE, LT, LTE, or NE.</p>"}, "key": {"shape": "UsageStatisticsFilter<PERSON>ey", "locationName": "key", "documentation": "<p>The field to use in the condition.</p>"}, "values": {"shape": "__listOf__string", "locationName": "values", "documentation": "<p>An array that lists values to use in the condition, based on the value for the field specified by the key property. If the value for the key property is accountId, this array can specify multiple values. Otherwise, this array can specify only one value.</p> <p>Valid values for each supported field are:</p> <ul><li><p>accountId - The unique identifier for an Amazon Web Services account.</p></li> <li><p>freeTrialStartDate - The date and time, in UTC and extended ISO 8601 format, when the Amazon Macie free trial started for an account.</p></li> <li><p>serviceLimit - A Boolean (true or false) value that indicates whether an account has reached its monthly quota.</p></li> <li><p>total - A string that represents the current estimated cost for an account.</p></li></ul>"}}, "documentation": "<p>Specifies a condition for filtering the results of a query for quota and usage data for one or more Amazon Macie accounts.</p>"}, "UsageStatisticsFilterComparator": {"type": "string", "documentation": "<p>The operator to use in a condition that filters the results of a query for Amazon Macie account quotas and usage data. Valid values are:</p>", "enum": ["GT", "GTE", "LT", "LTE", "EQ", "NE", "CONTAINS"]}, "UsageStatisticsFilterKey": {"type": "string", "documentation": "<p>The field to use in a condition that filters the results of a query for Amazon Macie account quotas and usage data. Valid values are:</p>", "enum": ["accountId", "serviceLimit", "freeTrialStartDate", "total"]}, "UsageStatisticsSortBy": {"type": "structure", "members": {"key": {"shape": "UsageStatisticsSortKey", "locationName": "key", "documentation": "<p>The field to sort the results by.</p>"}, "orderBy": {"shape": "OrderBy", "locationName": "orderBy", "documentation": "<p>The sort order to apply to the results, based on the value for the field specified by the key property. Valid values are: ASC, sort the results in ascending order; and, DESC, sort the results in descending order.</p>"}}, "documentation": "<p>Specifies criteria for sorting the results of a query for Amazon Macie account quotas and usage data.</p>"}, "UsageStatisticsSortKey": {"type": "string", "documentation": "<p>The field to use to sort the results of a query for Amazon Macie account quotas and usage data. Valid values are:</p>", "enum": ["accountId", "total", "serviceLimitValue", "freeTrialStartDate"]}, "UsageTotal": {"type": "structure", "members": {"currency": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "locationName": "currency", "documentation": "<p>The type of currency that the value for the metric (estimatedCost) is reported in.</p>"}, "estimatedCost": {"shape": "__string", "locationName": "estimatedCost", "documentation": "<p>The estimated value for the metric.</p>"}, "type": {"shape": "UsageType", "locationName": "type", "documentation": "<p>The name of the metric. Possible values are: AUTOMATED_OBJECT_MONITORING, to monitor S3 objects for automated sensitive data discovery; AUTOMATED_SENSITIVE_DATA_DISCOVERY, to analyze S3 objects for automated sensitive data discovery; DATA_INVENTORY_EVALUATION, to monitor S3 buckets; and, SENSITIVE_DATA_DISCOVERY, to run classification jobs.</p>"}}, "documentation": "<p>Provides aggregated data for an Amazon Macie usage metric. The value for the metric reports estimated usage data for an account for the preceding 30 days or the current calendar month to date, depending on the time period (timeRange) specified in the request.</p>"}, "UsageType": {"type": "string", "documentation": "<p>The name of an Amazon Macie usage metric for an account. Possible values are:</p>", "enum": ["DATA_INVENTORY_EVALUATION", "SENSITIVE_DATA_DISCOVERY", "AUTOMATED_SENSITIVE_DATA_DISCOVERY", "AUTOMATED_OBJECT_MONITORING"]}, "UserIdentity": {"type": "structure", "members": {"assumedRole": {"shape": "AssumedRole", "locationName": "assumedRole", "documentation": "<p>If the action was performed with temporary security credentials that were obtained using the AssumeRole operation of the Security Token Service (STS) API, the identifiers, session context, and other details about the identity.</p>"}, "awsAccount": {"shape": "AwsAccount", "locationName": "awsAccount", "documentation": "<p>If the action was performed using the credentials for another Amazon Web Services account, the details of that account.</p>"}, "awsService": {"shape": "AwsService", "locationName": "awsService", "documentation": "<p>If the action was performed by an Amazon Web Services account that belongs to an Amazon Web Service, the name of the service.</p>"}, "federatedUser": {"shape": "FederatedUser", "locationName": "federatedUser", "documentation": "<p>If the action was performed with temporary security credentials that were obtained using the GetFederationToken operation of the Security Token Service (STS) API, the identifiers, session context, and other details about the identity.</p>"}, "iamUser": {"shape": "IamUser", "locationName": "iamUser", "documentation": "<p>If the action was performed using the credentials for an Identity and Access Management (IAM) user, the name and other details about the user.</p>"}, "root": {"shape": "UserIdentityRoot", "locationName": "root", "documentation": "<p>If the action was performed using the credentials for your Amazon Web Services account, the details of your account.</p>"}, "type": {"shape": "UserIdentityType", "locationName": "type", "documentation": "<p>The type of entity that performed the action.</p>"}}, "documentation": "<p>Provides information about the type and other characteristics of an entity that performed an action on an affected resource.</p>"}, "UserIdentityRoot": {"type": "structure", "members": {"accountId": {"shape": "__string", "locationName": "accountId", "documentation": "<p>The unique identifier for the Amazon Web Services account.</p>"}, "arn": {"shape": "__string", "locationName": "arn", "documentation": "<p>The Amazon Resource Name (ARN) of the principal that performed the action. The last section of the ARN contains the name of the user or role that performed the action.</p>"}, "principalId": {"shape": "__string", "locationName": "principalId", "documentation": "<p>The unique identifier for the entity that performed the action.</p>"}}, "documentation": "<p>Provides information about an Amazon Web Services account and entity that performed an action on an affected resource. The action was performed using the credentials for your Amazon Web Services account.</p>"}, "UserIdentityType": {"type": "string", "documentation": "<p>The type of entity that performed the action on the affected resource. Possible values are:</p>", "enum": ["AssumedRole", "IAMUser", "FederatedUser", "Root", "AWSAccount", "AWSService"]}, "UserPausedDetails": {"type": "structure", "members": {"jobExpiresAt": {"shape": "__timestampIso8601", "locationName": "jobExpiresAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when the job or job run will expire and be cancelled if you don't resume it first.</p>"}, "jobImminentExpirationHealthEventArn": {"shape": "__string", "locationName": "jobImminentExpirationHealthEventArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Health event that Amazon Macie sent to notify you of the job or job run's pending expiration and cancellation. This value is null if a job has been paused for less than 23 days.</p>"}, "jobPausedAt": {"shape": "__timestampIso8601", "locationName": "jobPausedAt", "documentation": "<p>The date and time, in UTC and extended ISO 8601 format, when you paused the job.</p>"}}, "documentation": "<p>Provides information about when a classification job was paused. For a one-time job, this object also specifies when the job will expire and be cancelled if it isn't resumed. For a recurring job, this object also specifies when the paused job run will expire and be cancelled if it isn't resumed. This object is present only if a job's current status (jobStatus) is USER_PAUSED. The information in this object applies only to a job that was paused while it had a status of RUNNING.</p>"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "__string", "locationName": "message", "documentation": "<p>The explanation of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an error that occurred due to a syntax error in a request.</p>", "exception": true, "error": {"httpStatusCode": 400}}, "WeeklySchedule": {"type": "structure", "members": {"dayOfWeek": {"shape": "DayOfWeek", "locationName": "dayOfWeek", "documentation": "<p>The day of the week when <PERSON> Macie runs the job.</p>"}}, "documentation": "<p>Specifies a weekly recurrence pattern for running a classification job.</p>"}, "__boolean": {"type": "boolean"}, "__double": {"type": "double"}, "__integer": {"type": "integer"}, "__listOfAdminAccount": {"type": "list", "member": {"shape": "AdminAccount"}}, "__listOfAllowListSummary": {"type": "list", "member": {"shape": "AllowListSummary"}}, "__listOfBatchGetCustomDataIdentifierSummary": {"type": "list", "member": {"shape": "BatchGetCustomDataIdentifierSummary"}}, "__listOfBucketMetadata": {"type": "list", "member": {"shape": "BucketMetadata"}}, "__listOfClassificationScopeSummary": {"type": "list", "member": {"shape": "ClassificationScopeSummary"}}, "__listOfCriteriaForJob": {"type": "list", "member": {"shape": "CriteriaForJob"}}, "__listOfCustomDataIdentifierSummary": {"type": "list", "member": {"shape": "CustomDataIdentifierSummary"}}, "__listOfDetectedDataDetails": {"type": "list", "member": {"shape": "DetectedDataDetails"}}, "__listOfDetection": {"type": "list", "member": {"shape": "Detection"}}, "__listOfFinding": {"type": "list", "member": {"shape": "Finding"}}, "__listOfFindingType": {"type": "list", "member": {"shape": "FindingType"}}, "__listOfFindingsFilterListItem": {"type": "list", "member": {"shape": "FindingsFilterListItem"}}, "__listOfGroupCount": {"type": "list", "member": {"shape": "GroupCount"}}, "__listOfInvitation": {"type": "list", "member": {"shape": "Invitation"}}, "__listOfJobScopeTerm": {"type": "list", "member": {"shape": "JobScopeTerm"}}, "__listOfJobSummary": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "__listOfKeyValuePair": {"type": "list", "member": {"shape": "KeyValuePair"}}, "__listOfListJobsFilterTerm": {"type": "list", "member": {"shape": "ListJobsFilterTerm"}}, "__listOfManagedDataIdentifierSummary": {"type": "list", "member": {"shape": "ManagedDataIdentifierSummary"}}, "__listOfMatchingResource": {"type": "list", "member": {"shape": "MatchingResource"}}, "__listOfMember": {"type": "list", "member": {"shape": "Member"}}, "__listOfResourceProfileArtifact": {"type": "list", "member": {"shape": "ResourceProfileArtifact"}}, "__listOfS3BucketDefinitionForJob": {"type": "list", "member": {"shape": "S3BucketDefinitionForJob"}}, "__listOfS3BucketName": {"type": "list", "member": {"shape": "S3BucketName"}}, "__listOfSearchResourcesCriteria": {"type": "list", "member": {"shape": "SearchResourcesCriteria"}}, "__listOfSearchResourcesTagCriterionPair": {"type": "list", "member": {"shape": "SearchResourcesTagCriterionPair"}}, "__listOfSensitivityInspectionTemplatesEntry": {"type": "list", "member": {"shape": "SensitivityInspectionTemplatesEntry"}}, "__listOfSuppressDataIdentifier": {"type": "list", "member": {"shape": "SuppressDataIdentifier"}}, "__listOfTagCriterionPairForJob": {"type": "list", "member": {"shape": "TagCriterionPairForJob"}}, "__listOfTagValuePair": {"type": "list", "member": {"shape": "TagValuePair"}}, "__listOfUnavailabilityReasonCode": {"type": "list", "min": 0, "member": {"shape": "UnavailabilityReasonCode"}}, "__listOfUnprocessedAccount": {"type": "list", "member": {"shape": "UnprocessedAccount"}}, "__listOfUsageByAccount": {"type": "list", "member": {"shape": "UsageByAccount"}}, "__listOfUsageRecord": {"type": "list", "member": {"shape": "UsageRecord"}}, "__listOfUsageStatisticsFilter": {"type": "list", "member": {"shape": "UsageStatisticsFilter"}}, "__listOfUsageTotal": {"type": "list", "member": {"shape": "UsageTotal"}}, "__listOf__string": {"type": "list", "member": {"shape": "__string"}}, "__long": {"type": "long"}, "__string": {"type": "string"}, "__stringMin1Max1024PatternSS": {"type": "string", "min": 1, "max": 1024, "pattern": "^[\\s\\S]+$"}, "__stringMin1Max128": {"type": "string", "min": 1, "max": 128}, "__stringMin1Max128Pattern": {"type": "string", "min": 1, "max": 128, "pattern": "^.+$"}, "__stringMin1Max2048": {"type": "string", "min": 1, "max": 2048}, "__stringMin1Max512PatternSS": {"type": "string", "min": 1, "max": 512, "pattern": "^[\\s\\S]+$"}, "__stringMin22Max22PatternAZ0922": {"type": "string", "min": 22, "max": 22, "pattern": "^[a-z0-9]{22}$"}, "__stringMin3Max255PatternAZaZ093255": {"type": "string", "min": 3, "max": 255, "pattern": "^[A-Za-z0-9.\\-_]{3,255}$"}, "__stringMin71Max89PatternArnAwsAwsCnAwsUsGovMacie2AZ19920D12AllowListAZ0922": {"type": "string", "min": 71, "max": 89, "pattern": "^arn:(aws|aws-cn|aws-us-gov):macie2:[a-z1-9-]{9,20}:\\d{12}:allow-list\\/[a-z0-9]{22}$"}, "__timestampIso8601": {"type": "timestamp", "timestampFormat": "iso8601"}, "__timestampUnix": {"type": "timestamp", "timestampFormat": "unixTimestamp"}}, "documentation": "<p><PERSON> Macie</p>"}