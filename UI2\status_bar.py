from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel, QSizePolicy, QProgressBar, QSpacerItem
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QCursor
from language_manager import get_text, set_language

class StatusBar(QWidget):
    progress_signal = pyqtSignal(int)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(28)
        self.setStyleSheet('''
            QWidget {
                background: #f3f3f3;
                border-top: 1px solid #e0e0e0;
            }
            QLabel {
                color: #444;
                font-size: 13px;
                font-family: 'Noto Sans CJK SC', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', sans-serif;
                padding: 0 12px;
            }
            QLabel#offlineLabel {
                background: transparent;
                border-radius: 4px;
                max-width: 80px;
            }
            QLabel#offlineLabel:hover {
                background: #ffe0b2;
                color: #1976d2;
            }
            QLabel#errorLabel {
                color: #d32f2f;
                font-weight: bold;          
            }
        ''')
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setSpacing(0)

        # 离线标签
        self.offline_label = QLabel(get_text("offline"))
        self.offline_label.setObjectName("offlineLabel")
        self.offline_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        self.offline_label.setMaximumWidth(50)
        self.offline_label.setToolTip(get_text("device_status"))
        layout.addWidget(self.offline_label)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)  # 显示文本
        self.progress_bar.setFixedHeight(12)
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        self.progress_bar.setToolTip(get_text("measurement_progress_bar"))
        self.progress_bar.setStyleSheet('''
            QProgressBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 1px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4facfe, stop:0.2 #00f2fe, stop:0.5 #4facfe, stop:0.8 #00f2fe, stop:1 #4facfe);
                border-radius: 5px;
                margin: 1px;
                /* 镜面效果边框 */
                border-top: 1px solid rgba(255, 255, 255, 0.6);
                border-left: 1px solid rgba(255, 255, 255, 0.4);
                border-right: 1px solid rgba(255, 255, 255, 0.2);
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }
            QProgressBar::chunk:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.2 #764ba2, stop:0.5 #667eea, stop:0.8 #764ba2, stop:1 #667eea);
            }
        ''')
        layout.addWidget(self.progress_bar)
        self.progress_signal.connect(self.set_progress)

        # 故障标签
        self.error_label = QLabel(get_text("communication_error_exclamation"))
        self.error_label.setObjectName("errorLabel")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignLeft| Qt.AlignmentFlag.AlignVCenter)
        self.error_label.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Preferred)
        self.error_label.setToolTip(get_text("communication_error"))
        self.error_label.setStyleSheet('QLabel#errorLabel { qproperty-wordWrap: false; }')
        layout.addWidget(self.error_label)

        # 用户信息标签
        self.user_label = QLabel("未登录")
        self.user_label.setObjectName("userLabel")
        self.user_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        self.user_label.setMaximumWidth(150)
        self.user_label.setToolTip(get_text("current_user"))
        self.user_label.setStyleSheet("""
            QLabel#userLabel {
                color: #1976d2;
                font-weight: bold;
                padding: 0 8px;
                border-left: 2px solid #1976d2;
                margin-left: 10px;
            }
        """)
        layout.addWidget(self.user_label)

        # 添加弹性空间，让内容都靠左
        layout.addSpacerItem(QSpacerItem(0, 0, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))

        self.language = 'zh'

    def set_status(self, text: str):
        self.offline_label.setText(text)

    def set_progress(self, value: int):
        self.progress_bar.setValue(value)
        # 显示百分比文本
        if value > 0:
            self.progress_bar.setFormat(f"{value}%")
        else:
            self.progress_bar.setFormat("")

    def set_error(self, text: str):
        self.error_label.setText(text)

    def set_online(self):
        self.offline_label.setText(get_text('online'))
        self.error_label.hide()

    def set_offline(self):
        self.offline_label.setText('离线')
        self.error_label.show()

    def set_user_info(self, username, role):
        """设置用户信息"""
        role_text = "管理员" if role == "admin" else "普通用户"
        self.user_label.setText(f"{username} ({role_text})")
    
    def update_language(self, language):
        set_language(language)
        self.language = language
        if language == 'zh':
            self.offline_label.setText(get_text('offline'))
            self.error_label.setText(get_text('communication_error_exclamation'))
        else:
            self.offline_label.setText(get_text('offline'))
            self.error_label.setText(get_text('communication_error_exclamation')) 