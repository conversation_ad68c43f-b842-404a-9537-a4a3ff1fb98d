from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel, QSizePolicy, QProgressBar, QSpacerItem, QToolButton
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import <PERSON><PERSON>ursor, QPixmap, QIcon
from language_manager import get_text, set_language

class StatusBar(QWidget):
    progress_signal = pyqtSignal(int)
    device_status_clicked = pyqtSignal(str)  # 设备状态点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(28)
        self.setStyleSheet('''
            QWidget {
                background: #f3f3f3;
                border-top: 1px solid #e0e0e0;
            }
            QLabel {
                color: #444;
                font-size: 13px;
                font-family: 'Noto Sans CJK SC', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'Deja<PERSON><PERSON> Sans', sans-serif;
                padding: 0 12px;
            }
            QLabel#offlineLabel {
                background: transparent;
                border-radius: 4px;
                max-width: 80px;
            }
            QLabel#offlineLabel:hover {
                background: #ffe0b2;
                color: #1976d2;
            }
            QLabel#errorLabel {
                color: #d32f2f;
                font-weight: bold;          
            }
            QLabel#deviceStatusLabel {
                background: transparent;
                border-radius: 4px;
                padding: 2px 8px;
                margin: 0 4px;
                font-size: 11px;
                border: 1px solid #ddd;
            }
            QLabel#deviceStatusLabel:hover {
                background: #e3f2fd;
                border-color: #2196f3;
                cursor: pointer;
            }
            QLabel#spectrometerStatus {
                background: #e8f5e8;
                border-color: #4caf50;
                color: #2e7d32;
            }
            QLabel#laserStatus {
                background: #fff3e0;
                border-color: #ff9800;
                color: #e65100;
            }
            QLabel#powerStatus {
                background: #f3e5f5;
                border-color: #9c27b0;
                color: #6a1b9a;
            }
            QLabel#spectrometerStatus.disconnected {
                background: #ffebee;
                border-color: #f44336;
                color: #c62828;
            }
            QLabel#laserStatus.disconnected {
                background: #ffebee;
                border-color: #f44336;
                color: #c62828;
            }
            QLabel#powerStatus.disconnected {
                background: #ffebee;
                border-color: #f44336;
                color: #c62828;
            }
        ''')
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setSpacing(0)

        # 离线标签
        self.offline_label = QLabel(get_text("offline"))
        self.offline_label.setObjectName("offlineLabel")
        self.offline_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        self.offline_label.setMaximumWidth(50)
        self.offline_label.setToolTip(get_text("device_status"))
        layout.addWidget(self.offline_label)

        # 设备状态指示器
        self.spectrometer_status = QLabel("光谱仪: 未连接")
        self.spectrometer_status.setObjectName("deviceStatusLabel spectrometerStatus disconnected")
        self.spectrometer_status.setToolTip("点击查看光谱仪详细信息")
        self.spectrometer_status.mousePressEvent = lambda e: self.device_status_clicked.emit("spectrometer")
        layout.addWidget(self.spectrometer_status)

        self.laser_status = QLabel("激光器: 未连接")
        self.laser_status.setObjectName("deviceStatusLabel laserStatus disconnected")
        self.laser_status.setToolTip("点击查看激光器详细信息")
        self.laser_status.mousePressEvent = lambda e: self.device_status_clicked.emit("laser")
        layout.addWidget(self.laser_status)

        self.power_status = QLabel("电源: 未连接")
        self.power_status.setObjectName("deviceStatusLabel powerStatus disconnected")
        self.power_status.setToolTip("点击查看电源详细信息")
        self.power_status.mousePressEvent = lambda e: self.device_status_clicked.emit("power")
        layout.addWidget(self.power_status)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)  # 显示文本
        self.progress_bar.setFixedHeight(12)
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        self.progress_bar.setToolTip(get_text("measurement_progress_bar"))
        self.progress_bar.setStyleSheet('''
            QProgressBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 1px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4facfe, stop:0.2 #00f2fe, stop:0.5 #4facfe, stop:0.8 #00f2fe, stop:1 #4facfe);
                border-radius: 5px;
                margin: 1px;
                /* 镜面效果边框 */
                border-top: 1px solid rgba(255, 255, 255, 0.6);
                border-left: 1px solid rgba(255, 255, 255, 0.4);
                border-right: 1px solid rgba(255, 255, 255, 0.2);
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }
            QProgressBar::chunk:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.2 #764ba2, stop:0.5 #667eea, stop:0.8 #764ba2, stop:1 #667eea);
            }
        ''')
        layout.addWidget(self.progress_bar)
        self.progress_signal.connect(self.set_progress)

        # 故障标签
        self.error_label = QLabel(get_text("communication_error_exclamation"))
        self.error_label.setObjectName("errorLabel")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignLeft| Qt.AlignmentFlag.AlignVCenter)
        self.error_label.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Preferred)
        self.error_label.setToolTip(get_text("communication_error"))
        self.error_label.setStyleSheet('QLabel#errorLabel { qproperty-wordWrap: false; }')
        layout.addWidget(self.error_label)

        # 用户信息标签
        self.user_label = QLabel("未登录")
        self.user_label.setObjectName("userLabel")
        self.user_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        self.user_label.setMaximumWidth(150)
        self.user_label.setToolTip(get_text("current_user"))
        self.user_label.setStyleSheet("""
            QLabel#userLabel {
                color: #1976d2;
                font-weight: bold;
                padding: 0 8px;
                border-left: 2px solid #1976d2;
                margin-left: 10px;
            }
        """)
        layout.addWidget(self.user_label)

        # 添加弹性空间，让内容都靠左
        layout.addSpacerItem(QSpacerItem(0, 0, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))

        self.language = 'zh'

    def set_status(self, text: str):
        self.offline_label.setText(text)

    def set_progress(self, value: int):
        self.progress_bar.setValue(value)
        # 显示百分比文本
        if value > 0:
            self.progress_bar.setFormat(f"{value}%")
        else:
            self.progress_bar.setFormat("")

    def set_error(self, text: str):
        self.error_label.setText(text)

    def set_online(self):
        self.offline_label.setText(get_text('online'))
        self.error_label.hide()

    def set_offline(self):
        self.offline_label.setText('离线')
        self.error_label.show()

    def set_user_info(self, username, role):
        """设置用户信息"""
        role_text = "管理员" if role == "admin" else "普通用户"
        self.user_label.setText(f"{username} ({role_text})")
    
    def update_device_status(self, device_type: str, connected: bool, message: str):
        """更新设备状态显示"""
        if device_type == "spectrometer":
            label = self.spectrometer_status
            device_name = "光谱仪"
        elif device_type == "laser":
            label = self.laser_status
            device_name = "激光器"
        elif device_type == "power":
            label = self.power_status
            device_name = "电源"
        else:
            return
        
        # 更新文本和样式
        status_text = "已连接" if connected else "未连接"
        label.setText(f"{device_name}: {status_text}")
        
        # 更新样式类
        if connected:
            label.setProperty("class", "connected")
            label.setStyle(label.style())  # 刷新样式
        else:
            label.setProperty("class", "disconnected")
            label.setStyle(label.style())  # 刷新样式
        
        # 更新工具提示
        label.setToolTip(f"{device_name}: {message}")
    
    def show_connection_attempt(self, device_type: str, message: str):
        """显示连接尝试状态"""
        if device_type == "spectrometer":
            label = self.spectrometer_status
        elif device_type == "laser":
            label = self.laser_status
        elif device_type == "power":
            label = self.power_status
        else:
            return
        
        # 显示连接尝试状态
        label.setText(f"{label.text().split(':')[0]}: 连接中...")
        label.setToolTip(f"正在尝试连接: {message}")
    
    def show_reconnect_result(self, device_type: str, success: bool):
        """显示重连结果"""
        if device_type == "spectrometer":
            label = self.spectrometer_status
            device_name = "光谱仪"
        elif device_type == "laser":
            label = self.laser_status
            device_name = "激光器"
        elif device_type == "power":
            label = self.power_status
            device_name = "电源"
        else:
            return
        
        if success:
            label.setText(f"{device_name}: 已连接")
            label.setProperty("class", "connected")
            label.setToolTip(f"{device_name}: 重连成功")
        else:
            label.setText(f"{device_name}: 连接失败")
            label.setProperty("class", "disconnected")
            label.setToolTip(f"{device_name}: 重连失败，请检查设备")
        
        label.setStyle(label.style())  # 刷新样式
    
    def update_language(self, language):
        set_language(language)
        self.language = language
        if language == 'zh':
            self.offline_label.setText(get_text('offline'))
            self.error_label.setText(get_text('communication_error_exclamation'))
        else:
            self.offline_label.setText(get_text('offline'))
            self.error_label.setText(get_text('communication_error_exclamation')) 