Metadata-Version: 2.1
Name: distributed
Version: 2022.2.1
Summary: Distributed scheduler for Dask
Home-page: https://distributed.dask.org
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: BSD
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: System :: Distributed Computing
Requires-Python: >=3.8
License-File: LICENSE.txt
License-File: AUTHORS.md
Requires-Dist: click (>=6.6)
Requires-Dist: cloudpickle (>=1.5.0)
Requires-Dist: dask (==2022.02.1)
Requires-Dist: jinja2
Requires-Dist: msgpack (>=0.6.0)
Requires-Dist: packaging (>=20.0)
Requires-Dist: psutil (>=5.0)
Requires-Dist: sortedcontainers (!=2.0.0,!=2.0.1)
Requires-Dist: tblib (>=1.6.0)
Requires-Dist: toolz (>=0.8.2)
Requires-Dist: tornado (>=6.0.3)
Requires-Dist: zict (>=0.1.3)
Requires-Dist: pyyaml
Requires-Dist: setuptools

Distributed
===========

|Test Status| |Longitudinal Report| |Coverage| |Doc Status| |Discourse| |Version Status| |NumFOCUS|

A library for distributed computation.  See documentation_ for more details.

.. _documentation: https://distributed.dask.org
.. |Test Status| image:: https://github.com/dask/distributed/workflows/Tests/badge.svg?branch=main
   :target: https://github.com/dask/distributed/actions?query=workflow%3A%22Tests%22
.. |Longitudinal Report| image:: https://github.com/dask/distributed/workflows/Test%20Report/badge.svg?branch=main
   :target: https://dask.github.io/distributed/test_report.html
   :alt: Longitudinal test report
.. |Doc Status| image:: https://readthedocs.org/projects/distributed/badge/?version=latest
   :target: https://distributed.dask.org
   :alt: Documentation Status
.. |Coverage| image:: https://codecov.io/gh/dask/distributed/branch/main/graph/badge.svg
   :target: https://codecov.io/gh/dask/distributed/branch/main
   :alt: Coverage status
.. |Discourse| image:: https://img.shields.io/discourse/users?logo=discourse&server=https%3A%2F%2Fdask.discourse.group
   :alt: Discuss Dask-related things and ask for help
   :target: https://dask.discourse.group
.. |Version Status| image:: https://img.shields.io/pypi/v/distributed.svg
   :target: https://pypi.python.org/pypi/distributed/
.. |NumFOCUS| image:: https://img.shields.io/badge/powered%20by-NumFOCUS-orange.svg?style=flat&colorA=E1523D&colorB=007D8A
   :target: https://www.numfocus.org/


