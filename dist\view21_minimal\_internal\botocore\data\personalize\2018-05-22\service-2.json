{"version": "2.0", "metadata": {"apiVersion": "2018-05-22", "endpointPrefix": "personalize", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Personalize", "serviceId": "Personalize", "signatureVersion": "v4", "signingName": "personalize", "targetPrefix": "AmazonPersonalize", "uid": "personalize-2018-05-22"}, "operations": {"CreateBatchInferenceJob": {"name": "CreateBatchInferenceJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateBatchInferenceJobRequest"}, "output": {"shape": "CreateBatchInferenceJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a batch inference job. The operation can handle up to 50 million records and the input file must be in JSON format. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/creating-batch-inference-job.html\">Creating a batch inference job</a>. </p>"}, "CreateBatchSegmentJob": {"name": "CreateBatchSegmentJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateBatchSegmentJobRequest"}, "output": {"shape": "CreateBatchSegmentJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a batch segment job. The operation can handle up to 50 million records and the input file must be in JSON format. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/recommendations-batch.html\">Getting batch recommendations and user segments</a>.</p>"}, "CreateCampaign": {"name": "CreateCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCampaignRequest"}, "output": {"shape": "CreateCampaignResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a campaign that deploys a solution version. When a client calls the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> and <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetPersonalizedRanking.html\">GetPersonalizedRanking</a> APIs, a campaign is specified in the request.</p> <p> <b>Minimum Provisioned TPS and Auto-Scaling</b> </p> <important> <p> A high <code>minProvisionedTPS</code> will increase your bill. We recommend starting with 1 for <code>minProvisionedTPS</code> (the default). Track your usage using Amazon CloudWatch metrics, and increase the <code>minProvisionedTPS</code> as necessary.</p> </important> <p>A transaction is a single <code>GetRecommendations</code> or <code>GetPersonalizedRanking</code> call. Transactions per second (TPS) is the throughput and unit of billing for Amazon Personalize. The minimum provisioned TPS (<code>minProvisionedTPS</code>) specifies the baseline throughput provisioned by Amazon Personalize, and thus, the minimum billing charge. </p> <p> If your TPS increases beyond <code>minProvisionedTPS</code>, Amazon Personalize auto-scales the provisioned capacity up and down, but never below <code>minProvisionedTPS</code>. There's a short time delay while the capacity is increased that might cause loss of transactions.</p> <p>The actual TPS used is calculated as the average requests/second within a 5-minute window. You pay for maximum of either the minimum provisioned TPS or the actual TPS. We recommend starting with a low <code>minProvisionedTPS</code>, track your usage using Amazon CloudWatch metrics, and then increase the <code>minProvisionedTPS</code> as necessary.</p> <p> <b>Status</b> </p> <p>A campaign can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>To get the campaign status, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeCampaign.html\">DescribeCampaign</a>.</p> <note> <p>Wait until the <code>status</code> of the campaign is <code>ACTIVE</code> before asking the campaign for recommendations.</p> </note> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListCampaigns.html\">ListCampaigns</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeCampaign.html\">DescribeCampaign</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_UpdateCampaign.html\">UpdateCampaign</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteCampaign.html\">DeleteCampaign</a> </p> </li> </ul>", "idempotent": true}, "CreateDataset": {"name": "CreateDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetRequest"}, "output": {"shape": "CreateDatasetResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates an empty dataset and adds it to the specified dataset group. Use <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a> to import your training data to a dataset.</p> <p>There are three types of datasets:</p> <ul> <li> <p>Interactions</p> </li> <li> <p>Items</p> </li> <li> <p>Users</p> </li> </ul> <p>Each dataset type has an associated schema with required field types. Only the <code>Interactions</code> dataset is required in order to train a model (also referred to as creating a solution).</p> <p>A dataset can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>To get the status of the dataset, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDataset.html\">DescribeDataset</a>.</p> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListDatasets.html\">ListDatasets</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDataset.html\">DescribeDataset</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteDataset.html\">DeleteDataset</a> </p> </li> </ul>", "idempotent": true}, "CreateDatasetExportJob": {"name": "CreateDatasetExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetExportJobRequest"}, "output": {"shape": "CreateDatasetExportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p> Creates a job that exports data from your dataset to an Amazon S3 bucket. To allow Amazon Personalize to export the training data, you must specify an service-linked IAM role that gives Amazon Personalize <code>PutObject</code> permissions for your Amazon S3 bucket. For information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/export-data.html\">Exporting a dataset</a> in the Amazon Personalize developer guide. </p> <p> <b>Status</b> </p> <p>A dataset export job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul> <p> To get the status of the export job, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetExportJob.html\">DescribeDatasetExportJob</a>, and specify the Amazon Resource Name (ARN) of the dataset export job. The dataset export is complete when the status shows as ACTIVE. If the status shows as CREATE FAILED, the response includes a <code>failureReason</code> key, which describes why the job failed. </p>", "idempotent": true}, "CreateDatasetGroup": {"name": "CreateDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetGroupRequest"}, "output": {"shape": "CreateDatasetGroupResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates an empty dataset group. A dataset group is a container for Amazon Personalize resources. A dataset group can contain at most three datasets, one for each type of dataset:</p> <ul> <li> <p>Interactions</p> </li> <li> <p>Items</p> </li> <li> <p>Users</p> </li> </ul> <p> A dataset group can be a Domain dataset group, where you specify a domain and use pre-configured resources like recommenders, or a Custom dataset group, where you use custom resources, such as a solution with a solution version, that you deploy with a campaign. If you start with a Domain dataset group, you can still add custom resources such as solutions and solution versions trained with recipes for custom use cases and deployed with campaigns. </p> <p>A dataset group can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING</p> </li> </ul> <p>To get the status of the dataset group, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a>. If the status shows as CREATE FAILED, the response includes a <code>failureReason</code> key, which describes why the creation failed.</p> <note> <p>You must wait until the <code>status</code> of the dataset group is <code>ACTIVE</code> before adding a dataset to the group.</p> </note> <p>You can specify an Key Management Service (KMS) key to encrypt the datasets in the group. If you specify a KMS key, you must also include an Identity and Access Management (IAM) role that has permission to access the key.</p> <p class=\"title\"> <b>APIs that require a dataset group ARN in the request</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateEventTracker.html\">CreateEventTracker</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a> </p> </li> </ul> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListDatasetGroups.html\">ListDatasetGroups</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteDatasetGroup.html\">DeleteDatasetGroup</a> </p> </li> </ul>"}, "CreateDatasetImportJob": {"name": "CreateDatasetImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDatasetImportJobRequest"}, "output": {"shape": "CreateDatasetImportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a job that imports training data from your data source (an Amazon S3 bucket) to an Amazon Personalize dataset. To allow Amazon Personalize to import the training data, you must specify an IAM service role that has permission to read from the data source, as Amazon Personalize makes a copy of your data and processes it internally. For information on granting access to your Amazon S3 bucket, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/granting-personalize-s3-access.html\">Giving Amazon Personalize Access to Amazon S3 Resources</a>. </p> <important> <p>By default, a dataset import job replaces any existing data in the dataset that you imported in bulk. To add new records without replacing existing data, specify INCREMENTAL for the import mode in the CreateDatasetImportJob operation.</p> </important> <p> <b>Status</b> </p> <p>A dataset import job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul> <p>To get the status of the import job, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetImportJob.html\">DescribeDatasetImportJob</a>, providing the Amazon Resource Name (ARN) of the dataset import job. The dataset import is complete when the status shows as ACTIVE. If the status shows as CREATE FAILED, the response includes a <code>failureReason</code> key, which describes why the job failed.</p> <note> <p>Importing takes time. You must wait until the status shows as ACTIVE before training a model using the dataset.</p> </note> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListDatasetImportJobs.html\">ListDatasetImportJobs</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetImportJob.html\">DescribeDatasetImportJob</a> </p> </li> </ul>"}, "CreateEventTracker": {"name": "CreateEventTracker", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEventTrackerRequest"}, "output": {"shape": "CreateEventTrackerResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates an event tracker that you use when adding event data to a specified dataset group using the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_UBS_PutEvents.html\">PutEvents</a> API.</p> <note> <p>Only one event tracker can be associated with a dataset group. You will get an error if you call <code>CreateEventTracker</code> using the same dataset group as an existing event tracker.</p> </note> <p>When you create an event tracker, the response includes a tracking ID, which you pass as a parameter when you use the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_UBS_PutEvents.html\">PutEvents</a> operation. Amazon Personalize then appends the event data to the Interactions dataset of the dataset group you specify in your event tracker. </p> <p>The event tracker can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>To get the status of the event tracker, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeEventTracker.html\">DescribeEventTracker</a>.</p> <note> <p>The event tracker must be in the ACTIVE state before using the tracking ID.</p> </note> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListEventTrackers.html\">ListEventTrackers</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeEventTracker.html\">DescribeEventTracker</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteEventTracker.html\">DeleteEventTracker</a> </p> </li> </ul>", "idempotent": true}, "CreateFilter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFilterRequest"}, "output": {"shape": "CreateFilterResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a recommendation filter. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter.html\">Filtering recommendations and user segments</a>.</p>"}, "CreateMetricAttribution": {"name": "CreateMetricAttribution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateMetricAttributionRequest"}, "output": {"shape": "CreateMetricAttributionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a metric attribution. A metric attribution creates reports on the data that you import into Amazon Personalize. Depending on how you imported the data, you can view reports in Amazon CloudWatch or Amazon S3. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>.</p>"}, "CreateRecommender": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateRecommenderRequest"}, "output": {"shape": "CreateRecommenderResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates a recommender with the recipe (a Domain dataset group use case) you specify. You create recommenders for a Domain dataset group and specify the recommender's Amazon Resource Name (ARN) when you make a <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> request. </p> <p> <b>Minimum recommendation requests per second</b> </p> <important> <p>A high <code>minRecommendationRequestsPerSecond</code> will increase your bill. We recommend starting with 1 for <code>minRecommendationRequestsPerSecond</code> (the default). Track your usage using Amazon CloudWatch metrics, and increase the <code>minRecommendationRequestsPerSecond</code> as necessary.</p> </important> <p>When you create a recommender, you can configure the recommender's minimum recommendation requests per second. The minimum recommendation requests per second (<code>minRecommendationRequestsPerSecond</code>) specifies the baseline recommendation request throughput provisioned by Amazon Personalize. The default minRecommendationRequestsPerSecond is <code>1</code>. A recommendation request is a single <code>GetRecommendations</code> operation. Request throughput is measured in requests per second and Amazon Personalize uses your requests per second to derive your requests per hour and the price of your recommender usage. </p> <p> If your requests per second increases beyond <code>minRecommendationRequestsPerSecond</code>, Amazon Personalize auto-scales the provisioned capacity up and down, but never below <code>minRecommendationRequestsPerSecond</code>. There's a short time delay while the capacity is increased that might cause loss of requests.</p> <p> Your bill is the greater of either the minimum requests per hour (based on minRecommendationRequestsPerSecond) or the actual number of requests. The actual request throughput used is calculated as the average requests/second within a one-hour window. We recommend starting with the default <code>minRecommendationRequestsPerSecond</code>, track your usage using Amazon CloudWatch metrics, and then increase the <code>minRecommendationRequestsPerSecond</code> as necessary. </p> <p> <b>Status</b> </p> <p>A recommender can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>STOP PENDING &gt; STOP IN_PROGRESS &gt; INACTIVE &gt; START PENDING &gt; START IN_PROGRESS &gt; ACTIVE</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>To get the recommender status, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeRecommender.html\">DescribeRecommender</a>.</p> <note> <p>Wait until the <code>status</code> of the recommender is <code>ACTIVE</code> before asking the recommender for recommendations.</p> </note> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListRecommenders.html\">ListRecommenders</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeRecommender.html\">DescribeRecommender</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_UpdateRecommender.html\">UpdateRecommender</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteRecommender.html\">DeleteRecommender</a> </p> </li> </ul>", "idempotent": true}, "CreateSchema": {"name": "CreateSchema", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSchemaRequest"}, "output": {"shape": "CreateSchemaResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an Amazon Personalize schema from the specified schema string. The schema you create must be in Avro JSON format.</p> <p>Amazon Personalize recognizes three schema variants. Each schema is associated with a dataset type and has a set of required field and keywords. If you are creating a schema for a dataset in a Domain dataset group, you provide the domain of the Domain dataset group. You specify a schema when you call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>.</p> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListSchemas.html\">ListSchemas</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSchema.html\">DescribeSchema</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteSchema.html\">DeleteSchema</a> </p> </li> </ul>", "idempotent": true}, "CreateSolution": {"name": "CreateSolution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSolutionRequest"}, "output": {"shape": "CreateSolutionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Creates the configuration for training a model. A trained model is known as a solution version. After the configuration is created, you train the model (create a solution version) by calling the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolutionVersion.html\">CreateSolutionVersion</a> operation. Every time you call <code>CreateSolutionVersion</code>, a new version of the solution is created.</p> <p>After creating a solution version, you check its accuracy by calling <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_GetSolutionMetrics.html\">GetSolutionMetrics</a>. When you are satisfied with the version, you deploy it using <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateCampaign.html\">CreateCampaign</a>. The campaign provides recommendations to a client through the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> API.</p> <p>To train a model, Amazon Personalize requires training data and a recipe. The training data comes from the dataset group that you provide in the request. A recipe specifies the training algorithm and a feature transformation. You can specify one of the predefined recipes provided by Amazon Personalize. </p> <note> <p>Amazon Personalize doesn't support configuring the <code>hpoObjective</code> for solution hyperparameter optimization at this time.</p> </note> <p> <b>Status</b> </p> <p>A solution can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>To get the status of the solution, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolution.html\">DescribeSolution</a>. Wait until the status shows as ACTIVE before calling <code>CreateSolutionVersion</code>.</p> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListSolutions.html\">ListSolutions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolutionVersion.html\">CreateSolutionVersion</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolution.html\">DescribeSolution</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteSolution.html\">DeleteSolution</a> </p> </li> </ul> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListSolutionVersions.html\">ListSolutionVersions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolutionVersion.html\">DescribeSolutionVersion</a> </p> </li> </ul>"}, "CreateSolutionVersion": {"name": "CreateSolutionVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSolutionVersionRequest"}, "output": {"shape": "CreateSolutionVersionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceInUseException"}, {"shape": "TooManyTagsException"}, {"shape": "ResourceAlreadyExistsException"}], "documentation": "<p>Trains or retrains an active solution in a Custom dataset group. A solution is created using the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a> operation and must be in the ACTIVE state before calling <code>CreateSolutionVersion</code>. A new version of the solution is created every time you call this operation.</p> <p> <b>Status</b> </p> <p>A solution version can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING</p> </li> <li> <p>CREATE IN_PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> <li> <p>CREATE STOPPING</p> </li> <li> <p>CREATE STOPPED</p> </li> </ul> <p>To get the status of the version, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolutionVersion.html\">DescribeSolutionVersion</a>. Wait until the status shows as ACTIVE before calling <code>CreateCampaign</code>.</p> <p>If the status shows as CREATE FAILED, the response includes a <code>failureReason</code> key, which describes why the job failed.</p> <p class=\"title\"> <b>Related APIs</b> </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListSolutionVersions.html\">ListSolutionVersions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolutionVersion.html\">DescribeSolutionVersion</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListSolutions.html\">ListSolutions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolution.html\">DescribeSolution</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DeleteSolution.html\">DeleteSolution</a> </p> </li> </ul>"}, "DeleteCampaign": {"name": "DeleteCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCampaignRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Removes a campaign by deleting the solution deployment. The solution that the campaign is based on is not deleted and can be redeployed when needed. A deleted campaign can no longer be specified in a <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> request. For information on creating campaigns, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateCampaign.html\">CreateCampaign</a>.</p>", "idempotent": true}, "DeleteDataset": {"name": "DeleteDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDatasetRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a dataset. You can't delete a dataset if an associated <code>DatasetImportJob</code> or <code>SolutionVersion</code> is in the CREATE PENDING or IN PROGRESS state. For more information on datasets, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>.</p>", "idempotent": true}, "DeleteDatasetGroup": {"name": "DeleteDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDatasetGroupRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a dataset group. Before you delete a dataset group, you must delete the following:</p> <ul> <li> <p>All associated event trackers.</p> </li> <li> <p>All associated solutions.</p> </li> <li> <p>All datasets in the dataset group.</p> </li> </ul>", "idempotent": true}, "DeleteEventTracker": {"name": "DeleteEventTracker", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEventTrackerRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes the event tracker. Does not delete the event-interactions dataset from the associated dataset group. For more information on event trackers, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateEventTracker.html\">CreateEventTracker</a>.</p>", "idempotent": true}, "DeleteFilter": {"name": "DeleteFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFilterRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a filter.</p>"}, "DeleteMetricAttribution": {"name": "DeleteMetricAttribution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteMetricAttributionRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a metric attribution.</p>", "idempotent": true}, "DeleteRecommender": {"name": "DeleteRecommen<PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRecommenderRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deactivates and removes a recommender. A deleted recommender can no longer be specified in a <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> request.</p>", "idempotent": true}, "DeleteSchema": {"name": "DeleteSchema", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSchemaRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes a schema. Before deleting a schema, you must delete all datasets referencing the schema. For more information on schemas, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSchema.html\">CreateSchema</a>.</p>", "idempotent": true}, "DeleteSolution": {"name": "DeleteSolution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSolutionRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Deletes all versions of a solution and the <code>Solution</code> object itself. Before deleting a solution, you must delete all campaigns based on the solution. To determine what campaigns are using the solution, call <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListCampaigns.html\">ListCampaigns</a> and supply the Amazon Resource Name (ARN) of the solution. You can't delete a solution if an associated <code>SolutionVersion</code> is in the CREATE PENDING or IN PROGRESS state. For more information on solutions, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a>.</p>", "idempotent": true}, "DescribeAlgorithm": {"name": "DescribeAlgorithm", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAlgorithmRequest"}, "output": {"shape": "DescribeAlgorithmResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given algorithm.</p>", "idempotent": true}, "DescribeBatchInferenceJob": {"name": "DescribeBatchInferenceJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeBatchInferenceJobRequest"}, "output": {"shape": "DescribeBatchInferenceJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the properties of a batch inference job including name, Amazon Resource Name (ARN), status, input and output configurations, and the ARN of the solution version used to generate the recommendations.</p>", "idempotent": true}, "DescribeBatchSegmentJob": {"name": "DescribeBatchSegmentJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeBatchSegmentJobRequest"}, "output": {"shape": "DescribeBatchSegmentJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the properties of a batch segment job including name, Amazon Resource Name (ARN), status, input and output configurations, and the ARN of the solution version used to generate segments.</p>", "idempotent": true}, "DescribeCampaign": {"name": "DescribeCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCampaignRequest"}, "output": {"shape": "DescribeCampaignResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given campaign, including its status.</p> <p>A campaign can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>When the <code>status</code> is <code>CREATE FAILED</code>, the response includes the <code>failureReason</code> key, which describes why.</p> <p>For more information on campaigns, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateCampaign.html\">CreateCampaign</a>.</p>", "idempotent": true}, "DescribeDataset": {"name": "DescribeDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetRequest"}, "output": {"shape": "DescribeDatasetResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given dataset. For more information on datasets, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>.</p>", "idempotent": true}, "DescribeDatasetExportJob": {"name": "DescribeDatasetExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetExportJobRequest"}, "output": {"shape": "DescribeDatasetExportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the dataset export job created by <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetExportJob.html\">CreateDatasetExportJob</a>, including the export job status.</p>", "idempotent": true}, "DescribeDatasetGroup": {"name": "DescribeDatasetGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetGroupRequest"}, "output": {"shape": "DescribeDatasetGroupResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given dataset group. For more information on dataset groups, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a>.</p>", "idempotent": true}, "DescribeDatasetImportJob": {"name": "DescribeDatasetImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDatasetImportJobRequest"}, "output": {"shape": "DescribeDatasetImportJobResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the dataset import job created by <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a>, including the import job status.</p>", "idempotent": true}, "DescribeEventTracker": {"name": "DescribeEventTracker", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEventTrackerRequest"}, "output": {"shape": "DescribeEventTrackerResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an event tracker. The response includes the <code>trackingId</code> and <code>status</code> of the event tracker. For more information on event trackers, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateEventTracker.html\">CreateEventTracker</a>.</p>", "idempotent": true}, "DescribeFeatureTransformation": {"name": "DescribeFeatureTransformation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFeatureTransformationRequest"}, "output": {"shape": "DescribeFeatureTransformationResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given feature transformation.</p>", "idempotent": true}, "DescribeFilter": {"name": "DescribeFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFilterRequest"}, "output": {"shape": "DescribeFilterResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a filter's properties.</p>", "idempotent": true}, "DescribeMetricAttribution": {"name": "DescribeMetricAttribution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeMetricAttributionRequest"}, "output": {"shape": "DescribeMetricAttributionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a metric attribution.</p>"}, "DescribeRecipe": {"name": "DescribeRecipe", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRecipeRequest"}, "output": {"shape": "DescribeRecipeResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a recipe.</p> <p>A recipe contains three items:</p> <ul> <li> <p>An algorithm that trains a model.</p> </li> <li> <p>Hyperparameters that govern the training.</p> </li> <li> <p>Feature transformation information for modifying the input data before training.</p> </li> </ul> <p>Amazon Personalize provides a set of predefined recipes. You specify a recipe when you create a solution with the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a> API. <code>CreateSolution</code> trains a model by using the algorithm in the specified recipe and a training dataset. The solution, when deployed as a campaign, can provide recommendations using the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> API.</p>", "idempotent": true}, "DescribeRecommender": {"name": "DescribeRecommender", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRecommenderRequest"}, "output": {"shape": "DescribeRecommenderResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given recommender, including its status.</p> <p>A recommender can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>STOP PENDING &gt; STOP IN_PROGRESS &gt; INACTIVE &gt; START PENDING &gt; START IN_PROGRESS &gt; ACTIVE</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul> <p>When the <code>status</code> is <code>CREATE FAILED</code>, the response includes the <code>failureReason</code> key, which describes why.</p> <p>The <code>modelMetrics</code> key is null when the recommender is being created or deleted.</p> <p>For more information on recommenders, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateRecommender.html\">C<PERSON>R<PERSON>ommender</a>.</p>", "idempotent": true}, "DescribeSchema": {"name": "DescribeSchema", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSchemaRequest"}, "output": {"shape": "DescribeSchemaResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a schema. For more information on schemas, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSchema.html\">CreateSchema</a>.</p>", "idempotent": true}, "DescribeSolution": {"name": "DescribeSolution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSolutionRequest"}, "output": {"shape": "DescribeSolutionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a solution. For more information on solutions, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a>.</p>", "idempotent": true}, "DescribeSolutionVersion": {"name": "DescribeSolutionVersion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSolutionVersionRequest"}, "output": {"shape": "DescribeSolutionVersionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a specific version of a solution. For more information on solutions, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a> </p>", "idempotent": true}, "GetSolutionMetrics": {"name": "GetSolutionMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSolutionMetricsRequest"}, "output": {"shape": "GetSolutionMetricsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Gets the metrics for the specified solution version.</p>"}, "ListBatchInferenceJobs": {"name": "ListBatchInferenceJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListBatchInferenceJobsRequest"}, "output": {"shape": "ListBatchInferenceJobsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Gets a list of the batch inference jobs that have been performed off of a solution version.</p>", "idempotent": true}, "ListBatchSegmentJobs": {"name": "ListBatchSegmentJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListBatchSegmentJobsRequest"}, "output": {"shape": "ListBatchSegmentJobsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Gets a list of the batch segment jobs that have been performed off of a solution version that you specify.</p>", "idempotent": true}, "ListCampaigns": {"name": "ListCampaigns", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCampaignsRequest"}, "output": {"shape": "ListCampaignsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of campaigns that use the given solution. When a solution is not specified, all the campaigns associated with the account are listed. The response provides the properties for each campaign, including the Amazon Resource Name (ARN). For more information on campaigns, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateCampaign.html\">CreateCampaign</a>.</p>", "idempotent": true}, "ListDatasetExportJobs": {"name": "ListDatasetExportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetExportJobsRequest"}, "output": {"shape": "ListDatasetExportJobsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of dataset export jobs that use the given dataset. When a dataset is not specified, all the dataset export jobs associated with the account are listed. The response provides the properties for each dataset export job, including the Amazon Resource Name (ARN). For more information on dataset export jobs, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetExportJob.html\">CreateDatasetExportJob</a>. For more information on datasets, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>.</p>", "idempotent": true}, "ListDatasetGroups": {"name": "ListDatasetGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetGroupsRequest"}, "output": {"shape": "ListDatasetGroupsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of dataset groups. The response provides the properties for each dataset group, including the Amazon Resource Name (ARN). For more information on dataset groups, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a>.</p>", "idempotent": true}, "ListDatasetImportJobs": {"name": "ListDatasetImportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetImportJobsRequest"}, "output": {"shape": "ListDatasetImportJobsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of dataset import jobs that use the given dataset. When a dataset is not specified, all the dataset import jobs associated with the account are listed. The response provides the properties for each dataset import job, including the Amazon Resource Name (ARN). For more information on dataset import jobs, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a>. For more information on datasets, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>.</p>", "idempotent": true}, "ListDatasets": {"name": "ListDatasets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatasetsRequest"}, "output": {"shape": "ListDatasetsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns the list of datasets contained in the given dataset group. The response provides the properties for each dataset, including the Amazon Resource Name (ARN). For more information on datasets, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>.</p>", "idempotent": true}, "ListEventTrackers": {"name": "ListEventTrackers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEventTrackersRequest"}, "output": {"shape": "ListEventTrackersResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns the list of event trackers associated with the account. The response provides the properties for each event tracker, including the Amazon Resource Name (ARN) and tracking ID. For more information on event trackers, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateEventTracker.html\">CreateEventTracker</a>.</p>", "idempotent": true}, "ListFilters": {"name": "ListFilters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFiltersRequest"}, "output": {"shape": "ListFiltersResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Lists all filters that belong to a given dataset group.</p>", "idempotent": true}, "ListMetricAttributionMetrics": {"name": "ListMetricAttributionMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMetricAttributionMetricsRequest"}, "output": {"shape": "ListMetricAttributionMetricsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Lists the metrics for the metric attribution.</p>", "idempotent": true}, "ListMetricAttributions": {"name": "ListMetricAttributions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMetricAttributionsRequest"}, "output": {"shape": "ListMetricAttributionsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Lists metric attributions.</p>", "idempotent": true}, "ListRecipes": {"name": "ListRecipes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRecipesRequest"}, "output": {"shape": "ListRecipesResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidInputException"}], "documentation": "<p>Returns a list of available recipes. The response provides the properties for each recipe, including the recipe's Amazon Resource Name (ARN).</p>", "idempotent": true}, "ListRecommenders": {"name": "ListRecommenders", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRecommendersRequest"}, "output": {"shape": "ListRecommendersResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of recommenders in a given Domain dataset group. When a Domain dataset group is not specified, all the recommenders associated with the account are listed. The response provides the properties for each recommender, including the Amazon Resource Name (ARN). For more information on recommenders, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateRecommender.html\">CreateRecommender</a>.</p>", "idempotent": true}, "ListSchemas": {"name": "ListSchemas", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSchemasRequest"}, "output": {"shape": "ListSchemasResponse"}, "errors": [{"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns the list of schemas associated with the account. The response provides the properties for each schema, including the Amazon Resource Name (ARN). For more information on schemas, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSchema.html\">CreateSchema</a>.</p>", "idempotent": true}, "ListSolutionVersions": {"name": "ListSolutionVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSolutionVersionsRequest"}, "output": {"shape": "ListSolutionVersionsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of solution versions for the given solution. When a solution is not specified, all the solution versions associated with the account are listed. The response provides the properties for each solution version, including the Amazon Resource Name (ARN).</p>", "idempotent": true}, "ListSolutions": {"name": "ListSolutions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSolutionsRequest"}, "output": {"shape": "ListSolutionsResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Returns a list of solutions that use the given dataset group. When a dataset group is not specified, all the solutions associated with the account are listed. The response provides the properties for each solution, including the Amazon Resource Name (ARN). For more information on solutions, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a>.</p>", "idempotent": true}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Get a list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> attached to a resource.</p>"}, "StartRecommender": {"name": "StartRecommender", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartRecommenderRequest"}, "output": {"shape": "StartRecommenderResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Starts a recommender that is INACTIVE. Starting a recommender does not create any new models, but resumes billing and automatic retraining for the recommender.</p>", "idempotent": true}, "StopRecommender": {"name": "StopRecommender", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopRecommenderRequest"}, "output": {"shape": "StopRecommenderResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Stops a recommender that is ACTIVE. Stopping a recommender halts billing and automatic retraining for the recommender.</p>", "idempotent": true}, "StopSolutionVersionCreation": {"name": "StopSolutionVersionCreation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopSolutionVersionCreationRequest"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Stops creating a solution version that is in a state of CREATE_PENDING or CREATE IN_PROGRESS. </p> <p>Depending on the current state of the solution version, the solution version state changes as follows:</p> <ul> <li> <p>CREATE_PENDING &gt; CREATE_STOPPED</p> <p>or</p> </li> <li> <p>CREATE_IN_PROGRESS &gt; CREATE_STOPPING &gt; CREATE_STOPPED</p> </li> </ul> <p>You are billed for all of the training completed up until you stop the solution version creation. You cannot resume creating a solution version once it has been stopped.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyTagsException"}, {"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Add a list of tags to a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyTagKeysException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Remove <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> that are attached to a resource.</p>"}, "UpdateCampaign": {"name": "UpdateCampaign", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCampaignRequest"}, "output": {"shape": "UpdateCampaignResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Updates a campaign by either deploying a new solution or changing the value of the campaign's <code>minProvisionedTPS</code> parameter.</p> <p>To update a campaign, the campaign status must be ACTIVE or CREATE FAILED. Check the campaign status using the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeCampaign.html\">DescribeCampaign</a> operation.</p> <note> <p>You can still get recommendations from a campaign while an update is in progress. The campaign will use the previous solution version and campaign configuration to generate recommendations until the latest campaign update status is <code>Active</code>. </p> </note> <p>For more information on campaigns, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateCampaign.html\">CreateCampaign</a>.</p>", "idempotent": true}, "UpdateDataset": {"name": "UpdateDataset", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDatasetRequest"}, "output": {"shape": "UpdateDatasetResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Update a dataset to replace its schema with a new or existing one. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/updating-dataset-schema.html\">Replacing a dataset's schema</a>. </p>", "idempotent": true}, "UpdateMetricAttribution": {"name": "UpdateMetricAttribution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateMetricAttributionRequest"}, "output": {"shape": "UpdateMetricAttributionResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceAlreadyExistsException"}], "documentation": "<p>Updates a metric attribution.</p>"}, "UpdateRecommender": {"name": "Update<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateRecommenderRequest"}, "output": {"shape": "UpdateRecommenderResponse"}, "errors": [{"shape": "InvalidInputException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Updates the recommender to modify the recommender configuration. If you update the recommender to modify the columns used in training, Amazon Personalize automatically starts a full retraining of the models backing your recommender. While the update completes, you can still get recommendations from the recommender. The recommender uses the previous configuration until the update completes. To track the status of this update, use the <code>latestRecommenderUpdate</code> returned in the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeRecommender.html\">DescribeRecommender</a> operation. </p>", "idempotent": true}}, "shapes": {"AccountId": {"type": "string", "max": 256}, "Algorithm": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the algorithm.</p>"}, "algorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the algorithm.</p>"}, "algorithmImage": {"shape": "AlgorithmImage", "documentation": "<p>The URI of the Docker container for the algorithm image.</p>"}, "defaultHyperParameters": {"shape": "HyperParameters", "documentation": "<p>Specifies the default hyperparameters.</p>"}, "defaultHyperParameterRanges": {"shape": "DefaultHyperParameterRanges", "documentation": "<p>Specifies the default hyperparameters, their ranges, and whether they are tunable. A tunable hyperparameter can have its value determined during hyperparameter optimization (HPO).</p>"}, "defaultResourceConfig": {"shape": "ResourceConfig", "documentation": "<p>Specifies the default maximum number of training jobs and parallel training jobs.</p>"}, "trainingInputMode": {"shape": "TrainingInputMode", "documentation": "<p>The training input mode.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the role.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the algorithm was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the algorithm was last updated.</p>"}}, "documentation": "<p>Describes a custom algorithm.</p>"}, "AlgorithmImage": {"type": "structure", "required": ["dockerURI"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the algorithm image.</p>"}, "dockerURI": {"shape": "DockerURI", "documentation": "<p>The URI of the Docker container for the algorithm image.</p>"}}, "documentation": "<p>Describes an algorithm image.</p>"}, "Arn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):personalize:.*:.*:.+"}, "ArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 100}, "AutoMLConfig": {"type": "structure", "members": {"metricName": {"shape": "MetricName", "documentation": "<p>The metric to optimize.</p>"}, "recipeList": {"shape": "ArnList", "documentation": "<p>The list of candidate recipes.</p>"}}, "documentation": "<p>When the solution performs AutoML (<code>performAutoML</code> is true in <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a>), Amazon Personalize determines which recipe, from the specified list, optimizes the given metric. Amazon Personalize then uses that recipe for the solution.</p>"}, "AutoMLResult": {"type": "structure", "members": {"bestRecipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the best recipe.</p>"}}, "documentation": "<p>When the solution performs AutoML (<code>performAutoML</code> is true in <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a>), specifies the recipe that best optimized the specified metric.</p>"}, "AvroSchema": {"type": "string", "max": 10000}, "BatchInferenceJob": {"type": "structure", "members": {"jobName": {"shape": "Name", "documentation": "<p>The name of the batch inference job.</p>"}, "batchInferenceJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the batch inference job.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter used on the batch inference job.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If the batch inference job failed, the reason for the failure.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version from which the batch inference job was created.</p>"}, "numResults": {"shape": "NumBatchResults", "documentation": "<p>The number of recommendations generated by the batch inference job. This number includes the error messages generated for failed input records.</p>"}, "jobInput": {"shape": "BatchInferenceJobInput", "documentation": "<p>The Amazon S3 path that leads to the input data used to generate the batch inference job.</p>"}, "jobOutput": {"shape": "BatchInferenceJobOutput", "documentation": "<p>The Amazon S3 bucket that contains the output data generated by the batch inference job.</p>"}, "batchInferenceJobConfig": {"shape": "BatchInferenceJobConfig", "documentation": "<p>A string to string map of the configuration details of a batch inference job.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the Amazon Identity and Access Management (IAM) role that requested the batch inference job.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the batch inference job. The status is one of the following values:</p> <ul> <li> <p>PENDING</p> </li> <li> <p>IN PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch inference job was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch inference job was last updated.</p>"}}, "documentation": "<p>Contains information on a batch inference job.</p>"}, "BatchInferenceJobConfig": {"type": "structure", "members": {"itemExplorationConfig": {"shape": "HyperParameters", "documentation": "<p>A string to string map specifying the exploration configuration hyperparameters, including <code>explorationWeight</code> and <code>explorationItemAgeCutOff</code>, you want to use to configure the amount of item exploration Amazon Personalize uses when recommending items. See <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/native-recipe-new-item-USER_PERSONALIZATION.html\">User-Personalization</a>.</p>"}}, "documentation": "<p>The configuration details of a batch inference job.</p>"}, "BatchInferenceJobInput": {"type": "structure", "required": ["s3DataSource"], "members": {"s3DataSource": {"shape": "S3DataConfig", "documentation": "<p>The URI of the Amazon S3 location that contains your input data. The Amazon S3 bucket must be in the same region as the API endpoint you are calling.</p>"}}, "documentation": "<p>The input configuration of a batch inference job.</p>"}, "BatchInferenceJobOutput": {"type": "structure", "required": ["s3DataDestination"], "members": {"s3DataDestination": {"shape": "S3DataConfig", "documentation": "<p>Information on the Amazon S3 bucket in which the batch inference job's output is stored.</p>"}}, "documentation": "<p>The output configuration parameters of a batch inference job.</p>"}, "BatchInferenceJobSummary": {"type": "structure", "members": {"batchInferenceJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the batch inference job.</p>"}, "jobName": {"shape": "Name", "documentation": "<p>The name of the batch inference job.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the batch inference job. The status is one of the following values:</p> <ul> <li> <p>PENDING</p> </li> <li> <p>IN PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch inference job was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch inference job was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If the batch inference job failed, the reason for the failure.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the solution version used by the batch inference job.</p>"}}, "documentation": "<p>A truncated version of the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_BatchInferenceJob.html\">BatchInferenceJob</a>. The <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListBatchInferenceJobs.html\">ListBatchInferenceJobs</a> operation returns a list of batch inference job summaries.</p>"}, "BatchInferenceJobs": {"type": "list", "member": {"shape": "BatchInferenceJobSummary"}, "max": 100}, "BatchSegmentJob": {"type": "structure", "members": {"jobName": {"shape": "Name", "documentation": "<p>The name of the batch segment job.</p>"}, "batchSegmentJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the batch segment job.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter used on the batch segment job.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If the batch segment job failed, the reason for the failure.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version used by the batch segment job to generate batch segments.</p>"}, "numResults": {"shape": "NumBatchResults", "documentation": "<p>The number of predicted users generated by the batch segment job for each line of input data. The maximum number of users per segment is 5 million.</p>"}, "jobInput": {"shape": "BatchSegmentJobInput", "documentation": "<p>The Amazon S3 path that leads to the input data used to generate the batch segment job.</p>"}, "jobOutput": {"shape": "BatchSegmentJobOutput", "documentation": "<p>The Amazon S3 bucket that contains the output data generated by the batch segment job.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the Amazon Identity and Access Management (IAM) role that requested the batch segment job.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the batch segment job. The status is one of the following values:</p> <ul> <li> <p>PENDING</p> </li> <li> <p>IN PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch segment job was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch segment job last updated.</p>"}}, "documentation": "<p>Contains information on a batch segment job.</p>"}, "BatchSegmentJobInput": {"type": "structure", "required": ["s3DataSource"], "members": {"s3DataSource": {"shape": "S3DataConfig"}}, "documentation": "<p>The input configuration of a batch segment job.</p>"}, "BatchSegmentJobOutput": {"type": "structure", "required": ["s3DataDestination"], "members": {"s3DataDestination": {"shape": "S3DataConfig"}}, "documentation": "<p>The output configuration parameters of a batch segment job.</p>"}, "BatchSegmentJobSummary": {"type": "structure", "members": {"batchSegmentJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the batch segment job.</p>"}, "jobName": {"shape": "Name", "documentation": "<p>The name of the batch segment job.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the batch segment job. The status is one of the following values:</p> <ul> <li> <p>PENDING</p> </li> <li> <p>IN PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch segment job was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The time at which the batch segment job was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If the batch segment job failed, the reason for the failure.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version used by the batch segment job to generate batch segments.</p>"}}, "documentation": "<p>A truncated version of the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_BatchSegmentJob.html\">BatchSegmentJob</a> datatype. <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListBatchSegmentJobs.html\">ListBatchSegmentJobs</a> operation returns a list of batch segment job summaries.</p>"}, "BatchSegmentJobs": {"type": "list", "member": {"shape": "BatchSegmentJobSummary"}, "max": 100}, "Boolean": {"type": "boolean"}, "Campaign": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the campaign.</p>"}, "campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign. </p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of a specific version of the solution.</p>"}, "minProvisionedTPS": {"shape": "TransactionsPerSecond", "documentation": "<p>Specifies the requested minimum provisioned transactions (recommendations) per second. A high <code>minProvisionedTPS</code> will increase your bill. We recommend starting with 1 for <code>minProvisionedTPS</code> (the default). Track your usage using Amazon CloudWatch metrics, and increase the <code>minProvisionedTPS</code> as necessary.</p>"}, "campaignConfig": {"shape": "CampaignConfig", "documentation": "<p>The configuration details of a campaign.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the campaign.</p> <p>A campaign can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a campaign fails, the reason behind the failure.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the campaign was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the campaign was last updated.</p>"}, "latestCampaignUpdate": {"shape": "CampaignUpdateSummary"}}, "documentation": "<p>An object that describes the deployment of a solution version. For more information on campaigns, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateCampaign.html\">CreateCampaign</a>.</p>"}, "CampaignConfig": {"type": "structure", "members": {"itemExplorationConfig": {"shape": "HyperParameters", "documentation": "<p>Specifies the exploration configuration hyperparameters, including <code>explorationWeight</code> and <code>explorationItemAgeCutOff</code>, you want to use to configure the amount of item exploration Amazon Personalize uses when recommending items. Provide <code>itemExplorationConfig</code> data only if your solution uses the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/native-recipe-new-item-USER_PERSONALIZATION.html\">User-Personalization</a> recipe.</p>"}}, "documentation": "<p>The configuration details of a campaign.</p>"}, "CampaignSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the campaign.</p>"}, "campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the campaign.</p> <p>A campaign can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the campaign was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the campaign was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a campaign fails, the reason behind the failure.</p>"}}, "documentation": "<p>Provides a summary of the properties of a campaign. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeCampaign.html\">DescribeCampaign</a> API.</p>"}, "CampaignUpdateSummary": {"type": "structure", "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the deployed solution version.</p>"}, "minProvisionedTPS": {"shape": "TransactionsPerSecond", "documentation": "<p>Specifies the requested minimum provisioned transactions (recommendations) per second that Amazon Personalize will support.</p>"}, "campaignConfig": {"shape": "CampaignConfig"}, "status": {"shape": "Status", "documentation": "<p>The status of the campaign update.</p> <p>A campaign update can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a campaign update fails, the reason behind the failure.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the campaign update was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the campaign update was last updated.</p>"}}, "documentation": "<p>Provides a summary of the properties of a campaign update. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeCampaign.html\">DescribeCampaign</a> API.</p>"}, "Campaigns": {"type": "list", "member": {"shape": "CampaignSummary"}, "max": 100}, "CategoricalHyperParameterRange": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the hyperparameter.</p>"}, "values": {"shape": "CategoricalValues", "documentation": "<p>A list of the categories for the hyperparameter.</p>"}}, "documentation": "<p>Provides the name and range of a categorical hyperparameter.</p>"}, "CategoricalHyperParameterRanges": {"type": "list", "member": {"shape": "CategoricalHyperParameterRange"}, "max": 100}, "CategoricalValue": {"type": "string", "max": 1000}, "CategoricalValues": {"type": "list", "member": {"shape": "CategoricalValue"}, "max": 100}, "ColumnName": {"type": "string", "max": 150}, "ColumnNamesList": {"type": "list", "member": {"shape": "ColumnName"}, "max": 50}, "ContinuousHyperParameterRange": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the hyperparameter.</p>"}, "minValue": {"shape": "ContinuousMinValue", "documentation": "<p>The minimum allowable value for the hyperparameter.</p>"}, "maxValue": {"shape": "ContinuousMaxValue", "documentation": "<p>The maximum allowable value for the hyperparameter.</p>"}}, "documentation": "<p>Provides the name and range of a continuous hyperparameter.</p>"}, "ContinuousHyperParameterRanges": {"type": "list", "member": {"shape": "ContinuousHyperParameterRange"}, "max": 100}, "ContinuousMaxValue": {"type": "double", "min": -1000000}, "ContinuousMinValue": {"type": "double", "min": -1000000}, "CreateBatchInferenceJobRequest": {"type": "structure", "required": ["job<PERSON>ame", "solutionVersionArn", "jobInput", "jobOutput", "roleArn"], "members": {"jobName": {"shape": "Name", "documentation": "<p>The name of the batch inference job to create.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version that will be used to generate the batch inference recommendations.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter to apply to the batch inference job. For more information on using filters, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter-batch.html\">Filtering batch recommendations</a>.</p>"}, "numResults": {"shape": "NumBatchResults", "documentation": "<p>The number of recommendations to retrieve.</p>"}, "jobInput": {"shape": "BatchInferenceJobInput", "documentation": "<p>The Amazon S3 path that leads to the input file to base your recommendations on. The input material must be in JSON format.</p>"}, "jobOutput": {"shape": "BatchInferenceJobOutput", "documentation": "<p>The path to the Amazon S3 bucket where the job's output will be stored.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the Amazon Identity and Access Management role that has permissions to read and write to your input and output Amazon S3 buckets respectively.</p>"}, "batchInferenceJobConfig": {"shape": "BatchInferenceJobConfig", "documentation": "<p>The configuration details of a batch inference job.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the batch inference job.</p>"}}}, "CreateBatchInferenceJobResponse": {"type": "structure", "members": {"batchInferenceJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the batch inference job.</p>"}}}, "CreateBatchSegmentJobRequest": {"type": "structure", "required": ["job<PERSON>ame", "solutionVersionArn", "jobInput", "jobOutput", "roleArn"], "members": {"jobName": {"shape": "Name", "documentation": "<p>The name of the batch segment job to create.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version you want the batch segment job to use to generate batch segments.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter to apply to the batch segment job. For more information on using filters, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter-batch.html\">Filtering batch recommendations</a>.</p>"}, "numResults": {"shape": "NumBatchResults", "documentation": "<p>The number of predicted users generated by the batch segment job for each line of input data. The maximum number of users per segment is 5 million.</p>"}, "jobInput": {"shape": "BatchSegmentJobInput", "documentation": "<p>The Amazon S3 path for the input data used to generate the batch segment job.</p>"}, "jobOutput": {"shape": "BatchSegmentJobOutput", "documentation": "<p>The Amazon S3 path for the bucket where the job's output will be stored.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the Amazon Identity and Access Management role that has permissions to read and write to your input and output Amazon S3 buckets respectively.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the batch segment job.</p>"}}}, "CreateBatchSegmentJobResponse": {"type": "structure", "members": {"batchSegmentJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the batch segment job.</p>"}}}, "CreateCampaignRequest": {"type": "structure", "required": ["name", "solutionVersionArn"], "members": {"name": {"shape": "Name", "documentation": "<p>A name for the new campaign. The campaign name must be unique within your account.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version to deploy.</p>"}, "minProvisionedTPS": {"shape": "TransactionsPerSecond", "documentation": "<p>Specifies the requested minimum provisioned transactions (recommendations) per second that Amazon Personalize will support. A high <code>minProvisionedTPS</code> will increase your bill. We recommend starting with 1 for <code>minProvisionedTPS</code> (the default). Track your usage using Amazon CloudWatch metrics, and increase the <code>minProvisionedTPS</code> as necessary.</p>"}, "campaignConfig": {"shape": "CampaignConfig", "documentation": "<p>The configuration details of a campaign.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the campaign.</p>"}}}, "CreateCampaignResponse": {"type": "structure", "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign.</p>"}}}, "CreateDatasetExportJobRequest": {"type": "structure", "required": ["job<PERSON>ame", "datasetArn", "roleArn", "jobOutput"], "members": {"jobName": {"shape": "Name", "documentation": "<p>The name for the dataset export job.</p>"}, "datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset that contains the data to export.</p>"}, "ingestionMode": {"shape": "IngestionMode", "documentation": "<p>The data to export, based on how you imported the data. You can choose to export only <code>BULK</code> data that you imported using a dataset import job, only <code>PUT</code> data that you imported incrementally (using the console, PutEvents, PutUsers and PutItems operations), or <code>ALL</code> for both types. The default value is <code>PUT</code>. </p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that has permissions to add data to your output Amazon S3 bucket.</p>"}, "jobOutput": {"shape": "DatasetExportJobOutput", "documentation": "<p>The path to the Amazon S3 bucket where the job's output is stored.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the dataset export job.</p>"}}}, "CreateDatasetExportJobResponse": {"type": "structure", "members": {"datasetExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset export job.</p>"}}}, "CreateDatasetGroupRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "Name", "documentation": "<p>The name for the new dataset group.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the Identity and Access Management (IAM) role that has permissions to access the Key Management Service (KMS) key. Supplying an IAM role is only valid when also specifying a KMS key.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of a Key Management Service (KMS) key used to encrypt the datasets.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain of the dataset group. Specify a domain to create a Domain dataset group. The domain you specify determines the default schemas for datasets and the use cases available for recommenders. If you don't specify a domain, you create a Custom dataset group with solution versions that you deploy with a campaign. </p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the dataset group.</p>"}}}, "CreateDatasetGroupResponse": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the new dataset group.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain for the new Domain dataset group.</p>"}}}, "CreateDatasetImportJobRequest": {"type": "structure", "required": ["job<PERSON>ame", "datasetArn", "dataSource", "roleArn"], "members": {"jobName": {"shape": "Name", "documentation": "<p>The name for the dataset import job.</p>"}, "datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset that receives the imported data.</p>"}, "dataSource": {"shape": "DataSource", "documentation": "<p>The Amazon S3 bucket that contains the training data to import.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the IAM role that has permissions to read from the Amazon S3 data source.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the dataset import job.</p>"}, "importMode": {"shape": "ImportMode", "documentation": "<p>Specify how to add the new records to an existing dataset. The default import mode is <code>FULL</code>. If you haven't imported bulk records into the dataset previously, you can only specify <code>FULL</code>.</p> <ul> <li> <p>Specify <code>FULL</code> to overwrite all existing bulk data in your dataset. Data you imported individually is not replaced.</p> </li> <li> <p>Specify <code>INCREMENTAL</code> to append the new records to the existing data in your dataset. Amazon Personalize replaces any record with the same ID with the new one.</p> </li> </ul>"}, "publishAttributionMetricsToS3": {"shape": "Boolean", "documentation": "<p>If you created a metric attribution, specify whether to publish metrics for this import job to Amazon S3</p>"}}}, "CreateDatasetImportJobResponse": {"type": "structure", "members": {"datasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset import job.</p>"}}}, "CreateDatasetRequest": {"type": "structure", "required": ["name", "schemaArn", "datasetGroupArn", "datasetType"], "members": {"name": {"shape": "Name", "documentation": "<p>The name for the dataset.</p>"}, "schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the schema to associate with the dataset. The schema defines the dataset fields.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group to add the dataset to.</p>"}, "datasetType": {"shape": "DatasetType", "documentation": "<p>The type of dataset.</p> <p>One of the following (case insensitive) values:</p> <ul> <li> <p>Interactions</p> </li> <li> <p>Items</p> </li> <li> <p>Users</p> </li> </ul>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the dataset.</p>"}}}, "CreateDatasetResponse": {"type": "structure", "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset.</p>"}}}, "CreateEventTrackerRequest": {"type": "structure", "required": ["name", "datasetGroupArn"], "members": {"name": {"shape": "Name", "documentation": "<p>The name for the event tracker.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that receives the event data.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the event tracker.</p>"}}}, "CreateEventTrackerResponse": {"type": "structure", "members": {"eventTrackerArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event tracker.</p>"}, "trackingId": {"shape": "TrackingId", "documentation": "<p>The ID of the event tracker. Include this ID in requests to the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_UBS_PutEvents.html\">PutEvents</a> API.</p>"}}}, "CreateFilterRequest": {"type": "structure", "required": ["name", "datasetGroupArn", "filterExpression"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the filter to create.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group that the filter will belong to.</p>"}, "filterExpression": {"shape": "FilterExpression", "documentation": "<p>The filter expression defines which items are included or excluded from recommendations. Filter expression must follow specific format rules. For information about filter expression structure and syntax, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter-expressions.html\">Filter expressions</a>.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the filter.</p>"}}}, "CreateFilterResponse": {"type": "structure", "members": {"filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the new filter.</p>"}}}, "CreateMetricAttributionRequest": {"type": "structure", "required": ["name", "datasetGroupArn", "metrics", "metricsOutputConfig"], "members": {"name": {"shape": "Name", "documentation": "<p>A name for the metric attribution.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the destination dataset group for the metric attribution.</p>"}, "metrics": {"shape": "MetricAttributes", "documentation": "<p>A list of metric attributes for the metric attribution. Each metric attribute specifies an event type to track and a function. Available functions are <code>SUM()</code> or <code>SAMPLECOUNT()</code>. For SUM() functions, provide the dataset type (either Interactions or Items) and column to sum as a parameter. For example SUM(Items.PRICE).</p>"}, "metricsOutputConfig": {"shape": "MetricAttributionOutput", "documentation": "<p>The output configuration details for the metric attribution.</p>"}}}, "CreateMetricAttributionResponse": {"type": "structure", "members": {"metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the new metric attribution.</p>"}}}, "CreateRecommenderRequest": {"type": "structure", "required": ["name", "datasetGroupArn", "recipeArn"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the recommender.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the destination domain dataset group for the recommender.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe that the recommender will use. For a recommender, a recipe is a Domain dataset group use case. Only Domain dataset group use cases can be used to create a recommender. For information about use cases see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/domain-use-cases.html\">Choosing recommender use cases</a>. </p>"}, "recommenderConfig": {"shape": "RecommenderConfig", "documentation": "<p>The configuration details of the recommender.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the recommender.</p>"}}}, "CreateRecommenderResponse": {"type": "structure", "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender.</p>"}}}, "CreateSchemaRequest": {"type": "structure", "required": ["name", "schema"], "members": {"name": {"shape": "Name", "documentation": "<p>The name for the schema.</p>"}, "schema": {"shape": "AvroSchema", "documentation": "<p>A schema in Avro JSON format.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain for the schema. If you are creating a schema for a dataset in a Domain dataset group, specify the domain you chose when you created the Domain dataset group.</p>"}}}, "CreateSchemaResponse": {"type": "structure", "members": {"schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the created schema.</p>"}}}, "CreateSolutionRequest": {"type": "structure", "required": ["name", "datasetGroupArn"], "members": {"name": {"shape": "Name", "documentation": "<p>The name for the solution.</p>"}, "performHPO": {"shape": "Boolean", "documentation": "<p>Whether to perform hyperparameter optimization (HPO) on the specified or selected recipe. The default is <code>false</code>.</p> <p>When performing AutoML, this parameter is always <code>true</code> and you should not set it to <code>false</code>.</p>"}, "performAutoML": {"shape": "PerformAutoML", "documentation": "<important> <p>We don't recommend enabling automated machine learning. Instead, match your use case to the available Amazon Personalize recipes. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/determining-use-case.html\">Determining your use case.</a> </p> </important> <p>Whether to perform automated machine learning (AutoML). The default is <code>false</code>. For this case, you must specify <code>recipeArn</code>.</p> <p>When set to <code>true</code>, Amazon Personalize analyzes your training data and selects the optimal USER_PERSONALIZATION recipe and hyperparameters. In this case, you must omit <code>recipeArn</code>. Amazon Personalize determines the optimal recipe by running tests with different values for the hyperparameters. AutoML lengthens the training process as compared to selecting a specific recipe.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the recipe to use for model training. This is required when <code>performAutoML</code> is false.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that provides the training data.</p>"}, "eventType": {"shape": "EventType", "documentation": "<p>When your have multiple event types (using an <code>EVENT_TYPE</code> schema field), this parameter specifies which event type (for example, 'click' or 'like') is used for training the model.</p> <p>If you do not provide an <code>eventType</code>, Amazon Personalize will use all interactions for training with equal weight regardless of type.</p>"}, "solutionConfig": {"shape": "SolutionConfig", "documentation": "<p>The configuration to use with the solution. When <code>performAutoML</code> is set to true, Amazon Personalize only evaluates the <code>autoMLConfig</code> section of the solution configuration.</p> <note> <p>Amazon Personalize doesn't support configuring the <code>hpoObjective</code> at this time.</p> </note>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the solution.</p>"}}}, "CreateSolutionResponse": {"type": "structure", "members": {"solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the solution.</p>"}}}, "CreateSolutionVersionRequest": {"type": "structure", "required": ["solutionArn"], "members": {"name": {"shape": "Name", "documentation": "<p>The name of the solution version.</p>"}, "solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution containing the training configuration information.</p>"}, "trainingMode": {"shape": "TrainingMode", "documentation": "<p>The scope of training to be performed when creating the solution version. The <code>FULL</code> option trains the solution version based on the entirety of the input solution's training data, while the <code>UPDATE</code> option processes only the data that has changed in comparison to the input solution. Choose <code>UPDATE</code> when you want to incrementally update your solution version instead of creating an entirely new one.</p> <important> <p>The <code>UPDATE</code> option can only be used when you already have an active solution version created from the input solution using the <code>FULL</code> option and the input solution was trained with the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/native-recipe-new-item-USER_PERSONALIZATION.html\">User-Personalization</a> recipe or the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/native-recipe-hrnn-coldstart.html\">HRNN-Coldstart</a> recipe.</p> </important>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">tags</a> to apply to the solution version.</p>"}}}, "CreateSolutionVersionResponse": {"type": "structure", "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the new solution version.</p>"}}}, "DataSource": {"type": "structure", "members": {"dataLocation": {"shape": "S3Location", "documentation": "<p>The path to the Amazon S3 bucket where the data that you want to upload to your dataset is stored. For example: </p> <p> <code>s3://bucket-name/folder-name/</code> </p>"}}, "documentation": "<p>Describes the data source that contains the data to upload to a dataset.</p>"}, "Dataset": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the dataset.</p>"}, "datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset that you want metadata for.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}, "datasetType": {"shape": "DatasetType", "documentation": "<p>One of the following values:</p> <ul> <li> <p>Interactions</p> </li> <li> <p>Items</p> </li> <li> <p>Users</p> </li> </ul>"}, "schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the associated schema.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset.</p> <p>A dataset can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the dataset.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>A time stamp that shows when the dataset was updated.</p>"}, "latestDatasetUpdate": {"shape": "DatasetUpdateSummary", "documentation": "<p>Describes the latest update to the dataset.</p>"}}, "documentation": "<p>Provides metadata for a dataset.</p>"}, "DatasetExportJob": {"type": "structure", "members": {"jobName": {"shape": "Name", "documentation": "<p>The name of the export job.</p>"}, "datasetExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset export job.</p>"}, "datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset to export.</p>"}, "ingestionMode": {"shape": "IngestionMode", "documentation": "<p>The data to export, based on how you imported the data. You can choose to export <code>BULK</code> data that you imported using a dataset import job, <code>PUT</code> data that you imported incrementally (using the console, PutEvents, PutUsers and PutItems operations), or <code>ALL</code> for both types. The default value is <code>PUT</code>. </p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that has permissions to add data to your output Amazon S3 bucket.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset export job.</p> <p>A dataset export job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "jobOutput": {"shape": "DatasetExportJobOutput", "documentation": "<p>The path to the Amazon S3 bucket where the job's output is stored. For example:</p> <p> <code>s3://bucket-name/folder-name/</code> </p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the dataset export job.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) the status of the dataset export job was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a dataset export job fails, provides the reason why.</p>"}}, "documentation": "<p>Describes a job that exports a dataset to an Amazon S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetExportJob.html\">CreateDatasetExportJob</a>.</p> <p>A dataset export job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "DatasetExportJobOutput": {"type": "structure", "required": ["s3DataDestination"], "members": {"s3DataDestination": {"shape": "S3DataConfig"}}, "documentation": "<p>The output configuration parameters of a dataset export job.</p>"}, "DatasetExportJobSummary": {"type": "structure", "members": {"datasetExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset export job.</p>"}, "jobName": {"shape": "Name", "documentation": "<p>The name of the dataset export job.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset export job.</p> <p>A dataset export job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset export job was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset export job status was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a dataset export job fails, the reason behind the failure.</p>"}}, "documentation": "<p>Provides a summary of the properties of a dataset export job. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetExportJob.html\">DescribeDatasetExportJob</a> API.</p>"}, "DatasetExportJobs": {"type": "list", "member": {"shape": "DatasetExportJobSummary"}, "max": 100}, "DatasetGroup": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the dataset group.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}, "status": {"shape": "Status", "documentation": "<p>The current status of the dataset group.</p> <p>A dataset group can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING</p> </li> </ul>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the IAM role that has permissions to create the dataset group.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Key Management Service (KMS) key used to encrypt the datasets.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the dataset group.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The last update date and time (in Unix time) of the dataset group.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If creating a dataset group fails, provides the reason why.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain of a Domain dataset group.</p>"}}, "documentation": "<p>A dataset group is a collection of related datasets (Interactions, User, and Item). You create a dataset group by calling <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetGroup.html\">CreateDatasetGroup</a>. You then create a dataset and add it to a dataset group by calling <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDataset.html\">CreateDataset</a>. The dataset group is used to create and train a solution by calling <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a>. A dataset group can contain only one of each type of dataset.</p> <p>You can specify an Key Management Service (KMS) key to encrypt the datasets in the group.</p>"}, "DatasetGroupSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the dataset group.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset group.</p> <p>A dataset group can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset group was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset group was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If creating a dataset group fails, the reason behind the failure.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain of a Domain dataset group.</p>"}}, "documentation": "<p>Provides a summary of the properties of a dataset group. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetGroup.html\">DescribeDatasetGroup</a> API.</p>"}, "DatasetGroups": {"type": "list", "member": {"shape": "DatasetGroupSummary"}, "max": 100}, "DatasetImportJob": {"type": "structure", "members": {"jobName": {"shape": "Name", "documentation": "<p>The name of the import job.</p>"}, "datasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset import job.</p>"}, "datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset that receives the imported data.</p>"}, "dataSource": {"shape": "DataSource", "documentation": "<p>The Amazon S3 bucket that contains the training data to import.</p>"}, "roleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the IAM role that has permissions to read from the Amazon S3 data source.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset import job.</p> <p>A dataset import job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the dataset import job.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) the dataset was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a dataset import job fails, provides the reason why.</p>"}, "importMode": {"shape": "ImportMode", "documentation": "<p>The import mode used by the dataset import job to import new records.</p>"}, "publishAttributionMetricsToS3": {"shape": "Boolean", "documentation": "<p>Whether the job publishes metrics to Amazon S3 for a metric attribution.</p>"}}, "documentation": "<p>Describes a job that imports training data from a data source (Amazon S3 bucket) to an Amazon Personalize dataset. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateDatasetImportJob.html\">CreateDatasetImportJob</a>.</p> <p>A dataset import job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "DatasetImportJobSummary": {"type": "structure", "members": {"datasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job.</p>"}, "jobName": {"shape": "Name", "documentation": "<p>The name of the dataset import job.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset import job.</p> <p>A dataset import job can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset import job was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset import job status was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a dataset import job fails, the reason behind the failure.</p>"}, "importMode": {"shape": "ImportMode", "documentation": "<p>The import mode the dataset import job used to update the data in the dataset. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/updating-existing-bulk-data.html\">Updating existing bulk data</a>. </p>"}}, "documentation": "<p>Provides a summary of the properties of a dataset import job. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDatasetImportJob.html\">DescribeDatasetImportJob</a> API.</p>"}, "DatasetImportJobs": {"type": "list", "member": {"shape": "DatasetImportJobSummary"}, "max": 100}, "DatasetSchema": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the schema.</p>"}, "schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the schema.</p>"}, "schema": {"shape": "AvroSchema", "documentation": "<p>The schema.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the schema was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the schema was last updated.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain of a schema that you created for a dataset in a Domain dataset group.</p>"}}, "documentation": "<p>Describes the schema for a dataset. For more information on schemas, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSchema.html\">CreateSchema</a>.</p>"}, "DatasetSchemaSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the schema.</p>"}, "schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the schema.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the schema was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the schema was last updated.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain of a schema that you created for a dataset in a Domain dataset group.</p>"}}, "documentation": "<p>Provides a summary of the properties of a dataset schema. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSchema.html\">DescribeSchema</a> API.</p>"}, "DatasetSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the dataset.</p>"}, "datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset.</p>"}, "datasetType": {"shape": "DatasetType", "documentation": "<p>The dataset type. One of the following values:</p> <ul> <li> <p>Interactions</p> </li> <li> <p>Items</p> </li> <li> <p>Users</p> </li> <li> <p>Event-Interactions</p> </li> </ul>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset.</p> <p>A dataset can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the dataset was last updated.</p>"}}, "documentation": "<p>Provides a summary of the properties of a dataset. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeDataset.html\">DescribeDataset</a> API.</p>"}, "DatasetType": {"type": "string", "max": 256}, "DatasetUpdateSummary": {"type": "structure", "members": {"schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the schema that replaced the previous schema of the dataset.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the dataset update. </p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If updating a dataset fails, provides the reason why.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the dataset update.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The last update date and time (in Unix time) of the dataset.</p>"}}, "documentation": "<p>Describes an update to a dataset.</p>"}, "Datasets": {"type": "list", "member": {"shape": "DatasetSummary"}, "max": 100}, "Date": {"type": "timestamp"}, "DefaultCategoricalHyperParameterRange": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the hyperparameter.</p>"}, "values": {"shape": "CategoricalValues", "documentation": "<p>A list of the categories for the hyperparameter.</p>"}, "isTunable": {"shape": "Tunable", "documentation": "<p>Whether the hyperparameter is tunable.</p>"}}, "documentation": "<p>Provides the name and default range of a categorical hyperparameter and whether the hyperparameter is tunable. A tunable hyperparameter can have its value determined during hyperparameter optimization (HPO).</p>"}, "DefaultCategoricalHyperParameterRanges": {"type": "list", "member": {"shape": "DefaultCategoricalHyperParameterRange"}, "max": 100}, "DefaultContinuousHyperParameterRange": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the hyperparameter.</p>"}, "minValue": {"shape": "ContinuousMinValue", "documentation": "<p>The minimum allowable value for the hyperparameter.</p>"}, "maxValue": {"shape": "ContinuousMaxValue", "documentation": "<p>The maximum allowable value for the hyperparameter.</p>"}, "isTunable": {"shape": "Tunable", "documentation": "<p>Whether the hyperparameter is tunable.</p>"}}, "documentation": "<p>Provides the name and default range of a continuous hyperparameter and whether the hyperparameter is tunable. A tunable hyperparameter can have its value determined during hyperparameter optimization (HPO).</p>"}, "DefaultContinuousHyperParameterRanges": {"type": "list", "member": {"shape": "DefaultContinuousHyperParameterRange"}, "max": 100}, "DefaultHyperParameterRanges": {"type": "structure", "members": {"integerHyperParameterRanges": {"shape": "DefaultIntegerHyperParameterRanges", "documentation": "<p>The integer-valued hyperparameters and their default ranges.</p>"}, "continuousHyperParameterRanges": {"shape": "DefaultContinuousHyperParameterRanges", "documentation": "<p>The continuous hyperparameters and their default ranges.</p>"}, "categoricalHyperParameterRanges": {"shape": "DefaultCategoricalHyperParameterRanges", "documentation": "<p>The categorical hyperparameters and their default ranges.</p>"}}, "documentation": "<p>Specifies the hyperparameters and their default ranges. Hyperparameters can be categorical, continuous, or integer-valued.</p>"}, "DefaultIntegerHyperParameterRange": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the hyperparameter.</p>"}, "minValue": {"shape": "IntegerMinValue", "documentation": "<p>The minimum allowable value for the hyperparameter.</p>"}, "maxValue": {"shape": "IntegerMaxValue", "documentation": "<p>The maximum allowable value for the hyperparameter.</p>"}, "isTunable": {"shape": "Tunable", "documentation": "<p>Indicates whether the hyperparameter is tunable.</p>"}}, "documentation": "<p>Provides the name and default range of a integer-valued hyperparameter and whether the hyperparameter is tunable. A tunable hyperparameter can have its value determined during hyperparameter optimization (HPO).</p>"}, "DefaultIntegerHyperParameterRanges": {"type": "list", "member": {"shape": "DefaultIntegerHyperParameterRange"}, "max": 100}, "DeleteCampaignRequest": {"type": "structure", "required": ["campaignArn"], "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign to delete.</p>"}}}, "DeleteDatasetGroupRequest": {"type": "structure", "required": ["datasetGroupArn"], "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group to delete.</p>"}}}, "DeleteDatasetRequest": {"type": "structure", "required": ["datasetArn"], "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset to delete.</p>"}}}, "DeleteEventTrackerRequest": {"type": "structure", "required": ["eventTrackerArn"], "members": {"eventTrackerArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event tracker to delete.</p>"}}}, "DeleteFilterRequest": {"type": "structure", "required": ["filterArn"], "members": {"filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter to delete.</p>"}}}, "DeleteMetricAttributionRequest": {"type": "structure", "required": ["metricAttributionArn"], "members": {"metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The metric attribution's Amazon Resource Name (ARN).</p>"}}}, "DeleteRecommenderRequest": {"type": "structure", "required": ["recommenderArn"], "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender to delete.</p>"}}}, "DeleteSchemaRequest": {"type": "structure", "required": ["schemaArn"], "members": {"schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the schema to delete.</p>"}}}, "DeleteSolutionRequest": {"type": "structure", "required": ["solutionArn"], "members": {"solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the solution to delete.</p>"}}}, "DescribeAlgorithmRequest": {"type": "structure", "required": ["algorithmArn"], "members": {"algorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the algorithm to describe.</p>"}}}, "DescribeAlgorithmResponse": {"type": "structure", "members": {"algorithm": {"shape": "Algorithm", "documentation": "<p>A listing of the properties of the algorithm.</p>"}}}, "DescribeBatchInferenceJobRequest": {"type": "structure", "required": ["batchInferenceJobArn"], "members": {"batchInferenceJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the batch inference job to describe.</p>"}}}, "DescribeBatchInferenceJobResponse": {"type": "structure", "members": {"batchInferenceJob": {"shape": "BatchInferenceJob", "documentation": "<p>Information on the specified batch inference job.</p>"}}}, "DescribeBatchSegmentJobRequest": {"type": "structure", "required": ["batchSegmentJobArn"], "members": {"batchSegmentJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the batch segment job to describe.</p>"}}}, "DescribeBatchSegmentJobResponse": {"type": "structure", "members": {"batchSegmentJob": {"shape": "BatchSegmentJob", "documentation": "<p>Information on the specified batch segment job.</p>"}}}, "DescribeCampaignRequest": {"type": "structure", "required": ["campaignArn"], "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign.</p>"}}}, "DescribeCampaignResponse": {"type": "structure", "members": {"campaign": {"shape": "Campaign", "documentation": "<p>The properties of the campaign.</p>"}}}, "DescribeDatasetExportJobRequest": {"type": "structure", "required": ["datasetExportJobArn"], "members": {"datasetExportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset export job to describe.</p>"}}}, "DescribeDatasetExportJobResponse": {"type": "structure", "members": {"datasetExportJob": {"shape": "DatasetExportJob", "documentation": "<p>Information about the dataset export job, including the status.</p> <p>The status is one of the following values:</p> <ul> <li> <p>CREATE PENDING</p> </li> <li> <p>CREATE IN_PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> </ul>"}}}, "DescribeDatasetGroupRequest": {"type": "structure", "required": ["datasetGroupArn"], "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group to describe.</p>"}}}, "DescribeDatasetGroupResponse": {"type": "structure", "members": {"datasetGroup": {"shape": "DatasetGroup", "documentation": "<p>A listing of the dataset group's properties.</p>"}}}, "DescribeDatasetImportJobRequest": {"type": "structure", "required": ["datasetImportJobArn"], "members": {"datasetImportJobArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset import job to describe.</p>"}}}, "DescribeDatasetImportJobResponse": {"type": "structure", "members": {"datasetImportJob": {"shape": "DatasetImportJob", "documentation": "<p>Information about the dataset import job, including the status.</p> <p>The status is one of the following values:</p> <ul> <li> <p>CREATE PENDING</p> </li> <li> <p>CREATE IN_PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> </ul>"}}}, "DescribeDatasetRequest": {"type": "structure", "required": ["datasetArn"], "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset to describe.</p>"}}}, "DescribeDatasetResponse": {"type": "structure", "members": {"dataset": {"shape": "Dataset", "documentation": "<p>A listing of the dataset's properties.</p>"}}}, "DescribeEventTrackerRequest": {"type": "structure", "required": ["eventTrackerArn"], "members": {"eventTrackerArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event tracker to describe.</p>"}}}, "DescribeEventTrackerResponse": {"type": "structure", "members": {"eventTracker": {"shape": "EventTracker", "documentation": "<p>An object that describes the event tracker.</p>"}}}, "DescribeFeatureTransformationRequest": {"type": "structure", "required": ["featureTransformationArn"], "members": {"featureTransformationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the feature transformation to describe.</p>"}}}, "DescribeFeatureTransformationResponse": {"type": "structure", "members": {"featureTransformation": {"shape": "FeatureTransformation", "documentation": "<p>A listing of the FeatureTransformation properties.</p>"}}}, "DescribeFilterRequest": {"type": "structure", "required": ["filterArn"], "members": {"filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter to describe.</p>"}}}, "DescribeFilterResponse": {"type": "structure", "members": {"filter": {"shape": "Filter", "documentation": "<p>The filter's details.</p>"}}}, "DescribeMetricAttributionRequest": {"type": "structure", "required": ["metricAttributionArn"], "members": {"metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The metric attribution's Amazon Resource Name (ARN).</p>"}}}, "DescribeMetricAttributionResponse": {"type": "structure", "members": {"metricAttribution": {"shape": "MetricAttribution", "documentation": "<p>The details of the metric attribution.</p>"}}}, "DescribeRecipeRequest": {"type": "structure", "required": ["recipeArn"], "members": {"recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe to describe.</p>"}}}, "DescribeRecipeResponse": {"type": "structure", "members": {"recipe": {"shape": "Recipe", "documentation": "<p>An object that describes the recipe.</p>"}}}, "DescribeRecommenderRequest": {"type": "structure", "required": ["recommenderArn"], "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender to describe.</p>"}}}, "DescribeRecommenderResponse": {"type": "structure", "members": {"recommender": {"shape": "Recommender", "documentation": "<p>The properties of the recommender.</p>"}}}, "DescribeSchemaRequest": {"type": "structure", "required": ["schemaArn"], "members": {"schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the schema to retrieve.</p>"}}}, "DescribeSchemaResponse": {"type": "structure", "members": {"schema": {"shape": "DatasetSchema", "documentation": "<p>The requested schema.</p>"}}}, "DescribeSolutionRequest": {"type": "structure", "required": ["solutionArn"], "members": {"solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution to describe.</p>"}}}, "DescribeSolutionResponse": {"type": "structure", "members": {"solution": {"shape": "Solution", "documentation": "<p>An object that describes the solution.</p>"}}}, "DescribeSolutionVersionRequest": {"type": "structure", "required": ["solutionVersionArn"], "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version.</p>"}}}, "DescribeSolutionVersionResponse": {"type": "structure", "members": {"solutionVersion": {"shape": "SolutionVersion", "documentation": "<p>The solution version.</p>"}}}, "Description": {"type": "string"}, "DockerURI": {"type": "string", "max": 256}, "Domain": {"type": "string", "enum": ["ECOMMERCE", "VIDEO_ON_DEMAND"]}, "ErrorMessage": {"type": "string"}, "EventTracker": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the event tracker.</p>"}, "eventTrackerArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the event tracker.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account that owns the event tracker.</p>"}, "trackingId": {"shape": "TrackingId", "documentation": "<p>The ID of the event tracker. Include this ID in requests to the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_UBS_PutEvents.html\">PutEvents</a> API.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that receives the event data.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the event tracker.</p> <p>An event tracker can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the event tracker was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the event tracker was last updated.</p>"}}, "documentation": "<p>Provides information about an event tracker.</p>"}, "EventTrackerSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the event tracker.</p>"}, "eventTrackerArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event tracker.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the event tracker.</p> <p>An event tracker can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the event tracker was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the event tracker was last updated.</p>"}}, "documentation": "<p>Provides a summary of the properties of an event tracker. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeEventTracker.html\">DescribeEventTracker</a> API.</p>"}, "EventTrackers": {"type": "list", "member": {"shape": "EventTrackerSummary"}, "max": 100}, "EventType": {"type": "string", "max": 256}, "EventValueThreshold": {"type": "string", "max": 256}, "ExcludedDatasetColumns": {"type": "map", "key": {"shape": "DatasetType"}, "value": {"shape": "ColumnNamesList"}, "max": 3}, "FailureReason": {"type": "string"}, "FeatureTransformation": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the feature transformation.</p>"}, "featureTransformationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the FeatureTransformation object.</p>"}, "defaultParameters": {"shape": "FeaturizationParameters", "documentation": "<p>Provides the default parameters for feature transformation.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the feature transformation.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The last update date and time (in Unix time) of the feature transformation.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the feature transformation.</p> <p>A feature transformation can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}}, "documentation": "<p>Provides feature transformation information. Feature transformation is the process of modifying raw input data into a form more suitable for model training.</p>"}, "FeatureTransformationParameters": {"type": "map", "key": {"shape": "ParameterName"}, "value": {"shape": "ParameterValue"}, "max": 100}, "FeaturizationParameters": {"type": "map", "key": {"shape": "ParameterName"}, "value": {"shape": "ParameterValue"}, "max": 100}, "Filter": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the filter.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The time at which the filter was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The time at which the filter was last updated.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group to which the filter belongs.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If the filter failed, the reason for its failure.</p>"}, "filterExpression": {"shape": "FilterExpression", "documentation": "<p>Specifies the type of item interactions to filter out of recommendation results. The filter expression must follow specific format rules. For information about filter expression structure and syntax, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/filter-expressions.html\">Filter expressions</a>.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the filter.</p>"}}, "documentation": "<p>Contains information on a recommendation filter, including its ARN, status, and filter expression.</p>"}, "FilterExpression": {"type": "string", "max": 2500, "min": 1, "sensitive": true}, "FilterSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the filter.</p>"}, "filterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the filter.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The time at which the filter was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The time at which the filter was last updated.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group to which the filter belongs.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If the filter failed, the reason for the failure.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the filter.</p>"}}, "documentation": "<p>A short summary of a filter's attributes.</p>"}, "Filters": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 100}, "GetSolutionMetricsRequest": {"type": "structure", "required": ["solutionVersionArn"], "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version for which to get metrics.</p>"}}}, "GetSolutionMetricsResponse": {"type": "structure", "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The same solution version ARN as specified in the request.</p>"}, "metrics": {"shape": "Metrics", "documentation": "<p>The metrics for the solution version. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/working-with-training-metrics.html\"> Evaluating a solution version with metrics </a>.</p>"}}}, "HPOConfig": {"type": "structure", "members": {"hpoObjective": {"shape": "HPOObjective", "documentation": "<p>The metric to optimize during HPO.</p> <note> <p>Amazon Personalize doesn't support configuring the <code>hpoObjective</code> at this time.</p> </note>"}, "hpoResourceConfig": {"shape": "HPOResourceConfig", "documentation": "<p>Describes the resource configuration for HPO.</p>"}, "algorithmHyperParameterRanges": {"shape": "HyperParameterRanges", "documentation": "<p>The hyperparameters and their allowable ranges.</p>"}}, "documentation": "<p>Describes the properties for hyperparameter optimization (HPO).</p>"}, "HPOObjective": {"type": "structure", "members": {"type": {"shape": "HPOObjectiveType", "documentation": "<p>The type of the metric. Valid values are <code>Maximize</code> and <code>Minimize</code>.</p>"}, "metricName": {"shape": "MetricName", "documentation": "<p>The name of the metric.</p>"}, "metricRegex": {"shape": "MetricRegex", "documentation": "<p>A regular expression for finding the metric in the training job logs.</p>"}}, "documentation": "<p>The metric to optimize during hyperparameter optimization (HPO).</p> <note> <p>Amazon Personalize doesn't support configuring the <code>hpoObjective</code> at this time.</p> </note>"}, "HPOObjectiveType": {"type": "string", "max": 256}, "HPOResource": {"type": "string", "max": 256}, "HPOResourceConfig": {"type": "structure", "members": {"maxNumberOfTrainingJobs": {"shape": "HPOResource", "documentation": "<p>The maximum number of training jobs when you create a solution version. The maximum value for <code>maxNumberOfTrainingJobs</code> is <code>40</code>.</p>"}, "maxParallelTrainingJobs": {"shape": "HPOResource", "documentation": "<p>The maximum number of parallel training jobs when you create a solution version. The maximum value for <code>maxParallelTrainingJobs</code> is <code>10</code>.</p>"}}, "documentation": "<p>Describes the resource configuration for hyperparameter optimization (HPO).</p>"}, "HyperParameterRanges": {"type": "structure", "members": {"integerHyperParameterRanges": {"shape": "IntegerHyperParameterRanges", "documentation": "<p>The integer-valued hyperparameters and their ranges.</p>"}, "continuousHyperParameterRanges": {"shape": "ContinuousHyperParameterRanges", "documentation": "<p>The continuous hyperparameters and their ranges.</p>"}, "categoricalHyperParameterRanges": {"shape": "CategoricalHyperParameterRanges", "documentation": "<p>The categorical hyperparameters and their ranges.</p>"}}, "documentation": "<p>Specifies the hyperparameters and their ranges. Hyperparameters can be categorical, continuous, or integer-valued.</p>"}, "HyperParameters": {"type": "map", "key": {"shape": "ParameterName"}, "value": {"shape": "ParameterValue"}, "max": 100}, "ImportMode": {"type": "string", "enum": ["FULL", "INCREMENTAL"]}, "IngestionMode": {"type": "string", "enum": ["BULK", "PUT", "ALL"]}, "IntegerHyperParameterRange": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the hyperparameter.</p>"}, "minValue": {"shape": "IntegerMinValue", "documentation": "<p>The minimum allowable value for the hyperparameter.</p>"}, "maxValue": {"shape": "IntegerMaxValue", "documentation": "<p>The maximum allowable value for the hyperparameter.</p>"}}, "documentation": "<p>Provides the name and range of an integer-valued hyperparameter.</p>"}, "IntegerHyperParameterRanges": {"type": "list", "member": {"shape": "IntegerHyperParameterRange"}, "max": 100}, "IntegerMaxValue": {"type": "integer", "max": 1000000}, "IntegerMinValue": {"type": "integer", "min": -1000000}, "InvalidInputException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Provide a valid value for the field or parameter.</p>", "exception": true}, "InvalidNextTokenException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The token is not valid.</p>", "exception": true}, "ItemAttribute": {"type": "string", "max": 150, "min": 1}, "KmsKeyArn": {"type": "string", "max": 2048, "pattern": "arn:aws.*:kms:.*:[0-9]{12}:key/.*"}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The limit on the number of requests per second has been exceeded.</p>", "exception": true}, "ListBatchInferenceJobsRequest": {"type": "structure", "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version from which the batch inference jobs were created.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of batch inference job results to return in each page. The default value is 100.</p>"}}}, "ListBatchInferenceJobsResponse": {"type": "structure", "members": {"batchInferenceJobs": {"shape": "BatchInferenceJobs", "documentation": "<p>A list containing information on each job that is returned.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is <code>null</code> when there are no more results to return.</p>"}}}, "ListBatchSegmentJobsRequest": {"type": "structure", "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version that the batch segment jobs used to generate batch segments.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of batch segment job results to return in each page. The default value is 100.</p>"}}}, "ListBatchSegmentJobsResponse": {"type": "structure", "members": {"batchSegmentJobs": {"shape": "BatchSegmentJobs", "documentation": "<p>A list containing information on each job that is returned.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is <code>null</code> when there are no more results to return.</p>"}}}, "ListCampaignsRequest": {"type": "structure", "members": {"solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution to list the campaigns for. When a solution is not specified, all the campaigns associated with the account are listed.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_ListCampaigns.html\">ListCampaigns</a> for getting the next set of campaigns (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of campaigns to return.</p>"}}}, "ListCampaignsResponse": {"type": "structure", "members": {"campaigns": {"shape": "Campaigns", "documentation": "<p>A list of the campaigns.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of campaigns (if they exist).</p>"}}}, "ListDatasetExportJobsRequest": {"type": "structure", "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset to list the dataset export jobs for.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListDatasetExportJobs</code> for getting the next set of dataset export jobs (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of dataset export jobs to return.</p>"}}}, "ListDatasetExportJobsResponse": {"type": "structure", "members": {"datasetExportJobs": {"shape": "DatasetExportJobs", "documentation": "<p>The list of dataset export jobs.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of dataset export jobs (if they exist).</p>"}}}, "ListDatasetGroupsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListDatasetGroups</code> for getting the next set of dataset groups (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of dataset groups to return.</p>"}}}, "ListDatasetGroupsResponse": {"type": "structure", "members": {"datasetGroups": {"shape": "DatasetGroups", "documentation": "<p>The list of your dataset groups.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of dataset groups (if they exist).</p>"}}}, "ListDatasetImportJobsRequest": {"type": "structure", "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset to list the dataset import jobs for.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListDatasetImportJobs</code> for getting the next set of dataset import jobs (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of dataset import jobs to return.</p>"}}}, "ListDatasetImportJobsResponse": {"type": "structure", "members": {"datasetImportJobs": {"shape": "DatasetImportJobs", "documentation": "<p>The list of dataset import jobs.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of dataset import jobs (if they exist).</p>"}}}, "ListDatasetsRequest": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that contains the datasets to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListDatasetImportJobs</code> for getting the next set of dataset import jobs (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of datasets to return.</p>"}}}, "ListDatasetsResponse": {"type": "structure", "members": {"datasets": {"shape": "Datasets", "documentation": "<p>An array of <code>Dataset</code> objects. Each object provides metadata information.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of datasets (if they exist).</p>"}}}, "ListEventTrackersRequest": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of a dataset group used to filter the response.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListEventTrackers</code> for getting the next set of event trackers (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of event trackers to return.</p>"}}}, "ListEventTrackersResponse": {"type": "structure", "members": {"eventTrackers": {"shape": "EventTrackers", "documentation": "<p>A list of event trackers.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of event trackers (if they exist).</p>"}}}, "ListFiltersRequest": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the dataset group that contains the filters.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListFilters</code> for getting the next set of filters (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of filters to return.</p>"}}}, "ListFiltersResponse": {"type": "structure", "members": {"Filters": {"shape": "Filters", "documentation": "<p>A list of returned filters.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of filters (if they exist).</p>"}}}, "ListMetricAttributionMetricsRequest": {"type": "structure", "members": {"metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the metric attribution to retrieve attributes for.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of metrics to return in one page of results.</p>"}}}, "ListMetricAttributionMetricsResponse": {"type": "structure", "members": {"metrics": {"shape": "MetricAttributes", "documentation": "<p>The metrics for the specified metric attribution.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous <code>ListMetricAttributionMetricsResponse</code> request to retrieve the next page of results.</p>"}}}, "ListMetricAttributionsRequest": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The metric attributions' dataset group Amazon Resource Name (ARN).</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of metric attributions to return in one page of results.</p>"}}}, "ListMetricAttributionsResponse": {"type": "structure", "members": {"metricAttributions": {"shape": "MetricAttributions", "documentation": "<p>The list of metric attributions.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>"}}}, "ListRecipesRequest": {"type": "structure", "members": {"recipeProvider": {"shape": "RecipeProvider", "documentation": "<p>The default is <code>SERVICE</code>.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListRecipes</code> for getting the next set of recipes (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of recipes to return.</p>"}, "domain": {"shape": "Domain", "documentation": "<p> Filters returned recipes by domain for a Domain dataset group. Only recipes (Domain dataset group use cases) for this domain are included in the response. If you don't specify a domain, all recipes are returned. </p>"}}}, "ListRecipesResponse": {"type": "structure", "members": {"recipes": {"shape": "Recipes", "documentation": "<p>The list of available recipes.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of recipes.</p>"}}}, "ListRecommendersRequest": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Domain dataset group to list the recommenders for. When a Domain dataset group is not specified, all the recommenders associated with the account are listed.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListRecommenders</code> for getting the next set of recommenders (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of recommenders to return.</p>"}}}, "ListRecommendersResponse": {"type": "structure", "members": {"recommenders": {"shape": "Recommenders", "documentation": "<p>A list of the recommenders.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of recommenders (if they exist).</p>"}}}, "ListSchemasRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListSchemas</code> for getting the next set of schemas (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of schemas to return.</p>"}}}, "ListSchemasResponse": {"type": "structure", "members": {"schemas": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>A list of schemas.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token used to get the next set of schemas (if they exist).</p>"}}}, "ListSolutionVersionsRequest": {"type": "structure", "members": {"solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListSolutionVersions</code> for getting the next set of solution versions (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of solution versions to return.</p>"}}}, "ListSolutionVersionsResponse": {"type": "structure", "members": {"solutionVersions": {"shape": "SolutionVersions", "documentation": "<p>A list of solution versions describing the version properties.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of solution versions (if they exist).</p>"}}}, "ListSolutionsRequest": {"type": "structure", "members": {"datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token returned from the previous call to <code>ListSolutions</code> for getting the next set of solutions (if they exist).</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of solutions to return.</p>"}}}, "ListSolutionsResponse": {"type": "structure", "members": {"solutions": {"shape": "Solutions", "documentation": "<p>A list of the current solutions.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token for getting the next set of solutions (if they exist).</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource's Amazon Resource Name.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>The resource's tags.</p>"}}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "MetricAttribute": {"type": "structure", "required": ["eventType", "metricName", "expression"], "members": {"eventType": {"shape": "EventType", "documentation": "<p>The metric's event type.</p>"}, "metricName": {"shape": "MetricName", "documentation": "<p>The metric's name. The name helps you identify the metric in Amazon CloudWatch or Amazon S3.</p>"}, "expression": {"shape": "MetricExpression", "documentation": "<p>The attribute's expression. Available functions are <code>SUM()</code> or <code>SAMPLECOUNT()</code>. For SUM() functions, provide the dataset type (either Interactions or Items) and column to sum as a parameter. For example SUM(Items.PRICE).</p>"}}, "documentation": "<p>Contains information on a metric that a metric attribution reports on. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>.</p>"}, "MetricAttributes": {"type": "list", "member": {"shape": "MetricAttribute"}, "max": 10}, "MetricAttributesNamesList": {"type": "list", "member": {"shape": "MetricName"}, "max": 10}, "MetricAttribution": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The metric attribution's name.</p>"}, "metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The metric attribution's Amazon Resource Name (ARN).</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The metric attribution's dataset group Amazon Resource Name (ARN).</p>"}, "metricsOutputConfig": {"shape": "MetricAttributionOutput", "documentation": "<p>The metric attribution's output configuration.</p>"}, "status": {"shape": "Status", "documentation": "<p>The metric attribution's status.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The metric attribution's creation date time.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The metric attribution's last updated date time.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>The metric attribution's failure reason.</p>"}}, "documentation": "<p>Contains information on a metric attribution. A metric attribution creates reports on the data that you import into Amazon Personalize. Depending on how you import the data, you can view reports in Amazon CloudWatch or Amazon S3. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>.</p>"}, "MetricAttributionOutput": {"type": "structure", "required": ["roleArn"], "members": {"s3DataDestination": {"shape": "S3DataConfig"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that has permissions to add data to your output Amazon S3 bucket and add metrics to Amazon CloudWatch. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/measuring-recommendation-impact.html\">Measuring impact of recommendations</a>.</p>"}}, "documentation": "<p>The output configuration details for a metric attribution.</p>"}, "MetricAttributionSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the metric attribution.</p>"}, "metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The metric attribution's Amazon Resource Name (ARN).</p>"}, "status": {"shape": "Status", "documentation": "<p>The metric attribution's status.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The metric attribution's creation date time.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The metric attribution's last updated date time.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>The metric attribution's failure reason.</p>"}}, "documentation": "<p>Provides a summary of the properties of a metric attribution. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeMetricAttribution.html\">DescribeMetricAttribution</a>.</p>"}, "MetricAttributions": {"type": "list", "member": {"shape": "MetricAttributionSummary"}, "max": 100}, "MetricExpression": {"type": "string", "max": 256}, "MetricName": {"type": "string", "max": 256}, "MetricRegex": {"type": "string", "max": 256}, "MetricValue": {"type": "double"}, "Metrics": {"type": "map", "key": {"shape": "MetricName"}, "value": {"shape": "MetricValue"}, "max": 100}, "Name": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9\\-_]*"}, "NextToken": {"type": "string", "max": 1500}, "NumBatchResults": {"type": "integer"}, "ObjectiveSensitivity": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "OFF"]}, "OptimizationObjective": {"type": "structure", "members": {"itemAttribute": {"shape": "ItemAttribute", "documentation": "<p>The numerical metadata column in an Items dataset related to the optimization objective. For example, VIDEO_LENGTH (to maximize streaming minutes), or PRICE (to maximize revenue).</p>"}, "objectiveSensitivity": {"shape": "ObjectiveSensitivity", "documentation": "<p>Specifies how Amazon Personalize balances the importance of your optimization objective versus relevance.</p>"}}, "documentation": "<p>Describes the additional objective for the solution, such as maximizing streaming minutes or increasing revenue. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/optimizing-solution-for-objective.html\">Optimizing a solution</a>.</p>"}, "ParameterName": {"type": "string", "max": 256}, "ParameterValue": {"type": "string", "max": 1000}, "PerformAutoML": {"type": "boolean"}, "PerformHPO": {"type": "boolean"}, "Recipe": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the recipe.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe.</p>"}, "algorithmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the algorithm that Amazon Personalize uses to train the model.</p>"}, "featureTransformationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the FeatureTransformation object.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the recipe.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the recipe.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recipe was created.</p>"}, "recipeType": {"shape": "RecipeType", "documentation": "<p>One of the following values:</p> <ul> <li> <p>PERSONALIZED_RANKING</p> </li> <li> <p>RELATED_ITEMS</p> </li> <li> <p>USER_PERSONALIZATION</p> </li> </ul>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recipe was last updated.</p>"}}, "documentation": "<p>Provides information about a recipe. Each recipe provides an algorithm that Amazon Personalize uses in model training when you use the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_CreateSolution.html\">CreateSolution</a> operation. </p>"}, "RecipeProvider": {"type": "string", "enum": ["SERVICE"]}, "RecipeSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the recipe.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the recipe.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the recipe was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the recipe was last updated.</p>"}, "domain": {"shape": "Domain", "documentation": "<p>The domain of the recipe (if the recipe is a Domain dataset group use case).</p>"}}, "documentation": "<p>Provides a summary of the properties of a recipe. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeRecipe.html\">DescribeRecipe</a> API.</p>"}, "RecipeType": {"type": "string", "max": 256}, "Recipes": {"type": "list", "member": {"shape": "RecipeSummary"}, "max": 100}, "Recommender": {"type": "structure", "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Domain dataset group that contains the recommender.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the recommender.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe (Domain dataset group use case) that the recommender was created for. </p>"}, "recommenderConfig": {"shape": "RecommenderConfig", "documentation": "<p>The configuration details of the recommender.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recommender was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recommender was last updated.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the recommender.</p> <p>A recommender can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>STOP PENDING &gt; STOP IN_PROGRESS &gt; INACTIVE &gt; START PENDING &gt; START IN_PROGRESS &gt; ACTIVE</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a recommender fails, the reason behind the failure.</p>"}, "latestRecommenderUpdate": {"shape": "RecommenderUpdateSummary", "documentation": "<p>Provides a summary of the latest updates to the recommender. </p>"}, "modelMetrics": {"shape": "Metrics", "documentation": "<p>Provides evaluation metrics that help you determine the performance of a recommender. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/evaluating-recommenders.html\"> Evaluating a recommender</a>.</p>"}}, "documentation": "<p>Describes a recommendation generator for a Domain dataset group. You create a recommender in a Domain dataset group for a specific domain use case (domain recipe), and specify the recommender in a <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_RS_GetRecommendations.html\">GetRecommendations</a> request.</p>"}, "RecommenderConfig": {"type": "structure", "members": {"itemExplorationConfig": {"shape": "HyperParameters", "documentation": "<p>Specifies the exploration configuration hyperparameters, including <code>explorationWeight</code> and <code>explorationItemAgeCutOff</code>, you want to use to configure the amount of item exploration Amazon Personalize uses when recommending items. Provide <code>itemExplorationConfig</code> data only if your recommenders generate personalized recommendations for a user (not popular items or similar items).</p>"}, "minRecommendationRequestsPerSecond": {"shape": "TransactionsPerSecond", "documentation": "<p>Specifies the requested minimum provisioned recommendation requests per second that Amazon Personalize will support. A high <code>minRecommendationRequestsPerSecond</code> will increase your bill. We recommend starting with 1 for <code>minRecommendationRequestsPerSecond</code> (the default). Track your usage using Amazon CloudWatch metrics, and increase the <code>minRecommendationRequestsPerSecond</code> as necessary.</p>"}, "trainingDataConfig": {"shape": "TrainingDataConfig", "documentation": "<p> Specifies the training data configuration to use when creating a domain recommender. </p>"}}, "documentation": "<p>The configuration details of the recommender.</p>"}, "RecommenderSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the recommender.</p>"}, "recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Domain dataset group that contains the recommender.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe (Domain dataset group use case) that the recommender was created for.</p>"}, "recommenderConfig": {"shape": "RecommenderConfig", "documentation": "<p>The configuration details of the recommender.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the recommender. A recommender can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>STOP PENDING &gt; STOP IN_PROGRESS &gt; INACTIVE &gt; START PENDING &gt; START IN_PROGRESS &gt; ACTIVE</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recommender was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recommender was last updated.</p>"}}, "documentation": "<p>Provides a summary of the properties of the recommender.</p>"}, "RecommenderUpdateSummary": {"type": "structure", "members": {"recommenderConfig": {"shape": "RecommenderConfig", "documentation": "<p>The configuration details of the recommender update.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix format) that the recommender update was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the recommender update was last updated.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the recommender update.</p> <p>A recommender can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>STOP PENDING &gt; STOP IN_PROGRESS &gt; INACTIVE &gt; START PENDING &gt; START IN_PROGRESS &gt; ACTIVE</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a recommender update fails, the reason behind the failure.</p>"}}, "documentation": "<p>Provides a summary of the properties of a recommender update. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeRecommender.html\">DescribeRecommender</a> API.</p>"}, "Recommenders": {"type": "list", "member": {"shape": "RecommenderSummary"}, "max": 100}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource already exists.</p>", "exception": true}, "ResourceConfig": {"type": "map", "key": {"shape": "ParameterName"}, "value": {"shape": "ParameterValue"}, "max": 100}, "ResourceInUseException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource is in use.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Could not find the specified resource.</p>", "exception": true}, "RoleArn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+"}, "S3DataConfig": {"type": "structure", "required": ["path"], "members": {"path": {"shape": "S3Location", "documentation": "<p>The file path of the Amazon S3 bucket.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Key Management Service (KMS) key that Amazon Personalize uses to encrypt or decrypt the input and output files.</p>"}}, "documentation": "<p>The configuration details of an Amazon S3 input or output bucket.</p>"}, "S3Location": {"type": "string", "max": 256, "pattern": "(s3|http|https)://.+"}, "Schemas": {"type": "list", "member": {"shape": "DatasetSchemaSummary"}, "max": 100}, "Solution": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the solution.</p>"}, "solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the solution.</p>"}, "performHPO": {"shape": "PerformHPO", "documentation": "<p>Whether to perform hyperparameter optimization (HPO) on the chosen recipe. The default is <code>false</code>.</p>"}, "performAutoML": {"shape": "PerformAutoML", "documentation": "<important> <p>We don't recommend enabling automated machine learning. Instead, match your use case to the available Amazon Personalize recipes. For more information, see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/determining-use-case.html\">Determining your use case.</a> </p> </important> <p>When true, Amazon Personalize performs a search for the best USER_PERSONALIZATION recipe from the list specified in the solution configuration (<code>recipeArn</code> must not be specified). When false (the default), Amazon Personalize uses <code>recipeArn</code> for training.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the recipe used to create the solution. This is required when <code>performAutoML</code> is false.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group that provides the training data.</p>"}, "eventType": {"shape": "EventType", "documentation": "<p>The event type (for example, 'click' or 'like') that is used for training the model. If no <code>eventType</code> is provided, Amazon Personalize uses all interactions for training with equal weight regardless of type.</p>"}, "solutionConfig": {"shape": "SolutionConfig", "documentation": "<p>Describes the configuration properties for the solution.</p>"}, "autoMLResult": {"shape": "AutoMLResult", "documentation": "<p>When <code>performAutoML</code> is true, specifies the best recipe found.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the solution.</p> <p>A solution can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The creation date and time (in Unix time) of the solution.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the solution was last updated.</p>"}, "latestSolutionVersion": {"shape": "SolutionVersionSummary", "documentation": "<p>Describes the latest version of the solution, including the status and the ARN.</p>"}}, "documentation": "<p>An object that provides information about a solution. A solution is a trained model that can be deployed as a campaign.</p>"}, "SolutionConfig": {"type": "structure", "members": {"eventValueThreshold": {"shape": "EventValueThreshold", "documentation": "<p>Only events with a value greater than or equal to this threshold are used for training a model.</p>"}, "hpoConfig": {"shape": "HPOConfig", "documentation": "<p>Describes the properties for hyperparameter optimization (HPO).</p>"}, "algorithmHyperParameters": {"shape": "HyperParameters", "documentation": "<p>Lists the hyperparameter names and ranges.</p>"}, "featureTransformationParameters": {"shape": "FeatureTransformationParameters", "documentation": "<p>Lists the feature transformation parameters.</p>"}, "autoMLConfig": {"shape": "AutoMLConfig", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_AutoMLConfig.html\">AutoMLConfig</a> object containing a list of recipes to search when AutoML is performed.</p>"}, "optimizationObjective": {"shape": "OptimizationObjective", "documentation": "<p>Describes the additional objective for the solution, such as maximizing streaming minutes or increasing revenue. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/optimizing-solution-for-objective.html\">Optimizing a solution</a>.</p>"}, "trainingDataConfig": {"shape": "TrainingDataConfig", "documentation": "<p> Specifies the training data configuration to use when creating a custom solution version (trained model). </p>"}}, "documentation": "<p>Describes the configuration properties for the solution.</p>"}, "SolutionSummary": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the solution.</p>"}, "solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the solution.</p> <p>A solution can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> <li> <p>DELETE PENDING &gt; DELETE IN_PROGRESS</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the solution was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the solution was last updated.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recipe used by the solution.</p>"}}, "documentation": "<p>Provides a summary of the properties of a solution. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolution.html\">DescribeSolution</a> API.</p>"}, "SolutionVersion": {"type": "structure", "members": {"name": {"shape": "Name", "documentation": "<p>The name of the solution version.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the solution version.</p>"}, "solutionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the solution.</p>"}, "performHPO": {"shape": "PerformHPO", "documentation": "<p>Whether to perform hyperparameter optimization (HPO) on the chosen recipe. The default is <code>false</code>.</p>"}, "performAutoML": {"shape": "PerformAutoML", "documentation": "<p>When true, Amazon Personalize searches for the most optimal recipe according to the solution configuration. When false (the default), Amazon Personalize uses <code>recipeArn</code>.</p>"}, "recipeArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the recipe used in the solution.</p>"}, "eventType": {"shape": "EventType", "documentation": "<p>The event type (for example, 'click' or 'like') that is used for training the model.</p>"}, "datasetGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset group providing the training data.</p>"}, "solutionConfig": {"shape": "SolutionConfig", "documentation": "<p>Describes the configuration properties for the solution.</p>"}, "trainingHours": {"shape": "TrainingHours", "documentation": "<p>The time used to train the model. You are billed for the time it takes to train a model. This field is visible only after Amazon Personalize successfully trains a model.</p>"}, "trainingMode": {"shape": "TrainingMode", "documentation": "<p>The scope of training to be performed when creating the solution version. The <code>FULL</code> option trains the solution version based on the entirety of the input solution's training data, while the <code>UPDATE</code> option processes only the data that has changed in comparison to the input solution. Choose <code>UPDATE</code> when you want to incrementally update your solution version instead of creating an entirely new one.</p> <important> <p>The <code>UPDATE</code> option can only be used when you already have an active solution version created from the input solution using the <code>FULL</code> option and the input solution was trained with the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/native-recipe-new-item-USER_PERSONALIZATION.html\">User-Personalization</a> recipe or the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/native-recipe-hrnn-coldstart.html\">HRNN-Coldstart</a> recipe.</p> </important>"}, "tunedHPOParams": {"shape": "TunedHPOParams", "documentation": "<p>If hyperparameter optimization was performed, contains the hyperparameter values of the best performing model.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the solution version.</p> <p>A solution version can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING</p> </li> <li> <p>CREATE IN_PROGRESS</p> </li> <li> <p>ACTIVE</p> </li> <li> <p>CREATE FAILED</p> </li> <li> <p>CREATE STOPPING</p> </li> <li> <p>CREATE STOPPED</p> </li> </ul>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If training a solution version fails, the reason for the failure.</p>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that this version of the solution was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the solution was last updated.</p>"}}, "documentation": "<p>An object that provides information about a specific version of a <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_Solution.html\">Solution</a> in a Custom dataset group.</p>"}, "SolutionVersionSummary": {"type": "structure", "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the solution version.</p> <p>A solution version can be in one of the following states:</p> <ul> <li> <p>CREATE PENDING &gt; CREATE IN_PROGRESS &gt; ACTIVE -or- CREATE FAILED</p> </li> </ul>"}, "creationDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that this version of a solution was created.</p>"}, "lastUpdatedDateTime": {"shape": "Date", "documentation": "<p>The date and time (in Unix time) that the solution version was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>If a solution version fails, the reason behind the failure.</p>"}}, "documentation": "<p>Provides a summary of the properties of a solution version. For a complete listing, call the <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/API_DescribeSolutionVersion.html\">DescribeSolutionVersion</a> API.</p>"}, "SolutionVersions": {"type": "list", "member": {"shape": "SolutionVersionSummary"}, "max": 100}, "Solutions": {"type": "list", "member": {"shape": "SolutionSummary"}, "max": 100}, "StartRecommenderRequest": {"type": "structure", "required": ["recommenderArn"], "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender to start.</p>"}}}, "StartRecommenderResponse": {"type": "structure", "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender you started.</p>"}}}, "Status": {"type": "string", "max": 256}, "StopRecommenderRequest": {"type": "structure", "required": ["recommenderArn"], "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender to stop.</p>"}}}, "StopRecommenderResponse": {"type": "structure", "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender you stopped.</p>"}}}, "StopSolutionVersionCreationRequest": {"type": "structure", "required": ["solutionVersionArn"], "members": {"solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the solution version you want to stop creating.</p>"}}}, "Tag": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "tagValue"], "members": {"tagKey": {"shape": "TagKey", "documentation": "<p>One part of a key-value pair that makes up a tag. A key is a general label that acts like a category for more specific tag values.</p>"}, "tagValue": {"shape": "TagValue", "documentation": "<p>The optional part of a key-value pair that makes up a tag. A value acts as a descriptor within a tag category (key).</p>"}}, "documentation": "<p>The optional metadata that you apply to resources to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">Tagging Amazon Personalize recources</a>. </p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags to apply to the resource. For more information see <a href=\"https://docs.aws.amazon.com/personalize/latest/dg/tagging-resources.html\">Tagging Amazon Personalize recources</a>.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "Tags": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TooManyTagKeysException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request contains more tag keys than can be associated with a resource (50 tag keys per resource). </p>", "exception": true}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have exceeded the maximum number of tags you can apply to this resource. </p>", "exception": true}, "TrackingId": {"type": "string", "max": 256}, "TrainingDataConfig": {"type": "structure", "members": {"excludedDatasetColumns": {"shape": "ExcludedDatasetColumns", "documentation": "<p>Specifies the columns to exclude from training. Each key is a dataset type, and each value is a list of columns. Exclude columns to control what data Amazon Personalize uses to generate recommendations. For example, you might have a column that you want to use only to filter recommendations. You can exclude this column from training and Amazon Personalize considers it only when filtering. </p>"}}, "documentation": "<p>The training data configuration to use when creating a domain recommender or custom solution version (trained model).</p>"}, "TrainingHours": {"type": "double", "min": 0}, "TrainingInputMode": {"type": "string", "max": 256}, "TrainingMode": {"type": "string", "enum": ["FULL", "UPDATE"]}, "TransactionsPerSecond": {"type": "integer", "min": 1}, "Tunable": {"type": "boolean"}, "TunedHPOParams": {"type": "structure", "members": {"algorithmHyperParameters": {"shape": "HyperParameters", "documentation": "<p>A list of the hyperparameter values of the best performing model.</p>"}}, "documentation": "<p>If hyperparameter optimization (HPO) was performed, contains the hyperparameter values of the best performing model.</p>"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource's Amazon Resource Name (ARN).</p>"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>Keys to remove from the resource's tags.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateCampaignRequest": {"type": "structure", "required": ["campaignArn"], "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the campaign.</p>"}, "solutionVersionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of a new solution version to deploy.</p>"}, "minProvisionedTPS": {"shape": "TransactionsPerSecond", "documentation": "<p>Specifies the requested minimum provisioned transactions (recommendations) per second that Amazon Personalize will support. A high <code>minProvisionedTPS</code> will increase your bill. We recommend starting with 1 for <code>minProvisionedTPS</code> (the default). Track your usage using Amazon CloudWatch metrics, and increase the <code>minProvisionedTPS</code> as necessary.</p>"}, "campaignConfig": {"shape": "CampaignConfig", "documentation": "<p>The configuration details of a campaign.</p>"}}}, "UpdateCampaignResponse": {"type": "structure", "members": {"campaignArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The same campaign ARN as given in the request.</p>"}}}, "UpdateDatasetRequest": {"type": "structure", "required": ["datasetArn", "schemaArn"], "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset that you want to update.</p>"}, "schemaArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the new schema you want use.</p>"}}}, "UpdateDatasetResponse": {"type": "structure", "members": {"datasetArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the dataset you updated.</p>"}}}, "UpdateMetricAttributionRequest": {"type": "structure", "members": {"addMetrics": {"shape": "MetricAttributes", "documentation": "<p>Add new metric attributes to the metric attribution.</p>"}, "removeMetrics": {"shape": "MetricAttributesNamesList", "documentation": "<p>Remove metric attributes from the metric attribution.</p>"}, "metricsOutputConfig": {"shape": "MetricAttributionOutput", "documentation": "<p>An output config for the metric attribution.</p>"}, "metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the metric attribution to update.</p>"}}}, "UpdateMetricAttributionResponse": {"type": "structure", "members": {"metricAttributionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the metric attribution that you updated.</p>"}}}, "UpdateRecommenderRequest": {"type": "structure", "required": ["recommenderArn", "recommenderConfig"], "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the recommender to modify.</p>"}, "recommenderConfig": {"shape": "RecommenderConfig", "documentation": "<p>The configuration details of the recommender.</p>"}}}, "UpdateRecommenderResponse": {"type": "structure", "members": {"recommenderArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The same recommender Amazon Resource Name (ARN) as given in the request.</p>"}}}}, "documentation": "<p>Amazon Personalize is a machine learning service that makes it easy to add individualized recommendations to customers.</p>"}