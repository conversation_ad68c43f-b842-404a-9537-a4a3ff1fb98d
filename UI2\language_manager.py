#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语言管理模块
提供中英文文本翻译功能
"""

class LanguageManager:
    """语言管理器"""
    
    def __init__(self):
        self.current_language = 'zh'  # 默认中文
        self._translations = {
            'zh': {
                # 菜单栏
                'file': '文件',
                'open_spectrum_file': '打开图谱文件',
                'print_report': '打印报告',
                'save_files': '保存文件',
                'set_working_directory': '设置工作目录',
                'about': '关于',
                'recent_files': '最近文件',
                'no_recent_files': '无最近文件',
                
                'calibration': '校准',
                'laser_on': '开启激光',
                'laser_off': '关闭激光',
                'auxiliary_test': '辅助测试',
                'wavenumber_calibration': '波数校准',
                
                'settings': '设置',
                'language': '语言',
                'chinese': '中文',
                'english': 'English',
                'update_service': '更新服务',
                'software_management': '软件管理',
                'password_management': '密码管理',
                'log_viewer': '系统日志',
                
                'help': '帮助',
                'manual': '操作手册',
                'instrument_info': '仪器信息',
                'import_database': '导入数据库',
                
                # 侧边栏
                'data_management': '数据管理',
                'baseline_calibration': '基线校准',
                'peak_finding': '寻峰',
                'data_smoothing': '数据平滑',
                'normalization': '归一化',
                'restoration': '还原',
                'clear_spectrum': '清除图谱',
                'communication': '通讯',
                'user': '用户',
                
                # 编辑器
                'opened_spectrum_files': '已打开谱图文件',
                'no_file_loaded': '未加载文件',
                'current_test_filename': '当前测试文件名称：',
                'measurement_progress': '测点测量进度：',
                'laser_power': '激光功率 (%):',
                'integration_time': '积分时间 (ms):',
                'naming_rules': '命名规则',
                'sample_name': '样品名:',
                'department': '科室:',
                'patient_id': '患者编号:',
                'select_date': '选择日期:',
                'laser_parameters': '激光参数设置',
                'laser_power_percent': '激光功率(%):',
                'integration_time_ms': '积分时间(ms):',
                'average_count': '平均次数:',
                'confirm_params': '确定名称和参数',
                'start_measurement': '开始测量',
                'stop_measurement': '中止测量',
                'pause': '暂停',
                
                # 文件右键菜单
                'clear_spectrum': '清除谱图',
                'change_spectrum_color': '更改谱图颜色',
                'modify_spectrum_name': '修改谱图名称',
                'select_spectrum_color': '选择谱图颜色',
                
                # 光谱图右键菜单
                'zoom': '缩放',
                'pan': '平移',
                'save_image': '保存图片',
                'show_grid': '显示网格线',
                'hide_grid': '隐藏网格线',
                'reset_view_to_default': '恢复默认大小',
                'switch_to_raman_shift': '切换为拉曼位移',
                'switch_to_pixel_coordinates': '切换为像素坐标',
                
                # 坐标轴标签
                'intensity': '强度(a.u.)',
                'pixel': '像素',
                'raman_shift': '拉曼位移',
                'raman_shift_cm': '拉曼位移 (cm-1)',
                'pixel_coordinates': '像素坐标',
                
                # 对话框
                'warning': '警告',
                'success': '成功',
                'error': '错误',
                'confirm': '确认',
                'cancel': '取消',
                'ok': '确定',
                'yes': '是',
                'no': '否',
                'continue': '继续',
                'restart': '重新开始',
                'terminate': '终止',
                
                                 # 消息
                 'please_enter_sample_name': '请输入样品名',
                 'please_enter_department': '请输入科室',
                 'please_enter_patient_id': '请输入患者编号',
                 'name_and_params_saved': '名称和参数已保存',
                 'confirm_clear': '确认清除',
                 'confirm_clear_file': '您确定要清除文件 \'{}\' 及其谱图？',
                 'file_cleared': '已清除文件: {}',
                 'color_changed': '已更改文件 \'{}\' 的颜色',
                 'measurement_paused': '测量已暂停，请选择操作',
                 'measurement_completed': '测量完成',
                 'measurement_stopped': '测量已停止',
                 
                 # 辅助测试消息
                 'dark_current_completed': '采集暗电流完成，请接入元素灯，点击确定开始检测。',
                 'adjust_element_lamp': '采集完成后，请调整元素灯，点击确定继续检测，点击否关闭窗口。',
                 
                 # 保存文件对话框
                 'select_files_to_save': '选择要保存的文件',
                 'select_all': '全选',
                 'deselect_all': '取消全选',
                 'save_settings': '保存设置',
                 'save_format': '保存格式:',
                 'output_directory': '输出目录:',
                 'browse': '浏览...',
                 'select_output_directory': '选择输出目录...',
                 'filename_prefix': '文件名前缀:',
                 'optional_filename_prefix': '可选：文件名前缀',
                 'start_saving': '开始保存',
                 'close': '关闭',
                 'no_files_to_save': '没有可保存的文件，请先加载文件',
                 
                 # 系统日志
                 'system_log': '系统日志',
                 'log_viewer': '日志查看器',
                 
                 # 更新服务对话框
                 'service_update': '服务更新',
                 'update_package_location': '更新包位置：',
                 'not_selected': '未选择',
                 'select_update_package': '选择更新包(ZIP)',
                 'installation_directory': '安装目录：',
                 'select_installation_directory': '选择安装目录',
                 'update_instructions': '说明：\n1. 请选择下载好的ZIP格式更新包\n2. 选择软件安装目录，服务将更新到其下的Service文件夹中',
                 'incomplete_input': '输入不完整',
                 'please_select_package_and_directory': '请选择更新包和安装目录',
                 'confirm_update': '确认更新',
                 'about_to_update_service': '即将使用更新包更新服务\n\n更新包：{}\n目标目录：{}\n\n此操作将覆盖现有服务文件，是否继续？',
                 'select_service_update_package': '选择服务更新包',
                 'select_installation_directory_service': '选择安装目录（Service文件夹将在此目录下创建）',
                 'update_successful': '更新成功',
                 'service_updated_successfully': '服务更新成功！\n\n更新包：{}\n目标目录：{}\n\n服务已重新启动。',
                 'update_failed': '更新失败',
                 'service_update_failed': '服务更新失败：{}',
                 
                 # 软件管理对话框
                 'software_management': '软件管理',
                 'reset_password': '重置密码',
                 'reset_settings': '重置设置',
                 'change_settings': '修改设置',
                 'current_settings': '当前设置',
                 'username': '用户名：',
                 'max_curves': '最大曲线数：',
                 'x_axis': 'X轴：',
                 'output_format': '输出格式：',
                 'reset_all_settings': '重置所有设置到默认值',
                 'save_settings_to_file': '保存设置到文件',
                 'settings_saved_successfully': '设置保存成功',
                 'settings_reset_successfully': '设置重置成功',
                 
                 # 导入数据库对话框
                 'select_import_method': '选择导入方式',
                 'please_select_import_method': '请选择导入方式：',
                 'import_spectrum_files_to_database': '导入光谱文件到数据库',
                 'import_existing_database_file': '导入现有数据库文件',
                 'select_spectrum_data_files': '选择光谱数据文件',
                 'import_progress': '导入进度',
                 'importing_files': '正在导入 {} 个文件...',
                 'importing_file': '正在导入: {}',
                 'import_complete': '导入完成',
                 'import_successful': '导入成功',
                 'successfully_imported_files': '成功导入 {} 个光谱文件到数据库',
                 'import_results': '成功导入 {}/{} 个文件\n\n失败的文件：\n{}',
                 'and_more_files': '\n... 还有 {} 个文件',
                 
                 # 关于对话框
                 'software_name': '软件名称: 拉曼光谱鉴定软件 (英文名 neolly)',
                 'model_specification': '型号/规格: neoraman-neolily-01',
                 'release_version': '发布版本: V1.0',
                 'full_version': '完整版本: V1.0.0.1',
                 'release_date': '发布日期: 2025.6.01',
                 'security_level': '软件安全性级别: 中等',
                 'login_username': '登录用户名: {}',
                 'last_login_time': '最后登录时间: {}',
                 
                 # 操作手册
                 'manual_not_found': '未找到操作手册: {}',
                 'cannot_open_manual': '无法打开操作手册: {}',
                 
                 # 报告对话框
                 'raman_spectrum_analysis_report': '拉曼光谱分析报告',
                 'font': '字体',
                 'color': '颜色',
                 'background': '背景',
                 'report_generation_time': '生成时间：{}',
                 'file_information': '1. 文件信息',
                 'file_number': '文件 {}',
                 'parameter': '参数',
                 'value': '值',
                 'device_serial_number': '设备序列号',
                 'operator': '操作员',
                 'integration_time': '积分时间',
                 'laser_power': '激光功率',
                 'average_count': '平均次数',
                 'acquisition_mode': '采集模式',
                 'acquisition_interval': '采集间隔',
                 'pixel': '像素',
                 'none': '无',
                 'no_files_selected': '未选择任何文件',
                 'spectrum_analysis_results': '2. 光谱分析结果',
                 'spectrum_image_unavailable': '光谱图像不可用',
                 'save_report': '保存报告',
                 
                 # 密码管理对话框
                 'password_management': '密码管理',
                 'username_colon': '用户名:',
                 'new_password': '新密码:',
                 'enter_new_password': '请输入新密码',
                 'confirm_password': '确认密码:',
                 'enter_confirm_password': '请再次输入新密码',
                 'change_password': '修改密码',
                 'password_changed_successfully': '密码修改成功！',
                 'password_change_failed': '密码修改失败：{}',
                 'password_mismatch': '两次输入的密码不一致',
                 'invalid_password': '密码格式不正确',
                 'password_requirements': '密码要求：至少8位，包含字母和数字',
                 
                 # 用户设置对话框
                 'operator': '操作员：',
                 'work_unit': '工作单位：',
                 'contact_phone': '联系电话：',
                 'work_address': '工作地址：',
                 'auto_login': '是否自动登陆',
                 'settings': '设置',
                 'settings_successful': '设置成功。',
                 
                 # 通讯对话框
                 'laser_communication': '激光器通讯',
                 'spectrometer_communication': '光谱仪通讯',
                 'connected': '已连接',
                 'disconnected': '未连接',
                 
                 # 日志查看器
                 'log_filter': '日志过滤器',
                 'level': '级别:',
                 'category': '类别:',
                 'days': '天数:',
                 'limit': '限制:',
                 'refresh': '刷新',
                 'export_logs': '导出日志',
                 'cleanup_now': '立即清理',
                 'time': '时间',
                 'level_col': '级别',
                 'category_col': '类别',
                 'message': '消息',
                 'details': '详情',
                 'create_time': '创建时间',
                 'all': '全部',
                 'records': ' 条',
                 
                 # 更新服务进度
                 'updating_service': '正在更新服务...',
                 'stopping_service': '正在停止服务...',
                 'backing_up_files': '正在备份现有文件...',
                 'extracting_package': '正在解压更新包...',
                 'copying_new_files': '正在复制新文件...',
                 'reinstalling_service': '正在重新安装服务...',
                 'starting_service': '正在启动服务...',
                 'unsupported_format': '不支持的文件格式: {}',
                 
                 # 日志查看器状态
                 'ready': '就绪',
                 'stop_auto_refresh': '停止自动刷新',
                 'start_auto_refresh': '开始自动刷新',
                 
                 # 日志查看器消息
                 'loaded_logs': '加载了 {} 条日志记录 - {}',
                 'export_logs_title': '导出日志',
                 'confirm_cleanup': '确认清理',
                 'cleanup_confirm_message': '确定要清理15天前的日志吗？此操作不可撤销！',
                 'cleanup_complete': '完成',
                 'logs_cleanup_complete': '日志清理完成',
                
                # 状态栏
                'online': '在线',
                'offline': '离线',
                'device_status': '设备状态\n当前设备状态（例如：离线、连接）',
                'measurement_progress_bar': '测量进度条\n显示当前孔位测量进度',
                'communication_error': 'E401 ：通讯故障',
                'communication_error_exclamation': 'E401 ：通讯故障！',
                'current_user': '当前登录用户',
                
                # 用户设置对话框
                'user_settings': '用户设置',
                'settings_successful': '设置成功。',
                
                # 通讯对话框
                'establish_communication': '建立通讯',
                'laser_connected': '激光器已连接',
                'laser_disconnected': '激光器未连接',
                'spectrometer_connected': '光谱仪已连接',
                'spectrometer_disconnected': '光谱仪未连接',
                
                # 文件信息
                'measurement_parameters': '测量参数',
                'spectrum_data': '光谱数据 ({} 点)',
                'raman_shift_range': '拉曼位移: {:.2f} - {:.2f} cm-1',
                'intensity_range': '强度: {:.2f} - {:.2f}',
                
                # 其他
                'loading': '加载中...',
                'processing': '处理中...',
                'completed': '已完成',
                'failed': '失败',
                'unknown': '未知',
            },
            'en': {
                # 菜单栏
                'file': 'File',
                'open_spectrum_file': 'Open Spectrum File',
                'print_report': 'Print Report',
                'save_files': 'Save Files',
                'set_working_directory': 'Set Working Directory',
                'about': 'About',
                'recent_files': 'Recent Files',
                'no_recent_files': 'No Recent Files',
                
                'calibration': 'Calibration',
                'laser_on': 'Laser On',
                'laser_off': 'Laser Off',
                'auxiliary_test': 'Auxiliary Test',
                'wavenumber_calibration': 'Wavenumber Calibration',
                
                'settings': 'Settings',
                'language': 'Language',
                'chinese': '中文',
                'english': 'English',
                'update_service': 'Update Service',
                'software_management': 'Software Management',
                'password_management': 'Password Management',
                'log_viewer': 'System Log',
                
                'help': 'Help',
                'manual': 'Manual',
                'instrument_info': 'Instrument Info',
                'import_database': 'Import Database',
                
                # 侧边栏
                'data_management': 'Data Management',
                'baseline_calibration': 'Baseline Calibration',
                'peak_finding': 'Peak Finding',
                'data_smoothing': 'Data Smoothing',
                'normalization': 'Normalization',
                'restoration': 'Restoration',
                'clear_spectrum': 'Clear Spectrum',
                'communication': 'Communication',
                'user': 'User',
                
                # 编辑器
                'opened_spectrum_files': 'Opened Spectrum Files',
                'no_file_loaded': 'No file loaded',
                'current_test_filename': 'Current Test Filename:',
                'measurement_progress': 'Measurement Progress:',
                'laser_power': 'Laser Power (%):',
                'integration_time': 'Integration Time (ms):',
                'naming_rules': 'Naming Rules',
                'sample_name': 'Sample Name:',
                'department': 'Department:',
                'patient_id': 'Patient ID:',
                'select_date': 'Select Date:',
                'laser_parameters': 'Laser Parameters',
                'laser_power_percent': 'Laser Power (%):',
                'integration_time_ms': 'Integration Time (ms):',
                'average_count': 'Average Count:',
                'confirm_params': 'Confirm Name and Parameters',
                'start_measurement': 'Start Measurement',
                'stop_measurement': 'Stop Measurement',
                'pause': 'Pause',
                
                # 文件右键菜单
                'clear_spectrum': 'Clear Spectrum',
                'change_spectrum_color': 'Change Spectrum Color',
                'modify_spectrum_name': 'Modify Spectrum Name',
                'select_spectrum_color': 'Select Spectrum Color',
                
                # 光谱图右键菜单
                'zoom': 'Zoom',
                'pan': 'Pan',
                'save_image': 'Save Image',
                'show_grid': 'Show Grid',
                'hide_grid': 'Hide Grid',
                'reset_view_to_default': 'Reset View to Default',
                'switch_to_raman_shift': 'Switch to Raman Shift',
                'switch_to_pixel_coordinates': 'Switch to Pixel Coordinates',
                
                # 坐标轴标签
                'intensity': 'Intensity (a.u.)',
                'pixel': 'Pixel',
                'raman_shift': 'Raman Shift',
                'raman_shift_cm': 'Raman Shift (cm-1)',
                'pixel_coordinates': 'Pixel Coordinates',
                
                # 对话框
                'warning': 'Warning',
                'success': 'Success',
                'error': 'Error',
                'confirm': 'Confirm',
                'cancel': 'Cancel',
                'ok': 'OK',
                'yes': 'Yes',
                'no': 'No',
                'continue': 'Continue',
                'restart': 'Restart',
                'terminate': 'Terminate',
                
                                 # 消息
                 'please_enter_sample_name': 'Please enter sample name',
                 'please_enter_department': 'Please enter department',
                 'please_enter_patient_id': 'Please enter patient ID',
                 'name_and_params_saved': 'Name and parameters saved',
                 'confirm_clear': 'Confirm Clear',
                 'confirm_clear_file': 'Are you sure you want to clear file \'{}\' and its spectrum?',
                 'file_cleared': 'File cleared: {}',
                 'color_changed': 'Color changed for file \'{}\'',
                 'measurement_paused': 'Measurement paused, please select operation',
                 'measurement_completed': 'Measurement completed',
                 'measurement_stopped': 'Measurement stopped',
                 
                 # 辅助测试消息
                 'dark_current_completed': 'Dark current collection completed, please connect element lamp and click OK to start detection.',
                 'adjust_element_lamp': 'Collection completed, please adjust element lamp, click OK to continue detection, click No to close window.',
                 
                 # 保存文件对话框
                 'select_files_to_save': 'Select Files to Save',
                 'select_all': 'Select All',
                 'deselect_all': 'Deselect All',
                 'save_settings': 'Save Settings',
                 'save_format': 'Save Format:',
                 'output_directory': 'Output Directory:',
                 'browse': 'Browse...',
                 'select_output_directory': 'Select output directory...',
                 'filename_prefix': 'Filename Prefix:',
                 'optional_filename_prefix': 'Optional: filename prefix',
                 'start_saving': 'Start Saving',
                 'close': 'Close',
                 'no_files_to_save': 'No files to save, please load files first',
                 
                 # 系统日志
                 'system_log': 'System Log',
                 'log_viewer': 'Log Viewer',
                 
                 # 更新服务对话框
                 'service_update': 'Service Update',
                 'update_package_location': 'Update Package Location:',
                 'not_selected': 'Not Selected',
                 'select_update_package': 'Select Update Package (ZIP)',
                 'installation_directory': 'Installation Directory:',
                 'select_installation_directory': 'Select Installation Directory',
                 'update_instructions': 'Instructions:\n1. Please select the downloaded ZIP format update package\n2. Select the software installation directory, the service will be updated to the Service folder under it',
                 'incomplete_input': 'Incomplete Input',
                 'please_select_package_and_directory': 'Please select update package and installation directory',
                 'confirm_update': 'Confirm Update',
                 'about_to_update_service': 'About to update service with update package\n\nUpdate Package: {}\nTarget Directory: {}\n\nThis operation will overwrite existing service files, continue?',
                 'select_service_update_package': 'Select Service Update Package',
                 'select_installation_directory_service': 'Select Installation Directory (Service folder will be created under this directory)',
                 'update_successful': 'Update Successful',
                 'service_updated_successfully': 'Service updated successfully!\n\nUpdate Package: {}\nTarget Directory: {}\n\nService has been restarted.',
                 'update_failed': 'Update Failed',
                 'service_update_failed': 'Service update failed: {}',
                 
                 # 软件管理对话框
                 'software_management': 'Software Management',
                 'reset_password': 'Reset Password',
                 'reset_settings': 'Reset Settings',
                 'change_settings': 'Change Settings',
                 'current_settings': 'Current Settings',
                 'username': 'Username:',
                 'max_curves': 'Max Curves:',
                 'x_axis': 'X Axis:',
                 'output_format': 'Output Format:',
                 'reset_all_settings': 'Reset all settings to default values',
                 'save_settings_to_file': 'Save settings to file',
                 'settings_saved_successfully': 'Settings saved successfully',
                 'settings_reset_successfully': 'Settings reset successfully',
                 
                 # 导入数据库对话框
                 'select_import_method': 'Select Import Method',
                 'please_select_import_method': 'Please select import method:',
                 'import_spectrum_files_to_database': 'Import Spectrum Files to Database',
                 'import_existing_database_file': 'Import Existing Database File',
                 'select_spectrum_data_files': 'Select Spectrum Data Files',
                 'import_progress': 'Import Progress',
                 'importing_files': 'Importing {} files...',
                 'importing_file': 'Importing: {}',
                 'import_complete': 'Import Complete',
                 'import_successful': 'Import Successful',
                 'successfully_imported_files': 'Successfully imported {} spectrum files to database',
                 'import_results': 'Successfully imported {}/{} files\n\nFailed files:\n{}',
                 'and_more_files': '\n... and {} more files',
                 
                 # 关于对话框
                 'software_name': 'Software Name: Raman Spectrum Identification Software (English name neolly)',
                 'model_specification': 'Model/Specification: neoraman-neolily-01',
                 'release_version': 'Release Version: V1.0',
                 'full_version': 'Full Version: V1.0.0.1',
                 'release_date': 'Release Date: 2025.6.01',
                 'security_level': 'Software Security Level: Medium',
                 'login_username': 'Login Username: {}',
                 'last_login_time': 'Last Login Time: {}',
                 
                 # 操作手册
                 'manual_not_found': 'Manual not found: {}',
                 'cannot_open_manual': 'Cannot open manual: {}',
                 
                 # 报告对话框
                 'raman_spectrum_analysis_report': 'Raman Spectrum Analysis Report',
                 'font': 'Font',
                 'color': 'Color',
                 'background': 'Background',
                 'report_generation_time': 'Generation Time: {}',
                 'file_information': '1. File Information',
                 'file_number': 'File {}',
                 'parameter': 'Parameter',
                 'value': 'Value',
                 'device_serial_number': 'Device Serial Number',
                 'operator': 'Operator',
                 'integration_time': 'Integration Time',
                 'laser_power': 'Laser Power',
                 'average_count': 'Average Count',
                 'acquisition_mode': 'Acquisition Mode',
                 'acquisition_interval': 'Acquisition Interval',
                 'pixel': 'Pixel',
                 'none': 'None',
                 'no_files_selected': 'No files selected',
                 'spectrum_analysis_results': '2. Spectrum Analysis Results',
                 'spectrum_image_unavailable': 'Spectrum image unavailable',
                 'save_report': 'Save Report',
                 
                 # 密码管理对话框
                 'password_management': 'Password Management',
                 'username_colon': 'Username:',
                 'new_password': 'New Password:',
                 'enter_new_password': 'Please enter new password',
                 'confirm_password': 'Confirm Password:',
                 'enter_confirm_password': 'Please enter new password again',
                 'change_password': 'Change Password',
                 'password_changed_successfully': 'Password changed successfully!',
                 'password_change_failed': 'Password change failed: {}',
                 'password_mismatch': 'Passwords do not match',
                 'invalid_password': 'Invalid password format',
                 'password_requirements': 'Password requirements: at least 8 characters, including letters and numbers',
                 
                 # 用户设置对话框
                 'operator': 'Operator:',
                 'work_unit': 'Work Unit:',
                 'contact_phone': 'Contact Phone:',
                 'work_address': 'Work Address:',
                 'auto_login': 'Auto Login',
                 'settings': 'Settings',
                 'settings_successful': 'Settings saved successfully.',
                 
                 # 通讯对话框
                 'laser_communication': 'Laser Communication',
                 'spectrometer_communication': 'Spectrometer Communication',
                 'connected': 'Connected',
                 'disconnected': 'Disconnected',
                 
                 # 日志查看器
                 'log_filter': 'Log Filter',
                 'level': 'Level:',
                 'category': 'Category:',
                 'days': 'Days:',
                 'limit': 'Limit:',
                 'refresh': 'Refresh',
                 'export_logs': 'Export Logs',
                 'cleanup_now': 'Cleanup Now',
                 'time': 'Time',
                 'level_col': 'Level',
                 'category_col': 'Category',
                 'message': 'Message',
                 'details': 'Details',
                 'create_time': 'Create Time',
                 'all': 'All',
                 'records': ' records',
                 
                 # 更新服务进度
                 'updating_service': 'Updating service...',
                 'stopping_service': 'Stopping service...',
                 'backing_up_files': 'Backing up existing files...',
                 'extracting_package': 'Extracting update package...',
                 'copying_new_files': 'Copying new files...',
                 'reinstalling_service': 'Reinstalling service...',
                 'starting_service': 'Starting service...',
                 'unsupported_format': 'Unsupported file format: {}',
                 
                 # 日志查看器状态
                 'ready': 'Ready',
                 'stop_auto_refresh': 'Stop Auto Refresh',
                 'start_auto_refresh': 'Start Auto Refresh',
                 
                 # 日志查看器消息
                 'loaded_logs': 'Loaded {} log records - {}',
                 'export_logs_title': 'Export Logs',
                 'confirm_cleanup': 'Confirm Cleanup',
                 'cleanup_confirm_message': 'Are you sure you want to clean up logs older than 15 days? This operation cannot be undone!',
                 'cleanup_complete': 'Complete',
                 'logs_cleanup_complete': 'Log cleanup complete',
                
                # 状态栏
                'online': 'Online',
                'offline': 'Offline',
                'device_status': 'Device Status\nCurrent device status (e.g., offline, connected)',
                'measurement_progress_bar': 'Measurement Progress Bar\nShows current hole measurement progress',
                'communication_error': 'E401: Communication Error',
                'communication_error_exclamation': 'E401: Communication Error!',
                'current_user': 'Current User',
                
                # 用户设置对话框
                'user_settings': 'User Settings',
                'settings_successful': 'Settings saved successfully.',
                
                # 通讯对话框
                'establish_communication': 'Establish Communication',
                'laser_connected': 'Laser Connected',
                'laser_disconnected': 'Laser Disconnected',
                'spectrometer_connected': 'Spectrometer Connected',
                'spectrometer_disconnected': 'Spectrometer Disconnected',
                
                # 文件信息
                'measurement_parameters': 'Measurement Parameters',
                'spectrum_data': 'Spectrum Data ({} points)',
                'raman_shift_range': 'Raman Shift: {:.2f} - {:.2f} cm-1',
                'intensity_range': 'Intensity: {:.2f} - {:.2f}',
                
                # 其他
                'loading': 'Loading...',
                'processing': 'Processing...',
                'completed': 'Completed',
                'failed': 'Failed',
                'unknown': 'Unknown',
            }
        }
    
    def set_language(self, language):
        """设置当前语言"""
        if language in self._translations:
            self.current_language = language
    
    def get_text(self, key, *args):
        """获取翻译文本"""
        if self.current_language in self._translations:
            if key in self._translations[self.current_language]:
                text = self._translations[self.current_language][key]
                if args:
                    return text.format(*args)
                return text
        return key
    
    def get_current_language(self):
        """获取当前语言"""
        return self.current_language

# 全局语言管理器实例
language_manager = LanguageManager()

def get_text(key, *args):
    """便捷函数：获取翻译文本"""
    return language_manager.get_text(key, *args)

def set_language(language):
    """便捷函数：设置语言"""
    language_manager.set_language(language) 