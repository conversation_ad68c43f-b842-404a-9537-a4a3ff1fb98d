SIMPLE  =                    T / conforms to FITS standard                      BITPIX  =                    8 / array data type                                NAXIS   =                    0 / number of array dimensions                     WCSAXES =                    2 / Number of coordinate axes                      CRPIX1  =                128.0 / Pixel coordinate of reference point            CRPIX2  =                128.0 / Pixel coordinate of reference point            PC1_1   =    0.000249756880272 / Coordinate transformation matrix element       PC1_2   =    0.000230177809744 / Coordinate transformation matrix element       PC2_1   =    0.000230428519265 / Coordinate transformation matrix element       PC2_2   =   -0.000249965770577 / Coordinate transformation matrix element       CDELT1  =                    1 / [deg] Coordinate increment at reference point  CDELT2  =                    1 / [deg] Coordinate increment at reference point  CUNIT1  = 'deg'                / Units of coordinate increment and value        CUNIT2  = 'deg'                / Units of coordinate increment and value        CTYPE1  = 'RA---TAN-SIP'       / Right ascension, gnomonic projection           CTYPE2  = 'DEC--TAN-SIP'       / Declination, gnomonic projection               CRVAL1  =        202.482322805 / [deg] Coordinate value at reference point      CRVAL2  =          47.17511893 / [deg] Coordinate value at reference point      LONPOLE =                  180 / [deg] Native longitude of celestial pole       LATPOLE =          47.17511893 / [deg] Native latitude of celestial pole        RESTFRQ =                    0 / [Hz] Line rest frequency                       RESTWAV =                    0 / [Hz] Line rest wavelength                      CRDER1  =    4.02509762361E-05 / [deg] Random error in coordinate               CRDER2  =    3.42746131953E-05 / [deg] Random error in coordinate               RADESYS = 'ICRS'               / Equatorial coordinate system                   EQUINOX =                 2000 / [yr] Equinox of equatorial coordinates         A_3_0   =          -1.4172E-07                                                  B_3_0   =          -2.0249E-08                                                  B_1_2   =          -5.7813E-09                                                  B_1_1   =          -2.4386E-05                                                  B_2_1   =          -1.6583E-07                                                  B_2_0   =           2.1197E-06                                                  A_ORDER =                    3                                                  B_0_3   =          -1.6168E-07                                                  B_0_2   =             2.31E-05                                                  B_ORDER =                    3                                                  A_1_1   =           2.1886E-05                                                  A_1_2   =          -1.6847E-07                                                  A_0_2   =           2.9656E-06                                                  A_0_3   =           3.7746E-09                                                  A_2_0   =          -2.3863E-05                                                  A_2_1   =           -8.561E-09                                                  END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             