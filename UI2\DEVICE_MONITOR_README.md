# 设备监控功能说明

## 概述

设备监控功能用于实时监控光谱仪、激光器和电源的连接状态，当设备断开时自动进行重连，并在状态栏显示详细的设备状态信息。

## 主要功能

### 1. 实时状态监控
- **光谱仪状态**: 监控光谱仪USB连接状态
- **激光器状态**: 监控激光器串口连接状态  
- **电源状态**: 监控激光器电源开关状态

### 2. 自动重连机制
- 当设备断开时自动尝试重连
- 可配置重连次数和延迟时间
- 智能重连策略，避免频繁重连

### 3. 状态栏显示
- 实时显示各设备连接状态
- 颜色编码：绿色=已连接，红色=未连接
- 点击设备状态可查看详细信息

### 4. 用户通知
- 设备断开时显示通知对话框
- 重连成功/失败状态提示
- 可配置是否显示各种通知

## 使用方法

### 启动监控
设备监控会在用户登录成功后自动启动，无需手动操作。

### 查看设备状态
在状态栏可以看到三个设备状态指示器：
- **光谱仪**: 显示光谱仪连接状态
- **激光器**: 显示激光器连接状态
- **电源**: 显示电源状态

### 配置监控参数
1. 点击菜单栏 **设置** → **设备监控配置**
2. 在配置对话框中调整各种参数：
   - 检查间隔：设备状态检查频率
   - 自动重连：是否启用自动重连
   - 最大重连次数：重连尝试的最大次数
   - 重连延迟：重连尝试之间的等待时间
   - 设备监控开关：选择要监控的设备
   - 通知设置：配置各种通知选项

### 强制重连设备
1. 点击状态栏中的设备状态指示器
2. 在设备详情对话框中点击"强制重连"按钮
3. 系统将立即尝试重连该设备

## 配置参数说明

### 监控设置
- **检查间隔**: 设备状态检查的时间间隔（0.5-60秒）
- **启用自动重连**: 当设备断开时是否自动尝试重连

### 重连设置
- **最大重连次数**: 设备断开后的最大重连尝试次数（1-10次）
- **重连延迟**: 重连尝试之间的延迟时间（1-30秒）

### 设备特定设置
- **监控光谱仪**: 是否监控光谱仪连接状态
- **监控激光器**: 是否监控激光器连接状态
- **监控电源**: 是否监控电源状态

### 通知设置
- **设备断开时显示通知**: 设备断开时是否显示通知对话框
- **重连成功时显示通知**: 重连成功时是否显示通知
- **记录设备事件到日志**: 是否将设备事件记录到系统日志

## 故障排除

### 设备无法连接
1. 检查物理连接（USB线、串口线等）
2. 检查设备电源是否开启
3. 检查设备驱动是否正确安装
4. 尝试强制重连设备

### 自动重连失败
1. 检查重连次数设置是否合理
2. 检查重连延迟设置是否合适
3. 查看系统日志了解具体错误原因
4. 手动检查设备状态

### 监控功能异常
1. 重启应用程序
2. 检查设备监控配置
3. 查看控制台输出和系统日志
4. 联系技术支持

## 技术实现

### 核心组件
- **DeviceMonitor**: 设备监控器主类
- **StatusBar**: 状态栏显示组件
- **DeviceMonitorDialog**: 配置对话框

### 监控机制
- 使用定时器定期检查设备状态
- 多线程监控，不阻塞主界面
- 信号槽机制实现组件间通信

### 重连策略
- 指数退避算法避免频繁重连
- 可配置的重连参数
- 智能错误处理和恢复

## 注意事项

1. **性能影响**: 监控功能会占用少量系统资源，建议检查间隔不要设置过短
2. **网络环境**: 在复杂的网络环境中，可能需要调整重连参数
3. **设备兼容性**: 确保设备驱动和通信协议兼容
4. **日志管理**: 启用日志记录会增加磁盘使用量，定期清理旧日志文件

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 支持光谱仪、激光器、电源状态监控
- 实现自动重连功能
- 提供配置界面和状态显示
