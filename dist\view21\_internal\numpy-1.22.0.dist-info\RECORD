../../Scripts/f2py.exe,sha256=G1yy91v5mGsXfMWnRPLXMNYgZnhBg18GrlyGlW6yxxs,106328
numpy-1.22.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-1.22.0.dist-info/LICENSE.txt,sha256=dcfWTR1JxLcWXSSEEtnqGGf6Q_ILIJmQmemZTPaXrZg,48811
numpy-1.22.0.dist-info/LICENSES_bundled.txt,sha256=UWGZ0f1YKTYB3hrVHM26mHuBuGTJwRn50NMTMyInvPY,656
numpy-1.22.0.dist-info/METADATA,sha256=3_pkJDON9U_85NmL9QOATN9tJZ3h1NwelM0cuzKaclY,2105
numpy-1.22.0.dist-info/RECORD,,
numpy-1.22.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-1.22.0.dist-info/WHEEL,sha256=MRZ_p7RU4olp1XL4U2EYLkuYikriaVRqXBoKVLH_OSE,100
numpy-1.22.0.dist-info/entry_points.txt,sha256=PkM5Et7cLvG1wd4Qcw_h9JkDrbirVx0hqg8nGYlLW1o,86
numpy-1.22.0.dist-info/top_level.txt,sha256=4J9lbBMLnAiyxatxh8iRKV5Entd_6-oqbO7pzJjMsPw,6
numpy/.libs/libopenblas.EL2C6PLE4ZYW3ECEVIV3OXXGRN2NRFM2.gfortran-win_amd64.dll,sha256=i3TPF7EYDVeWrtmcge052mHnjmFB0AA7FLveU3jqJK4,35818505
numpy/LICENSE.txt,sha256=dcfWTR1JxLcWXSSEEtnqGGf6Q_ILIJmQmemZTPaXrZg,48811
numpy/__config__.py,sha256=uP8_kN8jRtzebxWSUWHuAPDK_g5EirrOjo4vCgrF92c,5346
numpy/__init__.cython-30.pxd,sha256=lewKbMGAQ7aV9kWpHtI9GbE_wkmfv1UK4m8VXdGI4PA,37291
numpy/__init__.pxd,sha256=MztMQM3whXzKgSkVl0x2Cf-F6SxBWcSwsuBoLUNiGao,35624
numpy/__init__.py,sha256=68drJU4Zt4icuqf43K8KDw2nxrWEhY-844u_fWir8kU,15738
numpy/__init__.pyi,sha256=npJ45UiO_B5pRDLagpzjUCsWPNaP9oXGKMf3PaDDUeM,151765
numpy/__pycache__/__config__.cpython-39.pyc,,
numpy/__pycache__/__init__.cpython-39.pyc,,
numpy/__pycache__/_distributor_init.cpython-39.pyc,,
numpy/__pycache__/_globals.cpython-39.pyc,,
numpy/__pycache__/_pytesttester.cpython-39.pyc,,
numpy/__pycache__/_version.cpython-39.pyc,,
numpy/__pycache__/conftest.cpython-39.pyc,,
numpy/__pycache__/ctypeslib.cpython-39.pyc,,
numpy/__pycache__/dual.cpython-39.pyc,,
numpy/__pycache__/matlib.cpython-39.pyc,,
numpy/__pycache__/setup.cpython-39.pyc,,
numpy/__pycache__/version.cpython-39.pyc,,
numpy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
numpy/_globals.py,sha256=40qg6Gji_iJtOz6slsLlrgcrY7h9m12sj07b-mAwNp4,4140
numpy/_pytesttester.py,sha256=qF74TG-8_ox-8-MNnbgAmf8g21B_ZvjxPbXw94oV7Wg,6884
numpy/_pytesttester.pyi,sha256=2Of_cPJHIfETfeaswCrzKz9rZ_gIrvkWFTQXeZQGxfU,485
numpy/_version.py,sha256=B2NwarrGZxtwSvUo7KIpnEJ_WNtzeCaW6ASrGA5FqFc,519
numpy/array_api/__init__.py,sha256=5lTDdDPE-ysR-t4mglmJVwrJJA4M41Wsa2CJzeZaGfo,10538
numpy/array_api/__pycache__/__init__.cpython-39.pyc,,
numpy/array_api/__pycache__/_array_object.cpython-39.pyc,,
numpy/array_api/__pycache__/_constants.cpython-39.pyc,,
numpy/array_api/__pycache__/_creation_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_data_type_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_dtypes.cpython-39.pyc,,
numpy/array_api/__pycache__/_elementwise_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_manipulation_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_searching_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_set_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_sorting_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_statistical_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/_typing.cpython-39.pyc,,
numpy/array_api/__pycache__/_utility_functions.cpython-39.pyc,,
numpy/array_api/__pycache__/linalg.cpython-39.pyc,,
numpy/array_api/__pycache__/setup.cpython-39.pyc,,
numpy/array_api/_array_object.py,sha256=EPowhWEmgL9AMB7hBkKAae4Rk0cwsXWJr9gdmLgcFL4,42489
numpy/array_api/_constants.py,sha256=l8tz1bWuEXiyzFAHCnx6_yPI4eXoZ4847Zn8yFIO_ow,72
numpy/array_api/_creation_functions.py,sha256=d3eD3FGwWt_arWRz7x43O9rMbPog6vS7fgQQEaJSopA,10402
numpy/array_api/_data_type_functions.py,sha256=MuJQhLzv-Kv3faKWMFEOrywLTp6ZbRI_1jIAlSQKvXw,3933
numpy/array_api/_dtypes.py,sha256=uKDX6j3LSsuH0-dHp_dP5IUxEGkUBdIy7qTX5DZGjQg,3850
numpy/array_api/_elementwise_functions.py,sha256=utzReK9xROLPc6b7ZYgvPzijn_3wW7ymkzHQ9Z6cfIY,25510
numpy/array_api/_manipulation_functions.py,sha256=1vL3B4vOGzwXUtzGZxqLRlLsiasAyjD11lJz_soFoIk,3041
numpy/array_api/_searching_functions.py,sha256=eL_gRYZ3Y6Z3OCu6qXl3y79dtnhCsGpeMZYwSVyI9wQ,1504
numpy/array_api/_set_functions.py,sha256=QYY4vnvOe9_OidJndij85fQJDCDWywkTVpy-psHW6RI,2396
numpy/array_api/_sorting_functions.py,sha256=Vm783vzLlxNchLG_B5twTDpeLb54EYeIztyOJC2T-aE,1130
numpy/array_api/_statistical_functions.py,sha256=lHtSE3pvpuy2TfzYp7F-hnK6nGNJ5bU0_g1FgtMDqk4,3493
numpy/array_api/_typing.py,sha256=MN5OX-mCYT8bLegZFGXoCDRDfHRp5WSSOrS9kmShU9s,1450
numpy/array_api/_utility_functions.py,sha256=LjVxM__wjulID49MF5jeLkCDQuRB50KDUPdvHgAtnmg,861
numpy/array_api/linalg.py,sha256=3C89JoqWSVqzDW3bMeVH-qtqS5etTl13BKiSdPGZsKE,16725
numpy/array_api/setup.py,sha256=MrEzBh9m4-EV4iekcvUESVM3MW0bHJ5VvXaaTzMFZOU,353
numpy/array_api/tests/__init__.py,sha256=afUKDkt_1ajE1TzYGn4cTp-jMMBfx8riogjk3AePPf0,289
numpy/array_api/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/array_api/tests/__pycache__/test_array_object.cpython-39.pyc,,
numpy/array_api/tests/__pycache__/test_creation_functions.cpython-39.pyc,,
numpy/array_api/tests/__pycache__/test_elementwise_functions.cpython-39.pyc,,
numpy/array_api/tests/test_array_object.py,sha256=ivgy0q8x9D9Uwx-oj15v2CK2Tk0N1niHFD4NrQtqf5M,14670
numpy/array_api/tests/test_creation_functions.py,sha256=0AuzJkFyCLw102ilOCE9lt6q24ERhYVJqqdUKfIhjoQ,5165
numpy/array_api/tests/test_elementwise_functions.py,sha256=J7783MYwmVOK_MbBSrnX53GKgCwItQOa-n1KsVu4lcc,3737
numpy/compat/__init__.py,sha256=kryOGy6TD3f9oEXy1sZZOxEMc50A7GtON1yf0nMPzr8,450
numpy/compat/__pycache__/__init__.cpython-39.pyc,,
numpy/compat/__pycache__/_inspect.cpython-39.pyc,,
numpy/compat/__pycache__/py3k.cpython-39.pyc,,
numpy/compat/__pycache__/setup.cpython-39.pyc,,
numpy/compat/_inspect.py,sha256=4PWDVD-iE3lZGrBCWdiLMn2oSytssuFszubUkC0oruA,7638
numpy/compat/py3k.py,sha256=7pBAYdQJ7YTE0a4yVjnAAM5ROgYUt-ofTWSl_9R-jf8,3744
numpy/compat/setup.py,sha256=PmRas58NGR72H-7OsQj6kElSUeQHjN75qVh5jlQIJmc,345
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/compat/tests/__pycache__/test_compat.cpython-39.pyc,,
numpy/compat/tests/test_compat.py,sha256=6i0bPM1Nqw0n3wtMphMU7ul7fkQNwcuH2Xxc9vpnQy8,495
numpy/conftest.py,sha256=Q4KMfF1mviYQZkYT0CLDqKdsgPgVgjVu6ljH3yDtmVI,4151
numpy/core/__init__.py,sha256=H4qId_3NJQzdX87t9f33wX7rV8F2KvtF7-mLimL8TQc,5836
numpy/core/__init__.pyi,sha256=nBhfHv0Vy8Cc37vQn3joQShGEvBy4fyo5aBahLSj5Xo,128
numpy/core/__pycache__/__init__.cpython-39.pyc,,
numpy/core/__pycache__/_add_newdocs.cpython-39.pyc,,
numpy/core/__pycache__/_add_newdocs_scalars.cpython-39.pyc,,
numpy/core/__pycache__/_asarray.cpython-39.pyc,,
numpy/core/__pycache__/_dtype.cpython-39.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-39.pyc,,
numpy/core/__pycache__/_exceptions.cpython-39.pyc,,
numpy/core/__pycache__/_internal.cpython-39.pyc,,
numpy/core/__pycache__/_machar.cpython-39.pyc,,
numpy/core/__pycache__/_methods.cpython-39.pyc,,
numpy/core/__pycache__/_string_helpers.cpython-39.pyc,,
numpy/core/__pycache__/_type_aliases.cpython-39.pyc,,
numpy/core/__pycache__/_ufunc_config.cpython-39.pyc,,
numpy/core/__pycache__/arrayprint.cpython-39.pyc,,
numpy/core/__pycache__/cversions.cpython-39.pyc,,
numpy/core/__pycache__/defchararray.cpython-39.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-39.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-39.pyc,,
numpy/core/__pycache__/function_base.cpython-39.pyc,,
numpy/core/__pycache__/generate_numpy_api.cpython-39.pyc,,
numpy/core/__pycache__/getlimits.cpython-39.pyc,,
numpy/core/__pycache__/memmap.cpython-39.pyc,,
numpy/core/__pycache__/multiarray.cpython-39.pyc,,
numpy/core/__pycache__/numeric.cpython-39.pyc,,
numpy/core/__pycache__/numerictypes.cpython-39.pyc,,
numpy/core/__pycache__/overrides.cpython-39.pyc,,
numpy/core/__pycache__/records.cpython-39.pyc,,
numpy/core/__pycache__/setup.cpython-39.pyc,,
numpy/core/__pycache__/setup_common.cpython-39.pyc,,
numpy/core/__pycache__/shape_base.cpython-39.pyc,,
numpy/core/__pycache__/umath.cpython-39.pyc,,
numpy/core/__pycache__/umath_tests.cpython-39.pyc,,
numpy/core/_add_newdocs.py,sha256=CUNCg7Crf2Qozip33E2nGMVZ-MdujUNT_lZjjoLX-3I,204347
numpy/core/_add_newdocs_scalars.py,sha256=sGojti2m3ZHVZTWhoOiJOZH_pq8i9oDgUG6uD-mwwGk,10543
numpy/core/_asarray.py,sha256=lhMS1tXvoy7rAPgtUbeQ8nFWU3g9xz1P6VuqEA-OOMw,4315
numpy/core/_asarray.pyi,sha256=-xf1LMMoBEjIiDvxt9CRgBMsaZmvYSgfIaoXuXw70wU,1044
numpy/core/_dtype.py,sha256=W1K6w2msrPFpqktbXaKgZE8yCsVsh1FTNvMQHfoDUCY,10425
numpy/core/_dtype_ctypes.py,sha256=O8tYBqU1QzCG1CXviBe6jrgHYnyIPqpci9GEy9lXO08,3790
numpy/core/_exceptions.py,sha256=fXtzao8C8_C81LF6HPsBsgMER0oCrQrncseFN-gcljo,8615
numpy/core/_internal.py,sha256=z4pPnQgSQ1m5gX8TH-J2RVonEyCN7aBHcVr6x9VVdPA,27294
numpy/core/_internal.pyi,sha256=LMBK7OKRnes233iHc67hp7JY1z2pVZKzc1r-VT4amzg,1081
numpy/core/_machar.py,sha256=DbHjMfwBo9B3XHuGzJ7npdtvPmFAXoPZNvNMoBobDQI,11841
numpy/core/_methods.py,sha256=dkP9Fp-3sKyL7zYMtE4-PcvBbcwKjIVML-FkuFyhog4,11157
numpy/core/_multiarray_tests.cp39-win_amd64.pyd,sha256=NoOYt6war6l5irN6njPcYr6cNZkqnF0ojmngQPYFDVg,118272
numpy/core/_multiarray_umath.cp39-win_amd64.pyd,sha256=KqPra91cPHYCYE27O1V8DgcTE4g9tMm3L5gUTS1GAz0,2986496
numpy/core/_operand_flag_tests.cp39-win_amd64.pyd,sha256=a_wYiQWI3JSHVsRyZNfDXC-_K8ZALKMjr2ws_0MIMV4,14336
numpy/core/_rational_tests.cp39-win_amd64.pyd,sha256=VL1kiK_azWsSD0QZp396qrqAs4MkKVQLkJR_XYVqWtw,48128
numpy/core/_simd.cp39-win_amd64.pyd,sha256=oS8t4vZaPadYR-M4e5XkC1E87o1sw-GQXiuV1fWyVCE,1400320
numpy/core/_string_helpers.py,sha256=xFVFp6go9I8O7PKRR2zwkOk2VJxlnXTFSYt4s7MwXGM,2955
numpy/core/_struct_ufunc_tests.cp39-win_amd64.pyd,sha256=k30KcMHULZ63QRwXNDvHjeuMjNbuoA-idkMQHhO18VY,14848
numpy/core/_type_aliases.py,sha256=pYawZtXDV3RP5FZjLrPaWfXFXnyyIiOmnDjL1aSRKbM,7507
numpy/core/_type_aliases.pyi,sha256=wRDsMdkdvPQWJ3BGn9YFFb7ugheC7NJaYYOdzWdJctc,418
numpy/core/_ufunc_config.py,sha256=GZlilK-aOGwxjVo9h7R0CxAqRX2ZELvjiPIPgpumsRU,13833
numpy/core/_ufunc_config.pyi,sha256=aJdz-ivxz0sLjcd1LO6Ez64Co72aTpISIxT1Nb0GLqs,1114
numpy/core/_umath_tests.cp39-win_amd64.pyd,sha256=6j8EgiN5RzViH2KLJBoJN2GvHx8z6zt42WIKWyeAZJ0,33792
numpy/core/arrayprint.py,sha256=Pt6pbluj4l8XK5LxW0H1v5Z1XzHAAWE49eiQp3ivj1M,64544
numpy/core/arrayprint.pyi,sha256=aw_1HFLVuvwhBfmEdAoA94V_QOyTMoOcnVxZlUfPzx4,4711
numpy/core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/core/defchararray.py,sha256=oBv3bktpIDEygbutJ3BqdBO-rakxpSICg0GQCm18B2E,72572
numpy/core/defchararray.pyi,sha256=2xetgDTfPDWCZZ4FTQc9qsb2HZ9sr7oLYV_FhZpAPmg,9647
numpy/core/einsumfunc.py,sha256=ZMP6TG1CTiaIxQTo9OYSsCKIBQsnoLMyFlo2OFXDcio,52876
numpy/core/einsumfunc.pyi,sha256=FHGEFt55pkzbdAqsTvJhfBBllwGJSOpk0c99Wi-Vhgw,3781
numpy/core/fromnumeric.py,sha256=CzsDkzas_kzzFsXe48sgSPBQFQBK4dJygP-ulbf3Zhc,128010
numpy/core/fromnumeric.pyi,sha256=vMtFVqhr5nPK9qtoi6U6Yqh5DQfL4TXicxhUtinA9D4,8424
numpy/core/function_base.py,sha256=zfEh8nDUBBu37nl-sNUA3VuaBLvr9z7tgE6ImJqNhmw,19548
numpy/core/function_base.pyi,sha256=UxQp94eNn_A2wQxgFPneTbzR6d-PjBTHMV6CPTJw3rM,1622
numpy/core/generate_numpy_api.py,sha256=RQCY7uoIZRyaCVar0lowJDq5MxshlqsrXcZgDngMOUA,7243
numpy/core/getlimits.py,sha256=gwHErBCLkJtl7SaFWrlPdD3ZoMWgyDherRR_Rd6Rdz8,24764
numpy/core/getlimits.pyi,sha256=5L45QmlFCfe9iQg8jALDfBwIdtfXnHGn52mNzeB6Heo,115
numpy/core/include/numpy/.doxyfile,sha256=kISUwZgVeWl649njq6TmhQiyqzPciaZ_QS9e1Q62pOE,60
numpy/core/include/numpy/__multiarray_api.h,sha256=xwso-RmaeTRHJRed_7VDkKhf30Ns4po6Is5Qk0Cavuw,64035
numpy/core/include/numpy/__ufunc_api.h,sha256=H7-TsFbz-CmyBFceTYhNcOm-GltjuAVLebBjctaRtA4,12925
numpy/core/include/numpy/_neighborhood_iterator_imp.h,sha256=sItfdxPWMM6KEBwCpNLC3VoSQVb4usMUC40zOK4zE_s,1967
numpy/core/include/numpy/_numpyconfig.h,sha256=CTgNlvwNlpUhGBxUavo0KV6RSl2N1peUsWNtJfIpZoc,891
numpy/core/include/numpy/arrayobject.h,sha256=f1YdhtzB7wAHDQwmClaFwOl3U-cxkm2UJqzpfQNyhOs,294
numpy/core/include/numpy/arrayscalars.h,sha256=ONfMOpGH6KLorCBcc8265NkFse4x1sZ5ZlnMnC4kM60,4000
numpy/core/include/numpy/experimental_dtype_api.h,sha256=r-Mo8m3hzq8R4IZCZsiyyTATd_1E2ENd5P0eMM46cEw,14653
numpy/core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/core/include/numpy/libdivide/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/core/include/numpy/libdivide/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/core/include/numpy/multiarray_api.txt,sha256=ZtQPwBMKLg-avoIQz1UQ1qoW14AWQYsOQlguwOjVOKQ,59885
numpy/core/include/numpy/ndarrayobject.h,sha256=rATnWQSOkNKkDmxywU_IIxzot1uEP3d4Q1P-Uo10VjA,11023
numpy/core/include/numpy/ndarraytypes.h,sha256=sKy1aHgaoZh8PKmDrreYB8WaK__iu_r0Qfpvd90bUQo,71620
numpy/core/include/numpy/noprefix.h,sha256=Tg5e3cHAD0YUvEmVcpHtkseLgSrwKyuHALnhYscQDTs,7086
numpy/core/include/numpy/npy_1_7_deprecated_api.h,sha256=O__npU5_uV0NpsFGEXCWYtlol_2kR6xy9-q0mAXV9-Q,4502
numpy/core/include/numpy/npy_3kcompat.h,sha256=SLUgK5KEuLJG3jpt7hJK6dBQ4h7tS_rYHHlEeLpd93I,16499
numpy/core/include/numpy/npy_common.h,sha256=Q9cwyqS7iElURgbXBZtxztZlBF-sv6FUzr2cLKYQDPg,40257
numpy/core/include/numpy/npy_cpu.h,sha256=tlYFBhOC6yswF9fZTxWlH2U8nBoOchnygVU5wiTR-5c,4732
numpy/core/include/numpy/npy_endian.h,sha256=G3x4fuvRgY6_Y0AWiJaQ5ZbtmMRRt_QUnYCwkqrHhPE,2863
numpy/core/include/numpy/npy_interrupt.h,sha256=9KhrOhicl4TW3pP6wugWNz5pDDvp9SroisKE04Bbaew,2004
numpy/core/include/numpy/npy_math.h,sha256=4dmF8S55O9yCYroaRGpnpsiqc7Jtmfc0Vlc6JldVfvc,21969
numpy/core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/core/include/numpy/npy_os.h,sha256=wqTXfDS8zlEb2z2G8qAKeZnje5vbnly4BbXBET_IEbc,937
numpy/core/include/numpy/numpyconfig.h,sha256=n5Z39Iz0hWkYXbTAZZi1PsGLuB_Kr_kM9iBtEYUSY-Q,2311
numpy/core/include/numpy/old_defines.h,sha256=jGkDx_FahMvHMTuoWFvx5g5bCV8btUj9pgYNoo_PtwA,6592
numpy/core/include/numpy/oldnumeric.h,sha256=2d5tfBGHavnHpQJUvX90ZJFkgsEF8TlpqXhHNUuE_54,931
numpy/core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/core/include/numpy/random/distributions.h,sha256=cisaBsr7T0hZct9arjcrUOejv87jR7HjoAFiSg20NIQ,10103
numpy/core/include/numpy/ufunc_api.txt,sha256=Qnbm8bUW9JXlOmCaX-cGztnUK-QKj0ecPPeNm6ZEgv0,7533
numpy/core/include/numpy/ufuncobject.h,sha256=uYQ8suXVbe8Jt20RkDzzch5YREpa5G62Fej8_NodWHY,12154
numpy/core/include/numpy/utils.h,sha256=QSuCQimPBuXMMKg6G5yzmys10Qv3bFkPFjqM7AspdgM,1224
numpy/core/lib/npy-pkg-config/mlib.ini,sha256=mQBSOI6opCVmMZK4vwIhLe5J7YevO3PbaHsI0MlJGHs,151
numpy/core/lib/npy-pkg-config/npymath.ini,sha256=5dwvhvbX3a_9toniEDvGPDGChbXIfFiLa36H4YOR-vw,380
numpy/core/lib/npymath.lib,sha256=tYzHAxHV_cZCMRePMYEFF1PHRsejfbMs7tJFJelp0l4,107964
numpy/core/memmap.py,sha256=_XXONlK35hVQqljype2sNt43fNAhBtBlkJwnedqTrnw,12025
numpy/core/memmap.pyi,sha256=0kC9UB-hku69gdPNPlYuSarJFp3sohZP7mqySoX45Z0,85
numpy/core/multiarray.py,sha256=ecELAQwcdHOe5GkX2iVZ6UdjppssxmGW2CzaZHk5k6I,57104
numpy/core/multiarray.pyi,sha256=gcHhQHIakRtQ5vRxlFfc-iO25uBQsiZqcKaxO9tGlKQ,23804
numpy/core/numeric.py,sha256=Vl_WurGGVi9PWGaR16Kr6iR1uF344f7A7sfam5Rgx5g,79508
numpy/core/numeric.pyi,sha256=2M6dvjYKT-szClocHM26ap6gH6wskT-MslMjk0Bx7Ec,14225
numpy/core/numerictypes.py,sha256=hpmWK5YRHkbjjNSnvoMPyRlurmmjr8M2wXVsjto_T0Q,17946
numpy/core/numerictypes.pyi,sha256=LS5SoqKTXqIZ6cERzFYzKn9B3n5wq9NlbDcj9JpVuC8,3730
numpy/core/overrides.py,sha256=o6-Gd4UDqw8rfu-U8X9qukEA-oV6lnUf2k-pnOiE8Eg,7491
numpy/core/records.py,sha256=qCYrXWgdiyAwbiaos3mgUiLZBQyvzlu3RAlypLkpDa0,38644
numpy/core/records.pyi,sha256=1XHmci7q5CM7lf4HQYwC0vMWaDH21M3-shdoPiNDIr8,5930
numpy/core/setup.py,sha256=Zhwu2OcUqN-DmVMfUYxPovInfKkDkT449m-TB5pBtV4,49170
numpy/core/setup_common.py,sha256=Xn9Xgu97VXelm8weXRiDTSXX8cnlXyuoeqF_o6s8tX0,20191
numpy/core/shape_base.py,sha256=BjFK0upxzeVozimsDCdDZFegvU5OLQd7KuTpF6YkDyU,29901
numpy/core/shape_base.pyi,sha256=D9UgZbM3JAltZizXQVneFv6jdfAOuFIgYeXJCHLXRvk,1887
numpy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/core/tests/__pycache__/_locales.cpython-39.pyc,,
numpy/core/tests/__pycache__/test__exceptions.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_abc.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_api.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_argparse.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_array_coercion.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_arraymethod.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_arrayprint.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_casting_unittests.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_conversion_utils.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_cpu_dispatcher.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_cpu_features.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_custom_dtypes.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_cython.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_datetime.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_defchararray.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_deprecations.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_dlpack.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_dtype.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_einsum.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_errstate.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_extint128.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_function_base.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_getlimits.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_half.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_hashtable.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_indexerrors.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_indexing.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_item_selection.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_longdouble.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_machar.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_mem_overlap.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_mem_policy.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_memmap.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_multiarray.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_nditer.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_numeric.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_numerictypes.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_overrides.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_print.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_protocols.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_records.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalar_ctors.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalar_methods.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarbuffer.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarinherit.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarmath.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarprint.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_shape_base.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_simd.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_simd_module.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_ufunc.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_umath.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_umath_accuracy.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_umath_complex.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_unicode.cpython-39.pyc,,
numpy/core/tests/_locales.py,sha256=PAV24baH5MIl_gH_VBi5glF-7kgifkK00WnAP0Hhc60,2266
numpy/core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/core/tests/data/generate_umath_validation_data.cpp,sha256=P1uo6NZ2uUa8SooprZWrp8rgCQK9KVhS79gVAYmRMJ0,6013
numpy/core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/core/tests/data/umath-validation-set-arccos.csv,sha256=8mDga_qwTZoPMm1UHPAqjLfBKTHTW5PT5sTeSQhg8pI,62794
numpy/core/tests/data/umath-validation-set-arccosh.csv,sha256=RN30zA_HBqlPb4UwCfk3gQMYNSopV765CQWnhG2Lx0g,62794
numpy/core/tests/data/umath-validation-set-arcsin.csv,sha256=TMvZI0veNaOHupfGPvS_pTRfX0yH33SoaQWT6Q9Epsc,62768
numpy/core/tests/data/umath-validation-set-arcsinh.csv,sha256=GFRD_4CZTEH47C71CWC6pVSWkJFMgxdii3rJXV3RAkw,61718
numpy/core/tests/data/umath-validation-set-arctan.csv,sha256=EFyJjE5dr5VBPLKlFf_7ZVI_s8Wx7FswdHEzs1mpYr8,61734
numpy/core/tests/data/umath-validation-set-arctanh.csv,sha256=0_cOGarj-biMitr6L1ZsBafWfDpecSOf-pk96wVOpIA,62768
numpy/core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/core/tests/data/umath-validation-set-cos.csv,sha256=qeR-Dg9vJB2Xz63q2b50tWUO1i0TWHMgYC75XGiy-AY,60497
numpy/core/tests/data/umath-validation-set-cosh.csv,sha256=FoMRNGCkmjaAspkoZ6EOTRkcUCUxnWdcq2NHfMyaPXg,62298
numpy/core/tests/data/umath-validation-set-exp.csv,sha256=mPhjF4KLe0bdwx38SJiNipD24ntLI_5aWc8h-V0UMgM,17903
numpy/core/tests/data/umath-validation-set-exp2.csv,sha256=ruPfs9R5l8NU43eP3HSiJYMzJMQkD0W5XwpkFRcVZNI,60053
numpy/core/tests/data/umath-validation-set-expm1.csv,sha256=onF_9BcC7wV_dkSRgazWVe9WaEOijyoBGUYSmaEc7OM,61728
numpy/core/tests/data/umath-validation-set-log.csv,sha256=CDPky64PjaURWhqkHxkLElmMiI21v5ugGGyzhdfUbnI,11963
numpy/core/tests/data/umath-validation-set-log10.csv,sha256=Gy6aRCYcWMBxTLIOLY9_zWymevNOGlc8cy5fjo1NnCg,70551
numpy/core/tests/data/umath-validation-set-log1p.csv,sha256=5hnT1xXhP9lCmLx_qZ3FMFrujTKdJS-5SZCsKX3yke0,61732
numpy/core/tests/data/umath-validation-set-log2.csv,sha256=ihQyfW16BQYldFbg2v7HErkm1efgGuops7tv7pwVCPI,70546
numpy/core/tests/data/umath-validation-set-sin.csv,sha256=aGuZ1Hr8i6PViQGA-UbAUjcXqAdcuAn9AqtFShv09Vg,59981
numpy/core/tests/data/umath-validation-set-sinh.csv,sha256=RI_UkXTgvcO2VAbFFZZSmR--RQbxSdQePc8gnfkLICE,61722
numpy/core/tests/data/umath-validation-set-tan.csv,sha256=H2Z3jO3nV6u0rXYPes3MnI4OSdaKcStfjjKoMiKsyFQ,61728
numpy/core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/core/tests/examples/__pycache__/setup.cpython-39.pyc,,
numpy/core/tests/examples/checks.pyx,sha256=nZXyjisKEKm2vVXM42k-P4h0BGLoa42ABHIGENJegC4,618
numpy/core/tests/examples/setup.py,sha256=JvbvFHHdkZx_eajlwMTtqHijLD-TZwXCZlRySAe-y94,521
numpy/core/tests/test__exceptions.py,sha256=PmFZh81gSxCUKkNs2wDaMICLM5uGp6lI92OynAd-KtM,2934
numpy/core/tests/test_abc.py,sha256=KbyIH9nsGOnXQ8VtnwU6QyUgaFDC0XfEs476CnJ_-Wg,2382
numpy/core/tests/test_api.py,sha256=QcEi7GCm6ZUy1Zn1G0255wUP8RJNmsArOQSmCvn-CYg,24078
numpy/core/tests/test_argparse.py,sha256=W4ys2ZU9B-LQwcuY16KNb3QExGVhxV32yJ1pG5-zF7w,2039
numpy/core/tests/test_array_coercion.py,sha256=7smkxpDA9E54yCOtZ32l7CBPbkeiUSXQnxS2BCC3jiQ,29270
numpy/core/tests/test_arraymethod.py,sha256=JxKpNRGGQvKzfQIy_xMB47t7J5gKUjWlHicEyfdzMOQ,3661
numpy/core/tests/test_arrayprint.py,sha256=FOo5rBHRKGOtQB29fA-zqQERK8cAAn3GzIyaDVTRVY4,38119
numpy/core/tests/test_casting_unittests.py,sha256=QmtyJ8kfZ5T1kF0qihViiOuIExugEXBBh1mGsdyir0E,30145
numpy/core/tests/test_conversion_utils.py,sha256=HB-6IpP4XD5tL_FM5Oat--gQ02MI_WG8BX0P5eyvUZg,6616
numpy/core/tests/test_cpu_dispatcher.py,sha256=vWLZWTYbMGanntWj1EfPcJMce0J4fizoPjmPVNGzIrs,1563
numpy/core/tests/test_cpu_features.py,sha256=5AoP62BPK7edHTTPmkimx06GmRo6jA5TL5oZP8Z_WDE,7018
numpy/core/tests/test_custom_dtypes.py,sha256=H0czaWzy9xtUnz7pY3Fr4w-q-xPIIwShoOcFD-qou-c,7878
numpy/core/tests/test_cython.py,sha256=SkCZWMLnSnDUKJjWLfbUHYx5EEgcZ8mghpAJbYs5fX0,3663
numpy/core/tests/test_datetime.py,sha256=MNtf9mr7rGQwH5RV7SRIiiWg49dd8h7E8DPYSh5yfp4,117491
numpy/core/tests/test_defchararray.py,sha256=VhAlRKEJSrcWcrZbZruagw1170409taptPGauwxp-HM,25256
numpy/core/tests/test_deprecations.py,sha256=5l2nZu3_rHIzKgzsvLDQuIogx38AamXQKYP-peQlfBI,48971
numpy/core/tests/test_dlpack.py,sha256=6FhFt5v4EoCpB0Novc0FuOeCuX-w94QP-dxD95bLsLo,3181
numpy/core/tests/test_dtype.py,sha256=SDPWTqmIY16P-YiVc23tUGUUT9TF74Cs0XoZHx43-10,64715
numpy/core/tests/test_einsum.py,sha256=F6_81xDS-V-yZSIcyzazhRVl6yKaZ_7gJSlSYvW1_Ew,50161
numpy/core/tests/test_errstate.py,sha256=kNCMfKs1xgXUrIRQM4qB8fEkVbREVE-p-yG2hsuHjd4,2125
numpy/core/tests/test_extint128.py,sha256=b8vP_hDPltdHoxWBn2vAmOgjJe2NVW_vjnatdCOtxu8,5862
numpy/core/tests/test_function_base.py,sha256=FjbJ8JMRcbBxllRXRZhxbE6nlfP1HL8I4kfyW8RKr0s,14820
numpy/core/tests/test_getlimits.py,sha256=zPOV3p6sUfrJMdMNd6XC3cQHtDhR38gG4Tzee0DSLVI,5352
numpy/core/tests/test_half.py,sha256=7vZngTRJJ45h4XUFws9fpHI_gYKPq9pz_DKUhZf8lEk,24370
numpy/core/tests/test_hashtable.py,sha256=6RRLuIzT9HAAJIOpLQijbv0PTdOEyWzZJ0mTH8lhJqs,1041
numpy/core/tests/test_indexerrors.py,sha256=iJu4EorQks1MmiwTV-fda-vd4HfCEwv9_Ba_rVea7xw,5263
numpy/core/tests/test_indexing.py,sha256=dPtZuD6jef8NAxDLlR1zmM4dxY3JxMLkJAyBcQCRd4Y,55383
numpy/core/tests/test_item_selection.py,sha256=8AukBkttHRVkRBujEDDSW0AN1i6FvRDeJO5Cot9teFc,3665
numpy/core/tests/test_longdouble.py,sha256=rUSdB2t2pyepIVWyaCL9YaCK_e8G64VI7HumbNKmzWg,13412
numpy/core/tests/test_machar.py,sha256=pauEGVLnT_k8tD0Meu_s_uxutPvSwpOPywlFoizehMg,1097
numpy/core/tests/test_mem_overlap.py,sha256=69RZVSFbEN3hGXhJB9PeRHiRMWLAPru2C-3xC2AAckc,30015
numpy/core/tests/test_mem_policy.py,sha256=b-h_ZwR98vt1G6c59toF1lHFIZc0fIzQLDdsPYWBYSo,16292
numpy/core/tests/test_memmap.py,sha256=2h9gFYyNGaGjXkcmQ1uL5isIVgs-tcafkcsI2eoKnKY,7684
numpy/core/tests/test_multiarray.py,sha256=WQ4qaY1GAlimd5l6BVwd8cjw02d9zKRjUL4khi_hyOA,357011
numpy/core/tests/test_nditer.py,sha256=Kqbn2G6j3MI9C_ivPst6yhaiQrgEXAtFpsjkAoYbltM,131257
numpy/core/tests/test_numeric.py,sha256=soGBIanJI13Skg21_H5XNnFbmuzu1WRsDldEwLIrhjU,138087
numpy/core/tests/test_numerictypes.py,sha256=c-xwoz-oawJff09G0tri2Q9FJQ0NLkH3vf_zmwGnheg,21400
numpy/core/tests/test_overrides.py,sha256=7eVH_G0MkDu7-kSu4HbnxCtE0OwNRmSqWmXCevGY438,20719
numpy/core/tests/test_print.py,sha256=I3-R4iNbFjmVdl5xHPb9ezYb4zDfdXNfzVZt3Lz3bqU,6937
numpy/core/tests/test_protocols.py,sha256=Etu0M6T-xFJjAyXy2EcCH48Tbe5VRnZCySjMy0RhbPY,1212
numpy/core/tests/test_records.py,sha256=910AOqDvIhCDsrmCQk737UhxDnQU3GSB5a2-w0h9hWM,20782
numpy/core/tests/test_regression.py,sha256=HeEL7ecNvK356A1RLxNG1NPHj__zzn75woh_BKBGPK4,93846
numpy/core/tests/test_scalar_ctors.py,sha256=ilVfUULT72ALjt1WH0B54winaYbWrRy62AewkvugJao,3803
numpy/core/tests/test_scalar_methods.py,sha256=aLOSld2H2HSTqjIRtsbhLeD-RlPibw7dSJHNC-2hlRs,7698
numpy/core/tests/test_scalarbuffer.py,sha256=E7nYL8Hnbr3d_9-oxvGtkWte2q6ZeteJhmi0kOQ_u8k,5790
numpy/core/tests/test_scalarinherit.py,sha256=OqjtqccS5mphfhoQxjYwf5lK6sIVhMRYV4y2V1kuq-s,2479
numpy/core/tests/test_scalarmath.py,sha256=cGRo6uaH-jADSWjMVR_nVv1aPDRIO9qar-Av3C9Om2w,33921
numpy/core/tests/test_scalarprint.py,sha256=U-CUIC0PqWZ5MaWRq4Xt1ko-8mu-YtxTAtoUB1q62Ew,19003
numpy/core/tests/test_shape_base.py,sha256=tndYVsnh6zATYP7q5PNgy_inq7OPWCP5z7ua6uZf3oQ,28010
numpy/core/tests/test_simd.py,sha256=58dKty-dsYKHk0XhGberLJediKCpVqA8YlZ2vXPbaLI,37686
numpy/core/tests/test_simd_module.py,sha256=Bpbg_3YKO4Nu4Jm8p_QIlhrz_3tLIO6ketPhJmA_hZU,3855
numpy/core/tests/test_ufunc.py,sha256=QQiRgUrLZFCuwQ2-SgL_OSp80myxcEoaFvzM5CoLt2c,106019
numpy/core/tests/test_umath.py,sha256=koQe_zyo76HU8KWX84WkDTj9cl3Cc89pAWNKSW0AX1g,154781
numpy/core/tests/test_umath_accuracy.py,sha256=BJXjtTv3GAp8Syk0yEZXlJNVnbhBLaANt5AEmwidbbE,3109
numpy/core/tests/test_umath_complex.py,sha256=TColPReMW9GpkZxDwU3NacXWO3fSAEWzpPqCGc1056U,23837
numpy/core/tests/test_unicode.py,sha256=c6SB-PZaiNH7HvEZ2xIrfAMPmvuJmcTo79aBBkdCFvY,12915
numpy/core/umath.py,sha256=IE9whDRUf3FOx3hdo6bGB0X_4OOJn_Wk6ajnbrc542A,2076
numpy/core/umath_tests.py,sha256=IuFDModusxI6j5Qk-VWYHRZDIE806dzvju0qYlvwmfY,402
numpy/ctypeslib.py,sha256=oH5RqrEjLWmh1zA9JOWdsQn0ZXLJCKfPDSvVO098iWo,18053
numpy/ctypeslib.pyi,sha256=bFeNejQZ-uwVOKtfwVc5aK1GdaKvL8HI79cBkKgxxz8,8508
numpy/distutils/__config__.py,sha256=uP8_kN8jRtzebxWSUWHuAPDK_g5EirrOjo4vCgrF92c,5346
numpy/distutils/__init__.py,sha256=kuNMyZmAP8MtuKKOGG5pMz5wWcLVzHkU7wW7CTwzNxY,1610
numpy/distutils/__init__.pyi,sha256=6KiQIH85pUXaIlow3KW06e1_ZJBocVY6lIGghNaW33A,123
numpy/distutils/__pycache__/__config__.cpython-39.pyc,,
numpy/distutils/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/__pycache__/_shell_utils.cpython-39.pyc,,
numpy/distutils/__pycache__/armccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/ccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/ccompiler_opt.cpython-39.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-39.pyc,,
numpy/distutils/__pycache__/core.cpython-39.pyc,,
numpy/distutils/__pycache__/cpuinfo.cpython-39.pyc,,
numpy/distutils/__pycache__/exec_command.cpython-39.pyc,,
numpy/distutils/__pycache__/extension.cpython-39.pyc,,
numpy/distutils/__pycache__/from_template.cpython-39.pyc,,
numpy/distutils/__pycache__/intelccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/lib2def.cpython-39.pyc,,
numpy/distutils/__pycache__/line_endings.cpython-39.pyc,,
numpy/distutils/__pycache__/log.cpython-39.pyc,,
numpy/distutils/__pycache__/mingw32ccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/misc_util.cpython-39.pyc,,
numpy/distutils/__pycache__/msvc9compiler.cpython-39.pyc,,
numpy/distutils/__pycache__/msvccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/npy_pkg_config.cpython-39.pyc,,
numpy/distutils/__pycache__/numpy_distribution.cpython-39.pyc,,
numpy/distutils/__pycache__/pathccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/setup.cpython-39.pyc,,
numpy/distutils/__pycache__/system_info.cpython-39.pyc,,
numpy/distutils/__pycache__/unixccompiler.cpython-39.pyc,,
numpy/distutils/_shell_utils.py,sha256=9pI0lXlRJxB22TPVBNUhWe7EnE-V6xIhMNQSR8LOw40,2704
numpy/distutils/armccompiler.py,sha256=L9NTe61a7Qonkfcv3SLg6DhjuSbDou7PARZL0RF6jsU,1071
numpy/distutils/ccompiler.py,sha256=pFIrSN9TcfXWEJC6eKSGLg2Z0n_o5NO4uixT3-6XPf0,28499
numpy/distutils/ccompiler_opt.py,sha256=C79bC0h7sVhI3mcnJOBU1tsxYB53tjBySCClGc7YpSE,99420
numpy/distutils/checks/cpu_asimd.c,sha256=sUn-v9fLpUUEtsrAfbGor76uYGhCINzXgkz0v3alZhQ,729
numpy/distutils/checks/cpu_asimddp.c,sha256=7_HRp5jMJBSz02PcmAaH6m07zAbtcjMgkw7-4vhTZI4,395
numpy/distutils/checks/cpu_asimdfhm.c,sha256=I7dTWSITAGwxHaLULZkh0nqnf-v9NF2kfQu7Tu90aB4,448
numpy/distutils/checks/cpu_asimdhp.c,sha256=BD9NPwN4mcUZE_KydEQ0b7GtTkfEljl4u0KbrohLYcU,343
numpy/distutils/checks/cpu_avx.c,sha256=69aCE28EArV-BmdFKhCA5djgNZAZtQg2zdea3VQD-co,799
numpy/distutils/checks/cpu_avx2.c,sha256=207hFoh4ojzMAPQ53ug_Y5qCFIgZ1e8SdI1-o2jzdB4,769
numpy/distutils/checks/cpu_avx512_clx.c,sha256=CfPjudkRZ9_xygLVOySSEjoAfkjjfu4ipkWK4uCahbU,864
numpy/distutils/checks/cpu_avx512_cnl.c,sha256=eKCPRk6p1B0bPAyOY0oWRKZMfa-c5g-skvJGGlG5I4Y,972
numpy/distutils/checks/cpu_avx512_icl.c,sha256=Zt8XOXZL85Ds5HvZlAwUVilT6mGbPU44Iir44ul6y2Y,1030
numpy/distutils/checks/cpu_avx512_knl.c,sha256=ikHHx87EfJ3-sjJoT5QeSIvmrHrO4oC9KJzCB-wp5BE,981
numpy/distutils/checks/cpu_avx512_knm.c,sha256=iVdJnZ5HY59XhUv4GzwqYRwz2E_jWJnk1uSz97MvxY0,1162
numpy/distutils/checks/cpu_avx512_skx.c,sha256=aOHpYdGPEx2FcnC7TKe9Nr7wQ0QWW20Uq3xRVSb4U90,1036
numpy/distutils/checks/cpu_avx512cd.c,sha256=zIl7AJXfxqnquZyHQvUAGr9M-vt62TIlylhdlrg-qkE,779
numpy/distutils/checks/cpu_avx512f.c,sha256=ibW0zon6XGYkdfnYETuPfREmE5OtO0HfuLTqXMsoqNA,775
numpy/distutils/checks/cpu_f16c.c,sha256=QxxI3vimUAkJ4eJ83va2mZzTJOk3yROI05fVY07H5To,890
numpy/distutils/checks/cpu_fma3.c,sha256=Cq0F_UpVJ4SYHcxXfaYoqHSYvWRJzZsB8IkOVl8K2ro,839
numpy/distutils/checks/cpu_fma4.c,sha256=Xy0YfVpQDCiFOOrCWH-RMkv7ms5ZAbSauwm2xEOT94o,314
numpy/distutils/checks/cpu_neon.c,sha256=3lc70da2_XbT1hA36YY7vGbapW3dDZlG8xoNXp2rMzY,387
numpy/distutils/checks/cpu_neon_fp16.c,sha256=XeOsWVVTmFecSl_luw2lNBGKxHaB2TtL7YnyIv8W0OU,262
numpy/distutils/checks/cpu_neon_vfpv4.c,sha256=oJ1NvRYfF7Yf6S_bZuaUntE0iOnkbmTNNVR_sbE4NV8,512
numpy/distutils/checks/cpu_popcnt.c,sha256=Jkslm5DiuxbI-fBcCIgJjxjidm-Ps_yfAb_jJIZonE8,1081
numpy/distutils/checks/cpu_sse.c,sha256=XitLZu_qxXDINNpbfcUAL7iduT1I63HjNgtyE72SCEo,706
numpy/distutils/checks/cpu_sse2.c,sha256=OJpQzshqCS6Cp9X1I1yqh2ZPa0b2AoSmJn6HdApOzYk,717
numpy/distutils/checks/cpu_sse3.c,sha256=AmZkvTpXcoCAfVckXgvwloutI5CTHkwHJD86pYsntgk,709
numpy/distutils/checks/cpu_sse41.c,sha256=5GvpgxPcDL39iydUjKyS6WczOiXTs14KeXvlWVOr6LQ,695
numpy/distutils/checks/cpu_sse42.c,sha256=8eYzhquuXjRRGp3isTX0cNUV3pXATEPc-J-CDYTgTaU,712
numpy/distutils/checks/cpu_ssse3.c,sha256=QXWKRz5fGQv5bn282bJL4h_92-yqHFG_Gp5uLKvcA34,725
numpy/distutils/checks/cpu_vsx.c,sha256=gxWpdnkMeoaBCzlU_j56brB38KFo4ItFsjyiyo3YrKk,499
numpy/distutils/checks/cpu_vsx2.c,sha256=ycKoKXszrZkECYmonzKd7TgflpZyVc1Xq-gtJqyPKxs,276
numpy/distutils/checks/cpu_vsx3.c,sha256=pNA4w2odwo-mUfSnKnXl5SVY1z2nOxPZZcNC-L2YX1w,263
numpy/distutils/checks/cpu_xop.c,sha256=sPhOvyT-mdlbf6RlbZvMrslRwHnTFgP-HXLjueS7nwU,246
numpy/distutils/checks/extra_avx512bw_mask.c,sha256=7IRO24mpcuXRhm3refGWP91sy0e6RmSkmUQCWyxy__0,654
numpy/distutils/checks/extra_avx512dq_mask.c,sha256=jFtOKEtZl3iTpfbmFNB-u4DQNXXBST2toKCpxFIjEa0,520
numpy/distutils/checks/extra_avx512f_reduce.c,sha256=hIcCLMm_aXPfrhzCsoFdQiryIrntPqfDxz0tNOR985w,1636
numpy/distutils/checks/extra_vsx_asm.c,sha256=anSZskhKZImNk0lsSJJY_8GJQ0h3dDrkrmrGitlS7Fw,981
numpy/distutils/checks/test_flags.c,sha256=7rgVefVOKOBaefG_6riau_tT2IqI4MFrbSMGNFnqUBQ,17
numpy/distutils/command/__init__.py,sha256=DCxnKqTLrauOD3Fc8b7qg9U3gV2k9SADevE_Q3H78ng,1073
numpy/distutils/command/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/command/__pycache__/autodist.cpython-39.pyc,,
numpy/distutils/command/__pycache__/bdist_rpm.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_clib.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_ext.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_py.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_scripts.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_src.cpython-39.pyc,,
numpy/distutils/command/__pycache__/config.cpython-39.pyc,,
numpy/distutils/command/__pycache__/config_compiler.cpython-39.pyc,,
numpy/distutils/command/__pycache__/develop.cpython-39.pyc,,
numpy/distutils/command/__pycache__/egg_info.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install_clib.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install_data.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install_headers.cpython-39.pyc,,
numpy/distutils/command/__pycache__/sdist.cpython-39.pyc,,
numpy/distutils/command/autodist.py,sha256=i2ip0Zru8_AFx3lNQhlZfj6o_vg-RQ8yu1WNstcIYhE,3866
numpy/distutils/command/bdist_rpm.py,sha256=9uZfOzdHV0_PRUD8exNNwafc0qUqUjHuTDxQcZXLIbg,731
numpy/distutils/command/build.py,sha256=48vCxFz9tA0EWAtP9BiFTzpz6l91qXa0z3O3pC_QMis,2627
numpy/distutils/command/build_clib.py,sha256=dU-pKIebuQGlBvp93y2t4tfJlW7Z64cBgUzofvmg_Hk,19703
numpy/distutils/command/build_ext.py,sha256=JuSz5u5LP23NtUIUdPv2Xe1_g60ubVaz-g4UYn8JI-k,31973
numpy/distutils/command/build_py.py,sha256=xBHZCtx91GqucanjIBETPeXmR-gyUKPDyr1iMx1ARWE,1175
numpy/distutils/command/build_scripts.py,sha256=AEQLNmO2v5N-GXl4lwd8v_nHlrauBx9Y-UudDcdCs_A,1714
numpy/distutils/command/build_src.py,sha256=6hPzM9yEdLAs2U06qke9CRmrjbSRhNQ7yVj6kCBDtOw,31945
numpy/distutils/command/config.py,sha256=DVm5hmolBpTcLTiHQC_mf0nLndAqn2fc1GTo4ucHt3I,21241
numpy/distutils/command/config_compiler.py,sha256=I-xAL3JxaGFfpR4lg7g0tDdA_t7zCt-D4JtOACCP_Ak,4495
numpy/distutils/command/develop.py,sha256=5ro-Sudt8l58JpKvH9FauH6vIfYRv2ohHLz-9eHytbc,590
numpy/distutils/command/egg_info.py,sha256=n6trbjRfD1qWc_hRtMFkOJsg82BCiLvdl-NeXyuceGc,946
numpy/distutils/command/install.py,sha256=s_0Uf39tFoRLUBlkrRK4YlROZsLdkI-IsuiFFaiS3ls,3157
numpy/distutils/command/install_clib.py,sha256=q3yrfJY9EBaxOIYUQoiu2-juNKLKAKKfXC0nrd4t6z0,1439
numpy/distutils/command/install_data.py,sha256=r8EVbIaXyN3aOmRugT3kp_F4Z03PsVX2l_x4RjTOWU4,872
numpy/distutils/command/install_headers.py,sha256=g5Ag2H3j3dz-qSwWegxiZSAnvAf0thYYFwfPVHf9rxc,944
numpy/distutils/command/sdist.py,sha256=XQM39b-MMO08bfE3SJrrtDWwX0XVnzCZqfAoVuuaFuE,760
numpy/distutils/conv_template.py,sha256=hL0DDy7tMJ-5I-63BmkWkoLNX2c5GiQdQhj-XNG3Tm8,9865
numpy/distutils/core.py,sha256=4vvNzpLy_9AfakXgzC6OITRThJd4OdfSmrzxhYu49Fc,8388
numpy/distutils/cpuinfo.py,sha256=l5G7myXNwEOTynBIEitH-ghaF8Zw5pHQAjaYpPKNtTQ,23322
numpy/distutils/exec_command.py,sha256=pZJtbHqveE9tNL1UZixsYDAEB9oN-ocJFW5LjWHscMc,10659
numpy/distutils/extension.py,sha256=gho-x1rzPK16ca8zakRKHvbZL4Gvp1VFTEToE2-2k4M,3675
numpy/distutils/fcompiler/__init__.py,sha256=IBYxVrrcP2i-Z7tdMFwxCRnmrMKSwVkwfWWIFU0tRBQ,41177
numpy/distutils/fcompiler/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/absoft.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/arm.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/compaq.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/environment.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/fujitsu.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/g95.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/gnu.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/hpux.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/ibm.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/intel.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/lahey.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/mips.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/nag.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/none.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/nv.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/pathf95.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/pg.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/sun.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/vast.cpython-39.pyc,,
numpy/distutils/fcompiler/absoft.py,sha256=4UKxvpWQIphSdi6vJb3qILML_zyM3K_m7ddhAMS5dBI,5655
numpy/distutils/fcompiler/arm.py,sha256=x_ym73nBEXIusMAFLKWB3_qWfEduHOr-9q656g0gzRo,2308
numpy/distutils/fcompiler/compaq.py,sha256=yyReqFAq42dy1zscMAV0GqVaYW7Iao1HtAUpnv5XTec,4023
numpy/distutils/fcompiler/environment.py,sha256=PVS1al3wahDNnneNVSl1sQhMPfz2dUXaIDVJfy0wZBU,3168
numpy/distutils/fcompiler/fujitsu.py,sha256=g4dTLDFfLRAzhYayIwyHGBw1Y36DKtPOCYfA823ldNA,1379
numpy/distutils/fcompiler/g95.py,sha256=1TJe4IynWYqqYBy8gJ-nz8WQ_TaSbv8k2UzUIY5Erqc,1372
numpy/distutils/fcompiler/gnu.py,sha256=oyidfkWmxWGiwtL9i-wd8uHgz8VpCgMwecNmGHQw_rc,20812
numpy/distutils/fcompiler/hpux.py,sha256=SLbDOPYgiixqE32GgUrAJjpDLFy9g7E01vGNZCGv6Pc,1394
numpy/distutils/fcompiler/ibm.py,sha256=HARaSCruJSuHbS3LruVESj2cndZUKTHiJZBn7NU4vo0,3636
numpy/distutils/fcompiler/intel.py,sha256=PgECyk3Q1RSYz9wiSZ7RrY0W5DbR_NujRTZe66QcziY,6756
numpy/distutils/fcompiler/lahey.py,sha256=EV3Zhwq-iowWAu4BFBPv_UGJ-IB-qxlxmi6WU1qHDOs,1372
numpy/distutils/fcompiler/mips.py,sha256=mlUNgGrRSLnNhtxQXWVfC9l4_OP2GMvOkgbZQwBon0A,1768
numpy/distutils/fcompiler/nag.py,sha256=FpoDQWW_Y3Anm9-Psml-eNySCGzCp9_jP2Ej4_AwDy8,2864
numpy/distutils/fcompiler/none.py,sha256=auMK2ou1WtJ20LeMbwCZJ3XofpT9A0YYbMVd-62Mi_E,786
numpy/distutils/fcompiler/nv.py,sha256=jRyTlRE57lFJq659Xi-oUIy79nXYucyHawspR_D8c44,1613
numpy/distutils/fcompiler/pathf95.py,sha256=ipbaZIO8sqPJ1lUppOurnboiTwRzIasWNAJvKmktvv4,1094
numpy/distutils/fcompiler/pg.py,sha256=cVcSFM9oR0KmO5AIb4Odw9OGslW6zvDGP88n-uEwxvQ,3696
numpy/distutils/fcompiler/sun.py,sha256=JMdFfKldTYlfW1DxV7nR09k5PZypKLWpP7wmQzmlnH0,1628
numpy/distutils/fcompiler/vast.py,sha256=JUGP68JGOUOBS9WbXftE-qCVUD13fpLyPnhpHfTL5y0,1719
numpy/distutils/from_template.py,sha256=BL-vypfI0GNJrTo-nKs445liTW2Qdfvrsu8RMjATL5A,8174
numpy/distutils/intelccompiler.py,sha256=77BDCj7_6Nnf92ZDeFQgA6aDKJGkzDQ7u0nuQGw1v8g,4345
numpy/distutils/lib2def.py,sha256=HQ7i5FUtBcFGNlSlN20lgVtiBAHQbGXxmYdvkaJTjLI,3760
numpy/distutils/line_endings.py,sha256=hlI71r840mhfu8lmzdHPVZ4NFm-kJDDUMV3lETblVTY,2109
numpy/distutils/log.py,sha256=a5-sPwcZei7kSP0ZQZH4tTrlRWHnL8jtzLCeUSPA_04,2990
numpy/distutils/mingw/gfortran_vs2003_hack.c,sha256=FDTA53KYTIhil9ytvZlocOqghQVp9LacLHn1IurV0wI,83
numpy/distutils/mingw32ccompiler.py,sha256=YPtvN4NLUgy925Yy6AYtzsHtp6blxpDGj_FgvGCHKio,23065
numpy/distutils/misc_util.py,sha256=fIFMy1xtLXS5Mb0NA86co4wDY2MT08jgs5AVCObNddo,92270
numpy/distutils/msvc9compiler.py,sha256=bCtCVJmGrBHPm9sOoxa3oSrdrEVCNQFEM5O5hdqX8Hc,2255
numpy/distutils/msvccompiler.py,sha256=sGGkjB-iSQFEfsMfQY8ZJfPKs6vm2DY9Y_OKi0Fk__0,1986
numpy/distutils/npy_pkg_config.py,sha256=q-ASkO8wZ5HmiTEH_6gzO2bPV4i5dz3bTu4EuSxFQJM,13409
numpy/distutils/numpy_distribution.py,sha256=nrdp8rlyjEBBV1tzzi5cE-aYeXB5U3X8T5-G0akXSoY,651
numpy/distutils/pathccompiler.py,sha256=a5CYDXilCaIC85v0fVh-wrb0fClv0A7mPS87aF1inUc,734
numpy/distutils/setup.py,sha256=zd5_kD7uTEOTgC8Fe93g9A_5AOBEySWwr-2OxHsBBEc,651
numpy/distutils/system_info.py,sha256=TAicwq6Mi8zUJ44c9Q9hc_-VVPMiNyvpSwiMj0vIZKs,114200
numpy/distutils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/distutils/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_build_ext.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt_conf.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_exec_command.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_gnu.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_intel.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_nagfor.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_from_template.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_log.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_mingw32ccompiler.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_misc_util.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_npy_pkg_config.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_shell_utils.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_system_info.cpython-39.pyc,,
numpy/distutils/tests/test_build_ext.py,sha256=te4h-jzABgSF_efL2LC-Hwc3hlq8lfqg5JEeHGQgV3A,2736
numpy/distutils/tests/test_ccompiler_opt.py,sha256=sexv_aeRrOkU9hK_3NVQtj8zPE1FhPdQkxCm2RjJdDg,28796
numpy/distutils/tests/test_ccompiler_opt_conf.py,sha256=LmFqpGwqGFgUwLQdV5xJQhs_xB9gnL7_P-7oOOUqa78,6521
numpy/distutils/tests/test_exec_command.py,sha256=Ltd4T3A_t3Oo_QSYjOkWKqPj-LttXEumEVKhLxVfZEU,7515
numpy/distutils/tests/test_fcompiler.py,sha256=SS5HOLIg0eqkmZTRKeWq9_ahW2tmV9c9piwYfzcBPmc,1320
numpy/distutils/tests/test_fcompiler_gnu.py,sha256=RlRHZbyazgKGY17NmdYSF3ehO0M0xXN4UkbsJzJz4i8,2191
numpy/distutils/tests/test_fcompiler_intel.py,sha256=4cppjLugoa8P4bjzYdiPxmyCywmP9plXOkfsklhnYsQ,1088
numpy/distutils/tests/test_fcompiler_nagfor.py,sha256=ntyr8f-67dNI0OF_l6-aeTwu9wW-vnxpheqrc4cXAUI,1124
numpy/distutils/tests/test_from_template.py,sha256=ZzUSEPyZIG4Zak3-TFqmRGXHMp58aKTuLKb0t-5XpDg,1147
numpy/distutils/tests/test_log.py,sha256=uKmJCgRwBVNwhokhP1UewhfQV4TSyjwwaeip0AaQo_4,794
numpy/distutils/tests/test_mingw32ccompiler.py,sha256=7X8V4hLMtsNj1pYoLkSSla04gJu66e87E_k-6ce3PrA,1651
numpy/distutils/tests/test_misc_util.py,sha256=YKK2WrJqVJ5o71mWL5oP0l-EVQmqKlf3XU8y7co0KYc,3300
numpy/distutils/tests/test_npy_pkg_config.py,sha256=1pQh-mApHjj0y9Ba2tqns79U8dsfDpJ9zcPdsa2qbps,2641
numpy/distutils/tests/test_shell_utils.py,sha256=okNSfjFSAvY3XyBsyZrKXAtV9RBmb7vX9o4ZLJc28Ds,2030
numpy/distutils/tests/test_system_info.py,sha256=MYZ2k5lQNg5l8hkZqwi13bSCZmeOPcgfjt3HIauwU-I,11320
numpy/distutils/unixccompiler.py,sha256=ED_e7yHVNj4oXMze6KY8TbPxjyvHDC6o4VNGAkFA5ZQ,5567
numpy/doc/__init__.py,sha256=llSbqjSXybPuXqt6WJFZhgYnscgYl4m1tUBy_LhfCE0,534
numpy/doc/__pycache__/__init__.cpython-39.pyc,,
numpy/doc/__pycache__/constants.cpython-39.pyc,,
numpy/doc/__pycache__/ufuncs.cpython-39.pyc,,
numpy/doc/constants.py,sha256=8jSZxoMlAwNxDbAdJQfiNvx5RgDDfp_ISjWKbpTqhsM,9567
numpy/doc/ufuncs.py,sha256=jL-idm49Qd8xNns12ZPp533jorDuDnUN2I96hbmFZoo,5497
numpy/dual.py,sha256=RgoFIabqn8Hu9lSRakjO1plfDdYBJQq_8Gn__rE8TqQ,2297
numpy/f2py/__init__.py,sha256=3xmSj85h_zwdkO71SbkEH5vffGxC4S5iz893ZFGPbNU,5994
numpy/f2py/__init__.pyi,sha256=OMzo7HHXPGF_Wlj3kNdTTaD8_e48ZUkvwIkGSK12-ms,1134
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__pycache__/__init__.cpython-39.pyc,,
numpy/f2py/__pycache__/__main__.cpython-39.pyc,,
numpy/f2py/__pycache__/__version__.cpython-39.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-39.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-39.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-39.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-39.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-39.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-39.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-39.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-39.pyc,,
numpy/f2py/__pycache__/f2py_testing.cpython-39.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-39.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-39.pyc,,
numpy/f2py/__pycache__/rules.cpython-39.pyc,,
numpy/f2py/__pycache__/setup.cpython-39.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-39.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-39.pyc,,
numpy/f2py/__version__.py,sha256=TisKvgcg4vh5Fptw2GS1JB_3bAQsWZIKhclEX6ZcAho,35
numpy/f2py/auxfuncs.py,sha256=zzW7g7WGqp9d31Pei72VEaywe7zVBgIxagvTvKPA4Pk,22636
numpy/f2py/capi_maps.py,sha256=bzZ4Z5zsAgUrjUlJ4oSUIZIDRvS98saGfSL5M0hnZhk,32143
numpy/f2py/cb_rules.py,sha256=saiig-kcW-ztYj1fz3Ct0zG0ZdhGX3uxPrzob1FHjcs,24926
numpy/f2py/cfuncs.py,sha256=1D6QFy7ttKo4nBnYEgrNB8sk-_r-5tF6X-ahT6fuMxc,50889
numpy/f2py/common_rules.py,sha256=pRpoVr457G3JcTD_W4V0AmgQBzAIKcqPw4j_Hv3elF0,5070
numpy/f2py/crackfortran.py,sha256=hOwoCCzkQv8EiwBqAym39XBPlBCMgOew1OpGPvhzTi4,133019
numpy/f2py/diagnose.py,sha256=LmGu6iZKr9WHfMZFIIEobZvRJd9Ktdqx0nYekWk4N7g,5384
numpy/f2py/f2py2e.py,sha256=IUU32L_Ov4PkFGwAosatwfN93ZyUYQmzp_wFGOEEi1c,24931
numpy/f2py/f2py_testing.py,sha256=vybOK-G_b0KYFNNZdhOCUf5pZExEdWfcIueIKODu024,1503
numpy/f2py/f90mod_rules.py,sha256=k-AxjnKtrMDFO4LYXSeEnYtlxFFtYysONHjTICuMka4,10081
numpy/f2py/func2subr.py,sha256=-e2VG2t0YDEOO_jIhLZRFLg5uoIoPGtcjir-4BzdNCs,9655
numpy/f2py/rules.py,sha256=fapaE9nOSHcrXFwxWUG52MhrveR9IMFiQvKBGJsggU8,61853
numpy/f2py/setup.py,sha256=AYA8aERgAo5gj7zt9IiYTnFK8qqjN2GzuxrfyuAJ_Qc,2406
numpy/f2py/src/fortranobject.c,sha256=vYA0Jv3GebldGmmm4mB1iBokAQv44v5S0-VU-Q9tklc,38732
numpy/f2py/src/fortranobject.h,sha256=cnO-2gqvn5BZfikyd2UtvZWB9hQ8tACMWu47iAtZxwM,4527
numpy/f2py/symbolic.py,sha256=YBtqohCJgMrCQZaTQET_LiB7l8pF3CI1tVt0o3njWoQ,54518
numpy/f2py/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/f2py/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_compile_function.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_module_doc.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-39.pyc,,
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=5f64EgUBV5fX_164hiUPgqQWO57ogZF1wDKbq5NAI3w,7526
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/module_data/mod.mod,sha256=EkjrU7NTZrOH68yKrz6C_eyJMSFSxGgC2yMQT9Zscek,412
numpy/f2py/tests/src/module_data/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/test_abstract_interface.py,sha256=GBxj3l7J02xQ4uE2t2LBK972OQ1Jl06tZgTbRN9FwTA,1883
numpy/f2py/tests/test_array_from_pyobj.py,sha256=kUB0hCAGPEjC46GaAEV7vog9eeVLP4GDUUz2c6kRXkc,23406
numpy/f2py/tests/test_assumed_shape.py,sha256=vQ2pligMnltD87SG0EektWnMbLE3Cgh7sd-58EQZEfA,1615
numpy/f2py/tests/test_block_docstring.py,sha256=AFgfCimjB0gcmqfyHhTmfRVCrMZunkIueoZGrV9SbaA,650
numpy/f2py/tests/test_callback.py,sha256=5SKq20nPw8O79yPAd2d83orOjnIs-nJU0bSb2YbY2Cs,8506
numpy/f2py/tests/test_common.py,sha256=tRwTdz6aQ0RYREFC-T-SfEyq25wWYo5pknVAqvvvBjM,827
numpy/f2py/tests/test_compile_function.py,sha256=d2zOi1oPKwqSMBILFSUuJeIX3BY6uAOBZnvYw5h3K54,4434
numpy/f2py/tests/test_crackfortran.py,sha256=sdetnMkRqdj6cTHKaQ5xr6_Td6m30dSrsEEI-TtuqwA,9762
numpy/f2py/tests/test_kind.py,sha256=eH_sM5X5wIwe3T9yVMH6DH8I4spsgRaeIork_FJG-8Q,1044
numpy/f2py/tests/test_mixed.py,sha256=faYM1laPKwStbz-RY-6b9LCuj2kFojTPIR0wxsosXoI,946
numpy/f2py/tests/test_module_doc.py,sha256=Qz_TonbInzgdY8KsnC2Y8mxmiqLWMruZKDPa0FPMbrE,980
numpy/f2py/tests/test_parameter.py,sha256=S1K9K9Uj5dWjmWF8euBUMJdGilIqn1nNH2FftcS1INU,4026
numpy/f2py/tests/test_quoted_character.py,sha256=81CR1ZIyKygz7noFJMcgaLdg18nY8zcFfrSN2L_U9xg,959
numpy/f2py/tests/test_regression.py,sha256=UMVOFSIybDbxS7zs3aPh6dQ1jAOusWgUh8plI1cDvzQ,1865
numpy/f2py/tests/test_return_character.py,sha256=9wZQWCzbzBMYh9Rg_0EnMQDmQhzupdrr_d9BVktpEwQ,4052
numpy/f2py/tests/test_return_complex.py,sha256=jYcoM3pZj0opGXXF69EsGQ6HD5SPaRhBeGx3RhnJx8c,4778
numpy/f2py/tests/test_return_integer.py,sha256=MpdNZLxeQwER4M4w5utHYe08gDzrjAWOq_z5wRO8hP8,4751
numpy/f2py/tests/test_return_logical.py,sha256=uP9rWNyA3UARKwaQ6Fsc8grh72G5xnp9XXFSWLdPZQ8,5028
numpy/f2py/tests/test_return_real.py,sha256=05OwUsgmI_G_pr197xUXgIgTNVOHygtY2kPqRiexeSY,5605
numpy/f2py/tests/test_semicolon_split.py,sha256=Ap6S5tGL6R8I2i9iUlVxElW4IMNoz0LxIHK1hLIYZhQ,1577
numpy/f2py/tests/test_size.py,sha256=ukbP0NlzqpfD6M_K58WK6IERSXc_6nHpyUqbYfEbk8M,1335
numpy/f2py/tests/test_string.py,sha256=jNj3vk1dVIiIfzybZyrcXwjfUu3HYTI0kWIBLrJ2rXc,4649
numpy/f2py/tests/test_symbolic.py,sha256=mZU-DwWwkxZ2gToydZRbzLH9PyNYdPxpomO_Se-8YQE,18491
numpy/f2py/tests/util.py,sha256=CeVG5YLv-81fTqOe6v3DJcnm6U_peLdm_cb_vraCaXk,9833
numpy/f2py/use_rules.py,sha256=ROzvjl0-GUOkT3kJS5KkYq8PsFxGjebA6uPq9-CyZEQ,3700
numpy/fft/__init__.py,sha256=efF5_sqvdBfwIBHcEvkIb0a7YWZvR5oa4jh_aelUTpk,8387
numpy/fft/__init__.pyi,sha256=VOw9Y2eJC1DWqeJsB2RArNXS95Lwx6dy2pwjFeNtYm8,611
numpy/fft/__pycache__/__init__.cpython-39.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-39.pyc,,
numpy/fft/__pycache__/helper.cpython-39.pyc,,
numpy/fft/__pycache__/setup.cpython-39.pyc,,
numpy/fft/_pocketfft.py,sha256=Eig0b3LbIFwSRFVcB6XHbBpAbDFIjO5gijB5_cY8_1o,54321
numpy/fft/_pocketfft.pyi,sha256=mBmJ-vteX5ICgiiGFT_y-vOcrthg7RlvkC0s9QKlduk,2477
numpy/fft/_pocketfft_internal.cp39-win_amd64.pyd,sha256=PBFCx4Vn1GA8FujcTD6MG0lZs4jbWParfF53oEyDwj8,116736
numpy/fft/helper.py,sha256=divqfKoOb0p-Zojqokhj3fQ5HzgWTigxDxZIQnN_TUQ,6375
numpy/fft/helper.pyi,sha256=gkv97QRAX77f-XhsSPVei_Gq4i-NsEC0sURYaiwTKtQ,1310
numpy/fft/setup.py,sha256=-aF3b5s_eL6Jl0rS_jMFtFPzbYyT55FQE2SgblAawf0,750
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-39.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-39.pyc,,
numpy/fft/tests/test_helper.py,sha256=iopdl7-SHA5E1Ex13rYdceskgoDI5T8_Kg12LfqK1HA,6315
numpy/fft/tests/test_pocketfft.py,sha256=ia4A2pJxy5Rohir3c47XlO3WiB9DWYIfFv4ueh5sVyg,13134
numpy/lib/__init__.py,sha256=D_APB98cfipadeVf9KXXCg4MRYwNl_JrjbrJ_Fityhc,1840
numpy/lib/__init__.pyi,sha256=MLLkUajrKTwBz6J5d7WEo04bzyJP_5wtWOKpPPd5DSI,5834
numpy/lib/__pycache__/__init__.cpython-39.pyc,,
numpy/lib/__pycache__/_datasource.cpython-39.pyc,,
numpy/lib/__pycache__/_iotools.cpython-39.pyc,,
numpy/lib/__pycache__/_version.cpython-39.pyc,,
numpy/lib/__pycache__/arraypad.cpython-39.pyc,,
numpy/lib/__pycache__/arraysetops.cpython-39.pyc,,
numpy/lib/__pycache__/arrayterator.cpython-39.pyc,,
numpy/lib/__pycache__/format.cpython-39.pyc,,
numpy/lib/__pycache__/function_base.cpython-39.pyc,,
numpy/lib/__pycache__/histograms.cpython-39.pyc,,
numpy/lib/__pycache__/index_tricks.cpython-39.pyc,,
numpy/lib/__pycache__/mixins.cpython-39.pyc,,
numpy/lib/__pycache__/nanfunctions.cpython-39.pyc,,
numpy/lib/__pycache__/npyio.cpython-39.pyc,,
numpy/lib/__pycache__/polynomial.cpython-39.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-39.pyc,,
numpy/lib/__pycache__/scimath.cpython-39.pyc,,
numpy/lib/__pycache__/setup.cpython-39.pyc,,
numpy/lib/__pycache__/shape_base.cpython-39.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-39.pyc,,
numpy/lib/__pycache__/twodim_base.cpython-39.pyc,,
numpy/lib/__pycache__/type_check.cpython-39.pyc,,
numpy/lib/__pycache__/ufunclike.cpython-39.pyc,,
numpy/lib/__pycache__/user_array.cpython-39.pyc,,
numpy/lib/__pycache__/utils.cpython-39.pyc,,
numpy/lib/_datasource.py,sha256=QV2Hb_c_V_2DxWv8TwSs8TpVn1N1P8m2KzZ3-sE5lRs,23337
numpy/lib/_iotools.py,sha256=IBMRLRtNoYbqUUPO9tite0VQYbMG8J7SN2ZCNj3pbQE,31770
numpy/lib/_version.py,sha256=IjsC8gQRuFo_Ns0fSuF0BW7ndA3h9jQV5mClyWe9A8s,5010
numpy/lib/_version.pyi,sha256=s4D_OzArROmCoR6bABfRb9dsL4AR3aZRA9f6s-GJ86U,720
numpy/lib/arraypad.py,sha256=V4iW49CT3KyPHdVK-4Q2kjJqgEb-N35LusA6G-Q2LKU,32102
numpy/lib/arraypad.pyi,sha256=B837wmsAyl2GfcI81Xp4tjBLQrEFsCLYW6r02Z_8OTU,1961
numpy/lib/arraysetops.py,sha256=1foGsO8myJ-SZ717DoOR0P-2mC1zpOVlKWoewUJN2Fk,27302
numpy/lib/arraysetops.pyi,sha256=n0n4WU-3lbobTsIDn8wPZX3oUkwZJglFLTvGC_FYIUg,8266
numpy/lib/arrayterator.py,sha256=29pO5S0ciEZwt1402Q0-5cRbyKspV4tlPX1-m_D_Hgc,7282
numpy/lib/arrayterator.pyi,sha256=9eNzn5pSJ1Uudgit0l1HAvWWYNu5RlId3yRrc_SbOvg,1596
numpy/lib/format.py,sha256=IyLm-XfyPLoI5m3lr7weK9mvssD61AQWmAHsGrWtd_g,32349
numpy/lib/format.pyi,sha256=5fN9dIYEd9xwI02TK9ZjDdhNwwjrsdqKlJJ5X8nprgE,781
numpy/lib/function_base.py,sha256=kboPlstrXsbalet3UmxwWpzfWvdrGlU60VxeOgiC00E,187071
numpy/lib/function_base.pyi,sha256=FyeaeqCKejm7zSisUoEXnWOvItz1vnAsatuo-EJ1Zv0,17234
numpy/lib/histograms.py,sha256=pCC7Ip_S_OynqkpC8epH1uYTv0-uqWyClvdESw5n8jI,41344
numpy/lib/histograms.pyi,sha256=ISiSMmXN27xuie8RNH6L8YkXKKRrS-QmaGQsKyGGrpE,1098
numpy/lib/index_tricks.py,sha256=RMKS_mvDv6AATR8sv1BAC7BPh91cbXB65yAKFhrviKw,31594
numpy/lib/index_tricks.pyi,sha256=w_nVRuqKqsfJtrWOry6WyqdQ0vPCh_WWljkd6X2bJlg,4426
numpy/lib/mixins.py,sha256=j_Hfrfp6d6lJAVFlM9y2lu6HHsK_yDInyU74SJWsZDQ,7228
numpy/lib/mixins.pyi,sha256=i8hQRpFQ9S3hxn345gy0rglF2MX1JV2G92kmEem6AMM,2215
numpy/lib/nanfunctions.py,sha256=opIM9WFIk7mD6dpJIa5x01S0RfiaZH0NOzY-UKyG1yo,67562
numpy/lib/nanfunctions.pyi,sha256=vx13EfBCNWEAWQ5VL65e__n7uVTdNsVzOsI5ene8JYI,671
numpy/lib/npyio.py,sha256=ptwuWLenS8ktrVHkt-AOAd51wyqyoHFmGyRuYEnS7Kw,92268
numpy/lib/npyio.pyi,sha256=YQGjxCzaqqjtvjbf8UaoXL4X0UHi9275Ti1Vk1LZNHA,7468
numpy/lib/polynomial.py,sha256=r0Pm8BLNkJefZykfU8VMZFnY6quTci0HKxMBaFvZxMM,45594
numpy/lib/polynomial.pyi,sha256=AO71NYllBXNQ_Wgd79SknYEmPrMPEwN7o9t4NCwjmDk,7283
numpy/lib/recfunctions.py,sha256=G8rUFdL72T_12xfxh8AMhjpm_tmZmQTyQu7aBtLDFQM,58132
numpy/lib/scimath.py,sha256=cRDjzILwHbLzgNVvr3d7Fuc4KKs5OpVTvHNPR02l0iE,15402
numpy/lib/scimath.pyi,sha256=V3Qf4OCi6OZzAR2efTprM4fd8b5aipCu0wPJybDvHt8,225
numpy/lib/setup.py,sha256=1s3corE4egZnDZ6I8au_mx7muRPgb3ZxL4Ko2SHt_2Q,417
numpy/lib/shape_base.py,sha256=ECamk2gUCc4bqtPz2rU2dOBnTD3QjOy_MeIof08hgC0,39631
numpy/lib/shape_base.pyi,sha256=V57_Wzx-_lzY35xRB9mhjtvENUWOhvp5_BMwtQEFN2M,5411
numpy/lib/stride_tricks.py,sha256=u7eOncvSXpj-Z72S3ROXPWnaQdPwN8GeSDdDHsJ_E5o,18456
numpy/lib/stride_tricks.pyi,sha256=0XdO2ln4FK_opFh3QbxQHGpX3PNt2jN-tsmt_UmfD38,1915
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_financial_expired.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-39.pyc,,
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=pTTVh8ezp-lwAK3fkgvdKU8Arp5NMKznVD-M6Ex_uA0,341
numpy/lib/tests/data/py3-objarr.npz,sha256=qQR0gS57e9ta16d_vCQjaaKM74gPdlwCPkp55P-qrdw,449
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=QUI-8kXh4rHVSC-G10dlflsonKudk3_hqi3CNRySMWM,10837
numpy/lib/tests/test__iotools.py,sha256=q44VFSi9VzWaf_dJ-MGBtYA7z7TFR0j0AF-rbzhLXoo,14096
numpy/lib/tests/test__version.py,sha256=v2TOlH4f1Pmzxn1HWby3eBgLO9tGnhwH2LvBXlXtHP4,2063
numpy/lib/tests/test_arraypad.py,sha256=ZoqM25xb5iI_K6ampX5cov2zFUwQZ8i0Xu3gY_ncdk0,55647
numpy/lib/tests/test_arraysetops.py,sha256=zd0sx_8ermgx028uEAtpr-CpMbdO8Ox70MGsOrdEO-w,29196
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_financial_expired.py,sha256=JfDHAoEDLPcKzcpzRsj8pijDSaFmQRWM6BiOjL4QFTs,371
numpy/lib/tests/test_format.py,sha256=u2GQZIG_-2GImx2mrzUZcXtF1yZnPwZyTfRvsr_SRUk,39167
numpy/lib/tests/test_function_base.py,sha256=w1zJf8ursYSnxxDaoEPMxfPp4RZ5QTmzJ4lh--1WNo0,148199
numpy/lib/tests/test_histograms.py,sha256=_uKM8dtMWCwGtIgIwog3s3jyPb8w804TWqy9iOkYeSI,34510
numpy/lib/tests/test_index_tricks.py,sha256=xOW7cRn9so6sybrMIY_Hx-r-X9CAYA-xPO7PpJggNL0,19472
numpy/lib/tests/test_io.py,sha256=iX7mtTO2cYb4F0be7C2jKYYVFapi8keUqHQtDYUD81c,105995
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=5ZgW1rWkCeqH9z69Cb4xbgiUr-gA8u1eZ2UMQ-O7G1g,45777
numpy/lib/tests/test_packbits.py,sha256=XpFIaL8mOWfzD3WQaxd6WrDFWh4Mc51EPXIOqxt3wS0,17922
numpy/lib/tests/test_polynomial.py,sha256=x2h1teRr7d-gcu17w0s3G4NlZt1jgOjKTiXP_7lPqVI,11698
numpy/lib/tests/test_recfunctions.py,sha256=f9J49n_8ot0zG0Bw4XXFx3l1XN2VeAu9ROgn0dV4GLY,42134
numpy/lib/tests/test_regression.py,sha256=NXKpFka0DPE7z0-DID-FGh0ITFXi8nEj6Pg_-uBcDIg,8504
numpy/lib/tests/test_shape_base.py,sha256=J-OJa6R9jJ4Yv3wdJ5YtZaUY73I4sQtNg9PtD8Ew78o,25176
numpy/lib/tests/test_stride_tricks.py,sha256=1zeBcvhltePbeE6SBoF4gomAYaZzwaHjvbWqcgI2JiY,23494
numpy/lib/tests/test_twodim_base.py,sha256=U79GdKW-Z4UOojP20-d3Pvdcsp4ImTmJNCEU84GkrwA,19220
numpy/lib/tests/test_type_check.py,sha256=ffuA-ndaMsUb0IvPalBMwGkoukIP9OZVJXCuq299qB8,15597
numpy/lib/tests/test_ufunclike.py,sha256=rpZTDbKPBVg5-byY1SKhkukyxB2Lvonw15ZARPE3mc0,3382
numpy/lib/tests/test_utils.py,sha256=C5Y3YQQ6wIWNklf4Mfp-Sfx6RL60nvgjJF1ne_XAPzA,4734
numpy/lib/twodim_base.py,sha256=8e0dyD52y8HV-erJ_Su66j9lbuKwnvPaWONBvU4UZxE,32466
numpy/lib/twodim_base.pyi,sha256=kPbZ3MKmeGRw_qkvM0v-VmRGDzX1ZaKRGuJRvKsGnbE,5863
numpy/lib/type_check.py,sha256=YSoY_-Dv0oz7XZXZ9--ZVsACp7a6k0jNzKoK7bYbn5w,21547
numpy/lib/type_check.pyi,sha256=OQ_JnONr1siVYXaH48DPNTGFkx3Cb-3dJSo8DpGilHY,5941
numpy/lib/ufunclike.py,sha256=snevUCqlqCQKAlzz5f8DdFXkAarEPafG8CHmzXMs-OI,8299
numpy/lib/ufunclike.pyi,sha256=aN1DpYuDcQ0_JxUyMv93sG6AxNHnsKpCyggbV53rR_c,1377
numpy/lib/user_array.py,sha256=5yqkyjCmUIASGNx2bt7_ZMWJQJszkbD1Kn06qqv7POA,8007
numpy/lib/utils.py,sha256=Bav4_B9lcgqt6qo42bWT_0u41LLfYQ4rDrUpVjxEqP4,34217
numpy/lib/utils.pyi,sha256=jjHVYnxczPkdBe3r7q3Jh9-rEce9Uwi6aDhNIGs9bNw,2521
numpy/linalg/__init__.py,sha256=EefgbxHkx8sLgXXLtpD8i8A_SCx-V4uz1Z_aQkmW6Ec,1893
numpy/linalg/__init__.pyi,sha256=3D5P7SCNvfeY0r2r9gSA7qwBnJ1y0eW5VM-GHqS_Bqw,682
numpy/linalg/__pycache__/__init__.cpython-39.pyc,,
numpy/linalg/__pycache__/linalg.cpython-39.pyc,,
numpy/linalg/__pycache__/setup.cpython-39.pyc,,
numpy/linalg/_umath_linalg.cp39-win_amd64.pyd,sha256=esiaGd69FAP8xW2jySnApXra6LXSU7y4BxBGAHJrC3g,168960
numpy/linalg/lapack_lite.cp39-win_amd64.pyd,sha256=A9qdwbyyII9Ybd8otypE-2COCLYxZDvOxvSbrQ9NV4o,22528
numpy/linalg/linalg.py,sha256=kOtIHEWAbuUIlCzvEy8gGuFMK3MrvCb51L3EfMPvEqc,92323
numpy/linalg/linalg.pyi,sha256=YayvRmor9a4wP8E2jJAe04g2AKnYLLrgTFmSAUoBfyA,7721
numpy/linalg/setup.py,sha256=b9DamRC29bjFlCl3vFlZuCQtk3_cnmbyH_10tGRkij4,2903
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=oD1jSIbuOrFvNL8ZwpymAW3n9xPsEMPMB3VldlDrlCs,78968
numpy/linalg/tests/test_regression.py,sha256=T0iQkRUhOoxIHHro5kyxU7GFRhN3pZov6UaJGXxtvu0,5745
numpy/ma/__init__.py,sha256=9i-au2uOZ_K9q2t9Ezc9nEAS74Y4TXQZMoP9601UitU,1458
numpy/ma/__init__.pyi,sha256=-BfZ-kYa_jh5Bk74jCAJzj6bigs7dzI8007xsi20sqs,6319
numpy/ma/__pycache__/__init__.cpython-39.pyc,,
numpy/ma/__pycache__/bench.cpython-39.pyc,,
numpy/ma/__pycache__/core.cpython-39.pyc,,
numpy/ma/__pycache__/extras.cpython-39.pyc,,
numpy/ma/__pycache__/mrecords.cpython-39.pyc,,
numpy/ma/__pycache__/setup.cpython-39.pyc,,
numpy/ma/__pycache__/testutils.cpython-39.pyc,,
numpy/ma/__pycache__/timer_comparison.cpython-39.pyc,,
numpy/ma/bench.py,sha256=4HgDHbz_nCW4GmVLyjqk5FcgU7O_cqxNyCHufLwyyIY,4989
numpy/ma/core.py,sha256=I3R-xUsoxXEVMrJotwEL4vQeKhhBF0BSSkHXaIEMapM,275536
numpy/ma/core.pyi,sha256=k-OSYiUWEeF8VU_ZOh2FaM85_fPEFPpPngFwo1s-JeI,14628
numpy/ma/extras.py,sha256=skwchZhGM4v50HxJXvbez2yhwwGU0d6FbATHw27cZGg,60160
numpy/ma/extras.pyi,sha256=cQzyeK_KloJ-jJ7M5n0Bs0ZbmIrlakiaJB1QCBSbBjs,2682
numpy/ma/mrecords.py,sha256=zy-LVXMJnyDCbsBfWyFCC8Z2-y6ApeXw9OfQJNiiWZg,28015
numpy/ma/mrecords.pyi,sha256=Em1lTpur9eSqAnvWmSHyq6KDFD5qZV4U9MZuT52POY0,2031
numpy/ma/setup.py,sha256=DCi5FtZTlkhR3ByJH5Sp5B962bfcWpaOjA-y7ueyoog,430
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-39.pyc,,
numpy/ma/tests/test_core.py,sha256=isXN3I5I8y6OuLTQSAM4pTfXMPGBqw2aRwgReHpTdfk,210931
numpy/ma/tests/test_deprecations.py,sha256=4f62l6k8ICMMQNLWa9KFuvVG0NsYwoGpR2q5DxdAoPo,2866
numpy/ma/tests/test_extras.py,sha256=rNvSgz32IKuEBsMFqDVPsDRt16uhExz5AYz-MKNffBs,69500
numpy/ma/tests/test_mrecords.py,sha256=KPuJanNCTIvPXPyXYR3Yp7LGuP2tkPmbc1iXB9Lt5zU,20376
numpy/ma/tests/test_old_ma.py,sha256=kvhUKtjeGU_mi-NAuTm_Z0Z9vRNR2sS0XDh_ddcagD4,33632
numpy/ma/tests/test_regression.py,sha256=Hi-p5QdcivsxUDSpTl3MGtAnmJCJPhhIj3c5nYI9rw8,3170
numpy/ma/tests/test_subclassing.py,sha256=6Bw3t27G8QTpDRwWq7ArEHniiswwFrxzkkFWyzE205Y,14673
numpy/ma/testutils.py,sha256=5nMEDNCI9e5KWPGQqFhi2_HPHJO5mZ_XQaMXwE4Fhl4,10527
numpy/ma/timer_comparison.py,sha256=xhRDTkkqvVLvB5HeFKIQqGuicRerabKKX3VmBUGc4Zs,16101
numpy/matlib.py,sha256=l-292Lvk_yUCK5Y_U9u1Xa8grW8Ss0uh23o8kj-Hhd8,10741
numpy/matrixlib/__init__.py,sha256=OYwN1yrpX0doHcXpzdRm2moAUO8BCmgufnmd6DS43pI,228
numpy/matrixlib/__init__.pyi,sha256=J5U8_RBiS7dMrTgGp-XquTD4d7iwb3XXlyb6s9r1wcI,294
numpy/matrixlib/__pycache__/__init__.cpython-39.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-39.pyc,,
numpy/matrixlib/__pycache__/setup.cpython-39.pyc,,
numpy/matrixlib/defmatrix.py,sha256=zDXeDEu5DcW3R5ijl_MjGzp9bXM36uhWm3s0hbMTzJc,31780
numpy/matrixlib/defmatrix.pyi,sha256=k9wTVTf-eHcko8YZ_krt2z7dPvojadkruf1JrYJRYiY,444
numpy/matrixlib/setup.py,sha256=DEY5vWe-ReFP31junY0nZ7HwDpRIVuHLUtiTXL_Kr3A,438
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=nbY_HkwzoJbhYhACiEN-cZmR644sVJvMKWUcsANPayQ,15435
numpy/matrixlib/tests/test_interaction.py,sha256=C1YtIubO6Qh8RR-XONzo8Mle4bu4SvwsvBnB0x0Gy4g,12229
numpy/matrixlib/tests/test_masked_matrix.py,sha256=aKC-imVpBSyjnokr0Ntn-e47P1MXhKzOSnGz1ji9J_c,9156
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=FgYV3hwkpO0qyshDzG7n1JfQ-kKwnSZnA68jJHS7TeM,958
numpy/polynomial/__init__.py,sha256=rtJL1VpB8PVOJSwKUpUJRYLdvZ4bkMnWNijL1ByTTWo,6973
numpy/polynomial/__init__.pyi,sha256=YuGJu_gte0yN92LcaZQcyIDVZ1AtEbE1AM51k7VVPuQ,750
numpy/polynomial/__pycache__/__init__.cpython-39.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-39.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-39.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-39.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-39.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-39.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-39.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-39.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-39.pyc,,
numpy/polynomial/__pycache__/setup.cpython-39.pyc,,
numpy/polynomial/_polybase.py,sha256=KOclxhhzXpqEXV3q_kc-pSeC5vPMjpdhAC9DBQ_galY,37626
numpy/polynomial/_polybase.pyi,sha256=8lKGg2lbtBYaTDGcVHrqCf_40ymaQOX9CcMcYGs2nrk,2322
numpy/polynomial/chebyshev.py,sha256=9V-JdKPLdk08yYZKTGYOEvlg5794mCJ1BHbPHe_K4Sw,64570
numpy/polynomial/chebyshev.pyi,sha256=AFySGYufyEi-55ATupi4Ar6mGupv9z-kdkh2PamuY4Q,1444
numpy/polynomial/hermite.py,sha256=UfHwp5uVs8NXzXNlp-w1LdtnzIyP6aQkUOWvkqIm3Xo,53940
numpy/polynomial/hermite.pyi,sha256=XmFHSOJFH_dYGC8DBdR_qSqbL7UB93HkL0_J6ssL4Rw,1269
numpy/polynomial/hermite_e.py,sha256=Lq36k03od-OhWsVq0fmfWi2TmTxQ_QGrTlmrQ4S-MS8,54055
numpy/polynomial/hermite_e.pyi,sha256=tAA3oJRpGKENC0-WZ0EcXsj9PYnKctfdEO6N7_lmCEE,1290
numpy/polynomial/laguerre.py,sha256=_Ka6fZ29Sk5szAEr_JL2Dh4ZRXpc0MvgHmiHSVEAwqc,52218
numpy/polynomial/laguerre.pyi,sha256=HnKXke8KWmU8uT_buXEegy7ZL4i0tncx5FbPqHqziKI,1230
numpy/polynomial/legendre.py,sha256=zyecIu0RwtICHkJudrxdSXraW9KjBHc9ZxboVKTvKqc,52928
numpy/polynomial/legendre.pyi,sha256=H1batvjMKK8qboBEwg9FcUFqxzmDsgkVlbjXHYcLz1I,1230
numpy/polynomial/polynomial.py,sha256=F1nIssmqcFMiQ9aSJo2xztFXAgUrR3bDZNGm0u-oKQo,50219
numpy/polynomial/polynomial.pyi,sha256=8RdEStWi3j8uLzmS4ImkfvV6kOnpW0PXsuNnKLh1pco,1179
numpy/polynomial/polyutils.py,sha256=goXNYkGla4D-kp7YH7YV_eDvDguKJ74tQuYKtUEmgko,22855
numpy/polynomial/polyutils.pyi,sha256=X7eGY_XslBGLXdJlPR71HLq6vfViZkUEkk0JaP70cHU,264
numpy/polynomial/setup.py,sha256=3MP1VT1AVy8_MdlhErP-lS0Rq5fActYCkxYeHJU88Vg,383
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-39.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=TIoMhOcCJd7bAAs-LVNUToLFtYORWRU1H5CSwaPdySU,18931
numpy/polynomial/tests/test_hermite.py,sha256=zGYN24ia2xx4IH16D6sfAxIipnZrGrIe7D8QMJZPw4Y,19132
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=hBgo8w_3iEQosX2CqjTkUstTiuTPLZmfQNQtyKudZLo,18048
numpy/polynomial/tests/test_legendre.py,sha256=v3ajjp0sg1o7njoLhbPftxaIWaxpY0pBp1suImZqJMw,19241
numpy/polynomial/tests/test_polynomial.py,sha256=i7CaTnxfuLkVZzByFTvOVQo7HeMdQpGxgHRmHCFMTlw,20838
numpy/polynomial/tests/test_polyutils.py,sha256=AQZZzxBCymhT1MTv8zCF_NC-nP0d9ybMNebT2d8P3j0,3700
numpy/polynomial/tests/test_printing.py,sha256=o0D4bNFYlFo0YpPYMB6RZwH5zEFpE6qjdeZ-j-D0YsI,16176
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=W_hFzGsKVQfdh3-U15gzsOKKAk8uZgioDkxKyuou4WA,7721
numpy/random/__init__.pyi,sha256=Fa2lJ5xKGp30vheGsOtXTb4S2Snc8Q4aQV42Z0iIjeU,2152
numpy/random/__pycache__/__init__.cpython-39.pyc,,
numpy/random/__pycache__/_pickle.cpython-39.pyc,,
numpy/random/__pycache__/setup.cpython-39.pyc,,
numpy/random/_bounded_integers.cp39-win_amd64.pyd,sha256=SdxD4yerA5wRv0CadAZP--IUEgzmhME8UTgizGWoXyY,251392
numpy/random/_bounded_integers.pxd,sha256=ugYlh8FvGggHCjEaqgO4S_MeRcZg3mw40sDYEqx07QQ,1698
numpy/random/_common.cp39-win_amd64.pyd,sha256=VzsPfuaaGLj6HiPEYSs5uGBHwxgTZ_FBrZCBv6suS2k,182272
numpy/random/_common.pxd,sha256=azT8PZpof-sD2L_zkPv2RmuWNYKV4kQaEEBXZuBHxf4,4853
numpy/random/_examples/cffi/__pycache__/extending.cpython-39.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-39.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=Jb5SRj8As2P07PtoeuiZHZctaY0oAb065PXbG4-T8fI,1884
numpy/random/_examples/cython/__pycache__/setup.cpython-39.pyc,,
numpy/random/_examples/cython/extending.pyx,sha256=_pKBslBsb8rGeFZkuQeAIhBdeIjDcX3roGqV_Jev7NE,2371
numpy/random/_examples/cython/extending_distributions.pyx,sha256=1zrMvPbKi0RinyZ93Syyy4OXGEOzAAKHSzTmDtN09ZY,3987
numpy/random/_examples/cython/setup.py,sha256=dnSUyiRdWWaMfhuNYllvSc8JUrUIknXXWE2CMYDAA50,1491
numpy/random/_examples/numba/__pycache__/extending.cpython-39.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-39.pyc,,
numpy/random/_examples/numba/extending.py,sha256=vnqUqQRvlAI-3VYDzIxSQDlb-smBAyj8fA1-M2IrOQw,2041
numpy/random/_examples/numba/extending_distributions.py,sha256=tU62JEW13VyNuBPhSpDWqd9W9ammHJCLv61apg90lMc,2101
numpy/random/_generator.cp39-win_amd64.pyd,sha256=BH3Qlyl5N3-2ScAXHZc0N12-w2Pv9-5HPNYmrR8EedM,691200
numpy/random/_generator.pyi,sha256=_l4p0UZg9xpL9hOwQuna60xqshfQJwMgAWa62qcA_Zw,22766
numpy/random/_mt19937.cp39-win_amd64.pyd,sha256=VnqSMYFVetiulZG8iXp2FUSBlvkZ6j8uHs8BhRrbx1I,80896
numpy/random/_mt19937.pyi,sha256=l71Xz5FTNmZWg5o6JF2TVGVCEdndhMmhw5DOS3Nmy0Y,757
numpy/random/_pcg64.cp39-win_amd64.pyd,sha256=j7Q-vkCtvdWgxkF9SnMrOrG7kkDKASw9Ugd7tZpy3wM,85504
numpy/random/_pcg64.pyi,sha256=A9sMgdkUaSMLE1VF5_o0UpXBtxWofrWwfhPenxxh5F4,1149
numpy/random/_philox.cp39-win_amd64.pyd,sha256=kQvKeLqx7V_vnafN0Q7MIJIQwQG23JPDcZ-98njya-E,72192
numpy/random/_philox.pyi,sha256=M4lgjqyMYamOHXwOtzKHvA_4L8zurSC4-KoVxmKqbo8,1037
numpy/random/_pickle.py,sha256=nusjpu3S4BDxHQ08GcUtJgUiiAboZo3mCEMI-CQj3GY,2385
numpy/random/_sfc64.cp39-win_amd64.pyd,sha256=nzL5mwTKVq6rsJAYk-LiwgeUIsd7qRWHc0u7lJ7_254,53760
numpy/random/_sfc64.pyi,sha256=MgPdu-UoX3CvTg6dtSJXrKNiaPpb6AIZyT_CQ52u0gg,748
numpy/random/bit_generator.cp39-win_amd64.pyd,sha256=5oR2-Uxmdyz91vjtsb0GIYDZ-_A1uaI28Lj5fCl7Pok,158208
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=TdJvDufgHo-AiA7NUdRCENWLpWOyaQ3dLA2zJ9vs7-k,3600
numpy/random/c_distributions.pxd,sha256=N8O_29MDGDOyMqmSKoaoooo4OJRRerBwsBXLw7_4j54,6147
numpy/random/lib/npyrandom.lib,sha256=dKLRS1EX1zrKTf8HOapXB5jO0kxSbrbv6tK1m0qVOQo,109292
numpy/random/mtrand.cp39-win_amd64.pyd,sha256=q40wu8tpVkB20Ml1EybopPaWWKrgqUYA1GYtnsSqW0s,587264
numpy/random/mtrand.pyi,sha256=sQaFMduce5esI4erA7MRGB615V9p4cknfn-7YgX4Abg,20577
numpy/random/setup.py,sha256=rEZzJX5m10ZQYk6Vsv4ezkdFWvztGf9ym2kIwaCZJVY,6816
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-39.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-39.pyc,,
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/test_direct.py,sha256=NLlyQC2gDpSmJ2Vj_rvrKZImvDkZOYVNvtx-Rdb4LVk,16907
numpy/random/tests/test_extending.py,sha256=MEvdRji34R-uLZ8G7uczp0hrpo0tHAU06xhvGhTiNxU,3588
numpy/random/tests/test_generator_mt19937.py,sha256=yuW8X68FaudEdovjqWmK5DlMLzQGn-qGS78lDfPgs8U,115171
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=YVgW77VNLSG36rf7v2_BitcRGooHRKr5syd_PcOjkDo,5789
numpy/random/tests/test_random.py,sha256=UE-hRj9h10Zcg3S9-1jeOKAnTpQtThJNSG5R4uPoB9c,71499
numpy/random/tests/test_randomstate.py,sha256=tnozs-Dhi-g5kIo5C5aK1Um-bbhwNRtnzlPiGZVHOXo,83538
numpy/random/tests/test_randomstate_regression.py,sha256=MgTKKmJlf_U9I_rmCRpIvPjPsq9ZMW1qGomnIcWr6cw,8133
numpy/random/tests/test_regression.py,sha256=QZc3xP9sPfihkUF7LAzW8zE4AaJ0nionJoYO3M6pAdk,5588
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=KggwK88hgKJyDscYQaODUCS7jLEk1QE9JW-jJRmvxig,29001
numpy/setup.py,sha256=oh8iagOMMwf1EIYkntC7XBeKQxu9EOVcU5aIrFaDxcA,1052
numpy/testing/__init__.py,sha256=RfaUXHdtdcM6LbtS_U2wInkR-0uXE_kpx9mVkfKSBCM,671
numpy/testing/__init__.pyi,sha256=uyfRV_7iyucIIDZn7qE2_Omp5FLs4_EavHbeKebpBPs,1886
numpy/testing/__pycache__/__init__.cpython-39.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-39.pyc,,
numpy/testing/__pycache__/setup.cpython-39.pyc,,
numpy/testing/__pycache__/utils.cpython-39.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-39.pyc,,
numpy/testing/_private/__pycache__/decorators.cpython-39.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-39.pyc,,
numpy/testing/_private/__pycache__/noseclasses.cpython-39.pyc,,
numpy/testing/_private/__pycache__/nosetester.cpython-39.pyc,,
numpy/testing/_private/__pycache__/parameterized.cpython-39.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-39.pyc,,
numpy/testing/_private/decorators.py,sha256=lKeZylqjrMV3zA_3IjBHX64mHNZ2Qo9PNFBeIQyzns4,11732
numpy/testing/_private/extbuild.py,sha256=0fKx1A4ppgTng7OCRmZl9C3iHvBbkto1ZUTOfkVTQGI,8059
numpy/testing/_private/noseclasses.py,sha256=OqMSWVZEg5GGgz8bsoDWKa3oxvXYoqPst6U423s8yBM,14880
numpy/testing/_private/nosetester.py,sha256=35S7suLWVzYBZ-zOt3ugthNZcMBCP7AjA-Z-4jqmtus,19980
numpy/testing/_private/parameterized.py,sha256=fqtI6SFJnyvr1tKlnfhPrA-IWqOUTdQg7c17Pa_X1T0,16593
numpy/testing/_private/utils.py,sha256=rXD3fSMyDxn5F5n9GoN4ZFCJGgwsl8IXRa4cMDKoQZo,87802
numpy/testing/_private/utils.pyi,sha256=7WzcMgjMbvTpRpcHFRzwueIVeNjm26Gg9Tu2Ma3h6qE,10306
numpy/testing/print_coercion_tables.py,sha256=GnC-nt2Sw2D7TJOoIgK3b2Nj63r9VFqGzPStMfdOBgs,6367
numpy/testing/setup.py,sha256=wQPOZ8LI1xqkSe7vfY0apJHJwEyJ490uSveFh1wmTuk,730
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/testing/tests/__pycache__/test_doctesting.cpython-39.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-39.pyc,,
numpy/testing/tests/test_doctesting.py,sha256=wUauOPx75yuJgIHNWlPCpF0EUIGKDI-nzlImCwGeYo0,1404
numpy/testing/tests/test_utils.py,sha256=R5p8INOqFFVRMDzJzViy8xRAAdKQ0-dvSLqirRxcgyc,57259
numpy/testing/utils.py,sha256=dJcf8XDd5szqpHy8tbPmXM43E4a1FQdc6HK-Lj_IQzk,1284
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/tests/__pycache__/test__all__.cpython-39.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-39.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-39.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-39.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-39.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-39.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-39.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-39.pyc,,
numpy/tests/test__all__.py,sha256=JziA96KUyXwWCPExbQcJBqe_RU1xQVrVwi1xhO8tzqM,230
numpy/tests/test_ctypeslib.py,sha256=a-lBRmW_gjLza_u0R1xhrzC3n_3R4MxCeLPBx8OS0Lo,12658
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_version.py,sha256=N6ithCsZfV6UW5lVVMIPZQ-Yht5WIFplUOTSLmwtUNw,1619
numpy/tests/test_public_api.py,sha256=ZZD1tsJScHXLmdBiIunQZ22dUJDhBQFFhgMTlsWWAfo,16389
numpy/tests/test_reloading.py,sha256=S4wjdcdS8CAR0BAUua71nIGR9tPKuyv4YFXxhCwQ4OQ,2308
numpy/tests/test_scripts.py,sha256=KNmMlcO0sube9TLF3McxJZwzM87lLBzXZpjA6qJQkq0,1619
numpy/tests/test_warnings.py,sha256=IMFVROBQqYZPibnHmwepGqEUQoBlDtdC8DlRulbMAd8,2354
numpy/typing/__init__.py,sha256=rfGXqMz8Q-HJSs9eK_V8swks1XBQI0xWeiPLHTBfwU4,11048
numpy/typing/__pycache__/__init__.cpython-39.pyc,,
numpy/typing/__pycache__/_add_docstring.cpython-39.pyc,,
numpy/typing/__pycache__/_array_like.cpython-39.pyc,,
numpy/typing/__pycache__/_char_codes.cpython-39.pyc,,
numpy/typing/__pycache__/_dtype_like.cpython-39.pyc,,
numpy/typing/__pycache__/_extended_precision.cpython-39.pyc,,
numpy/typing/__pycache__/_generic_alias.cpython-39.pyc,,
numpy/typing/__pycache__/_nbit.cpython-39.pyc,,
numpy/typing/__pycache__/_nested_sequence.cpython-39.pyc,,
numpy/typing/__pycache__/_scalars.cpython-39.pyc,,
numpy/typing/__pycache__/_shape.cpython-39.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-39.pyc,,
numpy/typing/__pycache__/setup.cpython-39.pyc,,
numpy/typing/_add_docstring.py,sha256=Fg_yvCZQLR2j-v8AXZ_qP80ad3RQalvVBhDQqaq8g2g,4077
numpy/typing/_array_like.py,sha256=z9Qg3CnLnr1N_JBfVGwZAUOiSJw3Gsc_k3dy9V6HHOA,3270
numpy/typing/_callable.pyi,sha256=b3uwfxmc0PgI94Zm-zPEVfC-PiGQ25h_amMr_qackUk,11107
numpy/typing/_char_codes.py,sha256=ErefqmXW6wxWven5dtd0MSmBgqZbDM-B0L2nQkrMkuQ,6018
numpy/typing/_dtype_like.py,sha256=qFJg7KwRK-9EFFHd4Q2Nz_beIt-yS91EG5m6_q6ay3Q,5604
numpy/typing/_extended_precision.py,sha256=mq4J2bIKeD8XGPTauPuAlwqevkywiZZBCCvsiRBvrdA,1154
numpy/typing/_generic_alias.py,sha256=_PYGRRiBSpFZZCXhcVXmOfD9qEYkTxBiYq8_nL5q5Sw,6623
numpy/typing/_nbit.py,sha256=jo5eJF4zrw7QdDw1dIEKLIIPiLM-1tEn95YbDXrdSPs,361
numpy/typing/_nested_sequence.py,sha256=pUYyaiodu2O31vpog3i9rsEdxx91qz1vt-HhN7ZxrVk,2744
numpy/typing/_scalars.py,sha256=V8LKjaaK6eSHVGRKCZIO1MvpGHNjYYMNeHSEfavLgQQ,987
numpy/typing/_shape.py,sha256=ewHNno8A6Eg3Fl-9JA5jguDk5Q56mEqHjD-vr3VjINY,197
numpy/typing/_ufunc.pyi,sha256=_0LGYbJQSHeb22wppgBQqwOEspZ4Ik8t0ZMwMjNqSF0,11732
numpy/typing/mypy_plugin.py,sha256=cfTWhPYCiA86cz8e03K0jN81FtF0-0X-GW-czNk2spE,6672
numpy/typing/setup.py,sha256=MdGTn_wxQKhOX-nkcJyDxlyz0tit-9sWiL2yQ32xYc0,421
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/typing/tests/__pycache__/test_generic_alias.cpython-39.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-39.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-39.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-39.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=VkJgIJOE0hVWplfu6sS6VmGkZQmwBHnKfrwuI2mUDno,3977
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=dkJ2RPHWndHPfGBS9JoDIxt_ZQMPQv_WmJjHcfh2CCw,1049
numpy/typing/tests/data/fail/array_like.pyi,sha256=q8omx4DDVcQ4m6KfZ5tBo_NTkjB98twICguQp-4xJjQ,470
numpy/typing/tests/data/fail/array_pad.pyi,sha256=JGCMd_sRBYlsPQ2d7EfLaNooTsg1P0jBuD5Ds2MeXAg,138
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=JhbPEvHbcrKGlfSk8BLIIhDj8jRQIQhfuCE2NBmmk1A,535
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=u1pNRrRUM8Q0o131lqGjvTQRy8-eHeazSvRArhnAyOo,494
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=VBOUHgafxOWJQ_tXLb0Phpu93FbLZIgo7mF1-EVUZ0c,535
numpy/typing/tests/data/fail/char.pyi,sha256=xCEKTpdp5Al-Qn-hxkp6i-Cgw7HEk53pt2XAzvpd58Y,2681
numpy/typing/tests/data/fail/chararray.pyi,sha256=_a695QkkSHZ9utlqUYwVUumC2QGhGxcZteB0iGlrFug,2358
numpy/typing/tests/data/fail/comparisons.pyi,sha256=VjriRnjoRGFUsyCheiGo1s5qErQ5ajQW7fCBltxp3Zc,915
numpy/typing/tests/data/fail/constants.pyi,sha256=esbeTGlTUSu3v3jMJ6GBZ_j7Q7RsyJRB47MaKNZaCOk,293
numpy/typing/tests/data/fail/datasource.pyi,sha256=B05CkL35mBUyKmuvi5MF3vTkZRwc0SOJo_r0cKVBBuA,410
numpy/typing/tests/data/fail/dtype.pyi,sha256=ltT4BFaX_KTVdRLw2dMg3_OiSNYjDSNrXsxby6eeLTw,354
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=tQxUQsvtkJIw6PxFwzc_ilw3HVSrrW74CKI1wA_Bq6g,758
numpy/typing/tests/data/fail/flatiter.pyi,sha256=YpW-SsU6ti1Pt2Adj-BgE0wQ9PAMYBylSN7y61Ujtfw,867
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=B1sr7aafdkbfc9WzHqjUg8MOLk4pH6i4zNEUz5rV45A,6146
numpy/typing/tests/data/fail/histograms.pyi,sha256=OuKIQ42wtONC6mkJ5ZWOQ_jGOxj7xHUtFN3FeFdrHTU,437
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=iQ4_ZMyKfS4q-9WXFQ9F5KL_JFUqFoOfhg0s-JvMx1E,499
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=A_fZIktpi_2Q9zanq-Hq0zdKvMXyCfE6PpgUQNYILHY,2134
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=CKOpJiyJzPfhyt01CwcqqCAbIJYD9TkYr7OEjRYU8is,942
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=UGzb3HzY1lUdiwLcT_myFfL-nWedrkhxDt9mt68eokQ,289
numpy/typing/tests/data/fail/lib_version.pyi,sha256=JWtuTLcjkZpGfXshlFpJO5vINxawn9S-mxLGH0-7kcw,164
numpy/typing/tests/data/fail/linalg.pyi,sha256=j6GGpOENz0nuZsza0Dyfy6MtjfRltqrbY8K_7g5H92I,1370
numpy/typing/tests/data/fail/memmap.pyi,sha256=eAX-nEKtOb06mL8EPECukmL8MwrehSVRu5TBlHiSBaQ,164
numpy/typing/tests/data/fail/modules.pyi,sha256=0vg0lxV-ax5bRjYPZh2h6--JjvCYQkZkGbrkckZ2DmM,670
numpy/typing/tests/data/fail/multiarray.pyi,sha256=fcEGSqi6GVn7UjzAZjl8p1mBQTYgWnUrN8tKu47cM-8,1771
numpy/typing/tests/data/fail/ndarray.pyi,sha256=2I4smD6MlUD23Xx8Rt1gCtjj_m-tI5JEma-Z0unrgas,416
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=ewPiWh1od6WHm-lIQ_Y6aZA1tZS0H9kQi3jORXXR28A,1353
numpy/typing/tests/data/fail/nditer.pyi,sha256=We6p5_nmfUdd_4CtwYZc5O7MTSMyM-Xw7mEUzdKPcP4,333
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=09ZF1I1xGxNZVG55MzDmClEUELXNwZluUpMtDbDmTEM,437
numpy/typing/tests/data/fail/npyio.pyi,sha256=V_rwbNzIzRv7a3ySSoaCcM1i2WWyoESwWzwr1w9C1Vw,810
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=gMj9jxXeI8nssH4B-8syNYXseb2NIkIXFHsWU7rRJJA,354
numpy/typing/tests/data/fail/random.pyi,sha256=RtCiLP7W36l5JPPWqdtr23CqJT5e1BwDm1ljWhWGa7k,2897
numpy/typing/tests/data/fail/rec.pyi,sha256=BxH41lR1wLvLrlash9mzkPFngDAXSPQQXvuHxYylHAI,721
numpy/typing/tests/data/fail/scalars.pyi,sha256=d1giLvADI9LKnu--jXPYJQTYQ7ozU5NStDxqxhf094Q,3048
numpy/typing/tests/data/fail/shape_base.pyi,sha256=ZU1KSP0k-i-npwIMUhp42-EMzrdZhOqPEnV8ah-ZJ6U,160
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=L0fJGun6CDq24yNdw2zeNVGGcIpEOyP2dmWj1pEbMz8,324
numpy/typing/tests/data/fail/testing.pyi,sha256=hqmO7nElmgg73Jhc5A0fOVsLRk3q5GCFemQuXfOg93U,1248
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=9zw1pINk3O1J0GpMBl_EMvJnjbfZR61n70o_D00sT3E,942
numpy/typing/tests/data/fail/type_check.pyi,sha256=0KG0c2LNUbUFChTYtbJ38eJUmfvUJl4Cn5G0vh1Bkrw,392
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=V5R7wY_P7KWn4IRxbLx42b1YF3wbIYzHEMr9BC41JHE,754
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=P541SlXJX7-aPTsOVw7WJNQpGYo8dqnjbtqezTlArxc,706
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=TiIj3qjjbAgNR0IahyYUGXDTA8AlSJLIKhDrfyzAHFw,1388
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=I96g0czQxXry3qxXChQ3BMwJJc79W6HXgBvYiyRmSXI,179
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=-iSPpOqauIOyCLKYKr76J9dyKMTucyh61ZoABXEBWlk,364
numpy/typing/tests/data/mypy.ini,sha256=17CYkY-Q5zscHyHfMCiApcjmAh1bUJ8XV2FckdHGI3c,149
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-39.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-39.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=qLF8vuUuB-mF10OACTW_nokszfiZ9tyy0-O7T6LNH04,7887
numpy/typing/tests/data/pass/array_constructors.py,sha256=xy_ExQXgLREMOdmwZfkU0pyDegck5uU_DVMWne_BSEc,2596
numpy/typing/tests/data/pass/array_like.py,sha256=JQu79ebM6_T9vtQls_S86WlqEfQdiqjRd5IIxTdquBo,926
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=zYz-ZNXpxjXDdo4fYWv_RQZq5af581dnaZrlu0-sSC8,1101
numpy/typing/tests/data/pass/comparisons.py,sha256=phjskmrmZ0bHTGLyNvo3mDuHCGR6I1OOj6S8ks71wRQ,3293
numpy/typing/tests/data/pass/dtype.py,sha256=7LeJ9EI_R2p15v-HmSRImS-wmWC1jwlgZykBuxBZCHg,1130
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=2xtMPvDgfhgjZIqiN3B3Wvy6Q9oBeo9uh4UkCAQNmwg,190
numpy/typing/tests/data/pass/fromnumeric.py,sha256=eM6RUBjVInsShXlblqq07-I0QwoST8n6g8WWuPSgYtA,4002
numpy/typing/tests/data/pass/index_tricks.py,sha256=Vn5iEuWlNdbr03zMEwAHvjBgI25-uCqRAJfUvRVWSp0,1556
numpy/typing/tests/data/pass/lib_utils.py,sha256=wHgHeuhfdv2cYi5_7xUv61wUnPEEmjc6xyFF6qj8wRY,445
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=wtFjvftYilKJcuehgIWJTL0yXOO72gJKZBfrw_yjNMY,1344
numpy/typing/tests/data/pass/mod.py,sha256=Intb9Ni_LlHhEka8kk81-601JCOl85jz_4_BQDA6iYI,1727
numpy/typing/tests/data/pass/modules.py,sha256=r6OC-qSjkLsR3uMpZVBahowrJp42i2t3LS7gCnuRIYo,638
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=-1iJDSvdD86k3yJCrWf1nouQrRHStf4cheiZ5OHFE78,1720
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=0DqSoVWggfBJwP36X8zigaW4qPtu2ixvyTkXsUiQlV8,2901
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/numeric.py,sha256=VCJ993mY9u8v4eNhJOJuOtc9Ics8AsAlAQtrsaTXth8,1567
numpy/typing/tests/data/pass/numerictypes.py,sha256=puK_OSekotLPXAht2Xw6Tff-BaFaTTgikL6PG-uChdI,1020
numpy/typing/tests/data/pass/random.py,sha256=uyiEBA6XOi3ctjK_HO_1rR3sdDa-V8_VP5I0ftSUAaQ,63320
numpy/typing/tests/data/pass/scalars.py,sha256=jDpxnSDTMimd-M8f4vEcFqya_QL_rnfR6r9Xe0ToH-s,3761
numpy/typing/tests/data/pass/simple.py,sha256=C8AUIF-d6G0r9o-xhZK7FIcDcdwIADFiFio6qVtDPZc,2854
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=l1JiZe3VXYLzgvbuk42Mk6fd_nvLsc9aJ233W0_MLm0,1170
numpy/typing/tests/data/pass/ufunclike.py,sha256=vhQJlqPAZegEyC882iIU0uCqoA7ijTOZP1Vij-MrgEI,1085
numpy/typing/tests/data/pass/ufuncs.py,sha256=xGuFo9gOvT04Pj7iNOhRIAIj_NHSTykIFyGV2Oj_0yA,479
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=84SQc0-Le9MIy9rC9pQ4JchXsaaRNY1sqKwxxEaMNuE,156
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=08Pu9hisSNnc8YDPDlPNK0Eyt43JBk3g1LT1Pn7ckRU,21210
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=AVWkukTV5nhH0-P22GfLWBbbKO6lb4wx9NvUsjlOroM,10190
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=4Q_Nc1cISA0e-fcEAGGQSxKUVBAcBWSCaPfs5zdkEgs,701
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=o6DS6E7YIXGlWzaFxTAbBcdiSySg13iTbROsTzKAREA,678
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=CfzXbyGl-NzFF27dTED8PGBgVdFbcXkatslHTrqbrtM,4731
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=RfN8_IrnsUwT9j9m71NuUr0YWgqAHvNbkDSkbr9O9kw,1147
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=e2OsUfoTxTdPdVZKJGeIP29jIdN01Nij1ESBsSsvQJU,3738
numpy/typing/tests/data/reveal/char.pyi,sha256=_5-Y6f-f1n3z0SiUHlTsg_9Ee3o9b-dyk40dOuBy5rU,8185
numpy/typing/tests/data/reveal/chararray.pyi,sha256=YXVN3bhbd6ePwXDhCgLslDIcF10qjO83FZvoh4BS4hg,6324
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=07azyt3l6QsqHVKmDy-dsfw9k5jhCVR2GWb13YXl1xY,7998
numpy/typing/tests/data/reveal/constants.pyi,sha256=F1YXi9jshwurwm6FcdTc1ofgHR-miMirBAwJb_rcxbw,1992
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=ERqyLcyLecN56PyLJ1nvbIhweW0z-o91BY88ETjEJ-Y,5193
numpy/typing/tests/data/reveal/datasource.pyi,sha256=K4XZlEsYAbbzpD3KkkECzLH1fjategKa4PCwF1N8o3g,578
numpy/typing/tests/data/reveal/dtype.pyi,sha256=17SSoPklHdsk-4X4RuHX9INJkkryRiQW6UET9H5epm0,2769
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=n3wzmOeLYynm9MjwOpyuugPfbbDjLI8-GtHINhm5CZE,1925
numpy/typing/tests/data/reveal/fft.pyi,sha256=A41nHw8LDzRXbhFLOQoQRN69S_cizvIGZA0t507votQ,1887
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=fc3e6mBGEpxJ6NaV3Ts_6IVP4vu7MD8xsVMhLCmlQAY,703
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=hZn03wbJcaxz5xJILNyekpB7Na0OLZbGaLuFroHW9Zk,9945
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=dL9PAvLQbeWovNaqcppC1qaTtkoikjHCwILOmwGnys4,1594
numpy/typing/tests/data/reveal/histograms.pyi,sha256=zM4XlZjWTgqEJRg6ytcSdG7_i-2DEasyMPw_CAyvznE,1410
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=TERVnmpLtO_BIRPWceYRjWndSCKIK3sUUhx6LrOX8hY,3511
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=VWIkBQ8GDKXM9ufSTma517PWIj9WaVj8E0W6q5Jcf38,9409
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=CGaEX0DcuNd7whDr_n6xKUxgSwg3xP2SQ07ctMjVwaE,6464
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=nxEEZ_LPmND57SYRxtNPXr3uvYm3XsaTcMgK2hj0z4Q,953
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=3uI1jeWbiyC2CEQ5-eqDkUZmb9871IywI6ZjetQUiUY,623
numpy/typing/tests/data/reveal/linalg.pyi,sha256=9FdN_kribCt6Pog1Zv4De1pTldTj8nlF83S4Yrk5LhQ,6486
numpy/typing/tests/data/reveal/matrix.pyi,sha256=88U9V-ZV3U603UcCSbm_pYj_AalrkE41Nz1Fqjdhs0M,3102
numpy/typing/tests/data/reveal/memmap.pyi,sha256=wcl0erC50dA3Qljlxno1rdtJKnarh_V70U2T1NjWbqQ,706
numpy/typing/tests/data/reveal/mod.pyi,sha256=irO1YSd5eF5VbFnOrYe1webn6Z-QtWtwE387SB8WHes,6136
numpy/typing/tests/data/reveal/modules.pyi,sha256=wmqhjyy7bQ_wkmyuuktHG_mTKY-ARCpmz8WNHOnkhdQ,1957
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=cuLOUXO9TWPxZdTsjaN7fqZnzy5KoleqfPKgBqy_y7s,5224
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=0n7NdwejO71wVgEx3TDGm9T9I8TRfNWU0oZDvWpRcGk,496
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=DZgkD8FQ0SGNVCoBHvDbRad2Xe_8_hLMBcTYzSWME8k,1964
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=hHZTCsUM2ir1nBhs0hCaN4ovEzRndhhTJAm1zVnmF2U,7682
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=-ITCn7JR2GFXdv8n_bJwFZCsVUCxP7BNAjtLM7ry43Y,939
numpy/typing/tests/data/reveal/nditer.pyi,sha256=hQXCNwKFQWHCBgn4OZQw1NVA23MufKMh46kKvzkzjps,2112
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=z5YPj8VTdQTU5vk9hpD0eThbEJJA7xczmKC4M2nFXMI,646
numpy/typing/tests/data/reveal/npyio.pyi,sha256=LVYOd7YXMCf8IUsKQfUVV17XaauDAQDxDKtm9v7XsCU,4559
numpy/typing/tests/data/reveal/numeric.pyi,sha256=Ozr4CWd3VlZTu5p9Gb7j0EBLbpRUQ-yg8zyP3bzRPCs,6945
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=m-7zLmnC6NFvzcgqcPN8kxix4JQmYnj87IVmgZoqYFc,1828
numpy/typing/tests/data/reveal/random.pyi,sha256=9PiAOViOdCUbRDFq9RTTQRT__05SQXQc132fFGRjtqo,130933
numpy/typing/tests/data/reveal/rec.pyi,sha256=ueitnE3YV7CW-iGP2m3ITNMgkCr8_BGAls6c4wxF5xo,3451
numpy/typing/tests/data/reveal/scalars.pyi,sha256=VIpkYUtSwqQNBV-faOUBPbyLQGocWL7DhG-LbrTaCAI,5860
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=e83ueAYSbHJsa4AV4ShYw-irEQb6XKrPnllyGDEgla4,2694
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=7C_5cZL6RCtA_7KT3DqkZvj5bkQSlgJXJDfvqdvdVhs,1593
numpy/typing/tests/data/reveal/testing.pyi,sha256=vH-ACMK_rAWfdS6kYFYu_yqbhMw25VJuZVY538TSCas,8973
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=ob0lLCvOuLWBy4mLgGopYKywwGRJkrUcWEH2NylNii4,3405
numpy/typing/tests/data/reveal/type_check.pyi,sha256=kW3dejKnprJ9C5oUTF9VzWmEc50TLiv5W3pRtOtOd0Y,3098
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=BhpX7d0FgYNw4IxJLIwWiW2Cmxg-Y2-vmnmc3o-Hbsc,1329
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=5s2EiUxwQWHSKu9Gq3pTowCZxaD_iVeOuGJujRCjVOs,1354
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=4kVZPNsH_tFX8J_86jjswqYLugfmQFTOQCY4_Iaue0w,2987
numpy/typing/tests/data/reveal/version.pyi,sha256=sDVJ6IuDtzgObpzUOzOBUzDVAvY1zKgKFe1OYratqu0,321
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=0tP2vV6PS9U1qqa8fliqrL9N0ACztzjkRIDN-XcIYWQ,456
numpy/typing/tests/test_generic_alias.py,sha256=zu5QzyHu-T1ueDshyBC8_bZTdy5nH105UZmvAiIJhdc,5534
numpy/typing/tests/test_isfile.py,sha256=KEPwqJBkXEbMRPCtSxFYULfPNipG5dyY2BRYwPI4Lu8,842
numpy/typing/tests/test_runtime.py,sha256=rlZjCEOhr7eM_FYvLmZg8zJk_ydhkYJGOxK3cC0p9sk,2533
numpy/typing/tests/test_typing.py,sha256=sYAiTCACjf6BIxpY7TCKF_rUkB4dvHRyhCp7EM3iXIk,15717
numpy/version.py,sha256=Ph0IYh9ExfFLZ1azhCwkO5pQGfNk_5ICpLaQ4Mfl3vU,490
