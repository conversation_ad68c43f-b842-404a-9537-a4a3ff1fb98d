{"cells": [{"cell_type": "markdown", "id": "e684608d", "metadata": {}, "source": ["![](./containerized_deployments.jpeg)"]}, {"cell_type": "markdown", "id": "e609fcaf", "metadata": {}, "source": ["<img src='./containerized_deployments.jpeg'></img>"]}, {"cell_type": "markdown", "id": "a0043f9e", "metadata": {}, "source": ["<div>\n", "    <img src='./containerized_deployments.jpeg'></img>\n", "</div>"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 5}