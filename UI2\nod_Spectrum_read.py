#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NOD文件读写器 - Python版本
基于C# NodFileType类转换而来
"""

import os
import pickle
import struct
import json
import re
import traceback
import csv
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import numpy as np


@dataclass
class GatherParameter:
    """采集参数类"""
    pixel_number: str = ""
    integration_time: str = ""
    laser_power: str = ""
    scan_times: str = ""
    
    def __post_init__(self):
        # 确保像素数是字符串类型以保持与C#版本一致
        if isinstance(self.pixel_number, int):
            self.pixel_number = str(self.pixel_number)


@dataclass 
class SpectrumData:
    """光谱数据类"""
    dark: Optional[np.ndarray] = None
    raw: Optional[np.ndarray] = None  
    intensity: Optional[np.ndarray] = None
    raman_shift: Optional[np.ndarray] = None
    dark_subtracted_pretreat: List = None
    is_processed: bool = False
    
    def __post_init__(self):
        if self.dark_subtracted_pretreat is None:
            self.dark_subtracted_pretreat = []


@dataclass
class ProcessedData:
    """处理后数据类"""
    intensity: Optional[np.ndarray] = None
    raw: Optional[np.ndarray] = None
    dark: Optional[np.ndarray] = None


@dataclass
class Spectrum:
    """光谱类"""
    name: str = ""
    creator: str = ""
    created: Optional[datetime] = None
    gather_parameter: Optional[GatherParameter] = None
    spectrum_data: Optional[SpectrumData] = None
    
    def __post_init__(self):
        if self.gather_parameter is None:
            self.gather_parameter = GatherParameter()
        if self.spectrum_data is None:
            self.spectrum_data = SpectrumData()


class NodFileReader:
    """NOD文件读取器"""
    
    def __init__(self, delimiter=';'):
        self.delimiter = delimiter
        
    def read_file(self, file_path: str) -> Optional[Spectrum]:
        """
        读取NOD文件
        Args:
            file_path: 文件路径
        Returns:
            Spectrum对象或None
        """
        try:
            # print(f"正在读取文件: {file_path}")
            
            # 尝试读取二进制序列化文件
            rows = self._read_binary_file(file_path)
            

            
            if not rows:
                # print("文件读取失败或文件为空")
                return None
                
            return self._parse_nod_data(rows)

            
        except Exception as e:
            # print(f"读取文件异常: {e}")
            return None
    
    def _read_binary_file(self, file_path: str) -> Optional[List[bytes]]:
        """读取二进制序列化文件（C# BinaryFormatter格式）"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                
            # print(f"文件大小: {len(content)} 字节")
            
            # 检查是否是C# BinaryFormatter序列化文件
            if len(content) < 20:
                return None
                
            # 查找字符串数据开始位置
            # C# BinaryFormatter会在序列化的List<byte[]>中存储UTF-16编码的字符串
            lines = []
            pos = 0
            
            # 跳过BinaryFormatter头部信息
            # 寻找UTF-16编码的字符串数据
            while pos < len(content) - 1:
                # 查找可能的UTF-16字符串
                if pos < len(content) - 50:  # 确保有足够的数据
                    # 尝试找到字符串长度前缀（LEB128编码）
                    str_len = self._read_leb128_length(content, pos)
                    if str_len > 0 and str_len < 10000:  # 合理的字符串长度
                        try:
                            # 读取UTF-16编码的字符串
                            start_pos = pos + self._get_leb128_size(str_len)
                            if start_pos + str_len * 2 <= len(content):
                                string_bytes = content[start_pos:start_pos + str_len * 2]
                                # 验证是否为有效的UTF-16字符串
                                text = string_bytes.decode('utf-16le')
                                if self._is_valid_nod_line(text):
                                    lines.append(string_bytes)
                                    pos = start_pos + str_len * 2
                                    continue
                        except:
                            pass
                
                pos += 1
            
            if lines:
                # print(f"从BinaryFormatter中解析出 {len(lines)} 行数据")
                return lines
            
            # 如果上述方法失败，尝试简单的字符串搜索
            #return self._fallback_string_extraction(content)
            
        except Exception as e:
            # print(f"读取二进制文件异常: {e}")
            return None
    
    def _read_leb128_length(self, data: bytes, pos: int) -> int:
        """读取LEB128编码的长度"""
        try:
            if pos >= len(data):
                return 0
            
            length = 0
            shift = 0
            while pos < len(data):
                byte = data[pos]
                length |= (byte & 0x7F) << shift
                pos += 1
                if (byte & 0x80) == 0:
                    break
                shift += 7
                if shift > 28:  # 防止无限循环
                    return 0
            return length
        except:
            return 0
    
    def _get_leb128_size(self, value: int) -> int:
        """获取LEB128编码的字节数"""
        if value == 0:
            return 1
        size = 0
        while value > 0:
            value >>= 7
            size += 1
        return size
    
    def _is_valid_nod_line(self, text: str) -> bool:
        """检查是否是有效的NOD文件行"""
        if not text or len(text) < 3:
            return False
        
        # 检查是否包含NOD文件的常见内容
        nod_indicators = [
            'Data Generated by Neodots',
            'File Version',
            '扫描时间', '检测时间', '操作员', '样品名称',
            'Raman Shift', 'Intensity', 'Pixel',
            '像素数', '积分时间', '激光功率', '扫描次数'
        ]
        
        for indicator in nod_indicators:
            if indicator in text:
                return True
        
        # 检查是否是数值行（拉曼位移和强度数据）
        parts = text.split(';')
        if len(parts) >= 2:
            try:
                float(parts[0])
                float(parts[1])
                return True
            except:
                pass
        
        return False
    

    def _parse_nod_data(self, rows: List[bytes]) -> Optional[Spectrum]:
        """解析NOD数据"""
        try:
            # 将字节数据转换为字符串并分离混合的行
            all_text_lines = []
            
            for i, row in enumerate(rows):
                try:
                    if isinstance(row, bytes):
                        # 解码Unicode字节
                        text = self._decode_unicode_bytes(row)
                        if text and text.strip():
                            # 分离可能混合在一起的多行数据
                            separated_lines = self._separate_mixed_lines(text)
                            all_text_lines.extend(separated_lines)
                            # print(f"行 {i+1} 解析出 {len(separated_lines)} 个子行")
                    else:
                        all_text_lines.append(str(row).strip())
                except Exception as e:
                    # print(f"解析行 {i+1} 失败: {e}")
                    continue
            
            if not all_text_lines:
                # print("无法解析行数据")
                return None
            
            # print(f"总共解析出 {len(all_text_lines)} 行数据")
            
            # 分离头部和数据部分
            head_data = []
            spectrum_data = []
            data_start_index = -1
            
            for i, line in enumerate(all_text_lines):
                clean_line = line.strip()
                if not clean_line:
                    continue
                    
                # print(f"处理行 {i+1}: '{clean_line[:50]}...'")
                
                line_parts = clean_line.split(self.delimiter)
                
                if len(line_parts) >= 2:
                    # 先清理字符串，再检查是否是数据开始行
                    first_part = self._clean_string(line_parts[0].strip())
                    second_part = self._clean_string(line_parts[1].strip())
                    
                    # 检查是否是数据开始行
                    if ((first_part == "Pixel" and second_part == "Raman Shift") or 
                        (first_part == "Raman Shift" and second_part == "Intensity") or
                        ("Raman Shift" in first_part and "Intensity" in second_part)):
                        # print(f"找到数据开始行: {i+1}")
                        data_start_index = i
                        break
                    
                    # 检查是否是数值数据行（即使还没找到标题行）
                    try:
                        float(first_part)
                        float(second_part)
                        # 如果能成功转换为浮点数，说明这是数据行
                        # print(f"发现数值数据行: {i+1}, 设置为数据开始")
                        data_start_index = i - 1  # 前一行应该是标题行
                        break
                    except ValueError:
                        # 不是数值数据，继续处理为头部数据
                        if len([first_part, second_part]) >= 2:
                            head_data.append([first_part, second_part])
            
            # print(f"头部数据行数: {len(head_data)}")
            # for i, head_row in enumerate(head_data):
            #     print(f"  头部 {i+1}: {head_row}")
            
            # 提取光谱数据
            if data_start_index >= 0:
                # print(f"从第 {data_start_index + 2} 行开始提取光谱数据")
                for i in range(data_start_index + 1, len(all_text_lines)):
                    line_parts = all_text_lines[i].split(self.delimiter)
                    if len(line_parts) >= 2:
                        # 清理数值数据
                        cleaned_parts = []
                        for part in line_parts:
                            clean_part = self._clean_string(part)
                            if clean_part:
                                cleaned_parts.append(clean_part)
                        
                        # 检查是否是有效的数值数据
                        if len(cleaned_parts) >= 2:
                            try:
                                # 尝试转换为浮点数，验证是否是有效的数值行
                                float(cleaned_parts[0])
                                float(cleaned_parts[1])
                                spectrum_data.append(cleaned_parts)
                            except ValueError:
                                # 如果不能转换为数值，跳过这一行
                                # print(f"跳过非数值行 {i+1}: {cleaned_parts}")
                                continue
                        
                # print(f"光谱数据行数: {len(spectrum_data)}")
                # if spectrum_data:
                #     print(f"  第一行光谱数据: {spectrum_data[0]}")
                #     if len(spectrum_data) > 1:
                #         print(f"  最后一行光谱数据: {spectrum_data[-1]}")
            
            if not head_data and not spectrum_data:
                # print("未找到有效的头部数据或光谱数据")
                return None
                
            # 创建光谱对象
            spectrum = self._create_spectrum_from_data(head_data, spectrum_data)
            return spectrum
            
        except Exception as e:
            # print(f"解析数据异常: {e}")
            # import traceback
            # traceback.print_exc()
            return None
    
    def _separate_mixed_lines(self, text: str) -> List[str]:
        """分离混合在一起的多行数据"""
        lines = []
        
        # 首先尝试按照NULL字符分离
        parts = text.split('\x00')
        
        for part in parts:
            clean_part = part.strip()
            if clean_part and len(clean_part) > 1:
                # 检查是否包含分号分隔的键值对
                if self.delimiter in clean_part:
                    lines.append(clean_part)
                elif any(indicator in clean_part for indicator in ['标题', '操作员', '描述', '扫描时间', '积分时间', '激光功率', 'Data Generated', 'File Version']):
                    lines.append(clean_part)
        
        # 如果上述方法没有找到足够的行，尝试其他分离方法
        if len(lines) < 2:
            # 尝试按照特定字符模式分离
            import re
            # 查找中文关键字作为分离点
            pattern = r'(标题|操作员|描述|扫描时间|积分时间|激光功率|扫描次数|采集模式|采样间隔|设备型号|像素个数|设备序列号|Raman Shift)'
            matches = list(re.finditer(pattern, text))
            
            if len(matches) > 1:
                for i in range(len(matches)):
                    start = matches[i].start()
                    end = matches[i+1].start() if i+1 < len(matches) else len(text)
                    segment = text[start:end].strip()
                    if segment and self.delimiter in segment:
                        lines.append(segment)
        
        return lines if lines else [text]
    
    def _clean_string(self, s: str) -> str:
        """清理字符串中的控制字符和无效字符"""
        if not s:
            return ""
        
        # 移除控制字符，保留中文、英文、数字、常用标点符号、小数点
        import re
        # 首先移除字符串开头的特殊前缀字符（如Ȁ）
        cleaned = re.sub(r'^[^\u4e00-\u9fff\u0020-\u007e]+', '', s)
        
        # 保留可打印字符、中文字符、数字、小数点、负号
        cleaned = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u3000-\u303f\uff00-\uffef\.\-0-9]', '', cleaned)
        
        return cleaned.strip()
    
    def _decode_unicode_bytes(self, data: bytes) -> str:
        """解码Unicode字节数据"""
        try:
            # 尝试UTF-16LE解码（与C#的Unicode.GetString对应）
            if len(data) >= 2:
                return data.decode('utf-16le')
        except:
            pass
        
        try:
            # 尝试UTF-16BE解码
            return data.decode('utf-16be')
        except:
            pass
            
        try:
            # 尝试UTF-8解码
            return data.decode('utf-8')
        except:
            pass
            
        try:
            # 尝试GB系列编码
            return data.decode('gbk')
        except:
            pass
        
        # 如果都失败了，返回原始字符串表示
        return str(data, errors='ignore')
    
    def _create_spectrum_from_data(self, head_data: List[List[str]], 
                                   spectrum_data: List[List[str]]) -> Spectrum:
        """从头部和数据创建光谱对象"""
        spectrum = Spectrum()
        
        # 解析头部数据
        for row in head_data:
            if len(row) >= 2:
                key = row[0].strip()
                value = row[1].strip() if len(row) > 1 else ""
                
                # 清理键名（移除特殊前缀字符）
                clean_key = self._clean_key(key)
                
                # print(f"解析字段: '{key}' -> '{clean_key}' = '{value}'")
                
                # 解析常用字段
                if clean_key in ["扫描时间", "检测时间"]:
                    try:
                        spectrum.created = datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                        # print(f"设置扫描时间: {spectrum.created}")
                    except:
                        try:
                            spectrum.created = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                            # print(f"设置扫描时间: {spectrum.created}")
                        except Exception as e:
                            # print(f"时间解析失败: {e}")
                            pass
                elif clean_key == "操作员":
                    spectrum.creator = value
                    # print(f"设置操作员: {spectrum.creator}")
                elif clean_key in ["文件名", "样品名称", "标题"]:
                    spectrum.name = value
                    # print(f"设置样品名称: {spectrum.name}")
                elif clean_key in ["像素数", "像素个数"]:
                    spectrum.gather_parameter.pixel_number = value
                    # print(f"设置像素数: {spectrum.gather_parameter.pixel_number}")
                elif clean_key.startswith("积分时间"):
                    spectrum.gather_parameter.integration_time = value
                    # print(f"设置积分时间: {spectrum.gather_parameter.integration_time}")
                elif clean_key.startswith("激光功率"):
                    spectrum.gather_parameter.laser_power = value
                    # print(f"设置激光功率: {spectrum.gather_parameter.laser_power}")
                elif clean_key.startswith("激光") or "laser" in clean_key.lower() or "power" in clean_key.lower():
                    spectrum.gather_parameter.laser_power = value
                    # print(f"设置激光功率(通用): {spectrum.gather_parameter.laser_power}")
                elif clean_key in ["扫描次数", "数", "平均次数"]:
                    spectrum.gather_parameter.scan_times = value
                    # print(f"设置扫描次数: {spectrum.gather_parameter.scan_times}")
                
                # 将所有字段都保存到一个字典中，便于后续显示
                if not hasattr(spectrum, 'all_metadata'):
                    spectrum.all_metadata = {}
                spectrum.all_metadata[clean_key] = value
        
        # 解析光谱数据
        if spectrum_data:
            self._parse_spectrum_data(spectrum, spectrum_data)
        
        # print(f"成功创建光谱对象: {spectrum.name}")
        return spectrum
    
    def _clean_key(self, key: str) -> str:
        """清理字段键名"""
        if not key:
            return ""
        
        # 移除特殊前缀字符（如Ȁ等）
        import re
        cleaned = re.sub(r'^[^\u4e00-\u9fff\u0020-\u007e]+', '', key)
        return cleaned.strip()
    
    def _parse_spectrum_data(self, spectrum: Spectrum, data_rows: List[List[str]]):
        """解析光谱数据部分"""
        try:
            if not data_rows:
                return
            
            # 确定像素数
            pixel_count = len(data_rows)
            if spectrum.gather_parameter.pixel_number:
                try:
                    pixel_count = int(spectrum.gather_parameter.pixel_number)
                except:
                    pass
            
            # 初始化数组
            spectrum.spectrum_data.dark = np.zeros(pixel_count)
            spectrum.spectrum_data.raw = np.zeros(pixel_count)
            spectrum.spectrum_data.intensity = np.zeros(pixel_count)
            spectrum.spectrum_data.raman_shift = np.zeros(pixel_count)
            
            # 判断数据格式
            has_background = len(data_rows[0]) > 2 if data_rows else False
            
            for i, row in enumerate(data_rows):
                if i >= pixel_count:
                    break
                    
                try:
                    if has_background and len(row) >= 4:
                        # 含背景数据格式: [pixel, raman_shift, raw, dark, intensity]
                        spectrum.spectrum_data.raman_shift[i] = float(row[1])
                        spectrum.spectrum_data.raw[i] = float(row[2])
                        spectrum.spectrum_data.dark[i] = float(row[3])
                        if len(row) > 4 and row[4].strip():
                            spectrum.spectrum_data.intensity[i] = float(row[4])
                    else:
                        # 不含背景数据格式: [raman_shift, intensity]
                        if len(row) >= 2:
                            spectrum.spectrum_data.raman_shift[i] = float(row[0])
                            spectrum.spectrum_data.intensity[i] = float(row[1])
                            
                except (ValueError, IndexError) as e:
                    print(f"解析第{i}行数据失败: {e}")
                    continue
            
            print(f"成功解析 {pixel_count} 个数据点")
            
        except Exception as e:
            print(f"解析光谱数据异常: {e}")
    
   
    def save_spectrum_to_csv(self, spectrum: Spectrum, output_dir: str = "exported_data") -> Optional[str]:
        """
        将光谱数据保存为CSV文件
        Args:
            spectrum: 光谱对象
            output_dir: 输出目录
        Returns:
            保存的文件路径或None
        """
        try:
            if not spectrum or not spectrum.spectrum_data:
                print("❌ 无有效光谱数据可保存")
                return None
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            safe_name = re.sub(r'[^\w\-_\.]', '_', spectrum.name) if spectrum.name else "spectrum"
            csv_filename = f"{safe_name}_spectrum.csv"
            csv_filepath = os.path.join(output_dir, csv_filename)
            
            # 写入CSV文件
            with open(csv_filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入头部信息
                writer.writerow(['# NOD文件光谱数据导出'])
                writer.writerow(['# 文件信息'])
                writer.writerow(['样品名称', spectrum.name])
                writer.writerow(['操作员', spectrum.creator])
                writer.writerow(['创建时间', str(spectrum.created) if spectrum.created else ''])
                
                # 写入所有元数据
                if hasattr(spectrum, 'all_metadata'):
                    for key, value in spectrum.all_metadata.items():
                        if key and value and key not in ['样品名称', '标题', '操作员', '扫描时间']:
                            writer.writerow([key, value])
                
                # 写入核心参数
                writer.writerow(['像素数', spectrum.gather_parameter.pixel_number])
                writer.writerow(['积分时间(ms)', spectrum.gather_parameter.integration_time])
                writer.writerow(['激光功率', spectrum.gather_parameter.laser_power])
                writer.writerow(['扫描次数', spectrum.gather_parameter.scan_times])
                writer.writerow([])  # 空行分隔
                
                # 写入光谱数据表头
                if spectrum.spectrum_data.raman_shift is not None and spectrum.spectrum_data.intensity is not None:
                    writer.writerow(['序号', '拉曼位移(cm-1)', '强度', '原始数据', '暗电流'])
                    
                    # 写入光谱数据
                    data_length = len(spectrum.spectrum_data.raman_shift)
                    for i in range(data_length):
                        row = [
                            i + 1,
                            f"{spectrum.spectrum_data.raman_shift[i]:.4f}",
                            f"{spectrum.spectrum_data.intensity[i]:.4f}",
                            f"{spectrum.spectrum_data.raw[i]:.4f}" if spectrum.spectrum_data.raw is not None else "",
                            f"{spectrum.spectrum_data.dark[i]:.4f}" if spectrum.spectrum_data.dark is not None else ""
                        ]
                        writer.writerow(row)
            
            print(f"✅ CSV文件已保存: {csv_filepath}")
            return csv_filepath
            
        except Exception as e:
            print(f"❌ 保存CSV文件失败: {e}")
            return None
    
    def save_spectrum_to_txt(self, spectrum: Spectrum, output_dir: str = "exported_data") -> Optional[str]:
        """
        将光谱数据保存为TXT文件
        Args:
            spectrum: 光谱对象
            output_dir: 输出目录
        Returns:
            保存的文件路径或None
        """
        try:
            if not spectrum or not spectrum.spectrum_data:
                print("❌ 无有效光谱数据可保存")
                return None
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            safe_name = re.sub(r'[^\w\-_\.]', '_', spectrum.name) if spectrum.name else "spectrum"
            txt_filename = f"{safe_name}_data.txt"
            txt_filepath = os.path.join(output_dir, txt_filename)
            
            # 写入TXT文件
            with open(txt_filepath, 'w', encoding='utf-8') as txtfile:
                # 写入头部信息
                txtfile.write("NOD文件光谱数据导出\n")
                txtfile.write("=" * 50 + "\n\n")
                
                txtfile.write("📋 文件信息:\n")
                txtfile.write("-" * 30 + "\n")
                txtfile.write(f"📝 样品名称: {spectrum.name}\n")
                txtfile.write(f"👤 操作员: {spectrum.creator}\n")
                txtfile.write(f"📅 创建时间: {spectrum.created if spectrum.created else '未知'}\n")
                
                # 写入所有元数据
                if hasattr(spectrum, 'all_metadata'):
                    for key, value in spectrum.all_metadata.items():
                        if key and value and key not in ['样品名称', '标题', '操作员', '扫描时间']:
                            txtfile.write(f"📄 {key}: {value}\n")
                
                txtfile.write(f"🔢 像素数: {spectrum.gather_parameter.pixel_number}\n")
                txtfile.write(f"⏱️ 积分时间: {spectrum.gather_parameter.integration_time}\n")
                txtfile.write(f"💡 激光功率: {spectrum.gather_parameter.laser_power}\n")
                txtfile.write(f"🔄 扫描次数: {spectrum.gather_parameter.scan_times}\n\n")
                
                # 写入光谱数据统计
                if spectrum.spectrum_data.raman_shift is not None and spectrum.spectrum_data.intensity is not None:
                    raman_data = spectrum.spectrum_data.raman_shift
                    intensity_data = spectrum.spectrum_data.intensity
                    data_points = len(raman_data)
                    
                    txtfile.write("📊 光谱数据统计:\n")
                    txtfile.write("-" * 30 + "\n")
                    txtfile.write(f"数据点总数: {data_points}\n")
                    txtfile.write(f"拉曼位移范围: {np.min(raman_data):.2f} - {np.max(raman_data):.2f} cm-1\n")
                    txtfile.write(f"强度范围: {np.min(intensity_data):.2f} - {np.max(intensity_data):.2f}\n\n")
                    
                    # 写入光谱数据
                    txtfile.write("📈 完整光谱数据:\n")
                    txtfile.write("-" * 50 + "\n")
                    txtfile.write(f"{'序号':<6} {'拉曼位移(cm-1)':<15} {'强度':<12} {'原始数据':<12} {'暗电流':<12}\n")
                    txtfile.write("-" * 70 + "\n")
                    
                    for i in range(data_points):
                        raw_val = spectrum.spectrum_data.raw[i] if spectrum.spectrum_data.raw is not None else 0
                        dark_val = spectrum.spectrum_data.dark[i] if spectrum.spectrum_data.dark is not None else 0
                        
                        txtfile.write(f"{i+1:<6} {raman_data[i]:<15.4f} {intensity_data[i]:<12.4f} "
                                     f"{raw_val:<12.4f} {dark_val:<12.4f}\n")
            
            print(f"✅ TXT文件已保存: {txt_filepath}")
            return txt_filepath
            
        except Exception as e:
            print(f"❌ 保存TXT文件失败: {e}")
            return None
    
    def export_all_formats(self, spectrum: Spectrum, output_dir: str = "exported_data") -> Dict[str, Optional[str]]:
        """
        将光谱数据导出为所有支持的格式
        Args:
            spectrum: 光谱对象
            output_dir: 输出目录
        Returns:
            包含各格式文件路径的字典
        """
        results = {
            'csv': None,
            'txt': None
        }
        
        if not spectrum:
            print("❌ 无有效光谱数据可导出")
            return results
        
        print(f"\n📁 开始导出光谱数据到: {output_dir}")
        print("-" * 50)
        
        # 导出CSV格式
        results['csv'] = self.save_spectrum_to_csv(spectrum, output_dir)
        
        # 导出TXT格式
        results['txt'] = self.save_spectrum_to_txt(spectrum, output_dir)
        
        # 显示导出摘要
        successful_exports = [fmt for fmt, path in results.items() if path is not None]
        if successful_exports:
            print(f"\n✅ 成功导出 {len(successful_exports)} 种格式: {', '.join(successful_exports).upper()}")
        else:
            print("\n❌ 导出失败")
        
        return results

    def get_formatted_metadata(self, spectrum: Spectrum) -> Dict[str, str]:
    #     """获取格式化的元数据信息"""
        metadata = {}
        
        # 基本信息
        metadata["样品名称"] = spectrum.name or "0"
        metadata["操作员"] = spectrum.creator or "0"
        metadata["创建时间"] = str(spectrum.created) if spectrum.created else "0"
        
        # 从all_metadata中获取信息
        if hasattr(spectrum, 'all_metadata'):
            metadata["文件版本"] = spectrum.all_metadata.get("File Version", "0")
            metadata["采集模式"] = spectrum.all_metadata.get("采集模式", "0")
            metadata["采样间隔"] = spectrum.all_metadata.get("采样间隔", "0")
            metadata["设备型号"] = spectrum.all_metadata.get("设备型号", "0")
            metadata["设备序列号"] = spectrum.all_metadata.get("设备序列号", "0")
        else:
            metadata["文件版本"] = "0"
            metadata["采集模式"] = "0"
            metadata["采样间隔"] = "0"
            metadata["设备型号"] = "0"
            metadata["设备序列号"] = "0"
        
        # 测量参数
        metadata["像素数"] = spectrum.gather_parameter.pixel_number or "0"
        metadata["积分时间"] = f"{spectrum.gather_parameter.integration_time}" if spectrum.gather_parameter.integration_time else "0"
        
        # 激光功率 - 尝试从多个源获取
        laser_power = spectrum.gather_parameter.laser_power
        if not laser_power and hasattr(spectrum, 'all_metadata'):
            # 尝试从all_metadata中查找激光功率相关字段
            for key, value in spectrum.all_metadata.items():
                if "激光" in key or "laser" in key.lower() or "power" in key.lower():
                    laser_power = value
                    break
        
        # 如果还是没有找到，根据设备版本设置默认值
        if not laser_power:
            if metadata["设备型号"] and "1.0" in metadata["设备型号"]:
                laser_power = "0"  # NeoLily 1.0 默认功率
            elif metadata["设备型号"] and "4.0" in metadata["设备型号"]:
                laser_power = "0"  # NeoLily 4.0 默认功率
            else:
                laser_power = "0"
        elif not "mW" in laser_power and not "W" in laser_power:
            laser_power = f"{laser_power} "
            
        metadata["激光功率"] = laser_power
        metadata["扫描次数"] = spectrum.gather_parameter.scan_times or "0"
        
        return metadata

    # ...existing code...


def test_nod_reader():
    """测试NOD文件读取器"""
    print("=== NOD文件读取器测试 ===")
    
    # 测试文件路径
    test_files = [
        r"E:\Desktop\view1-9\nod\H12纯化水.nod"
    ]
    
    reader = NodFileReader()
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"\n--- 测试文件: {os.path.basename(file_path)} ---")
            spectrum = reader.read_file(file_path)
            
            if spectrum:
                print("✓ 文件读取成功!")
                
                # 使用格式化的元数据显示
                formatted_metadata = reader.get_formatted_metadata(spectrum)
                
                print("\n📋 完整NOD文件信息:")
                print("-" * 50)
                print(f"📝 样品名称: {formatted_metadata['样品名称']}")
                print(f"� 操作员: {formatted_metadata['操作员']}")
                print(f"📅 创建时间: {formatted_metadata['创建时间']}")
                print(f"📄 文件版本: {formatted_metadata['文件版本']}")
                print(f"🔢 像素数: {formatted_metadata['像素数']}")
                print(f"⏱️ 积分时间: {formatted_metadata['积分时间']}")
                print(f"💡 激光功率: {formatted_metadata['激光功率']}")
                print(f"� 扫描次数: {formatted_metadata['扫描次数']}")
                print(f"🎯 采集模式: {formatted_metadata['采集模式']}")
                print(f"📏 采样间隔: {formatted_metadata['采样间隔']}")
                print(f"🔧 设备型号: {formatted_metadata['设备型号']}")
                print(f"🏷️ 设备序列号: {formatted_metadata['设备序列号']}")
                
                if spectrum.spectrum_data:
                    if spectrum.spectrum_data.raman_shift is not None:
                        print(f"拉曼位移范围: {np.min(spectrum.spectrum_data.raman_shift):.2f} - {np.max(spectrum.spectrum_data.raman_shift):.2f} cm-1")
                    if spectrum.spectrum_data.intensity is not None:
                        print(f"强度范围: {np.min(spectrum.spectrum_data.intensity):.2f} - {np.max(spectrum.spectrum_data.intensity):.2f}")
                        print(f"数据点数: {len(spectrum.spectrum_data.intensity)}")
                        
                        # 显示前5个数据点
                        print("前5个数据点:")
                        for i in range(min(5, len(spectrum.spectrum_data.intensity))):
                            shift = spectrum.spectrum_data.raman_shift[i] if spectrum.spectrum_data.raman_shift is not None else 0
                            intensity = spectrum.spectrum_data.intensity[i]
                            print(f"  {i+1}: 拉曼位移={shift:.2f}, 强度={intensity:.2f}")
                
                # 自动导出数据到CSV和TXT格式
                print("\n🚀 开始导出数据...")
                export_results = reader.export_all_formats(spectrum)
                
                # 显示导出结果
                if export_results['csv']:
                    print(f"📊 CSV文件已保存到: {export_results['csv']}")
                if export_results['txt']:
                    print(f"📄 TXT文件已保存到: {export_results['txt']}")
            else:
                print("✗ 文件读取失败")
        else:
            print(f"文件不存在: {file_path}")


# 添加一个新的测试函数来创建可视化输出


if __name__ == "__main__":
    test_nod_reader()
    # 创建简洁的总结报告
    
