# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_dynamic_libs, collect_submodules

# 获取项目根目录
project_root = os.path.abspath('.')
ui2_dir = os.path.join(project_root, 'UI2')
spectrumeter_dir = os.path.join(project_root, 'spectrumeter')

# 收集所有主要库的动态库和数据文件
print("正在收集动态库...")
pandas_binaries = collect_dynamic_libs('pandas')
numpy_binaries = collect_dynamic_libs('numpy')
scipy_binaries = collect_dynamic_libs('scipy')
matplotlib_binaries = collect_dynamic_libs('matplotlib')
cryptography_binaries = collect_dynamic_libs('cryptography')

print("正在收集数据文件...")
pandas_datas = collect_data_files('pandas')
numpy_datas = collect_data_files('numpy')
scipy_datas = collect_data_files('scipy')
matplotlib_datas = collect_data_files('matplotlib')
pyqt6_datas = collect_data_files('PyQt6')

# 数据文件列表
datas = [
    # UI2目录下的资源文件
    (os.path.join(ui2_dir, 'logo48.ico'), '.'),
    (os.path.join(ui2_dir, 'software_settings.json'), '.'),
    (os.path.join(ui2_dir, 'users.db'), '.'),
    
    # spectrumeter目录下的DLL文件
    (os.path.join(spectrumeter_dir, 'UserApplication.dll'), '.'),
    (os.path.join(spectrumeter_dir, 'SiUSBXp.dll'), '.'),
    (os.path.join(spectrumeter_dir, 'UserApplication.lib'), '.'),
    
    # spectrumeter目录下的Python文件
    (os.path.join(spectrumeter_dir, 'laser_controller_usb.py'), '.'),
    (os.path.join(spectrumeter_dir, 'raman_spectrometer.py'), '.'),
    (os.path.join(spectrumeter_dir, 'PythonExample_OtOGeneral.py'), '.'),
]

# 添加收集到的数据文件
datas.extend(pandas_datas)
datas.extend(numpy_datas)
datas.extend(scipy_datas)
datas.extend(matplotlib_datas)
datas.extend(pyqt6_datas)

# 二进制文件 - 包含所有收集到的动态库
binaries = []
binaries.extend(pandas_binaries)
binaries.extend(numpy_binaries)
binaries.extend(scipy_binaries)
binaries.extend(matplotlib_binaries)
binaries.extend(cryptography_binaries)

# 手动添加关键的DLL文件
binaries.append((os.path.join(spectrumeter_dir, 'UserApplication.dll'), '.'))
binaries.append((os.path.join(spectrumeter_dir, 'SiUSBXp.dll'), '.'))

# 收集所有子模块
print("正在收集子模块...")
pandas_submodules = collect_submodules('pandas')
numpy_submodules = collect_submodules('numpy')
scipy_submodules = collect_submodules('scipy')
matplotlib_submodules = collect_submodules('matplotlib')
pyqt6_submodules = collect_submodules('PyQt6')

# 隐藏导入模块 - 包含所有必要的模块
hiddenimports = [
    # PyQt6相关
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtPrintSupport',
    'PyQt6.sip',
    
    # matplotlib相关
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_qtagg',
    'matplotlib.backends.backend_agg',
    'matplotlib.backends.backend_tkagg',
    
    # 科学计算库
    'numpy',
    'scipy',
    'scipy.signal',
    'scipy.sparse',
    'scipy.sparse.linalg',
    'scipy.linalg',
    'scipy.special',
    'scipy.stats',
    'scipy.optimize',
    'scipy.interpolate',
    'scipy.integrate',
    
    # pandas相关 - 包含所有可能的子模块
    'pandas',
    'pandas._libs',
    'pandas._libs.window',
    'pandas._libs.window.aggregations',
    'pandas._libs.window.indexers',
    'pandas._libs.reduction',
    'pandas._libs.algos',
    'pandas._libs.hashtable',
    'pandas._libs.lib',
    'pandas._libs.tslib',
    'pandas._libs.interval',
    'pandas._libs.join',
    'pandas._libs.index',
    'pandas._libs.sparse',
    'pandas._libs.ops',
    'pandas._libs.parsers',
    'pandas._libs.writers',
    'pandas._libs.groupby',
    'pandas._libs.reshape',
    'pandas._libs.internals',
    'pandas._libs.arrays',
    'pandas.core',
    'pandas.core.window',
    'pandas.core.window.aggregations',
    'pandas.core.groupby',
    'pandas.io',
    'pandas.plotting',
    
    # 其他重要库
    'cryptography',
    'requests',
    'sqlite3',
    'pickle',
    'json',
    'csv',
    'ctypes',
    'threading',
    'datetime',
    'glob',
    'traceback',
    'atexit',
    'tempfile',
    'os',
    'sys',
    're',
    'time',
    'struct',
    'dataclasses',
    'typing',
    
    # 项目内部模块
    'language_manager',
    'log_manager', 
    'menu_bar',
    'sidebar',
    'editor',
    'status_bar',
    'login_dialog',
    'password_dialog',
    'db_user_manager',
    'integrated_measurement_logic',
    'report_dialog',
    'save_files_dialog',
    'spectrum_file_reader',
    'nod_Spectrum_read',
    'nod_file_serializer',
    'nod_raman_read',
    
    # spectrumeter模块
    'laser_controller_usb',
    'raman_spectrometer',
    'PythonExample_OtOGeneral',
]

# 添加收集到的子模块
hiddenimports.extend(pandas_submodules)
hiddenimports.extend(numpy_submodules)
hiddenimports.extend(scipy_submodules)
hiddenimports.extend(matplotlib_submodules)
hiddenimports.extend(pyqt6_submodules)

# 只排除PyQt5，保留其他所有环境
excludes = [
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'test',
    'unittest',
    'pdb',
    'doctest',
]

a = Analysis(
    [os.path.join(ui2_dir, 'main.py')],
    pathex=[ui2_dir, spectrumeter_dir],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='view21',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(ui2_dir, 'logo48.ico'),
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='view21_complete'
)
