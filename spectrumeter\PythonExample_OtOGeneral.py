import sys
from ctypes import *
import os.path

dll_name = "lib_linux_x64.so" if sys.platform == "linux" else "UserApplication.dll"
dllabspath = os.path.dirname(os.path.abspath(__file__)) + os.path.sep + dll_name
dll = CDLL(dllabspath)

if sys.platform != "win32":
    dll.UAI_SpectrometerUsbInit()

#This function gets usb connecting handle and information from spectrometer
def openDevice():
    hand = c_void_p(0)
    num = c_int(0)
    typelist = c_int(0)
    dll.UAI_SpectrometerGetDeviceList(pointer(typelist) , None)
    print("support number of Device Type = ", typelist.value)
    VIDPID = (c_int * (typelist.value * 2))()
    dll.UAI_SpectrometerGetDeviceList(pointer(typelist) , pointer(VIDPID))

    print("check num of devices...")

    for i in  range(typelist.value):
        print("Device Type_" + str(i) + ": VID(" + str(VIDPID[i*2]) + ") PID(" + str(VIDPID[(i*2)+1]) + ")")

    print("connect connecting device...")
    for i in  range(typelist.value):
        errorcode = dll.DLI_SpectrometerGetDeviceAmount(VIDPID[i*2], VIDPID[(i*2)+1], pointer(num))
        if(num.value > 0):
            print("VID(" + str(VIDPID[i*2]) + ") PID(" + str(VIDPID[(i*2)+1]) + ") device connected number = ", num.value)
        else:
            print("VID(" + str(VIDPID[i*2]) + ") PID(" + str(VIDPID[(i*2)+1]) + ") no device!!", errorcode, num.value)
            continue

        n = int(num.value)
        b = range(n)
        pb = (c_int * n)(*b)

        errorcode = dll.UAI_SpectrometerOpen(c_int(0),pointer(hand), VIDPID[i*2], VIDPID[(i*2)+1])
        if(errorcode != 0):print("errorcode = ", errorcode)
        break
    
    if(num.value ==0):
        sys.exit
    
    return hand

#This function just gets USB connecting handle , it does not get information from spectrometer
def getUSBHandleOnly():
    hand = c_void_p(0)
    num = c_int(0)
    typelist = c_int(0)
    dll.UAI_SpectrometerGetDeviceList(pointer(typelist) , None)
    print("support number of Device Type = ", typelist.value)
    VIDPID = (c_int * (typelist.value * 2))()
    dll.UAI_SpectrometerGetDeviceList(pointer(typelist) , pointer(VIDPID))

    print("check num of devices...")

    for i in  range(typelist.value):
        print("Device Type_" + str(i) + ": VID(" + str(VIDPID[i*2]) + ") PID(" + str(VIDPID[(i*2)+1]) + ")")

    print("connect connecting device...")
    for i in  range(typelist.value):
        errorcode = dll.DLI_SpectrometerGetDeviceAmount(VIDPID[i*2], VIDPID[(i*2)+1], pointer(num))
        if(num.value > 0):
            print("VID(" + str(VIDPID[i*2]) + ") PID(" + str(VIDPID[(i*2)+1]) + ") device connected number = ", num.value)
        else:
            print("VID(" + str(VIDPID[i*2]) + ") PID(" + str(VIDPID[(i*2)+1]) + ") no device!!", errorcode, num.value)
            continue

        n = int(num.value)
        b = range(n)
        pb = (c_int * n)(*b)

        errorcode = dll.DLI_SpectrometerUpdateHandle(c_int(0),pointer(hand), VIDPID[i*2], VIDPID[(i*2)+1])
        if(errorcode != 0):print("errorcode = ", errorcode)
        
        break
    
    if(num.value ==0):
        exit
    
    return hand

def getSpectrometerInfo(hand_p):
    SN = (c_char * 16)()
    errorcode = dll.UAI_SpectrometerGetSerialNumber(hand_p, pointer(SN))
    if(errorcode != 0):
        raise Exception("UAI_SpectrometerGetSerialNumber: {}".format(errorcode))

    MN = (c_char * 16)()
    errorcode = dll.UAI_SpectrometerGetModelName(hand_p, pointer(MN))
    if(errorcode != 0):
        raise Exception("UAI_SpectrometerGetModelName: {}".format(errorcode))

    FW_Version = c_uint()
    errorcode = dll.UAI_FirmwareGetVersion(hand_p, pointer(FW_Version))
    if(errorcode != 0):
        raise Exception("UAI_FirmwareGetVersion: {}".format(errorcode))

    FW_BuildNumber = c_uint()
    errorcode = dll.UAI_FirmwareGetBuildNumber(hand_p, pointer(FW_BuildNumber))
    if(errorcode != 0):
        raise Exception("UAI_FirmwareGetBuildNumber: {}".format(errorcode))

    bu_ver = [0] * 5
    fw_ver = [0] * 5
    for i in range(0, 4):
        bu_ver[i] = (FW_BuildNumber.value >> (i * 8)) & 0xFF

    fw_ver[0] = FW_Version.value & 0xFF
    sensor_ver = (FW_Version.value >> 8) & 0xFF
    ver_num = (FW_Version.value >> 24) & 0xFF + ((FW_Version.value >> 16) & 0xFF) * 256
    func_type = ((FW_Version.value >> 20) & 0xF)
    res = ''.join(chr(i) for i in bu_ver)
    FW_Name = "{}{}.{}.{}({})".format(chr(fw_ver[0]), sensor_ver, func_type, ver_num, res)

    frame_size = c_short()
    errorcode = dll.UAI_SpectromoduleGetFrameSize(hand_p, pointer(frame_size))
    if(errorcode != 0):
        raise Exception("UAI_SpectromoduleGetFrameSize: {}".format(errorcode))
    
    frame_size_raw = c_uint32()
    errorcode = dll.DLI_SpectromoduleGetFrameSize(hand_p, pointer(frame_size_raw))
    if(errorcode != 0):
        raise Exception("DLI_SpectromoduleGetFrameSize: {}".format(errorcode))


    print("SN: {}".format(SN.value.decode('utf-8')))
    print("MN: {}".format(MN.value.decode('utf-8')))
    print("Frame Size: {}".format(frame_size.value))
    print("Frame Size Raw: {}".format(frame_size_raw.value))
    print("Firmware: {}".format(FW_Name))
    
def ushort_to_date(intdate):
    # 提取年份、月份和日期
    year = (intdate >> 9) & 0x7F  # 取出高 7 位
    year += 2010  # 假設基於 2010 年
    mon = (intdate >> 5) & 0x0F  # 取出中間的 4 位
    day = intdate & 0x1F  # 取出最低的 5 位

    # 格式化日期字符串
    stringdate = f"{year}/{mon}/{day}"
    return stringdate

def date_to_ushort(strdata):
    # 初始化
    temp_int = [0, 0, 0]
    
    # 分割字符串並轉換為整數
    temp_str0 = strdata.split()  # split by space, equivalent to C# Split(new Char[] { ' ' })
    temp_str = temp_str0[0].split('/')  # split by '/', equivalent to C# Split(new Char[] { '/' })
    
    for i in range(len(temp_str)):
        temp_int[i] = int(temp_str[i])  # equivalent to int.Parse
    
    # 轉換年份為距離2010年的年數
    temp_int[0] = temp_int[0] - 2010
    
    # 將整數轉換為二進制字符串
    temp_str = [bin(i)[2:] for i in temp_int]  # bin() produces '0b' prefix, so we slice off the first 2 chars.
    
    # 補充二進制數字前綴零
    temp_str[1] = temp_str[1].zfill(4)  # equivalent to while (temp_str[1].Length < 4) in C#
    temp_str[2] = temp_str[2].zfill(5)  # equivalent to while (temp_str[2].Length < 5) in C#
    
    # 合併為一個二進制字符串
    strdata = ''.join(temp_str)  # concatenating the binary string parts
    
    # 將二進制字符串轉換為無符號短整數
    ushortdata = int(strdata, 2)  # equivalent to Convert.ToUInt16(strdata, 2) in C#
    return ushortdata

from datetime import datetime

def time(detail):
    save_time = ""
    curr_time = datetime.now()  # 獲取當前時間
    yea = str(curr_time.year)
    mon = str(curr_time.month)
    day = str(curr_time.day)
    hou = " 上午 " + str(curr_time.hour)
    min = str(curr_time.minute).zfill(2)  # 保證兩位數
    sec = str(curr_time.second).zfill(2)  # 保證兩位數
    mil = str(curr_time.microsecond // 1000).zfill(3)  # 毫秒部分，微秒轉換成毫秒並補足三位數

    # 判斷是上午還是下午
    if curr_time.hour >= 12:
        hou = " 下午 " + str(curr_time.hour - 12)  # 轉換為12小時制

    # 根據 'detail' 參數決定是否包含時間
    if not detail:
        save_time = yea + "/" + mon + "/" + day  # 只顯示日期
    else:
        save_time = yea + "/" + mon + "/" + day + hou + ":" + min + ":" + sec  # 顯示完整時間

    return save_time