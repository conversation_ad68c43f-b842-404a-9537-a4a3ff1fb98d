#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合测点规划显示区域和中间参数显示区域的显示逻辑

文件命名策略：
为避免同一孔位在不同时间的检测结果相互覆盖，文件名采用以下格式：
    原始文件名_孔位_日期时间_测量次数.扩展名
    例如：测试文件001_A01_20250709_143025_1.txt
    
其中：
- 原始文件名：用户在测点规划中设置的基础文件名
- 孔位：如 A01, B02 等
- 日期时间：格式为 YYYYMMDD_HHMMSS，确保每次测量的唯一性
- 测量次数：该孔位当前是第几次测量（平均次数中的序号）
- 扩展名：根据输出格式设置（txt/csv/nod）

这样即使是同一个孔位，在不同时间进行的测量也会生成不同的文件，
避免数据丢失，便于数据管理和追溯。
"""

import os
import json
import time
import csv
import threading
from datetime import datetime
from typing import List, Dict, Optional
import numpy as np
from PyQt6.QtCore import QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QMessageBox
from log_manager import log_user_action

class IntegratedMeasurementLogic(QObject):
    """整合测量逻辑类"""
    
    # 定义信号
    update_filename_signal = pyqtSignal(str)  # 更新文件名
    update_laser_power_signal = pyqtSignal(str)  # 更新激光功率
    update_integration_time_signal = pyqtSignal(str)  # 更新积分时间
    update_progress_signal = pyqtSignal(str)  # 更新进度
    update_hole_color_signal = pyqtSignal(str, str, QColor)  # 更新孔位颜色 (hole_id, status, color)
    measurement_completed_signal = pyqtSignal()  # 测量完成信号
    
    def __init__(self, editor_instance=None):
        super().__init__()
        self.editor = editor_instance
        
        # 测量状态
        self.measurement_running = False
        self.current_measurement_count = 0
        
        # 定时器
        self.measurement_timer = QTimer()
        self.measurement_timer.timeout.connect(self.process_next_measurement)
        
    def start_integrated_measurement(self, selected_holes_data: List[Dict] = None):
        """开始整合测量流程"""
        try:
            print("开始整合测量流程...")
            
            # 检查通讯状态 - 确保激光器和光谱仪都已连接
            if not self._check_communication_status():
                print("通讯状态检查失败，请确保激光器和光谱仪都已连接")
                # 显示用户友好的错误提示
                try:
                    from PyQt6.QtWidgets import QMessageBox
                    QMessageBox.warning(None, "通讯状态检查", "请先确保激光器和光谱仪都已连接\n\n点击侧边栏的通讯按钮检查设备连接状态")
                except Exception as e:
                    print(f"显示错误提示失败: {e}")
                return False
            
            # 自动关闭激光（如果激光已开启）
            if not self._auto_disable_laser():
                print("自动关闭激光失败，无法继续测量")
                return False
            
            # 直接从editor的命名参数中读取信息
            if not self.editor or not hasattr(self.editor, 'naming_params'):
                print("编辑器或命名参数未初始化")
                return False
            
            naming_params = self.editor.naming_params
            laser_params = getattr(self.editor, 'laser_params', {})
            
            # 验证命名参数
            if not naming_params.get('sample_name') or not naming_params.get('patient_id') or not naming_params.get('department'):
                print("命名参数不完整，请先设置样品名、患者编号和科室")
                return False
            
            # 生成测量数据
            measurement_data = {
                'sample_name': naming_params['sample_name'],
                'patient_id': naming_params['patient_id'],
                'department': naming_params['department'],
                'date': naming_params.get('selected_date', ''),
                'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),  # 保存时间戳到测量数据中
                'laser_power': laser_params.get('laser_power', 100),
                'integration_time': laser_params.get('integration_time', 1000),
                'average_count': laser_params.get('average_count', 3),
                'acquisition_interval': 500  # 设置采集间隔为500ms
            }
            
            # 生成文件名：样品名_患者编号_科室_日期时间
            base_filename = f"{measurement_data['sample_name']}_{measurement_data['patient_id']}_{measurement_data['department']}_{measurement_data['timestamp']}"
            
            # 更新中间参数显示 - 使用与文件命名一致的格式
            self.update_parameter_display(
                measurement_data['sample_name'],
                measurement_data['patient_id'], 
                measurement_data['department'],
                measurement_data['timestamp']  # 使用保存的时间戳
            )
            
            # 开始测量循环
            self.start_measurement_cycle(base_filename, measurement_data)
            
            log_user_action("开始整合测量", self._get_current_user(), {"文件名": base_filename})
            
            return True
            
        except Exception as e:
            print(f"开始整合测量流程失败: {e}")
            return False
    

    
    # def update_parameter_display(self, filename: str, patient_id: str, date: str, department: str):
    #     """更新中间参数显示区域"""
    #     try:
    #         log_user_action("参数变更", self._get_current_user(), {"filename": filename, "patient_id": patient_id, "date": date, "department": department})
    #         # 更新文件名显示
    #         display_name = f"{filename} - {patient_id} - {date} - {department}"
    #         self.update_filename_signal.emit(display_name)
            
    #         print(f"更新参数显示: {display_name}")
            
    #     except Exception as e:
    #         print(f"更新参数显示失败: {e}")
    def update_parameter_display(self, filename: str, patient_id: str, department: str, timestamp: str):
        """更新中间参数显示区域"""
        try:
            log_user_action("参数变更", self._get_current_user(), {"filename": filename, "patient_id": patient_id, "department": department, "timestamp": timestamp})
            # 更新文件名显示 - 使用与文件命名一致的格式
            display_name = f"{filename}_{patient_id}_{department}_{timestamp}"
            print(f"[调试] 发送参数显示信号: {display_name}")
            self.update_filename_signal.emit(display_name)
            
            print(f"[调试] 参数显示信号发送完成: {display_name}")
            
        except Exception as e:
            print(f"更新参数显示失败: {e}")
    

    

    
    def start_measurement_cycle(self, base_filename: str, measurement_data: Dict):
        """开始测量循环"""
        try:
            average_count = measurement_data.get('average_count', 3)
            print(f"开始测量循环: 文件名 {base_filename}, 平均次数 {average_count}")
            
            # 保存测量数据
            self.current_measurement_data = measurement_data
            self.current_base_filename = base_filename
            
            # 重置测量计数
            self.current_measurement_count = 0
            self.measurement_running = True
            
            # 立即更新初始参数显示 - 在测量开始前就显示
            self.update_initial_parameters_display(measurement_data, base_filename)
            
            # 强制刷新界面，确保初始参数更新立即显示
            self._force_ui_refresh()
            
            # 重置进度条为0（开始新的测量）
            if self.editor and hasattr(self.editor, "status_bar"):
                self.editor.status_bar.set_progress(0)
            
            # 直接开始第一次测量，不使用定时器
            self.process_next_measurement()
            
        except Exception as e:
            print(f"开始测量循环失败: {e}")
    
    def process_next_measurement(self):
        """处理下一次测量"""
        try:
            # 立即检查测量是否仍在运行
            if not self.measurement_running:
                print("测量已停止，跳过处理下一次测量")
                return
            
            if not hasattr(self, 'current_measurement_data') or not hasattr(self, 'current_base_filename'):
                print("测量数据未初始化")
                return
            
            average_count = self.current_measurement_data.get('average_count', 3)
            
            # 检查是否还需要继续测量
            if self.current_measurement_count >= average_count:
                print(f"测量已完成，共 {average_count} 次")
                self.complete_measurement()
                return
            
            # 增加测量计数
            self.current_measurement_count += 1
            
            # 读取测量参数
            laser_power = self.current_measurement_data.get('laser_power', 100)
            integration_time = self.current_measurement_data.get('integration_time', 1000)
            
            # 生成当前次数的文件名（不重复添加时间戳）
            current_filename = f"{self.current_base_filename}_{self.current_measurement_count}"
            
            # 立即更新中间参数显示区域 - 提前更新，不等待测量完成
            print(f"[调试] 开始更新参数显示 - 第 {self.current_measurement_count} 次测量")
            self.update_measurement_parameters(laser_power, integration_time, average_count)
            
            # 立即更新文件名显示 - 提前显示当前测量文件名
            print(f"[调试] 开始更新文件名显示 - 文件名: {current_filename}")
            self.update_filename_display_immediately(current_filename)
            
            # 更新测量进度
            progress_text = f"{self.current_measurement_count}/{average_count}"
            self.update_progress_signal.emit(progress_text)
            
            print(f"[调试] 参数更新完成，开始执行测量 - 进度: {progress_text} - 文件名: {current_filename}")
            
            # 强制刷新界面，确保参数更新立即显示
            self._force_ui_refresh()
            
            # 执行单次拉曼光谱测量
            self.execute_single_measurement(current_filename, laser_power, integration_time, current_filename, 
                                           self.current_measurement_count, average_count)
            
        except Exception as e:
            print(f"处理下一次测量失败: {e}")
    
    def execute_single_measurement(self, filename: str, laser_power: int, integration_time: int, 
                                  full_filename: str, current_count: int, total_count: int):
        """执行单次拉曼光谱测量"""
        try:
            # 立即检查测量是否仍在运行
            if not self.measurement_running:
                print("测量已停止，跳过执行单次测量")
                return
                
            # 1. 向光谱仪发送测量参数并开始采集
            if self.send_measurement_to_ros(filename, laser_power, integration_time):
                # 2. 采集成功后，立即处理测量结果
                self.handle_ros_measurement_result(
                    filename, laser_power, integration_time, full_filename, current_count, total_count)
            else:
                print("光谱仪采集失败，跳过本次测量")
            
        except Exception as e:
            print(f"执行单次测量失败: {e}")
    
    def send_measurement_to_ros(self, filename: str, laser_power: int, integration_time: int):
        """向光谱仪发送测量参数并开始采集"""
        try:
            # 立即检查测量是否仍在运行
            if not self.measurement_running:
                print("测量已停止，跳过发送测量参数")
                return False
            # 导入光谱仪模块
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            spectrumeter_dir = os.path.join(os.path.dirname(current_dir), 'spectrumeter')
            if spectrumeter_dir not in sys.path:
                sys.path.insert(0, spectrumeter_dir)
            
            from raman_spectrometer import RamanSpectrometer
            
            # 创建或获取光谱仪实例
            if not hasattr(self, 'spectrometer') or not self.spectrometer:
                self.spectrometer = RamanSpectrometer()
                
                # 连接设备
                if self.spectrometer.connect_device() != "yes":
                    print("光谱仪连接失败")
                    return False
                
                # 获取帧大小和波长信息
                if not self.spectrometer.get_frame_size():
                    print("获取帧大小失败")
                    return False
                    
                if not self.spectrometer.get_wavelength_and_raman_shift():
                    print("获取波长信息失败")
                    return False
                
                # 加载线性校正数据
                self.spectrometer.load_linear_correction()
            else:
                # 如果光谱仪实例已存在，检查激光控制器状态并重新初始化
                try:
                    if hasattr(self.spectrometer, 'laser_controller') and self.spectrometer.laser_controller:
                        # 检查激光控制器状态
                        status = self.spectrometer.laser_controller.get_status_summary()
                        if not status.get("serial_connected", False):
                            print("激光控制器串口连接异常，重新初始化...")
                            # 重新创建激光控制器
                            from laser_controller_usb import LaserController
                            self.spectrometer.laser_controller = LaserController()
                        elif not status.get("hardware_power_on", False):
                            print("激光器硬件电源开关未打开，请检查硬件")
                            return False
                except Exception as e:
                    print(f"检查激光控制器状态失败: {e}")
                    # 重新创建激光控制器
                    try:
                        from laser_controller_usb import LaserController
                        self.spectrometer.laser_controller = LaserController()
                    except Exception as e2:
                        print(f"重新创建激光控制器失败: {e2}")
                        return False
            
            # 设置测量参数
            print(f"设置光谱仪参数: 激光功率 {laser_power}%, 积分时间 {integration_time}ms")
            self.spectrometer.set_integration_time(integration_time)
            self.spectrometer.set_laser_power(laser_power)
            
            # 最后检查激光控制器状态
            try:
                if hasattr(self.spectrometer, 'laser_controller') and self.spectrometer.laser_controller:
                    status = self.spectrometer.laser_controller.get_status_summary()
                    if not status.get("serial_connected", False):
                        print("激光控制器串口未连接，无法进行测量")
                        # 显示用户友好的错误提示
                        try:
                            from PyQt6.QtWidgets import QMessageBox
                            QMessageBox.warning(None, "激光控制器错误", "激光控制器串口未连接，无法进行测量\n\n请检查：\n1. 激光器USB线是否连接\n2. 串口驱动是否正确安装\n3. 点击侧边栏通讯按钮检查连接状态")
                        except Exception as e:
                            print(f"显示错误提示失败: {e}")
                        return False
                    if not status.get("hardware_power_on", False):
                        print("激光器硬件电源开关未打开，请检查硬件")
                        # 显示用户友好的错误提示
                        try:
                            from PyQt6.QtWidgets import QMessageBox
                            QMessageBox.warning(None, "激光器硬件错误", "激光器硬件电源开关未打开，无法进行测量\n\n请检查：\n1. 激光器电源开关是否打开\n2. 电源指示灯是否亮起\n3. 电源线是否连接正常")
                        except Exception as e:
                            print(f"显示错误提示失败: {e}")
                        return False
                    print("激光控制器状态正常，可以开始测量")
            except Exception as e:
                print(f"激光控制器状态检查失败: {e}")
                return False
            
            # 开始采集
            print("开始光谱采集...")
            if self.spectrometer.start_acquisition():
                print("光谱采集完成")
                return True
            else:
                print("光谱采集失败")
                return False
                
        except Exception as e:
            print(f"向光谱仪发送测量参数失败: {e}")
            return False
    
    def handle_ros_measurement_result(self, filename: str, laser_power: int, integration_time: int, 
                                     full_filename: str, current_count: int, total_count: int):
        """处理光谱仪测量结果"""
        try:
            # 立即检查测量是否仍在运行
            if not self.measurement_running:
                print("测量已停止，跳过处理测量结果")
                return
                
            log_user_action("接收光谱仪测量结果", self._get_current_user(), {"文件名": filename, "激光功率": laser_power, "积分时间": integration_time, "文件": full_filename, "当前次数": current_count, "总次数": total_count})
            
            # 从光谱仪获取实际数据
            if not hasattr(self, 'spectrometer') or not self.spectrometer:
                print("光谱仪实例不存在")
                return
                
            if not hasattr(self.spectrometer, 'spectrum_data') or not self.spectrometer.spectrum_data:
                print("光谱数据不存在")
                return
            
            # 从spectrum_data中提取raman_shifts和corrected_intensities
            raman_shift = []
            intensity = []
            
            for spectrum_point in self.spectrometer.spectrum_data:
                raman_shift.append(spectrum_point[2])  # raman_shift
                intensity.append(spectrum_point[4])    # corrected_intensities
            
            # 存储测量数据
            measurement_data = self.create_measurement_data(
                filename, full_filename, laser_power, integration_time, 
                raman_shift, intensity, current_count, total_count
            )
            
            # 保存数据文件
            self.save_measurement_data(measurement_data, full_filename, current_count)
            
            # 更新编辑器显示
            self.update_editor_display(measurement_data)
            
            # 更新光谱图显示
            self.update_spectrum_display(raman_shift, intensity)
            
            # 自动加载保存的文件到编辑器
            work_directory = self._get_current_work_directory()
            output_format = getattr(self.editor, 'output_format', 'txt') if self.editor else 'txt'
            filepath = os.path.join(work_directory, f"{full_filename}.{output_format}")
            self.auto_load_saved_file(filepath)
            
            # 光谱显示完成后，更新进度条百分比
            if self.editor and hasattr(self.editor, "status_bar"):
                percent = int(current_count / total_count * 100)
                self.editor.status_bar.set_progress(percent)
                print(f"进度条已更新: {percent}%")
            
            print(f"完成第 {current_count}/{total_count} 次测量")
            
            # 检查是否完成所有测量
            if current_count >= total_count:
                print(f"所有 {total_count} 次测量已完成")
                
                # 延迟一秒后完成测量（检查测量状态）
                QTimer.singleShot(1000, lambda: self.complete_measurement() if self.measurement_running else None)
            else:
                # 继续下一次测量，使用设置的采集间隔延迟（检查测量状态）
                acquisition_interval = self.current_measurement_data.get('acquisition_interval', 500)
                print(f"[调试] 等待采集间隔: {acquisition_interval}ms 后继续下一次测量")
                QTimer.singleShot(acquisition_interval, lambda: self.process_next_measurement() if self.measurement_running else None)
            
        except Exception as e:
            print(f"处理测量结果失败: {e}")
    
    def update_measurement_parameters(self, laser_power: int, integration_time: int, average_count: int):
        """更新测量参数显示"""
        try:
            print(f"[调试] 发送激光功率信号: {laser_power}%")
            # 更新激光功率
            self.update_laser_power_signal.emit(f"{laser_power}%")
            
            print(f"[调试] 发送积分时间信号: {integration_time}ms")
            # 更新积分时间
            self.update_integration_time_signal.emit(f"{integration_time}ms")
            
            # 获取并显示采集间隔
            acquisition_interval = self.current_measurement_data.get('acquisition_interval', 500)
            print(f"[调试] 采集间隔: {acquisition_interval}ms")
            
            print(f"[调试] 测量参数信号发送完成: 激光功率 {laser_power}%, 积分时间 {integration_time}ms, 采集间隔 {acquisition_interval}ms")
            
        except Exception as e:
            print(f"更新测量参数失败: {e}")
    
    def update_filename_display_immediately(self, current_filename: str):
        """立即更新文件名显示，不等待测量完成"""
        try:
            # 从当前测量数据中获取信息
            if hasattr(self, 'current_measurement_data'):
                sample_name = self.current_measurement_data.get('sample_name', '')
                patient_id = self.current_measurement_data.get('patient_id', '')
                department = self.current_measurement_data.get('department', '')
                timestamp = self.current_measurement_data.get('timestamp', '')  # 使用保存的时间戳
                
                # 使用与文件命名一致的格式显示
                display_name = f"{sample_name}_{patient_id}_{department}_{timestamp}_{current_filename.split('_')[-1]}"
                print(f"[调试] 发送文件名信号: {display_name}")
                self.update_filename_signal.emit(display_name)
                
                print(f"[调试] 文件名信号发送完成: {display_name}")
            else:
                # 如果没有测量数据，直接显示文件名
                self.update_filename_signal.emit(current_filename)
                print(f"立即更新文件名显示: {current_filename}")
                
        except Exception as e:
            print(f"立即更新文件名显示失败: {e}")
    
    def update_initial_parameters_display(self, measurement_data: Dict, base_filename: str):
        """在测量开始前立即更新初始参数显示"""
        try:
            # 立即更新激光功率和积分时间
            laser_power = measurement_data.get('laser_power', 100)
            integration_time = measurement_data.get('integration_time', 1000)
            average_count = measurement_data.get('average_count', 3)
            
            self.update_laser_power_signal.emit(f"{laser_power}%")
            self.update_integration_time_signal.emit(f"{integration_time}ms")
            
            # 立即更新文件名显示（显示基础文件名，不包含测量次数）
            sample_name = measurement_data.get('sample_name', '')
            patient_id = measurement_data.get('patient_id', '')
            department = measurement_data.get('department', '')
            timestamp = measurement_data.get('timestamp', '')  # 使用保存的时间戳
            
            # 使用与文件命名一致的格式显示
            display_name = f"{sample_name}_{patient_id}_{department}_{timestamp}"
            print(f"[调试] 发送初始文件名信号: {display_name}")
            self.update_filename_signal.emit(display_name)
            
            print(f"[调试] 初始参数信号发送完成: 激光功率 {laser_power}%, 积分时间 {integration_time}ms, 文件名 {display_name}")
            
        except Exception as e:
            print(f"立即更新初始参数显示失败: {e}")
    

    
    def complete_measurement(self):
        """完成测量流程"""
        try:
            print("测量流程完成")
            
            self.measurement_running = False
            
            # 测量完成后，将进度条归零
            if self.editor and hasattr(self.editor, "status_bar"):
                self.editor.status_bar.set_progress(0)
            
            # 只关闭激光器，保持光谱仪连接
            if hasattr(self, 'spectrometer') and self.spectrometer:
                try:
                    self.spectrometer.close_laser_controller()
                    print("激光器已关闭，光谱仪保持连接")
                except Exception as e:
                    print(f"关闭激光器失败: {e}")
            
            # 发送测量完成信号
            self.measurement_completed_signal.emit()
            
            # 通知编辑器更新测量状态为完成
            if self.editor and hasattr(self.editor, 'measurement_state'):
                self.editor.measurement_state = "completed"
                # 重置按钮状态
                if hasattr(self.editor, 'start_measurement_btn'):
                    self.editor.start_measurement_btn.setEnabled(False)
                if hasattr(self.editor, 'stop_measurement_btn'):
                    self.editor.stop_measurement_btn.setEnabled(False)
            
            # 显示完成消息
            if self.editor:
                QMessageBox.information(self.editor, "测量完成", "测量已完成！")
            
        except Exception as e:
            print(f"完成测量流程失败: {e}")
    
    def stop_measurement(self):
        """停止测量流程"""
        try:
            print("立即停止测量流程...")
            
            # 立即设置停止标志
            self.measurement_running = False
            
            # 立即停止测量时将进度条归零
            if self.editor and hasattr(self.editor, "status_bar"):
                self.editor.status_bar.set_progress(0)
            
            # 立即关闭激光器，保持光谱仪连接
            if hasattr(self, 'spectrometer') and self.spectrometer:
                try:
                    print("正在关闭激光器...")
                    self.spectrometer.close_laser_controller()
                    print("激光器已关闭，光谱仪保持连接")
                except Exception as e:
                    print(f"关闭激光器失败: {e}")
            
            # 立即清理所有状态
            self.current_measurement_count = 0
            
            # 立即清理测量数据
            if hasattr(self, 'current_measurement_data'):
                delattr(self, 'current_measurement_data')
            if hasattr(self, 'current_base_filename'):
                delattr(self, 'current_base_filename')
            
            # 强制刷新界面
            if self.editor and hasattr(self.editor, '_force_ui_refresh'):
                self.editor._force_ui_refresh()
            
            print("测量流程立即停止完成")
        except Exception as e:
            print(f"停止测量流程失败: {e}")
    



    
    def close_spectrometer_completely(self):
        """完全关闭光谱仪（包括设备连接）"""
        try:
            if hasattr(self, 'spectrometer') and self.spectrometer:
                # 先关闭激光器
                try:
                    self.spectrometer.close_laser_controller()
                    print("激光器已关闭")
                except Exception as e:
                    print(f"关闭激光器失败: {e}")
                
                # 关闭光谱仪设备连接
                try:
                    import sys
                    import os
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    spectrumeter_dir = os.path.join(os.path.dirname(current_dir), 'spectrumeter')
                    if spectrumeter_dir not in sys.path:
                        sys.path.insert(0, spectrumeter_dir)
                    
                    import PythonExample_OtOGeneral as oto
                    if hasattr(self.spectrometer, 'hand') and self.spectrometer.hand:
                        oto.closeDevice(self.spectrometer.hand)
                        print("光谱仪设备连接已关闭")
                except Exception as e:
                    print(f"关闭光谱仪设备连接失败: {e}")
                
                # 清理光谱仪实例
                self.spectrometer = None
                print("光谱仪实例已清理")
        except Exception as e:
            print(f"完全关闭光谱仪失败: {e}")

    def create_measurement_data(self, filename: str, full_filename: str, laser_power: int, 
                               integration_time: int, raman_shift, intensity, current_count: int, total_count: int) -> Dict:
        """创建测量数据结构"""
        try:
            # 获取当前时间
            now = datetime.now()
            scan_time = now.strftime("%Y-%m-%d %H:%M:%S")
            
            # 获取操作员信息（从登录界面传入）
            operator = getattr(self.editor, 'current_user', '未知操作员') if self.editor else '未知操作员'
            
            # 获取设备信息（设备传入）
            device_model = getattr(self.editor, 'device_model', '未知设备型号') if self.editor else '未知设备型号'
            device_serial = getattr(self.editor, 'device_serial', '未知序列号') if self.editor else '未知序列号'
            
            # 计算像素个数
            pixel_count = len(raman_shift)
            
            # 文件名已经在之前生成，这里直接使用传入的full_filename
            # 格式：样品名_患者编号_科室_日期时间_测量次数
            # 例如：测试样品_P001_内科_20250804_142530_1
            
            measurement_data = {
                # 固定信息
                'data_generated_by': 'Neolily',
                'file_version': '4',
                'description': '无',
                'acquisition_mode': '精确采集',
                'sampling_interval': '500',  # 设置采集间隔为500ms
                
                # 动态信息
                'filename': full_filename,
                'original_filename': filename,  # 新增：保存原始文件名
                'datetime_str': now.strftime("%Y%m%d_%H%M%S"),   # 新增：保存日期时间字符串
                'operator': operator,
                'scan_time': scan_time,
                'integration_time': integration_time,
                'laser_power': laser_power,
                'average_count': total_count,
                'current_measurement': current_count,
                'device_model': device_model,
                'device_serial': device_serial,
                'pixel_count': pixel_count,
                
                # 光谱数据
                'raman_shift': raman_shift,
                'intensity': intensity,
                
                # 位置信息（使用文件名作为标识）
                'hole_id': filename
            }
            
            return measurement_data
            
        except Exception as e:
            print(f"创建测量数据失败: {e}")
            return {}
    
    def save_measurement_data(self, measurement_data: Dict, base_filename: str, measurement_count: int):
        """保存测量数据到文件"""
        try:
            log_user_action("保存测量数据", self._get_current_user(), {"filename": measurement_data.get('filename', ''), "测量次数": measurement_count})
            if not measurement_data:
                return
            
            # 获取输出格式（从菜单栏设置中获取，默认为txt）
            output_format = getattr(self.editor, 'output_format', 'txt') if self.editor else 'txt'
            
            # 获取工作目录（优先从主窗口的菜单栏获取最新设置）
            work_directory = self._get_current_work_directory()
            
            # 添加调试信息
            print(f"[调试] 编辑器实例: {self.editor}")
            if self.editor:
                print(f"[调试] 编辑器是否有work_directory属性: {hasattr(self.editor, 'work_directory')}")
                if hasattr(self.editor, 'work_directory'):
                    print(f"[调试] 编辑器工作目录: {self.editor.work_directory}")
            print(f"[调试] 最终使用的工作目录: {work_directory}")
            
            # 确保目录存在
            os.makedirs(work_directory, exist_ok=True)
            
            # 使用带有日期时间的完整文件名（已在measurement_data中生成）
            filename = f"{measurement_data['filename']}.{output_format}"
            filepath = os.path.join(work_directory, filename)
            
            # 记录文件命名信息
            original_name = measurement_data.get('original_filename', base_filename)
            datetime_str = measurement_data.get('datetime_str', '')
            
            print(f"保存文件: {original_name} -> {filename}")
            print(f"  时间: {datetime_str}")
            print(f"  测量次数: {measurement_count}")
            print(f"  路径: {filepath}")
            
            # 根据格式保存文件
            if output_format.lower() == 'txt':
                self.save_as_txt(measurement_data, filepath)
            elif output_format.lower() == 'csv':
                self.save_as_csv(measurement_data, filepath)
            elif output_format.lower() == 'nod':
                self.save_as_nod(measurement_data, filepath)
            else:
                print(f"不支持的输出格式: {output_format}")
                return  # 如果格式不支持，直接返回
                
            print(f"✅ 数据已保存: {filepath}")
            
            # 自动加载保存的文件到编辑器中
            self.auto_load_saved_file(filepath)
            
        except Exception as e:
            print(f"❌ 保存测量数据失败: {e}")
    
    def save_as_txt(self, data: Dict, filepath: str):
        """保存为TXT格式"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                # 写入文件头信息
                f.write(f"Data Generated by;{data['data_generated_by']}\n")
                f.write(f"File Version;{data['file_version']}\n")
                f.write(f"文件名称;{data['filename']}\n")
                f.write(f"原始文件名;{data.get('original_filename', '')}\n")  # 新增：原始文件名
                f.write(f"生成时间戳;{data.get('datetime_str', '')}\n")      # 新增：时间戳
                f.write(f"操作员;{data['operator']}\n")
                f.write(f"描述;{data['description']}\n")
                f.write(f"扫描时间;{data['scan_time']}\n")
                f.write(f"积分时间(ms);{data['integration_time']}\n")
                f.write(f"激光功率(mW);{data['laser_power']}\n")
                f.write(f"平均次数;{data['average_count']}\n")
                f.write(f"当前测量次数;{data['current_measurement']}\n")      # 新增：当前测量次数
                f.write(f"采集模式;{data['acquisition_mode']}\n")
                f.write(f"采样间隔;{data['sampling_interval']}\n")
                f.write(f"设备型号;{data['device_model']}\n")
                f.write(f"像素个数;{data['pixel_count']}\n")
                f.write(f"设备序列号;{data['device_serial']}\n")
                f.write(f"孔位;{data.get('hole_id', '')}\n")               # 新增：孔位信息
                f.write("\n")
                
                # 写入光谱数据
                f.write("Raman Shift;Dark Subtracted\n")
                for shift, intensity in zip(data['raman_shift'], data['intensity']):
                    f.write(f"{shift:.2f};{intensity:.2f}\n")
                    
        except Exception as e:
            print(f"保存TXT文件失败: {e}")
    
    def save_as_csv(self, data: Dict, filepath: str):
        """保存为CSV格式"""
        try:
            import csv
            with open(filepath, 'w', newline='', encoding='gbk') as f:
                writer = csv.writer(f)
                
                # 写入文件头信息
                writer.writerow(['Data Generated by',data['data_generated_by']])
                writer.writerow(['File Version',data['file_version']])
                writer.writerow(['文件名称',data['filename']])
                writer.writerow(['原始文件名',data.get('original_filename', '')])  # 新增：原始文件名
                writer.writerow(['生成时间戳',data.get('datetime_str', '')])      # 新增：时间戳
                writer.writerow(['操作员',data['operator']])
                writer.writerow(['描述',data['description']])
                writer.writerow(['扫描时间',data['scan_time']])
                writer.writerow(['积分时间(ms)',data['integration_time']])
                writer.writerow(['激光功率(mW)',data['laser_power']])
                writer.writerow(['平均次数',data['average_count']])
                writer.writerow(['当前测量次数', data['current_measurement']])      # 新增：当前测量次数
                writer.writerow(['采集模式', data['acquisition_mode']])
                writer.writerow(['采样间隔', data['sampling_interval']])
                writer.writerow(['设备型号', data['device_model']])
                writer.writerow(['像素个数', data['pixel_count']])
                writer.writerow(['设备序列号', data['device_serial']])
                writer.writerow(['孔位', data.get('hole_id', '')])               # 新增：孔位信息
                #writer.writerow([])  # 空行
                
                # 写入光谱数据
                writer.writerow(['Raman Shift', 'Dark Subtracted'])
                for shift, intensity in zip(data['raman_shift'], data['intensity']):
                    writer.writerow([f"{shift:.2f}", f"{intensity:.2f}"])
                    
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
    
    def save_as_nod(self, data: Dict, filepath: str):
        """保存为NOD格式"""
        try:
            # 导入NOD序列化器
            try:
                from nod_file_serializer import NodFileSerializer
                serializer = NodFileSerializer()
                # 兼容字段转换
                spectrum_data = self._convert_to_nod_format(data)
                success = serializer.serialize_to_nod(spectrum_data, filepath)
                if not success:
                    print(f"NOD文件保存失败: {filepath}")
            except ImportError:
                print("NOD文件序列化器不可用，使用JSON格式保存")
                import json
                with open(filepath, 'w', encoding='utf-8') as f:
                    # 将numpy数组转换为列表
                    data_copy = data.copy()
                    data_copy['Raman Shift'] = data['raman_shift'].tolist()
                    data_copy['Dark Subtracted'] = data['intensity'].tolist()
                    json.dump(data_copy, f, ensure_ascii=False, indent=2)
        
        except Exception as e:
            print(f"保存NOD文件失败: {e}")

    def _convert_to_nod_format(self, data: Dict) -> Dict:
        """将测量数据转换为NOD序列化器需要的格式"""
        # 兼容字段名，按nod_file_serializer.py要求组织
        spectrum_data = {}
        # 样品名称
        spectrum_data['sample_name'] = data.get('filename', '')
        spectrum_data['operator'] = data.get('operator', '')
        spectrum_data['scan_time'] = data.get('scan_time', '')
        # 采集参数
        spectrum_data['gather_parameters'] = {
            'pixel_number': data.get('pixel_count', 0),
            'integration_time': data.get('integration_time', 0),
            'laser_power': data.get('laser_power', 0),
            'scan_count': data.get('average_count', 1)
        }
        # 光谱元信息
        spectrum_data['spectrum_metadata'] = {
            '描述': data.get('description', ''),
            '采集模式': data.get('acquisition_mode', ''),
            '采样间隔': data.get('sampling_interval', ''),
            '设备型号': data.get('device_model', ''),
            '设备序列号': data.get('device_serial', ''),
            '孔位': data.get('hole_id', '')
        }
        # 光谱数据
        spectrum_data['raman_shift'] = data.get('raman_shift', [])
        spectrum_data['intensity'] = data.get('intensity', [])
        return spectrum_data
    
    def update_editor_display(self, measurement_data: Dict):
        """更新编辑器中的文件内容视图"""
        try:
            if not self.editor:
                return
                
            # 格式化显示内容
            display_content = self.format_data_for_display(measurement_data)
            
            # 更新编辑器显示（需要编辑器实现相应方法）
            if hasattr(self.editor, 'update_file_content_view'):
                self.editor.update_file_content_view(display_content)
                
        except Exception as e:
            pass  # 静默处理错误
    
    def update_spectrum_display(self, raman_shift, intensity):
        """更新光谱图显示"""
        try:
            if not self.editor:
                return
                
            # 更新光谱图显示（需要编辑器实现相应方法）
            if hasattr(self.editor, 'update_spectrum_plot'):
                self.editor.update_spectrum_plot(raman_shift, intensity)
                
            print(f"已更新光谱图显示，数据点数: {len(raman_shift)}")
                
        except Exception as e:
            print(f"更新光谱图显示失败: {e}")
    
    def format_data_for_display(self, data: Dict) -> str:
        """格式化数据用于显示"""
        try:
            content = []
            content.append(f"Data Generated by: {data['data_generated_by']}")
            content.append(f"File Version: {data['file_version']}")
            content.append(f"文件名称: {data['filename']}")
            content.append(f"原始文件名: {data.get('original_filename', '')}")     # 新增：原始文件名
            content.append(f"生成时间戳: {data.get('datetime_str', '')}")         # 新增：时间戳
            content.append(f"操作员: {data['operator']}")
            content.append(f"描述: {data['description']}")
            content.append(f"扫描时间: {data['scan_time']}")
            content.append(f"积分时间(ms): {data['integration_time']}")
            content.append(f"激光功率(mW): {data['laser_power']}")
            content.append(f"平均次数: {data['average_count']}")
            content.append(f"当前测量: {data['current_measurement']}")             # 修改：更清楚的显示
            content.append(f"采集模式: {data['acquisition_mode']}")
            content.append(f"采样间隔: {data['sampling_interval']}")
            content.append(f"设备型号: {data['device_model']}")
            content.append(f"像素个数: {data['pixel_count']}")
            content.append(f"设备序列号: {data['device_serial']}")
            content.append(f"文件名: {data['hole_id']}")
            content.append("")
            content.append("拉曼光谱数据:")
            content.append("拉曼位移\t强度(a.u.)")
            
            # 只显示前100个数据点，避免界面过长
            for i, (shift, intensity) in enumerate(zip(data['raman_shift'], data['intensity'])):
                if i < 100:
                    content.append(f"{shift:.2f}\t{intensity:.2f}")
                elif i == 100:
                    content.append("...")
                    break
                    
            return "\n".join(content)
            
        except Exception as e:
            print(f"格式化显示数据失败: {e}")
            return "数据格式化失败"

    def auto_load_saved_file(self, filepath: str):
        """自动加载保存的文件到编辑器中"""
        try:
            if not self.editor:
                return
                
            # 使用专门的方法加载测量结果，不影响现有文件的显示
            if hasattr(self.editor, 'load_measurement_result_file'):
                self.editor.load_measurement_result_file(filepath)
                print(f"已自动加载测量结果文件: {filepath}")
            elif hasattr(self.editor, 'load_data_files'):
                # 备选方案：调用现有方法，但这可能影响文件显示顺序
                self.editor.load_data_files([filepath])
                print(f"已自动加载文件到编辑器: {filepath}")
            else:
                print("编辑器没有相应的文件加载方法")
                
        except Exception as e:
            print(f"自动加载文件失败: {e}")
    
    def _get_current_work_directory(self):
        """获取当前的工作目录，优先从主窗口的菜单栏获取"""
        try:
            # 方法1：从编辑器向上遍历找到主窗口
            widget = self.editor
            main_window = None
            while widget:
                if hasattr(widget, 'menu_bar'):
                    main_window = widget
                    break
                widget = widget.parent() if hasattr(widget, 'parent') and widget.parent else None
            
            # 方法2：如果方法1失败，尝试直接从QApplication获取主窗口
            if not main_window:
                from PyQt6.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    for widget in app.topLevelWidgets():
                        if hasattr(widget, 'menu_bar'):
                            main_window = widget
                            break
            
            # 如果找到主窗口，尝试从菜单栏获取工作目录
            if main_window and hasattr(main_window, 'menu_bar'):
                menu_bar = main_window.menu_bar
                if hasattr(menu_bar, 'work_dir') and menu_bar.work_dir:
                    print(f"[调试] 从菜单栏获取工作目录: {menu_bar.work_dir}")
                    # 同时更新编辑器的工作目录，保持同步
                    if self.editor:
                        self.editor.work_directory = menu_bar.work_dir
                    return menu_bar.work_dir
            
            # 如果无法从菜单栏获取，则从编辑器获取
            if self.editor and hasattr(self.editor, 'work_directory'):
                editor_work_dir = self.editor.work_directory
                print(f"[调试] 从编辑器获取工作目录: {editor_work_dir}")
                return editor_work_dir
            
            # 最后使用默认值
            default_dir = os.path.join(os.getcwd(), 'exported_data')
            print(f"[调试] 使用默认工作目录: {default_dir}")
            return default_dir
            
        except Exception as e:
            print(f"[调试] 获取工作目录失败: {e}")
            return os.path.join(os.getcwd(), 'exported_data')

    def _auto_disable_laser(self):
        """自动关闭激光（如果激光已开启）"""
        try:
            print("检查激光状态，如果已开启则自动关闭...")
            
            # 获取主窗口
            if not self.editor:
                print("编辑器实例未初始化")
                return False
            
            # 尝试获取主窗口
            main_window = self.editor.parent()
            while main_window and not hasattr(main_window, 'sidebar'):
                main_window = main_window.parent()
            
            if not main_window or not hasattr(main_window, 'sidebar'):
                print("无法找到主窗口或侧边栏")
                return False
            
            # 获取侧边栏
            sidebar = main_window.sidebar
            if not sidebar:
                print("无法获取侧边栏")
                return False
            
            # 检查激光器连接状态
            if not hasattr(sidebar, 'laser_connected') or not sidebar.laser_connected:
                print("激光器未连接，跳过激光状态检查")
                return True
            
            # 尝试创建激光控制器实例来检查激光状态
            try:
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                spectrumeter_dir = os.path.join(os.path.dirname(current_dir), 'spectrumeter')
                if spectrumeter_dir not in sys.path:
                    sys.path.insert(0, spectrumeter_dir)
                
                from laser_controller_usb import LaserController
                
                # 创建激光控制器实例
                laser_controller = LaserController()
                
                # 检查激光器状态
                status = laser_controller.get_status_summary()
                
                # 检查串口连接状态
                if not status.get("serial_connected", False):
                    print("激光器串口未连接，无法检查激光状态")
                    return False
                
                # 检查硬件电源开关状态
                if not status.get("hardware_power_on", False):
                    print("激光器硬件电源开关未打开，无需关闭激光")
                    return True
                
                # 检查激光是否已开启（软件状态）
                if laser_controller.is_enabled:
                    print("检测到激光已开启，正在自动关闭...")
                    
                    # 显示用户提示
                    try:
                        from PyQt6.QtWidgets import QMessageBox
                        QMessageBox.information(None, "激光控制", "检测到激光已开启，正在自动关闭激光以准备测量...")
                    except Exception as e:
                        print(f"显示提示信息失败: {e}")
                    
                    # 关闭激光
                    if laser_controller.disable_laser():
                        print("激光已成功关闭")
                        
                        # 等待一段时间确保激光完全关闭
                        import time
                        time.sleep(1.0)
                        
                        # 再次检查激光状态
                        status_after = laser_controller.get_status_summary()
                        if not laser_controller.is_enabled:
                            print("激光状态确认：已成功关闭")
                            return True
                        else:
                            print("激光关闭失败，状态检查显示激光仍处于开启状态")
                            return False
                    else:
                        print("激光关闭命令执行失败")
                        return False
                else:
                    print("激光未开启，无需关闭")
                    return True
                    
            except Exception as e:
                print(f"自动关闭激光过程中发生错误: {e}")
                return False
            finally:
                # 清理激光控制器实例
                try:
                    if 'laser_controller' in locals():
                        laser_controller.close()
                except Exception as e:
                    print(f"清理激光控制器实例失败: {e}")
            
        except Exception as e:
            print(f"自动关闭激光失败: {e}")
            return False

    def _check_communication_status(self):
        """检查通讯状态 - 确保激光器和光谱仪都已连接"""
        try:
            # 获取主窗口
            if not self.editor:
                print("编辑器实例未初始化")
                return False
            
            # 尝试获取主窗口
            main_window = self.editor.parent()
            while main_window and not hasattr(main_window, 'sidebar'):
                main_window = main_window.parent()
            
            if not main_window or not hasattr(main_window, 'sidebar'):
                print("无法找到主窗口或侧边栏")
                return False
            
            # 获取侧边栏
            sidebar = main_window.sidebar
            if not sidebar:
                print("无法获取侧边栏")
                return False
            
            # 检查激光器连接状态
            if not hasattr(sidebar, 'laser_connected'):
                print("侧边栏没有laser_connected属性")
                return False
                
            if not sidebar.laser_connected:
                print("激光器未连接，请先连接激光器（点击侧边栏通讯按钮）")
                return False
            
            # 检查光谱仪连接状态
            if not hasattr(sidebar, 'spectrometer_connected'):
                print("侧边栏没有spectrometer_connected属性")
                return False
                
            if not sidebar.spectrometer_connected:
                print("光谱仪未连接，请先连接光谱仪（点击侧边栏通讯按钮）")
                return False
            
            print("通讯状态检查通过：激光器和光谱仪都已连接")
            return True
            
        except Exception as e:
            print(f"检查通讯状态失败: {e}")
            return False

    # 辅助函数：获取当前用户名
    def _get_current_user(self):
        if hasattr(self, 'editor') and hasattr(self.editor, '_get_current_user'):
            return self.editor._get_current_user()
        return 'unknown'
    
    def _force_ui_refresh(self):
        """强制刷新界面，确保参数更新立即显示"""
        try:
            if self.editor:
                # 强制处理所有待处理的事件
                from PyQt6.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    app.processEvents()
                    print("[调试] 界面强制刷新完成")
        except Exception as e:
            print(f"[调试] 界面强制刷新失败: {e}")
 
# 使用示例
def create_integrated_measurement_logic(editor_instance):
    """创建整合测量逻辑实例"""
    logic = IntegratedMeasurementLogic(editor_instance)
    
    # 连接信号到编辑器的方法
    if editor_instance:
        logic.update_filename_signal.connect(editor_instance.filename_display.setText)
        logic.update_laser_power_signal.connect(editor_instance.laser_power_display.setText)
        logic.update_integration_time_signal.connect(editor_instance.integration_time_display.setText)
        logic.update_progress_signal.connect(editor_instance.measurement_progress_display.setText)
        
        # 连接孔位颜色更新信号（需要实现相应的方法）
        logic.update_hole_color_signal.connect(editor_instance.update_hole_color)
        logic.measurement_completed_signal.connect(editor_instance.on_measurement_completed)
    
    return logic