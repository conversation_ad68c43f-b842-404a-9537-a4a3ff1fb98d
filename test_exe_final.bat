@echo off
echo ========================================
echo View21 拉曼光谱分析系统 - 最终打包测试
echo ========================================
echo.

echo 检查打包文件是否存在...
if exist "dist\view21_minimal\view21.exe" (
    echo ✓ 最新打包文件存在: dist\view21_minimal\view21.exe
) else (
    echo ✗ 最新打包文件不存在
    goto :error
)

if exist "dist\view21\view21.exe" (
    echo ✓ 旧版本存在: dist\view21\view21.exe （可能有pandas问题）
) else (
    echo ✗ 旧版本不存在
)

if exist "dist\view21.exe" (
    echo ✓ 单文件版本存在: dist\view21.exe （有DLL问题，不推荐）
) else (
    echo ✗ 单文件版本不存在
)

echo.
echo 检查关键依赖文件...
if exist "dist\view21_minimal\_internal\UserApplication.dll" (
    echo ✓ 光谱仪驱动文件存在
) else (
    echo ✗ 光谱仪驱动文件缺失
)

if exist "dist\view21_minimal\_internal\SiUSBXp.dll" (
    echo ✓ USB驱动文件存在
) else (
    echo ✗ USB驱动文件缺失
)

if exist "dist\view21_minimal\_internal\logo48.ico" (
    echo ✓ 应用图标存在
) else (
    echo ✗ 应用图标缺失
)

if exist "dist\view21_minimal\_internal\software_settings.json" (
    echo ✓ 软件设置文件存在
) else (
    echo ✗ 软件设置文件缺失
)

if exist "dist\view21_minimal\_internal\users.db" (
    echo ✓ 用户数据库存在
) else (
    echo ✗ 用户数据库缺失
)

echo.
echo 检查Python库文件...
if exist "dist\view21_minimal\_internal\pandas" (
    echo ✓ Pandas库文件存在
) else (
    echo ✗ Pandas库文件缺失
)

if exist "dist\view21_minimal\_internal\numpy" (
    echo ✓ NumPy库文件存在
) else (
    echo ✗ NumPy库文件缺失
)

if exist "dist\view21_minimal\_internal\PyQt6" (
    echo ✓ PyQt6库文件存在
) else (
    echo ✗ PyQt6库文件缺失
)

echo.
echo 检查打包大小...
for %%I in ("dist\view21_minimal") do echo 打包目录大小: %%~zI 字节

echo.
echo ========================================
echo 测试完成！
echo.
echo 🎉 推荐使用: dist\view21_minimal\view21.exe
echo 这是最新的完整打包版本，包含所有必要的依赖文件
echo.
echo 📋 特点:
echo   - 包含完整的Python环境和所有依赖库
echo   - 排除了PyQt5，只使用PyQt6
echo   - 包含硬件驱动DLL文件
echo   - 包含应用程序配置和数据文件
echo.
echo 🚀 如果要测试运行，请手动双击:
echo   dist\view21_minimal\view21.exe
echo.
echo 📦 分发说明:
echo   - 将整个 view21_minimal 文件夹复制给用户
echo   - 用户只需双击 view21.exe 即可运行
echo   - 确保用户系统已安装 Visual C++ Redistributable
echo ========================================
pause
goto :end

:error
echo.
echo 打包可能未完成或失败，请检查打包过程
pause

:end
