####################################################################################
##
## VOTable-Simple Specification
##
## This is the specification of the VOTable-Simple (VOTS) format, given as an
## example data table with comments and references.  This data table format is
## intended to provide a way of specifying metadata and data for simple tabular
## data sets.  This specification is intended as a subset of the VOTable data
## model and allow easy generation of a VOTable-compliant data structure.  This
## provides a uniform starting point for generating table documentation and
## performing database table creation and ingest.
##
## A python application is available which uses the STILTS java package to
## convert from a VOTS format to any of the (many) output formats supported by
## STILTS.  This application can also generate a documentation file (in
## reStructured Text format) or a Django model definition from a VOTS table.
##
## Key VOTable and STILTS references:
##  Full spec: http://www.ivoa.net/Documents/latest/VOT.html
##  Datatypes: http://www.ivoa.net/Documents/REC/VOTable/VOTable-20040811.html#ToC11
##  FIELD def: http://www.ivoa.net/Documents/REC/VOTable/VOTable-20040811.html#ToC25 
##  STILTS   : http://www.star.bris.ac.uk/~mbt/stilts/
##
## The VOTable-Simple format consists of header information followed by the tabular
## data elements.  The VOTS header lines are all preceded by a single '#' character.
## Comments are preceded by '##' at the beginning of a line.
##
## The VOTS header defines the metadata associated with the table. In the
## VOTable-Simple format words in all CAPS (followed by ::) refer to the
## corresponding metadata elements in the VOTable specification.  For instance
## the DESCRIPTION:: keyword precedes the lines that are used in the VOTable
## <DESCRIPTION /> element.  The COOSYS::, PARAM::, and FIELD:: keywords are
## each followed by a whitespace-delimited table that defines the corresponding
## VOTable elements and attributes.
##
## The actual table data must follow the header and consist of space or tab delimited 
## data fields.  The chosen delimiter must be used consistently throughout the table.
##
##----------------------------------------------------------------------------------
## Table description, corresponding to the VOTable TABLE::DESCRIPTION element.
##----------------------------------------------------------------------------------
# DESCRIPTION::
# This is a sample table that shows a proposed format for generation of tables
# for the C-COSMOS collaboration.  This format is compatible with simple 'awk' or
# S-mongo style processing but also allows full self-documentation and conversion
# to more robust data formats (FITS, VOTable, postgres database ingest, etc).
# 
##----------------------------------------------------------------------------------
## Coordinate system specification COOSYS.  This is a "future" feature, as the
## current conversion code does not use this field.
##----------------------------------------------------------------------------------
# COOSYS::
# ID     equinox      epoch     system
# J2000  J2000.        J2000.   eq_FK5
# 
##----------------------------------------------------------------------------------
## Set the TABLE::PARAM values, which are values that apply for the entire table.
##----------------------------------------------------------------------------------
# PARAM::
# name     datatype     value        description
# version  string       1.1         'Table version'
# date     string       2007/12/01  'Table release date'
# 
##----------------------------------------------------------------------------------
## Define the column names via the FIELD element.  The attributes 'name',
## 'datatype', 'unit', and 'description' are required.  Optional attributes are:
## 'width', 'precision', 'ucd', 'utype', 'ref', and 'type'.
## See http://www.ivoa.net/Documents/REC/VOTable/VOTable-20040811.html#ToC25 for
## the VOTable definitions.
## Allowed values of datatype are:
##   boolean, unsignedByte, short, int, long, string, float, double
## Units: (from http://www.ivoa.net/Documents/REC/VOTable/VOTable-20040811.html#sec:unit)
##  The quantities in a column of the table may be expressed in some physical
##  unit, which is specified by the unit attribute of the FIELD. The syntax of
##  the unit string is defined in reference [3]; it is basically written as a
##  string without blanks or spaces, where the symbols . or * indicate a
##  multiplication, / stands for the division, and no special symbol is
##  required for a power. Examples are unit="m2" for m2, unit="cm-2.s-1.keV-1"
##  for cm-2s-1keV-1, or unit="erg/s" for erg s-1. The references [3] provide
##  also the list of the valid symbols, which is essentially restricted to the
##  Systeme International (SI) conventions, plus a few astronomical extensions
##  concerning units used for time, angular, distance and energy measurements.
##----------------------------------------------------------------------------------
# FIELD::
# name    datatype        unit        ucd    description
# id      int             ''          'meta.id'   'C-COSMOS short identifier number'
# name    string          ''          ''   'C-COSMOS long identifier name'
# ra      double          deg         'meta.cryptic'    'Right Ascension'
# dec     double          deg         ''    Declination
# flux    float           erg/cm2/s   ''    Flux
#
##----------------------------------------------------------------------------------
## Now the actual field data in the order specified by the FIELD:: list.
## The data fields can be separated by tabs or spaces.  If using spaces,
## any fields that contain a space must be enclosed in single quotes.
##
12     'CXOCS J193423+022312'  150.01212  2.52322  1.21e-13
13     'CXOCS J193322+024444'  150.02323  2.54444  1.21e-14
14     'CXOCS J195555+025555'  150.04444  2.55555  1.21e-15
