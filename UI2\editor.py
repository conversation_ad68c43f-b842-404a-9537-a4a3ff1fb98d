#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入所需的PyQt6模块
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QSplitter, QFrame,
    QTextEdit, QGraphicsView, QGraphicsScene, QGraphicsItem, QSizePolicy, QToolTip, QGraphicsProxyWidget,
    QMenu, QFormLayout, QLineEdit, QGraphicsSimpleTextItem, QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem,
    QDialog, QColorDialog, QMessageBox, QSpinBox, QComboBox, QDateEdit, QGroupBox
)
from PyQt6.QtCore import Qt, QTimer, QPointF, QRectF, pyqtSignal, QSize, QPoint, QDate
from PyQt6.QtGui import (
    <PERSON>A<PERSON>,QColor, QPalette, QPen, QBrush, QPainter, QPainterPath, QWheelEvent, QFont, QMouseEvent, QPixmap, QIcon
)

# 导入matplotlib相关模块
import matplotlib
import matplotlib.pyplot as plt
matplotlib.use('TkAgg')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Noto Sans CJK SC', 'Noto Sans CJK', 'Noto Sans CJK JP', 'Noto Sans CJK TC', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

matplotlib.use('Qt5Agg')  # 使用Qt5Agg后端，兼容PyQt6
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
from matplotlib.animation import FuncAnimation
from matplotlib.widgets import Button
import numpy as np
from scipy.signal import savgol_filter, find_peaks
from scipy.sparse import diags
from scipy import sparse
from scipy.sparse.linalg import spsolve
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
from PyQt6.QtWidgets import QMessageBox
from PyQt6.QtWidgets import QApplication
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import random
# 导入孔位处理脚本
try:
    from hole_position_processor import HolePositionProcessor
except ImportError:
    # 如果导入失败，尝试从上级目录导入
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    try:
        from hole_position_processor import HolePositionProcessor
    except ImportError:
        print("警告: 无法导入孔位处理脚本")
        HolePositionProcessor = None

# 导入整合测量逻辑
try:
    from integrated_measurement_logic import IntegratedMeasurementLogic
except ImportError:
    print("警告: 无法导入整合测量逻辑")
    IntegratedMeasurementLogic = None

import inspect
from log_manager import log_user_action
from language_manager import get_text, set_language

class SpectrumProcessor:
    """光谱数据处理类"""
    
    @staticmethod
    def smooth(wavelength, intensity, window_length=21, polyorder=3):
        """
        平滑处理 - 使用 Savitzky-Golay 滤波
        """
        try:
            n = len(intensity)
            # 保证 window_length 为奇数且不超过数据长度
            window_length = int(window_length)
            if window_length % 2 == 0:
                window_length += 1

            # 限制最大范围
            if window_length >= n:
                window_length = n - 1 if n % 2 == 1 else n - 2

            # 限制最小合法范围
            if window_length <= polyorder:
                window_length = polyorder + 2
                if window_length % 2 == 0:
                    window_length += 1

            return savgol_filter(intensity, window_length, polyorder)

        except Exception as e:
            print(f"平滑处理错误: {e}")
            return intensity.copy()
    
    
    @staticmethod
    def normalize(wavelength, intensity, method='max'):
        """
        归一化处理 - 简单最大值归一化
        """
        try:
            # 检查数据是否有效
            if np.all(intensity == 0):
                return intensity.copy()
            
            # 首先进行基线校正，确保所有数据为正
            min_val = np.min(intensity)
            adjusted_intensity = intensity.copy()
            if min_val < 0:
                adjusted_intensity -= min_val  # 移除负值
            
            # 最大值归一化
            max_val = np.max(adjusted_intensity)
            if max_val <= 0:
                return intensity.copy()
                
            # 执行归一化 - 简单地除以最大值
            normalized = adjusted_intensity / max_val
            
            return normalized
            
        except Exception as e:
            print(f"归一化处理错误: {e}")
            return intensity.copy()
    
    @staticmethod
    def baseline_correction(wavelength, intensity, lam=1e5, p=0.01, niter=20):
        """
        基线校正 - 使用Asymmetric Least Squares (ALS)算法
        """
        try:
            L = len(intensity)
            
            # 防止输入数据过少
            if L < 10:
                return intensity.copy()
                
            # 创建差分矩阵
            #D = diags([1, -2, 1], [0, -1, -2], shape=(L, L-2))
            D = sparse.diags([1, -2, 1], [0, -1, -2], shape=(L, L - 2)).tocsc()   
            # 初始化权重
            w = np.ones(L)
            
            # 迭代求解
            for i in range(niter):
                W = diags(w, 0)
                Z = W + lam * D.dot(D.transpose())
                z = spsolve(Z, w * intensity)
                w = p * (intensity > z) + (1-p) * (intensity < z)
            corrected = intensity - z
            corrected[corrected < 0] = 0  # 将负值设为0
            # 返回校正后的数据
            return corrected
        except Exception as e:
            print(f"基线校正错误: {e}")
            return intensity.copy()

    @staticmethod
    def calculate_fwhm(wavelength, intensity, peak_indices):
        """
        计算峰的半高宽 (Full Width at Half Maximum)
        严格按照C#代码逻辑实现
        
        参数:
            wavelength: 波长/拉曼位移数组
            intensity: 强度数组
            peak_indices: 峰值索引数组
            
        返回:
            fwhm_values: 每个峰对应的半高宽数组
        """
        fwhm_values = []
        
        # 先对整个曲线进行基线校准，仅用于FWHM计算
        try:
            baseline_corrected_intensity = SpectrumProcessor.baseline_correction(wavelength, intensity.copy())
        except Exception as e:
            print(f"基线校准失败，使用原始数据: {e}")
            baseline_corrected_intensity = intensity.copy()
        
        # 获取峰值对应的强度值
        peak_intensities = [baseline_corrected_intensity[idx] for idx in peak_indices]
        
        x_length = 0
        while x_length < len(peak_indices):
            for index24 in range(len(peak_intensities)):
                # 从峰值位置向左递减查找半高点
                for index23 in range(peak_indices[x_length], -1, -1):
                    if baseline_corrected_intensity[index23] <= peak_intensities[index24] / 2:
                        # 计算FWHM（左右半高点线性插值法）
                        peak_idx = peak_indices[index24]
                        half_height = peak_intensities[index24] / 2.0
                        
                        # 向左找半高点
                        left = peak_idx
                        while left > 0 and baseline_corrected_intensity[left] > half_height:
                            left -= 1
                        
                        # 计算左半高点位置（线性插值）
                        if left == 0:
                            left_x = wavelength[0]
                        else:
                            left_x = wavelength[left] + (half_height - baseline_corrected_intensity[left]) * (wavelength[left + 1] - wavelength[left]) / (baseline_corrected_intensity[left + 1] - baseline_corrected_intensity[left])
                        
                        # 向右找半高点
                        right = peak_idx
                        while right < len(baseline_corrected_intensity) - 1 and baseline_corrected_intensity[right] > half_height:
                            right += 1
                        
                        # 计算右半高点位置（线性插值）
                        if right == len(baseline_corrected_intensity) - 1:
                            right_x = wavelength[len(baseline_corrected_intensity) - 1]
                        else:
                            right_x = wavelength[right - 1] + (half_height - baseline_corrected_intensity[right - 1]) * (wavelength[right] - wavelength[right - 1]) / (baseline_corrected_intensity[right] - baseline_corrected_intensity[right - 1])
                        
                        # 计算FWHM
                        fwhm = right_x - left_x
                        
                        # 根据C#代码逻辑，对特定波长范围且半高宽大于8.0的峰进行修正
                        if right_x > 400 and right_x < 440 and fwhm > 8.0:
                            fwhm = fwhm - 1.5
                        
                        fwhm_values.append(fwhm)
                        x_length += 1
                        break
                    
                if x_length >= len(peak_indices):
                    break
        
        return np.array(fwhm_values)

class MatplotlibSpectrumView(QWidget):
    """基于matplotlib的光谱显示视图，可嵌入到PyQt6界面中，实现spectrometer_viewer.py的所有功能"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_data()  # 先设置数据
        self.setup_ui()    # 再设置UI
        self.modified_peaks = {}  # key: 曲线索引或文件名, value: {'wavelengths':..., 'intensities':...}
        self._is_shift_down = False
        self._is_ctrl_down = False
        # 恢复事件绑定
        self.canvas.mpl_connect('key_press_event', self.on_key_press)
        self.canvas.mpl_connect('key_release_event', self.on_key_release)
        self.canvas.mpl_connect('button_press_event', self.on_peak_mouse_click)
        self.canvas.mpl_connect('scroll_event', self.on_scroll)

    def on_key_press(self, event):
        if event.key == 'shift':
            self._is_shift_down = True
        if event.key == 'control':
            self._is_ctrl_down = True

    def on_key_release(self, event):
        if event.key == 'shift':
            self._is_shift_down = False
        if event.key == 'control':
            self._is_ctrl_down = False

    def on_scroll(self, event):
        """处理鼠标滚轮事件，实现在鼠标位置进行缩放"""
        if event.inaxes != self.ax:
            return
        
        # 获取当前坐标轴范围
        xlim = self.ax.get_xlim()
        ylim = self.ax.get_ylim()
        
        # 获取鼠标位置
        xdata = event.xdata
        ydata = event.ydata
        
        if xdata is None or ydata is None:
            return
        
        # 缩放因子
        zoom_factor = 0.1
        if event.button == 'up':  # 向上滚动，放大
            scale_factor = 1 - zoom_factor
        elif event.button == 'down':  # 向下滚动，缩小
            scale_factor = 1 + zoom_factor
        else:
            return
        
        # 计算新的坐标轴范围，以鼠标位置为中心缩放
        x_left = xdata - (xdata - xlim[0]) * scale_factor
        x_right = xdata + (xlim[1] - xdata) * scale_factor
        y_bottom = ydata - (ydata - ylim[0]) * scale_factor
        y_top = ydata + (ylim[1] - ydata) * scale_factor
        
        # 设置新的坐标轴范围
        self.ax.set_xlim([x_left, x_right])
        self.ax.set_ylim([y_bottom, y_top])
        
        # 刷新画布
        self.canvas.draw()

    def setup_data(self):
        """初始化数据相关变量"""
        self.processor = SpectrumProcessor()
        self.processing_mode = "none"
        self.is_smoothed = False
        self.is_normalized = False
        self.is_baseline_corrected = False
        self.max_history = 1000
        self.intensity_history = []
        self.timestamp_history = []
        # 新增：保存原始历史曲线数据
        self._intensity_history_original = []
        self.display_raw = True
        self.refresh_rate = 1.0
        self.y_min = 0.0
        self.y_max = 2000.0
        self.fixed_y_axis = False
        # 新增：网格线显示状态
        self._show_grid = True
        
        # 新增：坐标轴单位切换相关变量
        self._use_pixel_coordinates = False  # 默认使用拉曼位移
        self._raman_shift_data = None  # 保存原始拉曼位移数据
        self._pixel_data = None  # 保存像素坐标数据
        
        # 数据锁
        self.data_lock = threading.Lock()
        self.new_data_available = False
        
        # 初始化空数据
        self.wavelength = np.array([])
        self.intensity = np.array([])
        self.intensity_raw = np.array([])
        self.intensity_original = np.array([])
        
        # 鼠标跟踪相关
        self.cursor_annotation = None
        self.cursor_point = None
        
        # 是否显示峰值点
        self.show_peak_point = False
        self.show_processed_line = False  # 新增：是否显示优化线
        self.show_raw_line = False  # 新增：是否显示原始数据线
        
        # 新增：记录当前模式
        self._zoom_mode = False
        self._pan_mode = False
        
    def setup_ui(self):
        """初始化UI组件"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建matplotlib图形和画布
        self.figure = Figure(figsize=(12, 9), dpi=80)
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        
        # 创建matplotlib工具栏（不添加到布局，实现隐藏）
        self.toolbar = NavigationToolbar(self.canvas, self)
        # layout.addWidget(self.toolbar)  # 注释掉，不显示工具栏
        layout.addWidget(self.canvas)
        
        # 设置初始图形
        self.setup_plot()
        
        # 连接鼠标移动事件
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.canvas.mpl_connect('button_press_event', self.on_peak_mouse_click)  # 新增：峰值点击事件
        
        # 创建动画更新
        self.setup_animation()

    def setup_plot(self):
        """设置初始图形"""
        self.ax.clear()
        #self.ax.set_title('拉曼光谱')
        # 根据当前坐标系设置正确的标签
        if self._use_pixel_coordinates:
            self.ax.set_xlabel(get_text('pixel_coordinates'))
        else:
            self.ax.set_xlabel(get_text('raman_shift_cm'))
        self.ax.set_ylabel(get_text('intensity'))
        # 设置网格线宽和颜色，保证视觉突出
        self.ax.grid(self._show_grid, linestyle='--', alpha=0.7, linewidth=1.2, color='#cccccc')
        # 让画布自适应填充
        self.canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.canvas.updateGeometry()
        self.figure.tight_layout()
        # 颜色列表

        self.color_list = ['b', 'g', 'r', 'c', 'm', 'y', 'k', 'orange', 'purple', 'brown', 'teal', 'navy']
        self.line_processed, = self.ax.plot([], [], color='b', linewidth=2, label=None)
        self.line_raw, = self.ax.plot([], [], color='b', linestyle='--', linewidth=2, alpha=0.6, label=None)
        self.history_lines = []
        for i in range(self.max_history):
            color = self.color_list[(i+1) % len(self.color_list)]
            # label 先设为 None，后续 update_plot 动态设置
            line, = self.ax.plot([], [], color=color, linewidth=2, alpha=0.6, label=None)
            self.history_lines.append(line)
        self.peak_point, = self.ax.plot([], [], 'ro', markersize=4)
        self.peak_texts = []
        self.cursor_annotation = self.ax.annotate('', xy=(0, 0), xytext=(10, 10),
                                   textcoords='offset points',
                                   bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                                   arrowprops=dict(arrowstyle='->'),
                                   visible=False)
        self.cursor_point, = self.ax.plot([], [], 'go', markersize=4, alpha=0.7, visible=False)
        self.curve_labels = []  # 新增：用于存储每条曲线的图例标签
        self.curve_colors = []  # 新增：用于存储每条曲线的颜色
        self.canvas.draw()

    def set_curve_labels(self, labels):
        """设置每条曲线的图例标签"""
        self.curve_labels = labels
    
    def set_curve_colors(self, colors):
        """设置每条曲线的颜色"""
        self.curve_colors = colors


    def setup_animation(self):
        """设置动画更新"""
        def update_plot(_):
            if not self.new_data_available:
                return self.line_processed, 
            with self.data_lock:
                # 检查是否有数据
                if self.wavelength is None or len(self.wavelength) == 0:
                    # 没有数据时，清除所有曲线
                    self.line_processed.set_data([], [])
                    self.line_raw.set_data([], [])
                    self.line_raw.set_visible(False)
                    
                    # 清除历史曲线
                    for line in self.history_lines:
                        line.set_data([], [])
                    
                    # 清除峰值点
                    self.peak_point.set_data([], [])
                    if hasattr(self, 'peak_texts'):
                        for t in self.peak_texts:
                            t.remove()
                        self.peak_texts = []
                    
                    # 清除图例
                    if hasattr(self.ax, 'legend_') and self.ax.legend_ is not None:
                        self.ax.legend_.remove()
                        self.ax.legend_ = None
                    
                    # 重置坐标轴
                    self.ax.set_xlim(0, 1)
                    self.ax.set_ylim(0, 1)
                    
                    self.new_data_available = False
                    return self.line_processed,
                
                # 主曲线颜色 - 使用统一颜色管理
                if hasattr(self, 'curve_colors') and len(self.curve_colors) > 0:
                    main_color = self.curve_colors[0]
                else:
                    main_color = self.color_list[0]
                self.line_processed.set_color(main_color)
                self.line_processed.set_data(self.wavelength, self.intensity)
                # 只在还原模式下显示原始光谱线
                if self.processing_mode == "none" and len(self.intensity_raw) > 0:
                    self.line_raw.set_color(main_color)
                    self.line_raw.set_data(self.wavelength, self.intensity_raw)
                    self.line_raw.set_visible(True)
                else:
                    self.line_raw.set_data([], [])
                    self.line_raw.set_visible(False)
                # 历史数据线 - 使用统一颜色管理
                for i, (line, hist_data) in enumerate(zip(self.history_lines, self.intensity_history)):
                    if hasattr(self, 'curve_colors') and len(self.curve_colors) > i + 1:
                        color = self.curve_colors[i + 1]
                    else:
                        color = self.color_list[(i+1) % len(self.color_list)]
                    line.set_color(color)
                    # 使用历史曲线各自的x轴数据
                    if hasattr(self, 'wavelength_history') and i < len(self.wavelength_history):
                        hist_x = self.wavelength_history[i]
                        # 根据当前坐标系转换x轴数据
                        if getattr(self, '_use_pixel_coordinates', False):
                            # 如果当前使用像素坐标，转换历史曲线的x轴
                            hist_x_display = np.arange(len(hist_x))
                        else:
                            # 使用原始拉曼位移
                            hist_x_display = hist_x
                        line.set_data(hist_x_display, hist_data)
                    else:
                        # 回退到使用主曲线的x轴（兼容性）
                        line.set_data(self.wavelength, hist_data)
                    line.set_alpha(0.6)
                # 更新峰值点（多峰显示，仅在寻峰后显示）
                if self.show_peak_point and hasattr(self, 'all_peaks_data') and self.all_peaks_data:
                    # 清除已有的峰值点和标签
                    self.peak_point.set_data([], [])
                    if hasattr(self, 'peak_texts'):
                        for t in self.peak_texts:
                            t.remove()
                    self.peak_texts = []
                    
                    # 为每个文件的峰值创建不同颜色的标记
                    #peak_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
                    peak_colors=['red']

                    for i, peak_data in enumerate(self.all_peaks_data):
                        if len(peak_data['peak_wavelengths']) > 0:
                            # 为每个文件的峰值创建单独的散点图
                            #color = peak_colors[i % len(peak_colors)]
                            color='#FF9999'
                            
                            # 创建峰值散点图（如果不存在则创建）
                            if not hasattr(self, f'peak_points_{i}'):
                                setattr(self, f'peak_points_{i}', self.ax.scatter([], [], c=color, s=80, marker='v', alpha=0.95, zorder=10))
                            peak_scatter = getattr(self, f'peak_points_{i}')
                            peak_scatter.set_offsets(np.column_stack([peak_data['peak_wavelengths'], peak_data['peak_intensities']]))
                            peak_scatter.set_color(color)  # 确保颜色正确设置
                            
                            # 为每个峰值添加标签（显示(x, y, 半高宽)在三角形上方）
                            for i, (x, y) in enumerate(zip(peak_data['peak_wavelengths'], peak_data['peak_intensities'])):
                                # 在绘制标签前获取y轴范围
                                ymin, ymax = self.ax.get_ylim()
                                y_offset = (ymax - ymin) * 0.02  # 2% y轴高度
                                
                                # 获取半高宽值
                                fwhm = peak_data['fwhm_values'][i] if 'fwhm_values' in peak_data and i < len(peak_data['fwhm_values']) else 0.0
                                
                                # 根据当前坐标系设置单位
                                if getattr(self, '_use_pixel_coordinates', False):
                                    x_unit = ""
                                    fwhm_unit = ""
                                else:
                                    x_unit = "cm-1"
                                    fwhm_unit = "cm-1"
                                
                                # 显示坐标和半高宽
                                label_text = f'({x:.1f}, {y:.1f}, {fwhm:.1f})'
                                t = self.ax.text(x, y + y_offset, label_text, 
                                    color=color, fontsize=9, ha='center', va='bottom',
                                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8, edgecolor=color, linewidth=0.8))
                                self.peak_texts.append(t)
                        else:
                            # 如果该曲线没有峰值，清空对应的散点图
                            if hasattr(self, f'peak_points_{i}'):
                                peak_scatter = getattr(self, f'peak_points_{i}')
                                peak_scatter.set_offsets(np.column_stack([[], []]))
                else:
                    # 不显示任何峰点和标签
                    self.peak_point.set_data([], [])
                    if hasattr(self, 'peak_texts'):
                        for t in self.peak_texts:
                            t.remove()
                        self.peak_texts = []
                    
                    # 清除所有峰值散点图
                    i = 0
                    while hasattr(self, f'peak_points_{i}'):
                        peak_scatter = getattr(self, f'peak_points_{i}')
                        peak_scatter.set_offsets(np.column_stack([[], []]))
                        i += 1
                #
                # 更新标题
                #self.ax.set_title('拉曼光谱')
                
                # 自动调整坐标轴范围
                if len(self.wavelength) > 0:
                    min_x = np.min(self.wavelength)
                    max_x = np.max(self.wavelength)
                    x_margin = (max_x - min_x) * 0.02 if max_x > min_x else 1
                    self.ax.set_xlim(min_x - x_margin, max_x + x_margin)

                    # y轴范围用所有曲线的最小/最大值，安全处理NaN/Inf
                    all_data = []
                    # 只在还原模式下包含原始数据
                    if self.processing_mode == "none":
                        all_data.append(self.intensity_raw)
                    all_data.extend(self.intensity_history)
                    if self.processing_mode != "none":
                        all_data.append(self.intensity)
                    
                    if all_data:
                        # 安全处理NaN/Inf值，过滤出有效数据
                        all_valid_values = []
                        for data in all_data:
                            if len(data) > 0:
                                # 过滤出有限值
                                finite_values = data[np.isfinite(data)]
                                if len(finite_values) > 0:
                                    all_valid_values.extend(finite_values)
                        
                        if len(all_valid_values) > 0:
                            y_min = np.min(all_valid_values)
                            y_max = np.max(all_valid_values)
                            
                            # 双重检查确保y_min和y_max都是有限值
                            if np.isfinite(y_min) and np.isfinite(y_max):
                                if y_min != y_max:
                                    y_margin = (y_max - y_min) * 0.05
                                    self.ax.set_ylim(y_min - y_margin, y_max + y_margin)
                                else:
                                    # y_min == y_max的情况，设置一个默认范围
                                    self.ax.set_ylim(y_min - 1, y_max + 1)
                            else:
                                # 如果仍然有问题，使用默认范围
                                print("警告：y轴数据包含无效值，使用默认范围")
                                self.ax.set_ylim(0, 1000)
                        else:
                            # 没有有效数据，使用默认范围
                            print("警告：没有有效的y轴数据，使用默认范围")
                            self.ax.set_ylim(0, 1000)
                
                # --- 新增：自动添加图例 ---
                legend_lines = []
                legend_labels = []
                if hasattr(self, 'curve_labels') and self.curve_labels:
                    # 主曲线
                    legend_lines.append(self.line_processed)
                    legend_labels.append(self.curve_labels[0] if len(self.curve_labels) > 0 else "主曲线")
                    # 历史曲线
                    for i, line in enumerate(self.history_lines):
                        if i < len(self.intensity_history) and i+1 < len(self.curve_labels):
                            legend_lines.append(line)
                            legend_labels.append(self.curve_labels[i+1])
                else:
                    legend_lines.append(self.line_processed)
                    legend_labels.append("主曲线")
                self.ax.legend(legend_lines, legend_labels, loc='upper right', fontsize=10, frameon=True)
                # --- END ---

                # 修复：每次刷新时同步网格线显示状态
                if self._show_grid:
                    self.ax.grid(True, linestyle='--', alpha=0.7, linewidth=1.2, color='#cccccc')
                else:
                    self.ax.grid(False)
                    for gridline in self.ax.get_xgridlines() + self.ax.get_ygridlines():
                        gridline.set_visible(False)

                self.new_data_available = False
                
            return self.line_processed,
        
        # 创建动画
        self.ani = FuncAnimation(
            self.figure, update_plot, interval=1000/self.refresh_rate,
            blit=False, cache_frame_data=False
        )

    def load_data_from_file(self, file_path):
        """从文件加载数据"""
        try:
            # 使用新的文件读取器
            try:
                from spectrum_file_reader import SpectrumFileReader
            except ImportError:
                import sys
                import os
                sys.path.append(os.path.dirname(__file__))
                from spectrum_file_reader import SpectrumFileReader
            
            reader = SpectrumFileReader()
            file_info, spectrum_data = reader.read_file(file_path)
            
            # 保存文件信息
            self.file_info = file_info
            
            # 提取光谱数据
            self.wavelength = spectrum_data[:, 0]  # 拉曼位移
            self.intensity = spectrum_data[:, 1]   # 强度
            self.intensity_original = self.intensity.copy()
            self.intensity_raw = self.intensity.copy()
            
            # 新增：保存拉曼位移和像素坐标数据
            self._raman_shift_data = self.wavelength.copy()
            self._pixel_data = np.arange(len(self.wavelength))
            
            # 初始化历史数据
            self.intensity_history = [self.intensity.copy()]
            self.timestamp_history = [0]
            # 新增：保存原始历史曲线数据
            self._intensity_history_original = [self.intensity.copy()]
            
            # 显示文件信息
            print("文件信息:")
            print(reader.get_file_info_display())
            print(f"光谱数据形状: {spectrum_data.shape}")
            print(f"拉曼位移范围: {np.min(self.wavelength):.2f} - {np.max(self.wavelength):.2f}")
            print(f"强度范围: {np.min(self.intensity):.2f} - {np.max(self.intensity):.2f}")
            
            # 更新显示
            self.new_data_available = True
            print('拉曼光谱数据加载完成')
            return True
            
        except Exception as e:
            print(f'文件读取失败: {e}')
            QMessageBox.critical(self, "读取失败", f"文件读取失败: {e}")
            return False
    

    
    
    def on_mouse_move(self, event):
        """处理鼠标移动事件，显示鼠标位置对应的坐标和强度值"""
        if event.inaxes != self.ax or event.xdata is None or event.ydata is None:
            self.cursor_annotation.set_visible(False)
            self.cursor_point.set_visible(False)
            self.canvas.draw_idle()
            return
            
        with self.data_lock:
            x = event.xdata
            y = event.ydata
            
            # 根据当前坐标系统设置正确的标签
            if getattr(self, '_use_pixel_coordinates', False):
                # 像素坐标
                x_label = "像素"
                x_unit = ""
            else:
                # 拉曼位移
                x_label = "拉曼位移"
                x_unit = "cm-1"
            
            # 注释内容为鼠标当前位置(x, y)
            self.cursor_annotation.xy = (x, y)
            self.cursor_annotation.set_text(f'{x_label}: {x:.1f}{x_unit}\n强度: {y:.1f}')
            self.cursor_annotation.set_fontsize(9)
            self.cursor_annotation.set_visible(True)
            # 可选：绿色点直接跟随鼠标（如不需要可注释掉）
            self.cursor_point.set_data([x], [y])
            self.cursor_point.set_visible(True)
            self.canvas.draw_idle()
    
    def apply_smooth(self):
        """应用平滑处理，对所有曲线分别处理 - 根据当前坐标系进行操作"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
            
            # 根据当前坐标系选择正确的x轴数据
            x_data = self.wavelength  # 当前显示的坐标轴数据（可能是拉曼位移或像素坐标）
            
            # 主曲线
            self.intensity = self.processor.smooth(x_data, self.intensity.copy())
            # 历史曲线
            if self.intensity_history:
                self.intensity_history = [self.processor.smooth(x_data, arr.copy()) for arr in self.intensity_history]
            if "smooth" not in self.processing_mode:
                if self.processing_mode == "none":
                    self.processing_mode = "smooth"
                else:
                    self.processing_mode += "+smooth"
            self.is_smoothed = True
            self.is_normalized = False
            self.is_baseline_corrected = False
            self.show_peak_point = False
            self.detected_peaks = None
            self.all_peaks_data = None  # 清除所有峰值数据
            self.new_data_available = True

    def apply_normalize(self):
        """应用归一化处理，对所有曲线分别处理 - 根据当前坐标系进行操作"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
            
            # 根据当前坐标系选择正确的x轴数据
            x_data = self.wavelength  # 当前显示的坐标轴数据（可能是拉曼位移或像素坐标）
            
            self.intensity = self.processor.normalize(x_data, self.intensity.copy())
            if self.intensity_history:
                self.intensity_history = [self.processor.normalize(x_data, arr.copy()) for arr in self.intensity_history]
            if "normalize" not in self.processing_mode:
                if self.processing_mode == "none":
                    self.processing_mode = "normalize"
                else:
                    self.processing_mode += "+normalize"
            self.is_normalized = True
            self.show_peak_point = False
            self.detected_peaks = None
            self.all_peaks_data = None  # 清除所有峰值数据
            self.new_data_available = True

    def apply_baseline_correction(self):
        """应用基线校正，对所有曲线分别处理 - 根据当前坐标系进行操作"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0:
                return
            
            # 根据当前坐标系选择正确的x轴数据
            x_data = self.wavelength  # 当前显示的坐标轴数据（可能是拉曼位移或像素坐标）
            
            self.intensity = self.processor.baseline_correction(x_data, self.intensity.copy())
            if self.intensity_history:
                self.intensity_history = [self.processor.baseline_correction(x_data, arr.copy()) for arr in self.intensity_history]
            if "baseline" not in self.processing_mode:
                if self.processing_mode == "none":
                    self.processing_mode = "baseline"
                else:
                    self.processing_mode += "+baseline"
            self.is_baseline_corrected = True
            self.show_peak_point = False
            self.detected_peaks = None
            self.all_peaks_data = None  # 清除所有峰值数据
            self.new_data_available = True

    def apply_restore(self):
        """还原到原始数据，重置所有处理链 - 根据当前坐标系进行操作"""
        with self.data_lock:
            if self.wavelength is None or len(self.wavelength) == 0 or self.intensity_original is None:
                return
            
            # 重置处理状态
            self.processing_mode = "none"
            self.is_smoothed = False
            self.is_normalized = False
            self.is_baseline_corrected = False
            
            # 还原数据到原始状态
            self.intensity = self.intensity_original.copy()
            
            # 还原历史曲线到原始数据（如果有保存的原始历史数据）
            if hasattr(self, '_intensity_history_original') and self._intensity_history_original:
                self.intensity_history = [arr.copy() for arr in self._intensity_history_original]
            
            # 关闭寻峰显示
            self.show_peak_point = False
            self.detected_peaks = None
            self.all_peaks_data = None  # 清除所有峰值数据
            
            # 根据当前坐标系设置正确的x轴数据
            if self._use_pixel_coordinates:
                self.wavelength = self._pixel_data.copy()
            else:
                self.wavelength = self._raman_shift_data.copy()
            
            self.new_data_available = True

    def clear_spectrum(self):
        """清除所有谱图曲线和数据"""
        with self.data_lock:
            # 清除所有数据
            self.wavelength = np.array([])
            self.intensity = np.array([])
            self.intensity_raw = np.array([])
            self.intensity_original = np.array([])
            self.intensity_history = []
            self.timestamp_history = []
            
            # 新增：清除原始历史曲线数据
            self._intensity_history_original = []
            
            # 新增：清除历史曲线的x轴数据
            if hasattr(self, 'wavelength_history'):
                self.wavelength_history = []
            
            # 新增：清除坐标轴相关数据
            self._raman_shift_data = None
            self._pixel_data = None
            self._use_pixel_coordinates = False
            
            # 重置处理状态
            self.processing_mode = "none"
            self.is_smoothed = False
            self.is_normalized = False
            self.is_baseline_corrected = False
            self.show_peak_point = False
            self.detected_peaks = None
            self.all_peaks_data = None  # 清除所有峰值数据
            
            # 清除曲线标签
            self.curve_labels = []
            
            # 立即清除图形显示
            self.line_processed.set_data([], [])
            self.line_raw.set_data([], [])
            self.line_raw.set_visible(False)
            
            # 清除历史曲线
            for line in self.history_lines:
                line.set_data([], [])
            
            # 清除峰值点
            self.peak_point.set_data([], [])
            if hasattr(self, 'peak_texts'):
                for t in self.peak_texts:
                    t.remove()
                self.peak_texts = []
            
            # 清除所有峰值散点图
            i = 0
            while hasattr(self, f'peak_points_{i}'):
                peak_scatter = getattr(self, f'peak_points_{i}')
                peak_scatter.set_offsets(np.column_stack([[], []]))
                i += 1
            
            # 清除图例
            if hasattr(self.ax, 'legend_') and self.ax.legend_ is not None:
                self.ax.legend_.remove()
                self.ax.legend_ = None
            
            # 重置坐标轴
            self.ax.set_xlim(0, 1)
            self.ax.set_ylim(0, 1)
            
            # 重置x轴标签
            self.ax.set_xlabel('拉曼位移 (cm-1)')
            
            # 标记需要更新
            self.new_data_available = True
            
            # 强制重绘
            self.canvas.draw()

    # def show_peak(self, snr_threshold=2.0, intensity_threshold=0.1, distance=10):
    #     """
    #     显示所有检测到的峰值点。支持所有算法处理后的数据寻峰。
    #     """
    #     with self.data_lock:
    #         # 根据当前处理模式调整寻峰参数
    #         if "normalize" in self.processing_mode:
    #             intensity_threshold = 0.1  # 归一化后强度在0-1之间
    #             return
    #         elif "baseline" in self.processing_mode:
    #             intensity_threshold = 50   # 基线校准后可能需要较低阈值
    #         elif "smooth" in self.processing_mode:
    #             snr_threshold = 1.5        # 平滑后噪声减少，可以降低信噪比要求
            
    #         self.show_peak_point = True
    #         self.new_data_available = True
    #         # 检测峰
    #         peaks = self.detect_peaks(intensity_threshold, snr_threshold, distance)
    #         self.detected_peaks = peaks

    # def detect_peaks(self, intensity_threshold=600, snr_threshold=3.0, distance=10):
    #     """
    #     直接用原始强度，峰高阈值和信噪比阈值进行多峰检测。
    #     参数：
    #         intensity_threshold: 峰强度阈值（原始强度）
    #         snr_threshold: 峰高/噪声的最小比值
    #         distance: 相邻两个峰之间的最小间隔（点数）
    #     返回: 峰索引数组
    #     """
    #     if self.intensity is None or len(self.intensity) == 0:
    #         return np.array([])
    #     # 估算噪声（用中位数绝对偏差）
    #     noise = np.median(np.abs(self.intensity - np.median(self.intensity)))
    #     if noise == 0:
    #         noise = 1e-6
    #     # 多峰检测
    #     peaks, props = find_peaks(
    #         self.intensity,
    #         height=intensity_threshold,
    #         distance=distance,
    #         prominence=snr_threshold * noise
    #     )
    #     return peaks

    def contextMenuEvent(self, event):
        from PyQt6.QtWidgets import QMenu
        menu = QMenu(self)
        # 只保留缩放、平移、保存图片、显示/隐藏网格线
        zoom_action = menu.addAction(get_text("zoom"))
        zoom_action.setCheckable(True)
        zoom_action.setChecked(self._zoom_mode)
        pan_action = menu.addAction(get_text("pan"))
        pan_action.setCheckable(True)
        pan_action.setChecked(self._pan_mode)
        save_action = menu.addAction(get_text("save_image"))
        # 新增：显示/隐藏网格线
        grid_action = menu.addAction(get_text("show_grid") if not self._show_grid else get_text("hide_grid"))
        grid_action.setCheckable(True)
        grid_action.setChecked(self._show_grid)
        
        # 新增：恢复默认大小按钮
        menu.addSeparator()  # 添加分隔线
        reset_view_action = menu.addAction(get_text("reset_view_to_default"))
        
        # 新增：坐标轴单位切换菜单项
        menu.addSeparator()  # 添加分隔线
        if self._use_pixel_coordinates:
            coord_action = menu.addAction(get_text("switch_to_raman_shift"))
        else:
            coord_action = menu.addAction(get_text("switch_to_pixel_coordinates"))
        
        # 弹出菜单
        action = menu.exec(event.globalPos())
        if action == zoom_action:
            if not self._zoom_mode:
                self.toolbar.zoom()
                self._zoom_mode = True
                self._pan_mode = False
            else:
                # 退出缩放模式需要再次调用zoom()来切换状态
                self.toolbar.zoom()
                self._zoom_mode = False
        elif action == pan_action:
            if not self._pan_mode:
                self.toolbar.pan()
                self._pan_mode = True
                self._zoom_mode = False
            else:
                # 退出平移模式需要再次调用pan()来切换状态
                self.toolbar.pan()
                self._pan_mode = False
        elif action == save_action:
            self.toolbar.save_figure()
        elif action == grid_action:
            self._show_grid = not self._show_grid
            if self._show_grid:
                self.ax.grid(True, linestyle='--', alpha=0.7, linewidth=1.2, color='#cccccc')
            else:
                self.ax.grid(False)
                for gridline in self.ax.get_xgridlines() + self.ax.get_ygridlines():
                    gridline.set_visible(False)
            self.canvas.draw_idle()
        elif action == reset_view_action:
            # 新增：处理恢复默认大小
            self.reset_view_to_default()
        elif action == coord_action:
            # 新增：处理坐标轴单位切换
            self.toggle_coordinate_system()
    #波数校正功能
    def save_modified_peaks(self, curve_idx, peak_data):
        """
        保存用户操作后的峰值
        """
        self.modified_peaks[curve_idx] = {
            'wavelengths': peak_data['peak_wavelengths'].copy(),
            'intensities': peak_data['peak_intensities'].copy(),
            'fwhm_values': peak_data.get('fwhm_values', np.array([])).copy()  # 保存半高宽信息
        }
        print(f"已保存用户修改后的峰值，曲线索引: {curve_idx}, 峰数: {len(peak_data['peak_wavelengths'])}")



    def reset_view_to_default(self):
        """恢复视图到默认大小"""
        with self.data_lock:
            if len(self.wavelength) > 0:
                # 恢复X轴到完整数据范围
                x_min = np.min(self.wavelength)
                x_max = np.max(self.wavelength)
                self.ax.set_xlim(x_min, x_max)
                
                # 恢复Y轴到自动调整范围
                if len(self.intensity) > 0:
                    y_min = np.min(self.intensity)
                    y_max = np.max(self.intensity)
                    y_margin = (y_max - y_min) * 0.05 if y_max > y_min else 1
                    self.ax.set_ylim(y_min - y_margin, y_max + y_margin)
                
                # 刷新画布
                self.canvas.draw_idle()
                print("视图已恢复到默认大小")

    def toggle_coordinate_system(self):
        """切换坐标轴单位（拉曼位移/像素坐标）"""
        with self.data_lock:
            if self._raman_shift_data is None or self._pixel_data is None:
                return
            
            # 切换坐标系统
            self._use_pixel_coordinates = not self._use_pixel_coordinates
            
            if self._use_pixel_coordinates:
                # 切换到像素坐标
                # 如果之前在拉曼位移坐标下进行了寻峰，先清除峰值显示并还原到原始数据
                if self.show_peak_point:
                    print("从拉曼位移寻峰切换到像素坐标，清除峰值并还原原始数据")
                    self.show_peak_point = False
                    self.all_peaks_data = None
                    self.detected_peaks = None
                    # 还原到原始数据
                    self.intensity = self.intensity_original.copy()
                    # 还原历史曲线到原始数据（如果有的话）
                    if hasattr(self, '_intensity_history_original') and self._intensity_history_original:
                        self.intensity_history = [arr.copy() for arr in self._intensity_history_original]
                    # 重置处理状态
                    self.processing_mode = "none"
                    self.is_smoothed = False
                    self.is_normalized = False
                    self.is_baseline_corrected = False
                
                # 切换坐标轴数据
                self.wavelength = self._pixel_data.copy()
                # 更新x轴标签
                self.ax.set_xlabel('像素坐标')
                print("已切换到像素坐标")
            else:
                # 切换到拉曼位移
                # 清除峰值显示（如果有的话）
                if self.show_peak_point:
                    self.ax.set_xlabel('拉曼位移(cm-1)')
                    print("从像素坐标切换到拉曼位移，清除峰值显示")
                    self.show_peak_point = False
                    self.all_peaks_data = None
                    self.detected_peaks = None
                
                # 切换坐标轴数据
                self.wavelength = self._raman_shift_data.copy()
                # 更新x轴标签
                self.ax.set_xlabel('拉曼位移 (cm-1)')
                print("已切换到拉曼位移")
            
            # 标记需要更新显示
            self.new_data_available = True

    def on_peak_mouse_click(self, event):
        self.canvas.setFocus()
        modifiers = QApplication.keyboardModifiers()
        is_shift = modifiers & Qt.KeyboardModifier.ShiftModifier
        is_ctrl = modifiers & Qt.KeyboardModifier.ControlModifier
        print(f"event.key={event.key}, event.button={event.button}, shift={self._is_shift_down}, ctrl={self._is_ctrl_down}")
        
        # 检查基本条件
        if event.inaxes != self.ax or event.button != 1:
            return
        
        x_click = event.xdata
        y_click = event.ydata
        if x_click is None or y_click is None:
            return
        
        # shift+左键：删除最近的峰值（需要已有峰值数据）
        if self._is_shift_down:
            if not hasattr(self, 'all_peaks_data') or not self.all_peaks_data:
                print("没有峰值数据可删除")
                return
                
            print("执行shift+左键：删除峰值")
            multi_curve = len(self.all_peaks_data) > 1
            selected_curve_idx = 0
            if multi_curve:
                selected_curve_idx = getattr(self, '_selected_curve_idx', 0)
            peak_data = self.all_peaks_data[selected_curve_idx]
            
            if len(peak_data['peak_wavelengths']) == 0:
                print("没有峰值可删除")
                return
            dists = np.hypot(peak_data['peak_wavelengths'] - x_click, peak_data['peak_intensities'] - y_click)
            idx = np.argmin(dists)
            peak_data['peak_wavelengths'] = np.delete(peak_data['peak_wavelengths'], idx)
            peak_data['peak_intensities'] = np.delete(peak_data['peak_intensities'], idx)
            # 同时删除对应的半高宽
            if 'fwhm_values' in peak_data and len(peak_data['fwhm_values']) > idx:
                peak_data['fwhm_values'] = np.delete(peak_data['fwhm_values'], idx)
            print(f"删除了峰值 {idx}，剩余峰值数: {len(peak_data['peak_wavelengths'])}")
            
            # 如果所有峰值都被删除了，清理显示
            if len(peak_data['peak_wavelengths']) == 0:
                # 检查是否所有曲线都没有峰值了
                all_empty = True
                for data in self.all_peaks_data:
                    if len(data['peak_wavelengths']) > 0:
                        all_empty = False
                        break
                
                if all_empty:
                    # 所有曲线都没有峰值，完全清除峰值显示
                    self.show_peak_point = False
                    self.all_peaks_data = None
                    print("所有峰值已删除，清除峰值显示")
            
            self.new_data_available = True
            self.canvas.draw_idle()
            self.save_modified_peaks(selected_curve_idx, peak_data)
            
        # ctrl+左键：添加新峰值（在任何算法状态下都可以）
        elif self._is_ctrl_down:
            print("执行ctrl+左键：添加峰值")
            
            # 如果没有峰值数据结构，创建一个
            if not hasattr(self, 'all_peaks_data') or not self.all_peaks_data:
                # 初始化峰值数据结构
                processing_mode = getattr(self, 'processing_mode', 'none')
                current_use_pixel = getattr(self, '_use_pixel_coordinates', False)
                coord_type = "像素坐标" if current_use_pixel else "拉曼位移"
                
                self.all_peaks_data = []
                
                # 为主曲线创建峰值数据
                self.all_peaks_data.append({
                    'file_path': None,
                    'wavelength': self.wavelength.copy(),
                    'intensity': self.intensity.copy(),
                    'peaks': np.array([]),
                    'peak_wavelengths': np.array([]),
                    'peak_intensities': np.array([]),
                    'fwhm_values': np.array([]),  # 添加半高宽字段
                    'processing_mode': processing_mode,
                    'coordinate_type': coord_type
                })
                
                # 为历史曲线创建峰值数据
                if hasattr(self, 'intensity_history') and self.intensity_history:
                    for idx, hist_intensity in enumerate(self.intensity_history):
                        if hasattr(self, 'wavelength_history') and len(self.wavelength_history) > idx:
                            hist_wavelength = self.wavelength_history[idx]
                        else:
                            hist_wavelength = self.wavelength.copy()
                        
                        self.all_peaks_data.append({
                            'file_path': None,
                            'wavelength': hist_wavelength.copy(),
                            'intensity': hist_intensity.copy(),
                            'peaks': np.array([]),
                            'peak_wavelengths': np.array([]),
                            'peak_intensities': np.array([]),
                            'fwhm_values': np.array([]),  # 添加半高宽字段
                            'processing_mode': processing_mode,
                            'coordinate_type': coord_type
                        })
                
                self.show_peak_point = True
                print(f"创建新的峰值数据结构，包含{len(self.all_peaks_data)}条曲线")
            
            # 确定操作的曲线
            multi_curve = len(self.all_peaks_data) > 1
            selected_curve_idx = 0
            if multi_curve:
                selected_curve_idx = getattr(self, '_selected_curve_idx', 0)
            peak_data = self.all_peaks_data[selected_curve_idx]
            
            # 添加峰值：在点击位置找到最近的曲线点
            if selected_curve_idx == 0:
                # 主曲线
                curve_x = self.wavelength
                curve_y = self.intensity
            else:
                # 历史曲线
                if hasattr(self, 'intensity_history') and len(self.intensity_history) > selected_curve_idx - 1:
                    curve_y = self.intensity_history[selected_curve_idx - 1]
                    # 使用对应的x轴数据
                    if hasattr(self, 'wavelength_history') and len(self.wavelength_history) > selected_curve_idx - 1:
                        curve_x = self.wavelength_history[selected_curve_idx - 1]
                    else:
                        curve_x = self.wavelength  # 回退到主曲线x轴
                else:
                    print("无法找到对应的历史曲线数据")
                    return
            
            idx = np.abs(curve_x - x_click).argmin()
            x_new = curve_x[idx]
            y_new = curve_y[idx]
            peak_data['peak_wavelengths'] = np.append(peak_data['peak_wavelengths'], x_new)
            peak_data['peak_intensities'] = np.append(peak_data['peak_intensities'], y_new)
            
            # 计算新添加峰值的半高宽
            if 'fwhm_values' not in peak_data:
                peak_data['fwhm_values'] = np.array([])
            fwhm_new = self.processor.calculate_fwhm(curve_x, curve_y, [idx])[0]
            peak_data['fwhm_values'] = np.append(peak_data['fwhm_values'], fwhm_new)
            
            print(f"为曲线{selected_curve_idx}添加了新峰值: ({x_new:.2f}, {y_new:.2f}, FWHM:{fwhm_new:.1f})，总峰值数: {len(peak_data['peak_wavelengths'])}")
            
            # 确保显示峰值
            self.show_peak_point = True
            self.new_data_available = True
            self.canvas.draw_idle()
            self.save_modified_peaks(selected_curve_idx, peak_data)



# 编辑器主界面，包含文件内容、Raman光谱显示、参数区和控制按钮#bo
class Editor(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化软件设置相关属性
        self.output_format = 'txt'  # 默认输出格式
        self.work_directory = './exported_data'  # 默认工作目录
        self.max_curves = 100  # 默认最大曲线数
        self.default_x_axis = "拉曼位移"  # 默认横坐标
        
        
        # 先初始化参数显示控件
        self.filename_display = QLabel("未加载")
        #self.filename_display.setStyleSheet("color: #1976d2; font-weight: bold;")
        self.filename_display.setWordWrap(True)
        self.filename_display.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.laser_power_display = QLabel("0")
        self.integration_time_display = QLabel("0")
        self.measurement_progress_display = QLabel("0 / 0")
        self.measurement_progress_display.setStyleSheet("color: #1976d2; font-weight: bold;")

        # 初始化命名规则和激光参数控件
        # 命名规则控件
        self.sample_name_edit = QLineEdit()
        self.sample_name_edit.setPlaceholderText("请输入样品名")
        self.sample_name_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QLineEdit:focus {
                border: 1px solid #1976d2;
            }
        """)
        
        self.department_edit = QLineEdit()
        self.department_edit.setPlaceholderText("请输入科室")
        self.department_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QLineEdit:focus {
                border: 1px solid #1976d2;
            }
        """)
        
        self.patient_id_edit = QLineEdit()
        self.patient_id_edit.setPlaceholderText("请输入患者编号")
        self.patient_id_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QLineEdit:focus {
                border: 1px solid #1976d2;
            }
        """)
        
        # 日期选择器
        self.date_picker = QDateEdit(QDate.currentDate())
        self.date_picker.setDisplayFormat("yyyy-M-d")
        self.date_picker.setCalendarPopup(True)
        self.date_picker.setStyleSheet("""
            QDateEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QDateEdit:focus {
                border: 1px solid #1976d2;
            }
        """)
        
        # 激光参数控件
        self.laser_power_spin = QSpinBox()
        self.laser_power_spin.setRange(0, 100)
        self.laser_power_spin.setValue(100)
        self.laser_power_spin.setSuffix(" %")
        self.laser_power_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QSpinBox:focus {
                border: 1px solid #1976d2;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border: none;
                background: #f0f0f0;
                width: 16px;
                border-radius: 2px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #e0e0e0;
            }
        """)
        
        self.integration_time_spin = QSpinBox()
        self.integration_time_spin.setRange(1, 999999)
        self.integration_time_spin.setValue(1000)
        self.integration_time_spin.setSuffix(" ms")
        self.integration_time_spin.setSingleStep(100)
        self.integration_time_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QSpinBox:focus {
                border: 1px solid #1976d2;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border: none;
                background: #f0f0f0;
                width: 16px;
                border-radius: 2px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #e0e0e0;
            }
        """)
        
        self.average_count_spin = QSpinBox()
        self.average_count_spin.setRange(1, 999999)
        self.average_count_spin.setValue(1)
        self.average_count_spin.setSuffix(" 次")
        self.average_count_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 4px;
                background: white;
            }
            QSpinBox:focus {
                border: 1px solid #1976d2;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border: none;
                background: #f0f0f0;
                width: 16px;
                border-radius: 2px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #e0e0e0;
            }
        """)
        
        # 确定按钮
        self.confirm_params_btn = QPushButton(get_text("confirm_params"))
        self.confirm_params_btn.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)

        self.loaded_files = []  # 存储所有已加载的文件路径
        
        # 添加颜色管理系统
        self.file_colors = {}  # 存储文件路径与颜色的映射关系
        self.color_palette = [
            QColor(31, 119, 180),   # 蓝色
            QColor(255, 127, 14),   # 橙色
            QColor(44, 160, 44),    # 绿色
            QColor(214, 39, 40),    # 红色
            QColor(148, 103, 189),  # 紫色
            QColor(140, 86, 75),    # 棕色
            QColor(227, 119, 194),  # 粉色
            QColor(127, 127, 127),  # 灰色
            QColor(188, 189, 34),   # 橄榄色
            QColor(23, 190, 207),   # 青色
            QColor(255, 152, 150),  # 浅红色
            QColor(174, 199, 232),  # 浅蓝色
        ]
        self.color_index = 0  # 当前颜色索引
        
        # # 初始化孔位处理器
        # self.hole_processor = None
        # if HolePositionProcessor:
        #     try:
        #         self.hole_processor = HolePositionProcessor()
        #         print("孔位处理器初始化成功")
        #     except Exception as e:
        #         print(f"孔位处理器初始化失败: {e}")
        
        # 初始化整合测量逻辑
        self.integrated_measurement_logic = None
        try:
            from integrated_measurement_logic import create_integrated_measurement_logic
            self.integrated_measurement_logic = create_integrated_measurement_logic(self)
            print("整合测量逻辑初始化成功")
        except Exception as e:
            print(f"整合测量逻辑初始化失败: {e}")
        
        self.setup_ui()

        # 根据默认横坐标设置应用坐标系
        self.apply_default_coordinate_system()

    def get_file_color(self, file_path):
        """为文件分配颜色，如果已存在则返回现有颜色"""
        if file_path not in self.file_colors:
            # 分配新颜色
            color = self.color_palette[self.color_index % len(self.color_palette)]
            self.file_colors[file_path] = color
            self.color_index += 1
        return self.file_colors[file_path]
    
    def get_matplotlib_color(self, qcolor):
        """将QColor转换为matplotlib颜色格式"""
        return (qcolor.red()/255.0, qcolor.green()/255.0, qcolor.blue()/255.0)


        # 文件名标签和左上角布局
        self.file_label = QLabel("未加载")
        self.top_left_widget = QWidget()
        self.top_left_layout = QVBoxLayout(self.top_left_widget)
        self.top_left_layout.addWidget(self.file_label)
        # 将 self.top_left_widget 添加到主布局左上角
        # ...existing code...




    def _get_current_user(self):
            mainwin = self.parent() if hasattr(self, 'parent') else None
            if mainwin and hasattr(mainwin, 'user_manager') and hasattr(mainwin.user_manager, 'current_user'):
                return mainwin.user_manager.current_user
            if hasattr(self, 'current_user'):
                return self.current_user
            return 'unknown'



    def setup_ui(self):
        """初始化编辑器UI组件"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 垂直分割器，上部为文件内容和Raman视图，下部为参数和按钮
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上部区域：文件内容和Raman视图
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(0)
        
        # 水平分割器：左为文件内容，右为Raman视图及标题
        top_splitter = QSplitter(Qt.Orientation.Horizontal)
        top_splitter.setChildrenCollapsible(False) 

        # 文件内容显示（改为树状结构，根节点为"已打开谱图文件"）
        self.file_content = QTreeWidget()
        self.file_content.setHeaderHidden(True)
        self.file_content.setSelectionMode(QTreeWidget.SelectionMode.NoSelection)  # 禁用选择模式
        self.file_content.itemChanged.connect(self.on_file_item_changed)
        self.file_content.itemClicked.connect(self.on_file_item_clicked)
        self.file_content.mousePressEvent = self.on_file_content_mouse_press
        self.file_content.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        # 设置右键菜单策略
        self.file_content.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.file_content.customContextMenuRequested.connect(self.show_file_context_menu)
        # 创建根节点
        self.file_root_item = QTreeWidgetItem(self.file_content, [get_text("opened_spectrum_files")])
        # 使根节点可勾选
        self.file_root_item.setFlags(self.file_root_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
        self.file_root_item.setCheckState(0, Qt.CheckState.Unchecked)
        self.file_content.addTopLevelItem(self.file_root_item)
        # 第一次导入时展开根节点显示文件名
        self.file_content.expandItem(self.file_root_item)
        self.file_content.resizeColumnToContents(0)
        # 防递归死循环标志
        self._updating_check_state = False
        # 存储文件展开状态的字典
        self._file_expanded_states = {}
        top_splitter.addWidget(self.file_content)


        # 右侧：包含标题栏、网格按钮和RamanView
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)

        # Raman光谱显示区
        self.raman_view = MatplotlibSpectrumView()
        right_layout.addWidget(self.raman_view)
        top_splitter.addWidget(right_widget)

        if self.default_x_axis == "像素":
            self.raman_view._use_pixel_coordinates = True
            print("设置 RamanView 默认坐标系：像素坐标")
        else:
            self.raman_view._use_pixel_coordinates = False
            print("设置 RamanView 默认坐标系：拉曼位移")
        
        # 重新设置图形以应用坐标轴标签
        if hasattr(self.raman_view, 'setup_plot'):
            self.raman_view.setup_plot()

        # 设置初始分割比例
        top_splitter.setSizes([120, 880])
        top_layout.addWidget(top_splitter)
        splitter.addWidget(top_widget)

        

        bottom_widget = QWidget()
        #bottom_widget.setStyleSheet("background: transparent; border: 4px solid #8B0000;border-radius: 4px;")  # 整体灰色背景，添加边框
        bottom_widget.setStyleSheet("background:#E6F3FF")  # 整体灰色背景，添加边框
        bottom_layout = QHBoxLayout(bottom_widget)
        bottom_widget.setFixedHeight(170)
        bottom_layout.setContentsMargins(12, 12, 12, 12)  # 设置上下左右边距为12像素
        bottom_layout.setSpacing(20)

        # 1. 左侧命名规则区
        naming_widget = QWidget()
        naming_widget.setStyleSheet("background: #ffffff; border: 1px solid #ddd; border-radius: 4px;")
        naming_layout = QVBoxLayout(naming_widget)
        naming_layout.setContentsMargins(8, 8, 8, 8)
        naming_layout.setSpacing(8)
        
        # 命名规则标题
        self.naming_title = QLabel(get_text("naming_rules"))
        self.naming_title.setStyleSheet("color: #1976d2; font-weight: bold; font-size: 14px;")
        naming_layout.addWidget(self.naming_title)
        
        # 命名规则表单
        naming_form = QFormLayout()
        naming_form.setSpacing(6)
        naming_form.setContentsMargins(0, 0, 0, 0)
        
        # 保存标签引用以便语言切换时更新
        self.sample_name_label = QLabel(get_text("sample_name"))
        self.department_label = QLabel(get_text("department"))
        self.patient_id_label = QLabel(get_text("patient_id"))
        self.select_date_label = QLabel(get_text("select_date"))
        
        naming_form.addRow(self.sample_name_label, self.sample_name_edit)
        naming_form.addRow(self.department_label, self.department_edit)
        naming_form.addRow(self.patient_id_label, self.patient_id_edit)
        naming_form.addRow(self.select_date_label, self.date_picker)
        
        naming_layout.addLayout(naming_form)
        bottom_layout.addWidget(naming_widget)
        
        # 2. 激光参数设置区
        laser_widget = QWidget()
        laser_widget.setStyleSheet("background: #ffffff; border: 1px solid #ddd; border-radius: 4px;")
        laser_layout = QVBoxLayout(laser_widget)
        laser_layout.setContentsMargins(8, 8, 8, 8)
        laser_layout.setSpacing(8)
        
        # 激光参数标题
        self.laser_title = QLabel(get_text("laser_parameters"))
        self.laser_title.setStyleSheet("color: #1976d2; font-weight: bold; font-size: 14px;")
        laser_layout.addWidget(self.laser_title)
        
        # 激光参数表单
        laser_form = QFormLayout()
        laser_form.setSpacing(6)
        laser_form.setContentsMargins(0, 0, 0, 0)
        
        # 保存标签引用以便语言切换时更新
        self.laser_power_percent_label = QLabel(get_text("laser_power_percent"))
        self.integration_time_ms_label = QLabel(get_text("integration_time_ms"))
        self.average_count_label = QLabel(get_text("average_count"))
        
        laser_form.addRow(self.laser_power_percent_label, self.laser_power_spin)
        laser_form.addRow(self.integration_time_ms_label, self.integration_time_spin)
        laser_form.addRow(self.average_count_label, self.average_count_spin)
        
        laser_layout.addLayout(laser_form)
        
        # 确定按钮
        laser_layout.addWidget(self.confirm_params_btn)
        
        bottom_layout.addWidget(laser_widget)






        
        # 2. 中间参数显示区
        self.param_widget = QWidget()
        self.param_widget.setStyleSheet("background: #ffffff; border: none; outline: none;")  # 完全移除边框
        self.param_widget.setContentsMargins(2, 0, 2, 0) 
        #self.param_widget.setFixedWidth(500)  # 设置参数区为较窄宽度
        form_layout = QFormLayout(self.param_widget)
        form_layout.setSpacing(2)
        form_layout.setContentsMargins(2, 2, 2, 2)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignLeft)

        # ...参数区控件初始化与样式...
        self.label_filename = QLabel(get_text("current_test_filename"))
        self.label_filename.setStyleSheet("color: #1976d2; font-weight: bold;")
        self.label_progress = QLabel(get_text("measurement_progress"))
        self.label_progress.setStyleSheet("color: #1976d2; font-weight: bold;")
        
        # 保存参数显示标签引用
        self.param_laser_power_label = QLabel(get_text("laser_power"))
        self.param_integration_time_label = QLabel(get_text("integration_time"))
        
        form_layout.addRow(self.label_filename, self.filename_display)
        form_layout.addRow(self.filename_display)
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)  # 设置为水平线
        line.setFrameShadow(QFrame.Shadow.Sunken)  # 设置阴影效果
        line.setLineWidth(2)  # 设置线宽
        line.setStyleSheet("background-color: #bbb; min-height: 2px; max-height: 2px; border: none;")
        # 将分割线添加到表单布局中
        form_layout.addRow(line)
        form_layout.addRow(self.param_laser_power_label, self.laser_power_display)
        form_layout.addRow(self.param_integration_time_label, self.integration_time_display)
        form_layout.addRow(self.label_progress, self.measurement_progress_display)
        #bottom_layout.addWidget(self.param_widget, stretch=1)
        bottom_layout.addWidget(self.param_widget)
        # 3. 右侧控制按钮区
        control_panel = QWidget()
        control_panel.setStyleSheet("background: transparent; border: none; outline: none;")  # 完全移除边框
        control_layout = QHBoxLayout(control_panel)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(10)
        control_layout.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        # 设置控制面板的大小策略，允许垂直拉伸
        control_panel.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)


        

        # 右侧两个按钮垂直布局
        right_btns_widget = QWidget()
        right_btns_widget.setStyleSheet("background: transparent; border: none; outline: none;")  # 完全移除边框
        right_btns_layout = QVBoxLayout(right_btns_widget)
        right_btns_layout.setContentsMargins(0, 0, 0, 0)
        right_btns_layout.setSpacing(0)
        right_btns_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)
        # 设置右侧按钮容器的大小策略
        right_btns_widget.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)


        # 开始测量按钮（右上）
        self.start_measurement_btn = QPushButton()
        # 移除固定高度，让按钮自适应
        self.start_measurement_btn.setFixedHeight(200//3)
        self.start_measurement_btn.setFixedWidth(150)
        self.start_measurement_btn.setIcon(QIcon(r"./UI2/64/64_64/画板 55.png"))
        self.start_measurement_btn.setIconSize(QSize(200, 200))  # 设置合理的图标大小
        self.start_measurement_btn.setStyleSheet("""
            QPushButton {
                border: none !important;
                background: transparent !important;
            }
            QPushButton:hover {
                background: #e3f2fd !important;
                border-radius: 5px !important;
            }
        """)
        self.start_measurement_btn.setToolTip(get_text("start_measurement"))
        # 设置按钮的大小策略，允许垂直拉伸
        self.start_measurement_btn.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        self.start_measurement_btn.setEnabled(False)  # 初始不可用
        right_btns_layout.addWidget(self.start_measurement_btn, alignment=Qt.AlignmentFlag.AlignHCenter)

        # 中止测量按钮（右下）
        self.stop_measurement_btn = QPushButton()
        # 移除固定高度，让按钮自适应
        self.stop_measurement_btn.setFixedHeight(200//3)
        self.stop_measurement_btn.setFixedWidth(150)
        self.stop_measurement_btn.setIcon(QIcon(r"./UI2/64/64_64/中止测试.png"))
        self.stop_measurement_btn.setIconSize(QSize(200, 200))  # 设置合理的图标大小
        self.stop_measurement_btn.setStyleSheet("""
            QPushButton {
                border: none !important;
                background: transparent !important;
            }
            QPushButton:hover {
                background: #ffebee !important;
                border-radius: 5px !important;
            }
        """)
        self.stop_measurement_btn.setToolTip(get_text("stop_measurement"))
        # 设置按钮的大小策略，允许垂直拉伸
        self.stop_measurement_btn.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        self.stop_measurement_btn.setEnabled(False)  # 初始不可用
        right_btns_layout.addWidget(self.stop_measurement_btn, alignment=Qt.AlignmentFlag.AlignHCenter)

       
        # 添加到主控制区布局：右侧两个按钮垂直
        control_layout.addSpacing(5)
        control_layout.addWidget(right_btns_widget, alignment=Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        
        bottom_layout.addWidget(control_panel)

        # 用自定义边框控件包裹
        # bordered = CustomBorderWidget()
        # bordered.set_inner_widget(bottom_widget)

        splitter.addWidget(bottom_widget)

        # 设置初始分割比例
        splitter.setSizes([900, 80])
        layout.addWidget(splitter)
        
        # 连接信号与槽
        self.setup_connections()
        
        # 初始化测量状态和定时器
        self.measurement_state = "stopped"  # stopped, running, completed
        self.blink_timer = QTimer()
        self.blink_timer.timeout.connect(self.blink_selected_point)
        self.blink_state = False
        
        # 初始化测量点
        self.measurement_points = []
        self.selected_point = None


        
        # # 初始化孔位处理信号监听器
        # self.hole_signal_timer = QTimer()
        # self.hole_signal_timer.timeout.connect(self.check_hole_processing_signal)
        # self.hole_signal_timer.start(1000)  # 每秒检查一次信号文件

    def setup_connections(self):
        """设置按钮信号连接"""
        self.start_measurement_btn.clicked.connect(self.handle_start_measurement)
        self.stop_measurement_btn.clicked.connect(self.handle_stop_measurement)
        self.confirm_params_btn.clicked.connect(self.handle_confirm_params)
        

    def handle_confirm_params(self):
        """处理确定名称和参数按钮点击"""
        try:
            # 获取命名规则参数
            sample_name = self.sample_name_edit.text().strip()
            department = self.department_edit.text().strip()
            patient_id = self.patient_id_edit.text().strip()
            selected_date = self.date_picker.date()
            
            # 获取激光参数
            laser_power = self.laser_power_spin.value()
            integration_time = self.integration_time_spin.value()
            average_count = self.average_count_spin.value()
            
            # 验证必填字段
            if not sample_name:
                QMessageBox.warning(self, get_text("warning"), get_text("please_enter_sample_name"))
                return
            if not department:
                QMessageBox.warning(self, get_text("warning"), get_text("please_enter_department"))
                return
            if not patient_id:
                QMessageBox.warning(self, get_text("warning"), get_text("please_enter_patient_id"))
                return
            
            # 保存参数到实例变量
            self.naming_params = {
                'sample_name': sample_name,
                'department': department,
                'patient_id': patient_id,
                'selected_date': selected_date.toString("yyyy-MM-dd")  # 将QDate转换为字符串
            }
            
            self.laser_params = {
                'laser_power': laser_power,
                'integration_time': integration_time,
                'average_count': average_count
            }
            
            # 显示成功消息
            QMessageBox.information(self, get_text("success"), get_text("name_and_params_saved"))
            
            # 启用开始测量按钮（重新激活，即使之前被终止测量禁用）
            self.start_measurement_btn.setEnabled(True)
            self.stop_measurement_btn.setEnabled(False)  # 确保中止测量按钮禁用
            
            # 重置测量状态，允许重新开始测量
            self.measurement_state = "stopped"
            
            # 记录用户操作
            log_user_action("保存命名规则和激光参数", self._get_current_user(), {
                'naming_params': self.naming_params,
                'laser_params': self.laser_params
            })
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存参数时发生错误: {str(e)}")

    def handle_start_measurement(self):
        """处理开始测量按钮点击"""
        if not hasattr(self, 'measurement_state') or self.measurement_state == "stopped":
            # 立即更新按钮状态，提供即时反馈
            self.start_measurement_btn.setEnabled(False)
            self.stop_measurement_btn.setEnabled(True)
            
            # 先尝试开始测量
            success = self.start_integrated_measurement()
            if success:
                # 测量成功启动，更新状态
                self.measurement_state = "running"
                print("测量启动成功，按钮状态已更新")
            else:
                # 测量启动失败，恢复按钮状态
                self.measurement_state = "stopped"
                self.start_measurement_btn.setEnabled(False)  # 保持禁用，需要重新保存参数
                self.stop_measurement_btn.setEnabled(False)
                print("测量启动失败，按钮状态已恢复")
        elif self.measurement_state == "paused":
            QMessageBox.warning(self, "警告", "测量已暂停，请使用中止测量按钮选择操作")
        elif self.measurement_state == "completed":
            QMessageBox.information(self, "提示", "测量已完成，如需重新测量请先保存参数")
        else:
            QMessageBox.warning(self, "警告", "测量正在进行中，请先停止当前测量")

    def handle_stop_measurement(self):
        """处理中止测量按钮点击 - 立即终止测量"""
        # 立即更新按钮状态，提供即时反馈
        self.start_measurement_btn.setEnabled(False)  # 禁用开始按钮，需要重新保存参数
        self.stop_measurement_btn.setEnabled(False)
        
        if self.measurement_state == "running":
            # 立即终止测量
            print("立即终止测量...")
            self.reset_to_initial_state()
        elif self.measurement_state == "paused":
            # 如果测量已暂停，也立即终止
            print("立即终止暂停的测量...")
            self.reset_to_initial_state()
        elif self.measurement_state == "completed":
            QMessageBox.information(self, "提示", "测量已完成，无需终止操作")
            # 恢复按钮状态
            self.start_measurement_btn.setEnabled(False)  # 保持禁用，需要重新保存参数
            self.stop_measurement_btn.setEnabled(False)
        else:
            QMessageBox.warning(self, get_text("warning"), "当前没有正在进行的测量")
            # 恢复按钮状态
            self.start_measurement_btn.setEnabled(False)  # 保持禁用，需要重新保存参数
            self.stop_measurement_btn.setEnabled(False)



    def reset_to_initial_state(self):
        """回到初始化状态"""
        print("开始重置到初始状态...")
        
        # 立即停止所有测量相关的流程
        if hasattr(self, 'integrated_measurement_logic') and self.integrated_measurement_logic:
            print("停止整合测量逻辑...")
            self.integrated_measurement_logic.stop_measurement()
        
        # 立即停止闪烁定时器
        if hasattr(self, 'blink_timer'):
            print("停止闪烁定时器...")
            self.blink_timer.stop()
        
        # 立即更新测量状态
        self.measurement_state = "stopped"
        
        # 立即重置按钮状态
        self.start_measurement_btn.setEnabled(False)  # 禁用开始测量按钮，需要重新保存参数才能激活
        self.stop_measurement_btn.setEnabled(False)
        
        # 立即清空显示内容
        self.filename_display.setText("未加载")
        self.laser_power_display.setText("0")
        self.integration_time_display.setText("0")
        self.measurement_progress_display.setText("0 / 0")
        
        # 强制刷新界面
        self._force_ui_refresh()
        
        print("已重置到初始状态，所有测量流程已停止")
    
    def _force_ui_refresh(self):
        """强制刷新界面，确保按钮状态更新立即显示"""
        try:
            # 强制处理所有待处理的事件
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                app.processEvents()
                print("界面强制刷新完成")
        except Exception as e:
            print(f"界面强制刷新失败: {e}")
    
    def stop_integrated_measurement(self):
        """停止整合测量流程"""
        try:
            if self.integrated_measurement_logic:
                self.integrated_measurement_logic.stop_measurement()
            
            # 更新状态
            self.measurement_state = "stopped"
            
            # 更新按钮状态
            self.start_measurement_btn.setEnabled(True)
            self.stop_measurement_btn.setEnabled(False)
            
            # 停止闪烁定时器
            if hasattr(self, 'blink_timer'):
                self.blink_timer.stop()
            
            print("整合测量流程已停止")
            
        except Exception as e:
            print(f"停止整合测量流程失败: {e}")
    
    def start_measurement(self):
        """开始检测流程"""
        self.measurement_state = "running"
        # 更新按钮状态
        self.start_measurement_btn.setEnabled(False)
        self.stop_measurement_btn.setEnabled(True)
        self.blink_timer.start(500)  # 500ms闪烁
        
    def stop_measurement(self):
        """停止检测流程"""
        self.measurement_state = "completed"
        # 更新按钮状态
        self.start_measurement_btn.setEnabled(True)
        self.stop_measurement_btn.setEnabled(False)
        self.blink_timer.stop()
        
    def blink_selected_point(self):
        """闪烁选中的测量点"""
        if self.selected_point:
            self.blink_state = not self.blink_state
            color = QColor(255, 0, 0) if self.blink_state else QColor(255, 165, 0)
            self.selected_point.setBrush(QBrush(color))
            self.spectrum_view.scene().update()
        
    def update_parameter_display(self, params):
        """更新参数显示区内容"""
        log_user_action("参数变更", self._get_current_user(), params)
        self.filename_display.setText(params.get('filename', 'N/A'))
        self.laser_power_display.setText(str(params.get('laser_power', 0)))
        self.integration_time_display.setText(str(params.get('integration_time', 0)))
        current = params.get('current_measurement', 1)
        total = params.get('total_measurements', 1)
        self.measurement_progress_display.setText(f"{current} / {total}")


        
    def handle_data_management(self):
        """处理数据管理操作"""
        log_user_action("点击数据管理按钮", self._get_current_user(), {})
        # TODO: 实现数据管理逻辑
        pass
        
    def handle_baseline_calibration(self):
        """处理基线校准信号"""
        log_user_action("点击基线校准按钮", self._get_current_user(), {})
        if hasattr(self.raman_view, 'apply_baseline_correction'):
            self.raman_view.apply_baseline_correction()
        
    def handle_data_smoothing(self):
        """处理数据平滑信号"""
        log_user_action("点击平滑按钮", self._get_current_user(), {})
        if hasattr(self.raman_view, 'apply_smooth'):
            self.raman_view.apply_smooth()
        
    def handle_normalization(self):
        """处理归一化信号"""
        log_user_action("点击归一化按钮", self._get_current_user(), {})
        if hasattr(self.raman_view, 'apply_normalize'):
            self.raman_view.apply_normalize()
        
    def handle_peak_finding(self):
        """处理寻峰信号 - 对当前显示的曲线进行寻峰（支持所有算法处理后）- 根据当前坐标系进行操作"""
        log_user_action("点击寻峰按钮", self._get_current_user(), {})
        all_peaks_data = []
        
        # 检查当前处理状态
        processing_mode = getattr(self.raman_view, 'processing_mode', 'none')
        current_use_pixel = getattr(self.raman_view, '_use_pixel_coordinates', False)
        coord_type = "像素坐标" if current_use_pixel else "拉曼位移"
        print(f"当前处理模式: {processing_mode}, 当前坐标系: {coord_type}")
        
        # 主曲线 - 使用当前显示的数据（包括所有算法处理后的数据）
        intensity = self.raman_view.intensity
        wavelength = self.raman_view.wavelength
        
        if intensity is not None and len(intensity) > 0:
            print(f"主曲线数据长度: {len(intensity)}")
            print(f"主曲线强度范围: {np.min(intensity):.2f} - {np.max(intensity):.2f}")
            print(f"坐标轴范围: {np.min(wavelength):.2f} - {np.max(wavelength):.2f}")
            
            # 根据处理模式调整寻峰参数
            intensity_threshold, snr_threshold, distance = self._get_peak_finding_params(processing_mode)
            print(f"寻峰参数 - 强度阈值: {intensity_threshold}, 信噪比: {snr_threshold}, 距离: {distance}")
            
            peaks = self.detect_peaks_for_data(intensity, intensity_threshold, snr_threshold, distance)
            if len(peaks) > 0:
                peak_wavelengths = wavelength[peaks]
                peak_intensities = intensity[peaks]
                # 计算半高宽
                fwhm_values = self.raman_view.processor.calculate_fwhm(wavelength, intensity, peaks)
                all_peaks_data.append({
                    'file_path': None,
                    'wavelength': wavelength,
                    'intensity': intensity,
                    'peaks': peaks,
                    'peak_wavelengths': peak_wavelengths,
                    'peak_intensities': peak_intensities,
                    'fwhm_values': fwhm_values,  # 添加半高宽信息
                    'processing_mode': processing_mode,  # 添加处理模式信息
                    'coordinate_type': coord_type  # 添加坐标系信息
                })
                print(f"主曲线找到 {len(peaks)} 个峰值")
            else:
                print("主曲线未找到峰值")
        
        # 历史曲线
        if hasattr(self.raman_view, 'intensity_history') and self.raman_view.intensity_history:
            for idx, hist_intensity in enumerate(self.raman_view.intensity_history):
                # 使用历史曲线自己的x轴数据（如果存在），否则使用主曲线的x轴
                if hasattr(self.raman_view, 'wavelength_history') and len(self.raman_view.wavelength_history) > idx:
                    hist_wavelength = self.raman_view.wavelength_history[idx]
                    # 根据当前坐标系设置正确的x轴数据
                    if current_use_pixel:
                        hist_wavelength = np.arange(len(hist_wavelength))  # 像素坐标
                else:
                    hist_wavelength = self.raman_view.wavelength  # 回退到主曲线的x轴
                
                # 对历史曲线使用相同的寻峰参数
                intensity_threshold, snr_threshold, distance = self._get_peak_finding_params(processing_mode)
                peaks = self.detect_peaks_for_data(hist_intensity, intensity_threshold, snr_threshold, distance)
                if len(peaks) > 0:
                    peak_wavelengths = hist_wavelength[peaks]
                    peak_intensities = hist_intensity[peaks]
                    # 计算半高宽
                    fwhm_values = self.raman_view.processor.calculate_fwhm(hist_wavelength, hist_intensity, peaks)
                    all_peaks_data.append({
                        'file_path': None,
                        'wavelength': hist_wavelength,
                        'intensity': hist_intensity,
                        'peaks': peaks,
                        'peak_wavelengths': peak_wavelengths,
                        'peak_intensities': peak_intensities,
                        'fwhm_values': fwhm_values,  # 添加半高宽信息
                        'processing_mode': processing_mode,  # 添加处理模式信息
                        'coordinate_type': coord_type  # 添加坐标系信息
                    })
                    print(f"历史曲线 {idx+1} 找到 {len(peaks)} 个峰值")
        
        if all_peaks_data:
            self.display_all_peaks(all_peaks_data)
            # 不调用refresh_spectrum_view()，避免还原到原始数据
            # 只强制刷新画布以显示峰值
            self.raman_view.canvas.draw_idle()
            total_peaks = sum(len(data['peaks']) for data in all_peaks_data)
            print(f"总共找到 {total_peaks} 个峰值 (在{coord_type}坐标系下)")
            #QMessageBox.information(self, "寻峰结果", f"在{processing_mode}处理后的数据中找到 {total_peaks} 个峰值")
        else:
            print(f"未找到任何峰值 (在{coord_type}坐标系下)")
            #QMessageBox.information(self, "寻峰结果", f"在{processing_mode}处理后的数据中未找到峰值")
    
    def _get_peak_finding_params(self, processing_mode):
        """根据处理模式获取合适的寻峰参数"""
        if "normalize" in processing_mode:
            # 归一化后的数据，强度范围在0-1之间
            return 0.1, 2.0, 10
        elif "baseline" in processing_mode:
            # 基线校准后的数据，可能强度较低
            return 50, 2.0, 10
        elif "smooth" in processing_mode:
            # 平滑后的数据，噪声减少
            return 80, 1.5, 10
        else:
            # 原始数据或其他处理
            return 600, 3.0, 10

    # def perform_peak_finding_on_all_files(self, file_paths):
    #     """对所有指定的文件进行寻峰"""
    #     try:
    #         from spectrum_file_reader import SpectrumFileReader
    #     except ImportError:
    #         import sys
    #         sys.path.append(os.path.dirname(__file__))
    #         from spectrum_file_reader import SpectrumFileReader
        
    #     reader = SpectrumFileReader()
    #     all_peaks_data = []  # 存储所有文件的峰值数据
        
    #     for file_path in file_paths:
    #         try:
    #             file_info, spectrum_data = reader.read_file(file_path)
    #             wavelength = spectrum_data[:, 0]  # 拉曼位移
    #             intensity = spectrum_data[:, 1]   # 强度
                
    #             # 对每个文件进行寻峰
    #             peaks = self.detect_peaks_for_data(intensity)
    #             if len(peaks) > 0:
    #                 peak_wavelengths = wavelength[peaks]
    #                 peak_intensities = intensity[peaks]
    #                 all_peaks_data.append({
    #                     'file_path': file_path,
    #                     'wavelength': wavelength,
    #                     'intensity': intensity,
    #                     'peaks': peaks,
    #                     'peak_wavelengths': peak_wavelengths,
    #                     'peak_intensities': peak_intensities
    #                 })
                    
    #         except Exception as e:
    #             print(f"文件寻峰失败: {file_path}, {e}")
        
    #     # 更新显示所有峰值
    #     self.display_all_peaks(all_peaks_data)
        
    def detect_peaks_for_data(self, intensity, intensity_threshold=600, snr_threshold=3.0, distance=10):
        """对指定强度数据进行寻峰检测"""
        if intensity is None or len(intensity) == 0:
            return np.array([])
        
        # 估算噪声（用中位数绝对偏差）
        noise = np.median(np.abs(intensity - np.median(intensity)))
        if noise == 0:
            noise = 1e-6
            
        # 多峰检测
        peaks, props = find_peaks(
            intensity,
            height=intensity_threshold,
            distance=distance,
            prominence=snr_threshold * noise
        )
        return peaks
        
    def display_all_peaks(self, all_peaks_data):
        """显示所有文件的峰值"""
        with self.raman_view.data_lock:
            # 设置寻峰显示标志
            self.raman_view.show_peak_point = True
            self.raman_view.all_peaks_data = all_peaks_data  # 存储所有峰值数据
            self.raman_view.new_data_available = True
        
    def handle_restoration(self):
        """处理还原信号"""
        log_user_action("点击还原按钮", self._get_current_user(), {})
        #self.refresh_spectrum_view()  # 重新加载所有勾选文件的原始数据
        if hasattr(self, 'raman_view') and self.raman_view is not None:
            self.raman_view.show_peak_point = False
            self.raman_view.all_peaks_data = None
            self.raman_view.detected_peaks = None
            # 重置处理状态
            self.raman_view.processing_mode = "none"
            if hasattr(self.raman_view, 'is_smoothed'):
                self.raman_view.is_smoothed = False
            if hasattr(self.raman_view, 'is_normalized'):
                self.raman_view.is_normalized = False
            if hasattr(self.raman_view, 'is_baseline_corrected'):
                self.raman_view.is_baseline_corrected = False
            self.raman_view.new_data_available = True
        self.refresh_spectrum_view() 

    def handle_clear_spectrum(self):
        """处理清除谱图信号"""
        log_user_action("点击清空光谱按钮", self._get_current_user(), {})
        # 弹出确认对话框
        reply = QMessageBox.question(
            self, 
            "确认清除", 
            "您确定要清除谱图曲线及左边的谱图曲线列表？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 清除谱图数据
            if hasattr(self.raman_view, 'clear_spectrum'):
                self.raman_view.clear_spectrum()
            
            # 清除文件列表
            self.file_root_item.takeChildren()
            self.loaded_files.clear()
            self.filename_display.setText("未加载文件")
        
    def update_language(self, language):
        """更新语言设置"""
        set_language(language)
        
        # 更新根节点文本
        self.file_root_item.setText(0, get_text('opened_spectrum_files'))
        
        # 更新按钮文本
        self.confirm_params_btn.setText(get_text('confirm_params'))
        
        # 更新按钮tooltip
        self.start_measurement_btn.setToolTip(get_text('start_measurement'))
        self.stop_measurement_btn.setToolTip(get_text('stop_measurement'))
        
        # 更新文件名显示
        if not hasattr(self, 'current_filename') or not self.current_filename:
            self.filename_display.setText(get_text('no_file_loaded'))
        
        # 更新参数显示标签
        if hasattr(self, 'param_widget'):
            # 重新设置参数区域的标签
            for i in range(self.param_widget.layout().rowCount()):
                item = self.param_widget.layout().itemAt(i, QFormLayout.ItemRole.LabelRole)
                if item and hasattr(item.widget(), 'text'):
                    widget = item.widget()
                    if widget == self.filename_display:
                        continue  # 跳过文件名显示
                    elif hasattr(self, 'laser_power_display') and widget == self.laser_power_display:
                        widget.setText(get_text('laser_power'))
                    elif hasattr(self, 'integration_time_display') and widget == self.integration_time_display:
                        widget.setText(get_text('integration_time'))
                    elif hasattr(self, 'measurement_progress_display') and widget == self.measurement_progress_display:
                        widget.setText(get_text('measurement_progress'))
        
        # 更新命名规则和激光参数区域的标题
        if hasattr(self, 'naming_title'):
            self.naming_title.setText(get_text('naming_rules'))
        if hasattr(self, 'laser_title'):
            self.laser_title.setText(get_text('laser_parameters'))
        
        # 更新命名规则表单标签
        if hasattr(self, 'sample_name_label'):
            self.sample_name_label.setText(get_text('sample_name'))
        if hasattr(self, 'department_label'):
            self.department_label.setText(get_text('department'))
        if hasattr(self, 'patient_id_label'):
            self.patient_id_label.setText(get_text('patient_id'))
        if hasattr(self, 'select_date_label'):
            self.select_date_label.setText(get_text('select_date'))
        
        # 更新激光参数表单标签
        if hasattr(self, 'laser_power_percent_label'):
            self.laser_power_percent_label.setText(get_text('laser_power_percent'))
        if hasattr(self, 'integration_time_ms_label'):
            self.integration_time_ms_label.setText(get_text('integration_time_ms'))
        if hasattr(self, 'average_count_label'):
            self.average_count_label.setText(get_text('average_count'))
        
        # 更新中间参数显示区域标签
        if hasattr(self, 'label_filename'):
            self.label_filename.setText(get_text('current_test_filename'))
        if hasattr(self, 'label_progress'):
            self.label_progress.setText(get_text('measurement_progress'))
        if hasattr(self, 'param_laser_power_label'):
            self.param_laser_power_label.setText(get_text('laser_power'))
        if hasattr(self, 'param_integration_time_label'):
            self.param_integration_time_label.setText(get_text('integration_time'))

    def show_peak(self):
        """显示峰值点"""
        self.show_peak_point = True
        self.new_data_available = True



    def load_data_files(self, file_paths):
        log_user_action("批量加载数据文件", self._get_current_user(), {"文件数": len(file_paths)})
        """加载多个数据文件，文件名依次显示在文件内容窗口，并可勾选显示"""
        def strip_txt(filename):
            import os  # 修复闭包作用域下os未定义的问题
            base = os.path.basename(str(filename))
            for ext in ['.txt', '.csv', '.nod']:
                if base.lower().endswith(ext):
                    return base[:-len(ext)]
            return base
        
        if isinstance(file_paths, (list, tuple)):
            files = list(file_paths)
        else:
            files = [file_paths]
        
        # 导入文件读取器
        try:
            from spectrum_file_reader import SpectrumFileReader
        except ImportError:
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from spectrum_file_reader import SpectrumFileReader
        
        for file_path in files:
            if file_path not in self.loaded_files:
                self.loaded_files.append(file_path)
                item = QTreeWidgetItem([strip_txt(file_path)])
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                # 新文件默认勾选
                item.setCheckState(0, Qt.CheckState.Checked)
                item.setFlags(
                    item.flags()
                    | Qt.ItemFlag.ItemIsEditable
                    | Qt.ItemFlag.ItemIsUserCheckable
                    | Qt.ItemFlag.ItemIsEnabled
                    | Qt.ItemFlag.ItemIsSelectable
                )
                item.setData(0, Qt.ItemDataRole.UserRole, file_path)
                
                # 使用统一的颜色管理系统
                file_color = self.get_file_color(file_path)
                item.setForeground(0, file_color)
                
                self.file_root_item.addChild(item)
                # 读取文件头信息并添加为子节点
                try:
                    reader = SpectrumFileReader()
                    file_info, _ = reader.read_file(file_path)
                    info_map = [
                        (get_text("pixel"), file_info.get("像素", "0")),
                        (get_text("average_count"), file_info.get("平均次数", "0")),
                        (get_text("integration_time_ms"), file_info.get("积分时间", "0")),
                        ("激光功率(mW)", file_info.get("激光功率", "0")),
                        ("采集间隔(ms)", file_info.get("采集间隔", "0")),
                        ("采集模式", file_info.get("采集模式", "0")),
                    ]
                    for label, value in info_map:
                        sub_item = QTreeWidgetItem([f"{label}：{value}"])
                        item.addChild(sub_item)
                except Exception as e:
                    print(f"读取文件信息失败: {e}")
        # 导入新文件时，所有原来的文件都自动折叠，只有最后一个新文件展开
        new_files = []
        for i in range(self.file_root_item.childCount()):
            child = self.file_root_item.child(i)
            file_path = child.data(0, Qt.ItemDataRole.UserRole)
            if file_path in files:
                new_files.append((i, child, file_path))
            else:
                # 原来的文件都折叠
                child.setExpanded(False)
                self._file_expanded_states[file_path] = False
        
        # 只有最后一个新文件展开
        if new_files:
            for i, (index, child, file_path) in enumerate(new_files):
                if i == len(new_files) - 1:  # 最后一个新文件
                    child.setExpanded(True)
                    self._file_expanded_states[file_path] = True
                else:  # 其他新文件也折叠
                    child.setExpanded(False)
                    self._file_expanded_states[file_path] = False
        
        self.file_content.resizeColumnToContents(0)
        
        # 新文件默认勾选，但不影响其他文件的勾选状态
        # 不更新根节点的勾选状态，保持用户之前的勾选状态
        
        # 清除峰值显示（导入新文件时）
        if hasattr(self, 'raman_view') and self.raman_view is not None:
            self.raman_view.all_peaks_data = None
            self.raman_view.show_peak_point = False
        
        # 加载文件后立即刷新视图
        self.update_file_display_with_current_processing()

    def on_file_item_changed(self, item):
        if getattr(self, '_updating_check_state', False):
            return
        self._updating_check_state = True
        try:
            if item is self.file_root_item:
                # 根节点被勾选/取消，全部子节点同步
                state = self.file_root_item.checkState(0)
                for i in range(self.file_root_item.childCount()):
                    child = self.file_root_item.child(i)
                    child.setCheckState(0, state)
            else:
                # 子节点变化，根节点状态联动
                checked = 0
                unchecked = 0
                for i in range(self.file_root_item.childCount()):
                    child = self.file_root_item.child(i)
                    if child.checkState(0) == Qt.CheckState.Checked:
                        checked += 1
                    elif child.checkState(0) == Qt.CheckState.Unchecked:
                        unchecked += 1
                if checked == self.file_root_item.childCount() and checked != 0:
                    self.file_root_item.setCheckState(0, Qt.CheckState.Checked)
                elif unchecked == self.file_root_item.childCount():
                    self.file_root_item.setCheckState(0, Qt.CheckState.Unchecked)
                else:
                    self.file_root_item.setCheckState(0, Qt.CheckState.PartiallyChecked)
        finally:
            self._updating_check_state = False
        
        # 确保勾选操作不影响展开状态 - 不在这里处理展开状态
        
        # 2. 文件名重命名时同步更新光谱图图例
        if item.parent() == self.file_root_item:
            # 这是文件节点，重命名后同步图例
            checked_labels = []
            for i in range(self.file_root_item.childCount()):
                child_item = self.file_root_item.child(i)
                if child_item.checkState(0) == Qt.CheckState.Checked:
                    checked_labels.append(child_item.text(0))
            if hasattr(self, 'raman_view') and hasattr(self.raman_view, 'set_curve_labels'):
                self.raman_view.set_curve_labels(checked_labels)
                self.raman_view.canvas.draw_idle()
        
        # 清除峰值显示
        if hasattr(self, 'raman_view') and self.raman_view is not None:
            self.raman_view.all_peaks_data = None
            self.raman_view.show_peak_point = False
        
        # 使用新的方法更新文件显示，保持当前算法处理状态
        self.update_file_display_with_current_processing()
    
    def update_file_display_with_current_processing(self):
        """更新文件显示，保持当前的算法处理状态"""
        if not hasattr(self, 'raman_view') or self.raman_view is None:
            return
            
        checked_files = []
        for i in range(self.file_root_item.childCount()):
            item = self.file_root_item.child(i)
            if item.checkState(0) == Qt.CheckState.Checked:
                checked_files.append(item.data(0, Qt.ItemDataRole.UserRole))
        
        if not checked_files:
            # 没有勾选任何文件，清空视图但保持处理状态
            self.raman_view.wavelength = np.array([])
            self.raman_view.intensity = np.array([])
            self.raman_view.intensity_history = []
            self.raman_view.timestamp_history = []
            self.raman_view.curve_labels = []
            self.raman_view.new_data_available = True
            self.raman_view.canvas.draw_idle()
            return
        
        # 获取当前的处理模式
        current_processing_mode = getattr(self.raman_view, 'processing_mode', 'none')
        print(f"文件显示更新 - 当前处理模式: {current_processing_mode}")
        
        # 读取所有勾选文件的原始数据
        all_wavelengths = []
        all_intensities = []
        
        try:
            from spectrum_file_reader import SpectrumFileReader
        except ImportError:
            import sys
            sys.path.append(os.path.dirname(__file__))
            from spectrum_file_reader import SpectrumFileReader
        
        reader = SpectrumFileReader()
        
        for file_path in checked_files:
            try:
                file_info, spectrum_data = reader.read_file(file_path)
                all_wavelengths.append(spectrum_data[:, 0])  # 拉曼位移
                all_intensities.append(spectrum_data[:, 1])  # 强度
            except Exception as e:
                print(f"文件读取失败: {file_path}, {e}")
                
        if not all_wavelengths:
            return
        
        # 不使用插值对齐，保持原始数据的完整性
        # 以第一个文件为主，其余文件作为历史曲线，各自保持原始x轴范围
        
        # 主曲线使用第一个文件的原始数据
        self.raman_view.wavelength = all_wavelengths[0]
        self.raman_view.intensity_original = all_intensities[0].copy()
        self.raman_view.intensity_raw = all_intensities[0].copy()
        self.raman_view._raman_shift_data = all_wavelengths[0].copy()
        self.raman_view._pixel_data = np.arange(len(all_wavelengths[0]))
        
        # 历史曲线保持各自的原始数据
        self.raman_view.intensity_history = [arr.copy() for arr in all_intensities[1:]]
        self.raman_view._intensity_history_original = [arr.copy() for arr in all_intensities[1:]]
        # 为历史曲线保存x轴数据（用于绘制时参考）
        if not hasattr(self.raman_view, 'wavelength_history'):
            self.raman_view.wavelength_history = []
        self.raman_view.wavelength_history = [x.copy() for x in all_wavelengths[1:]]
        
        print(f"多曲线加载完成: 主曲线范围 {all_wavelengths[0][0]:.2f} - {all_wavelengths[0][-1]:.2f}, 共 {len(all_wavelengths)} 个文件")
        
        # 根据当前坐标系设置正确的x轴数据
        current_use_pixel = getattr(self.raman_view, '_use_pixel_coordinates', False)
        if current_use_pixel:
            # 当前使用像素坐标
            self.raman_view.wavelength = self.raman_view._pixel_data.copy()
            x_data_for_processing = self.raman_view._pixel_data.copy()
        else:
            # 当前使用拉曼位移
            self.raman_view.wavelength = self.raman_view._raman_shift_data.copy()
            x_data_for_processing = self.raman_view._raman_shift_data.copy()
        
        # 根据当前处理模式应用算法
        if current_processing_mode != "none":
            print(f"应用当前处理模式: {current_processing_mode}")
            
            # 对主曲线应用处理
            processed_intensity = all_intensities[0].copy()
            if "baseline" in current_processing_mode:
                processed_intensity = self.raman_view.processor.baseline_correction(x_data_for_processing, processed_intensity)
            if "smooth" in current_processing_mode:
                processed_intensity = self.raman_view.processor.smooth(x_data_for_processing, processed_intensity)
            if "normalize" in current_processing_mode:
                processed_intensity = self.raman_view.processor.normalize(x_data_for_processing, processed_intensity)
            
            self.raman_view.intensity = processed_intensity
            
            # 对历史曲线应用相同的处理
            processed_history = []
            for idx, hist_intensity in enumerate(all_intensities[1:]):
                processed_hist = hist_intensity.copy()
                
                # 为每个历史曲线使用正确的x轴数据
                if current_use_pixel:
                    hist_x_data = np.arange(len(hist_intensity))
                else:
                    hist_x_data = all_wavelengths[idx + 1].copy()  # 使用对应的原始x轴数据
                
                if "baseline" in current_processing_mode:
                    processed_hist = self.raman_view.processor.baseline_correction(hist_x_data, processed_hist)
                if "smooth" in current_processing_mode:
                    processed_hist = self.raman_view.processor.smooth(hist_x_data, processed_hist)
                if "normalize" in current_processing_mode:
                    processed_hist = self.raman_view.processor.normalize(hist_x_data, processed_hist)
                processed_history.append(processed_hist)
            
            self.raman_view.intensity_history = processed_history
        else:
            # 无处理模式，使用原始数据
            self.raman_view.intensity = all_intensities[0].copy()
            self.raman_view.intensity_history = [arr.copy() for arr in all_intensities[1:]]
        
        self.raman_view.timestamp_history = [0 for _ in all_intensities[1:]]
        
        # 清空未用到的历史曲线
        for i in range(len(self.raman_view.history_lines)):
            if i >= len(self.raman_view.intensity_history):
                self.raman_view.history_lines[i].set_data([], [])
        
        # 确保wavelength_history长度与intensity_history一致
        if hasattr(self.raman_view, 'wavelength_history'):
            while len(self.raman_view.wavelength_history) > len(self.raman_view.intensity_history):
                self.raman_view.wavelength_history.pop()
        
        # 设置图例标签为树控件中的显示名称和颜色
        curve_labels = []
        curve_colors = []
        for i in range(self.file_root_item.childCount()):
            child_item = self.file_root_item.child(i)
            if child_item.checkState(0) == Qt.CheckState.Checked:
                curve_labels.append(child_item.text(0))
                child_file_path = child_item.data(0, Qt.ItemDataRole.UserRole)
                curve_colors.append(self.get_matplotlib_color(self.get_file_color(child_file_path)))
        
        self.raman_view.set_curve_labels(curve_labels)
        
        # 设置对应的颜色
        if hasattr(self.raman_view, 'set_curve_colors'):
            self.raman_view.set_curve_colors(curve_colors)
        
        self.raman_view.new_data_available = True
        self.raman_view.canvas.draw_idle()
        
        print(f"文件显示更新完成 - 显示 {len(checked_files)} 个文件，处理模式: {current_processing_mode}")

    # def load_data_file(self, file_path):
    #     log_user_action("加载数据文件", self._get_current_user(), {"文件": file_path})
    #     """加载数据文件并显示内容和光谱数据"""
    #     try:
    #         # 设置顶部标签为已加载文件
    #         self.filename_display.setText(f"已加载文件: {os.path.basename(file_path)}")
            
    #         # 使用spectrum_file_reader读取文件
    #         try:
    #             from spectrum_file_reader import SpectrumFileReader
                
    #             reader = SpectrumFileReader()
    #             file_info, spectrum_data = reader.read_file(file_path)
                
    #             # 清除峰值显示（加载新文件时）
    #             if hasattr(self, 'raman_view') and self.raman_view is not None:
    #                 self.raman_view.all_peaks_data = None
    #                 self.raman_view.show_peak_point = False
                
    #             # 更新文件内容显示
    #             self.update_file_content_display(file_path, file_info, spectrum_data)
                
    #             # 加载数据到光谱视图
    #             if hasattr(self, 'raman_view') and hasattr(self.raman_view, 'load_data_from_file'):
    #                 success = self.raman_view.load_data_from_file(file_path)
    #                 if success:
    #                     print(f"成功加载光谱数据: {file_path}")
    #                 else:
    #                     # 如果现有方法失败，尝试直接设置数据
    #                     self.load_spectrum_data_directly(spectrum_data)
    #             elif hasattr(self, 'raman_view'):
    #                 # 直接设置光谱数据
    #                 self.load_spectrum_data_directly(spectrum_data)
                
    #             # 将文件路径添加到已加载文件列表
    #             if file_path not in self.loaded_files:
    #                 self.loaded_files.append(file_path)
                
    #             print(f"成功加载数据文件: {file_path}")
                
    #         except ImportError:
    #             print("警告: 无法导入spectrum_file_reader模块")
    #         except Exception as e:
    #             print(f"读取文件失败: {e}")
                
    #     except Exception as e:
    #         print(f"加载数据文件失败: {e}")
    
    # def update_file_content_display(self, file_path: str, file_info: Dict, spectrum_data):
    #     """更新文件内容显示"""
    #     try:
    #         if not hasattr(self, 'file_content'):
    #             return
            
    #         # 获取文件名
    #         filename = os.path.basename(file_path)
            
    #         # 检查文件是否已经在树形控件中
    #         existing_item = None
    #         root = self.file_content.invisibleRootItem()
    #         for i in range(root.childCount()):
    #             item = root.child(i)
    #             if item.text(0) == filename:
    #                 existing_item = item
    #                 break
            
    #         # 如果文件不存在，创建新的树形项
    #         if existing_item is None:
    #             from PyQt6.QtWidgets import QTreeWidgetItem
    #             from PyQt6.QtCore import Qt
                
    #             file_item = QTreeWidgetItem(self.file_content)
    #             file_item.setFlags(file_item.flags() | Qt.ItemFlag.ItemIsEditable)

    #             file_item.setFlags(
    #                 file_item.flags()
    #                 | Qt.ItemFlag.ItemIsEditable
    #                 | Qt.ItemFlag.ItemIsUserCheckable
    #                 | Qt.ItemFlag.ItemIsEnabled
    #                 | Qt.ItemFlag.ItemIsSelectable
    #             )
    #             file_item.setText(0, filename)
    #             file_item.setFlags(file_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
    #             file_item.setCheckState(0, Qt.CheckState.Checked)
    #             file_item.setData(0, Qt.ItemDataRole.UserRole, file_path)
                
    #             # 添加文件信息子项
    #             info_item = QTreeWidgetItem(file_item)
    #             info_item.setText(0, get_text("measurement_parameters"))
                
    #             for key, value in file_info.items():
    #                 param_item = QTreeWidgetItem(info_item)
    #                 param_item.setText(0, f"{key}: {value}")
                
    #             # 添加光谱数据信息
    #             data_item = QTreeWidgetItem(file_item)
    #             data_item.setText(0, get_text("spectrum_data", len(spectrum_data)))
                
    #             if len(spectrum_data) > 0:
    #                 range_item = QTreeWidgetItem(data_item)
    #                 range_item.setText(0, get_text("raman_shift_range", spectrum_data[0, 0], spectrum_data[-1, 0]))
                    
    #                 intensity_item = QTreeWidgetItem(data_item)
    #                 intensity_item.setText(0, get_text("intensity_range", np.min(spectrum_data[:, 1]), np.max(spectrum_data[:, 1])))
                
    #             # 展开文件项
    #             file_item.setExpanded(True)
                
    #         print(f"已更新文件内容显示: {filename}")
            
    #     except Exception as e:
    #         print(f"更新文件内容显示失败: {e}")
    
    def load_spectrum_data_directly(self, spectrum_data):
        """直接加载光谱数据到显示视图"""
        try:
            if not hasattr(self, 'raman_view') or len(spectrum_data) == 0:
                return
            
            spectrum_view = self.raman_view
            
            # 提取拉曼位移和强度
            raman_shift = spectrum_data[:, 0]
            intensity = spectrum_data[:, 1]
            
            # 检查是否有数据锁
            if hasattr(spectrum_view, 'data_lock'):
                with spectrum_view.data_lock:
                    # 保存原始拉曼位移和像素坐标数据
                    spectrum_view._raman_shift_data = raman_shift.copy()
                    spectrum_view._pixel_data = np.arange(len(raman_shift))
                    
                    # 根据当前坐标系统设置正确的显示数据
                    if getattr(spectrum_view, '_use_pixel_coordinates', False):
                        # 使用像素坐标
                        spectrum_view.wavelength = spectrum_view._pixel_data.copy()
                        # 更新x轴标签
                        spectrum_view.ax.set_xlabel(get_text('pixel_coordinates'))
                    else:
                        # 使用拉曼位移
                        spectrum_view.wavelength = spectrum_view._raman_shift_data.copy()
                        # 更新x轴标签
                        spectrum_view.ax.set_xlabel(get_text('raman_shift_cm'))
                    
                    spectrum_view.intensity = intensity
                    spectrum_view.intensity_raw = intensity.copy()
                    spectrum_view.intensity_original = intensity.copy()
                    spectrum_view.new_data_available = True
                    
                    # 添加到历史记录
                    if hasattr(spectrum_view, 'intensity_history'):
                        spectrum_view.intensity_history.append(intensity.copy())
                        if hasattr(spectrum_view, 'timestamp_history'):
                            spectrum_view.timestamp_history.append(datetime.now())
                        
                        # 限制历史记录长度
                        if len(spectrum_view.intensity_history) > getattr(spectrum_view, 'max_history', 100):
                            spectrum_view.intensity_history.pop(0)
                            if hasattr(spectrum_view, 'timestamp_history'):
                                spectrum_view.timestamp_history.pop(0)
            else:
                # 直接设置数据
                # 保存原始拉曼位移和像素坐标数据
                spectrum_view._raman_shift_data = raman_shift.copy()
                spectrum_view._pixel_data = np.arange(len(raman_shift))
                
                # 根据当前坐标系统设置正确的显示数据
                if getattr(spectrum_view, '_use_pixel_coordinates', False):
                    # 使用像素坐标
                    spectrum_view.wavelength = spectrum_view._pixel_data.copy()
                else:
                    # 使用拉曼位移
                    spectrum_view.wavelength = spectrum_view._raman_shift_data.copy()
                
                spectrum_view.intensity = intensity
                if hasattr(spectrum_view, 'intensity_raw'):
                    spectrum_view.intensity_raw = intensity.copy()
                if hasattr(spectrum_view, 'intensity_original'):
                    spectrum_view.intensity_original = intensity.copy()
            
            # 触发重绘
            if hasattr(spectrum_view, 'canvas'):
                spectrum_view.canvas.draw()
            
            print(f"已直接加载光谱数据，数据点数: {len(spectrum_data)}")
            
        except Exception as e:
            print(f"直接加载光谱数据失败: {e}")
    
    def update_spectrum_plot(self, raman_shift, intensity):
        """更新光谱图显示（提供给集成测量逻辑调用）"""
        try:
            if not hasattr(self, 'raman_view'):
                return
            
            spectrum_view = self.raman_view
            
            # 检查是否有数据锁
            if hasattr(spectrum_view, 'data_lock'):
                with spectrum_view.data_lock:
                    # 保存当前主曲线到历史记录（如果有的话）
                    if hasattr(spectrum_view, 'intensity') and spectrum_view.intensity is not None and len(spectrum_view.intensity) > 0:
                        # 初始化历史记录列表（如果不存在）
                        if not hasattr(spectrum_view, 'intensity_history'):
                            spectrum_view.intensity_history = []
                        if not hasattr(spectrum_view, 'wavelength_history'):
                            spectrum_view.wavelength_history = []
                        if not hasattr(spectrum_view, 'timestamp_history'):
                            spectrum_view.timestamp_history = []
                        
                        # 将当前主曲线添加到历史记录
                        spectrum_view.intensity_history.append(spectrum_view.intensity.copy())
                        if hasattr(spectrum_view, 'wavelength') and spectrum_view.wavelength is not None:
                            spectrum_view.wavelength_history.append(spectrum_view.wavelength.copy())
                        else:
                            spectrum_view.wavelength_history.append(raman_shift.copy())
                        spectrum_view.timestamp_history.append(0)  # 可以是时间戳
                    
                    # 保存原始拉曼位移和像素坐标数据
                    spectrum_view._raman_shift_data = raman_shift.copy()
                    spectrum_view._pixel_data = np.arange(len(raman_shift))
                    
                    # 根据当前坐标系统设置正确的显示数据
                    if getattr(spectrum_view, '_use_pixel_coordinates', False):
                        # 使用像素坐标
                        spectrum_view.wavelength = spectrum_view._pixel_data.copy()
                        # 更新x轴标签
                        if hasattr(spectrum_view, 'ax'):
                            spectrum_view.ax.set_xlabel('像素坐标')
                    else:
                        # 使用拉曼位移
                        spectrum_view.wavelength = spectrum_view._raman_shift_data.copy()
                        # 更新x轴标签
                        if hasattr(spectrum_view, 'ax'):
                            spectrum_view.ax.set_xlabel('拉曼位移 (cm-1)')
                    
                    spectrum_view.intensity = intensity
                    spectrum_view.intensity_raw = intensity.copy()
                    spectrum_view.intensity_original = intensity.copy()
                    spectrum_view.new_data_available = True
            else:
                # 保存当前主曲线到历史记录（如果有的话）
                if hasattr(spectrum_view, 'intensity') and spectrum_view.intensity is not None and len(spectrum_view.intensity) > 0:
                    # 初始化历史记录列表（如果不存在）
                    if not hasattr(spectrum_view, 'intensity_history'):
                        spectrum_view.intensity_history = []
                    if not hasattr(spectrum_view, 'wavelength_history'):
                        spectrum_view.wavelength_history = []
                    if not hasattr(spectrum_view, 'timestamp_history'):
                        spectrum_view.timestamp_history = []
                    
                    # 将当前主曲线添加到历史记录
                    spectrum_view.intensity_history.append(spectrum_view.intensity.copy())
                    if hasattr(spectrum_view, 'wavelength') and spectrum_view.wavelength is not None:
                        spectrum_view.wavelength_history.append(spectrum_view.wavelength.copy())
                    else:
                        spectrum_view.wavelength_history.append(raman_shift.copy())
                    spectrum_view.timestamp_history.append(0)  # 可以是时间戳
                
                # 直接设置数据
                # 保存原始拉曼位移和像素坐标数据
                spectrum_view._raman_shift_data = raman_shift.copy()
                spectrum_view._pixel_data = np.arange(len(raman_shift))
                
                # 根据当前坐标系统设置正确的显示数据
                if getattr(spectrum_view, '_use_pixel_coordinates', False):
                    # 使用像素坐标
                    spectrum_view.wavelength = spectrum_view._pixel_data.copy()
                else:
                    # 使用拉曼位移
                    spectrum_view.wavelength = spectrum_view._raman_shift_data.copy()
                
                spectrum_view.intensity = intensity
                if hasattr(spectrum_view, 'intensity_raw'):
                    spectrum_view.intensity_raw = intensity.copy()
                if hasattr(spectrum_view, 'intensity_original'):
                    spectrum_view.intensity_original = intensity.copy()
            
            # 触发重绘
            if hasattr(spectrum_view, 'canvas'):
                spectrum_view.canvas.draw()
            
            print(f"已更新光谱图显示，数据点数: {len(raman_shift)}，历史曲线数: {len(getattr(spectrum_view, 'intensity_history', []))}")
            
        except Exception as e:
            print(f"更新光谱图显示失败: {e}")
    
    def update_file_content_view(self, content: str):
        """更新文件内容视图（提供给集成测量逻辑调用）"""
        try:
            if hasattr(self, 'file_content'):
                # 静默处理，不在命令行显示内容预览
                pass
            
        except Exception as e:
            pass  # 静默处理错误

    def on_file_content_mouse_press(self, event):
        """处理文件内容区域的鼠标点击事件"""
        # 调用原始的鼠标事件处理
        super(QTreeWidget, self.file_content).mousePressEvent(event)
        
        # 获取点击的项目和位置
        item = self.file_content.itemAt(event.pos())
        if item and item.parent() == self.file_root_item:
            # 检查是否点击在勾选框区域
            rect = self.file_content.visualItemRect(item)
            checkbox_width = 20  # 勾选框的大概宽度
            
            # 如果点击在勾选框区域内，不处理展开/折叠
            if event.pos().x() <= rect.x() + checkbox_width:
                return  # 点击了勾选框，不处理展开/折叠
            
            # 点击了文本区域，处理展开/折叠
            file_path = item.data(0, Qt.ItemDataRole.UserRole)
            if item.isExpanded():
                item.setExpanded(False)
                self._file_expanded_states[file_path] = False
            else:
                item.setExpanded(True)
                self._file_expanded_states[file_path] = True

    def on_file_item_clicked(self, item, column):
        """处理文件项单击事件 - 折叠/展开元信息"""
        # 这个函数现在主要用于其他点击事件，展开/折叠由mousePressEvent处理
        pass

    def show_file_context_menu(self, position):
        """显示文件项右键菜单"""
        item = self.file_content.itemAt(position)
        if item is None or item.parent() != self.file_root_item:
            return  # 只对文件节点显示菜单
        
        menu = QMenu(self)
        
        # 创建菜单项
        clear_action = QAction(get_text("clear_spectrum"), self)
        color_action = QAction(get_text("change_spectrum_color"), self)
        rename_action = QAction(get_text("modify_spectrum_name"), self)
        
        # 连接信号
        clear_action.triggered.connect(lambda: self.clear_single_spectrum(item))
        color_action.triggered.connect(lambda: self.change_spectrum_color(item))
        rename_action.triggered.connect(lambda: self.rename_spectrum(item))
        
        # 添加到菜单
        menu.addAction(clear_action)
        menu.addAction(color_action)
        menu.addAction(rename_action)
        
        # 显示菜单
        menu.exec(self.file_content.mapToGlobal(position))

    def clear_single_spectrum(self, item):
        """清除单个文件的谱图"""
        file_path = item.data(0, Qt.ItemDataRole.UserRole)
        file_name = item.text(0)
        
        # 确认对话框
        reply = QMessageBox.question(
            self, 
            get_text("confirm_clear"), 
            get_text("confirm_clear_file", file_name),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 从文件列表中移除
            if file_path in self.loaded_files:
                self.loaded_files.remove(file_path)
            
            # 从颜色映射中移除
            if file_path in self.file_colors:
                del self.file_colors[file_path]
            
            # 从展开状态字典中移除
            if file_path in self._file_expanded_states:
                del self._file_expanded_states[file_path]
            
            # 从树控件中移除
            self.file_root_item.removeChild(item)
            
            # 更新根节点状态
            total_children = self.file_root_item.childCount()
            if total_children == 0:
                self.file_root_item.setCheckState(0, Qt.CheckState.Unchecked)
            
            # 刷新光谱显示
            self.update_file_display_with_current_processing()
            
            print(get_text("file_cleared", file_name))

    def change_spectrum_color(self, item):
        """更改谱图颜色"""
        file_path = item.data(0, Qt.ItemDataRole.UserRole)
        current_color = self.get_file_color(file_path)
        
        # 打开颜色选择对话框
        color = QColorDialog.getColor(current_color, self, get_text("select_spectrum_color"))
        
        if color.isValid():
            # 更新颜色映射
            self.file_colors[file_path] = color
            
            # 更新文件项颜色
            item.setForeground(0, color)
            
            # 更新光谱图颜色
            if hasattr(self, 'raman_view') and self.raman_view is not None:
                # 获取所有勾选的文件
                checked_files = []
                curve_colors = []
                for i in range(self.file_root_item.childCount()):
                    child_item = self.file_root_item.child(i)
                    if child_item.checkState(0) == Qt.CheckState.Checked:
                        child_file_path = child_item.data(0, Qt.ItemDataRole.UserRole)
                        checked_files.append(child_file_path)
                        curve_colors.append(self.get_matplotlib_color(self.get_file_color(child_file_path)))
                
                # 更新曲线颜色
                if hasattr(self.raman_view, 'set_curve_colors'):
                    self.raman_view.set_curve_colors(curve_colors)
                    self.raman_view.canvas.draw_idle()
            
            print(get_text("color_changed", item.text(0)))

    def rename_spectrum(self, item):
        """修改谱图名称"""
        # 进入编辑模式，与双击效果相同
        self.file_content.editItem(item, 0)

    def process_hole_positions_and_update_ui(self):
        """处理孔位信息并更新UI显示"""
        if not self.hole_processor:
            print("孔位处理器未初始化")
            return
        
        try:
            # 获取UI显示数据
            ui_data = self.hole_processor.get_ui_display_data()
            if not ui_data:
                print("无法获取孔位数据")
                return
            
            # 更新UI显示
            self.update_ui_with_hole_data(ui_data)
            
            
            
            print("孔位信息处理完成，UI已更新")
            
        except Exception as e:
            print(f"处理孔位信息时出错: {e}")
    
    def update_ui_with_hole_data(self, ui_data):
        """使用孔位数据更新UI显示"""
        try:
            # 更新当前测试名称
            if ui_data['current_test_name']:
                self.filename_display.setText(ui_data['current_test_name'])
            else:
                self.filename_display.setText("未加载")
            
            # 更新激光功率
            if ui_data['laser_power']:
                self.laser_power_display.setText(ui_data['laser_power'])
            else:
                self.laser_power_display.setText("0")
            
            # 更新积分时间
            if ui_data['integration_time']:
                self.integration_time_display.setText(ui_data['integration_time'])
            else:
                self.integration_time_display.setText("0")
            
            # 更新测点测量进度
            if ui_data['measurement_progress']:
                self.measurement_progress_display.setText(ui_data['measurement_progress'])
            else:
                self.measurement_progress_display.setText("0 / 0")
            
            print(f"UI更新完成: 测试名称={ui_data['current_test_name']}, "
                  f"激光功率={ui_data['laser_power']}, "
                  f"积分时间={ui_data['integration_time']}, "
                  f"进度={ui_data['measurement_progress']}")
            
        except Exception as e:
            print(f"更新UI显示时出错: {e}")
    

    
    def check_hole_processing_signal(self):
        """检查孔位处理信号文件"""
        try:
            import os
            import json
            
            # 检查信号文件
            signal_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "temp", "hole_processing_signal.json")
            
            if os.path.exists(signal_file):
                # 读取信号文件
                with open(signal_file, 'r', encoding='utf-8') as f:
                    signal_data = json.load(f)
                
                # 检查是否是处理孔位信息的信号
                if signal_data.get('action') == 'process_hole_positions' and signal_data.get('status') == 'ready':
                    print("检测到孔位处理信号，开始处理...")
                    
                    # 处理孔位信息
                    self.process_hole_positions_and_update_ui()
                    
                    # 删除信号文件，避免重复处理
                    try:
                        os.remove(signal_file)
                        print("信号文件已删除")
                    except Exception as e:
                        print(f"删除信号文件失败: {e}")
                        
        except Exception as e:
            # 静默处理错误，避免影响主程序运行
            pass
    
    def update_hole_color(self, hole_id: str, status: str, color):
        """更新孔位颜色"""
        try:
            print(f"更新孔位 {hole_id} 颜色: {status}")
            
        except Exception as e:
            print(f"更新孔位颜色失败: {e}")
    
    def on_measurement_completed(self):
        """测量完成回调"""
        try:
            print("测量完成回调")
            # 设置测量状态为完成
            self.measurement_state = "completed"
            # 更新按钮状态
            self.start_measurement_btn.setEnabled(False)  # 测量完成后需要重新保存参数才能开始
            self.stop_measurement_btn.setEnabled(False)
            # 停止闪烁定时器
            if hasattr(self, 'blink_timer'):
                self.blink_timer.stop()
            # 不调用reset_to_initial_state，保持测量完成状态
            print("测量完成，状态已设置为completed")
        except Exception as e:
            print(f"测量完成回调失败: {e}")
    
    def start_integrated_measurement(self):
        try:
            if not self.integrated_measurement_logic:
                print("整合测量逻辑未初始化")
                return False
            
            # 检查命名参数是否已设置
            if not hasattr(self, 'naming_params') or not self.naming_params:
                print("请先设置命名参数")
                QMessageBox.warning(self, "警告", "请先设置命名参数（样品名、患者编号、科室等）")
                return False
            
            # 开始整合测量（不再需要孔位数据）
            success = self.integrated_measurement_logic.start_integrated_measurement()
            if success:
                print("整合测量流程已开始")
                # 只有在成功时才设置测量状态为running
                self.measurement_state = "running"
                return True
            else:
                print("整合测量流程启动失败")
                return False
        except Exception as e:
            print(f"开始整合测量流程失败: {e}")
            return False

    def stop_integrated_measurement(self):
        self.measurement_state = "stopped"
        try:
            if self.integrated_measurement_logic:
                self.integrated_measurement_logic.stop_measurement()
            # 更新按钮状态
            self.start_measurement_btn.setEnabled(True)
            self.stop_measurement_btn.setEnabled(False)
            print("整合测量流程已停止")
        except Exception as e:
            print(f"停止整合测量流程失败: {e}")
    
    def get_selected_holes_from_save(self):
        """从save目录中获取选中的孔位数据"""
        try:
            if not self.hole_processor:
                print("孔位处理器未初始化")
                return []
            
            # 获取处理后的孔位数据
            processed_data = self.hole_processor.process_hole_positions()
            if not processed_data:
                print("无法获取孔位数据")
                return []
            
            # 筛选有数据的孔位
            selected_holes = []
            for hole in processed_data.get('holes', []):
                if hole.get('has_save_data', False):
                    selected_holes.append({
                        'hole_id': hole.get('hole_id', ''),
                        'generated_name': hole.get('generated_name', ''),
                        'patient_id': hole.get('patient_id', ''),
                        'date': hole.get('date', ''),
                        'department': hole.get('department', ''),
                        'laser_power': hole.get('laser_power', 100),
                        'integration_time': hole.get('integration_time', 1000),
                        'average_count': hole.get('average_count', 3)
                    })
            
            print(f"获取到 {len(selected_holes)} 个选中的孔位")
            return selected_holes
            
        except Exception as e:
            print(f"获取选中孔位数据失败: {e}")
            return []

    def get_checked_files_info(self):
        """
        获取所有勾选文件的文件名及详细信息（含设备序列号、操作员、积分时间、激光功率等）
        返回: List[Dict]，每个dict包含filename, file_info
        """
        import os  # 添加os模块导入
        checked_files = []
        for i in range(self.file_root_item.childCount()):
            item = self.file_root_item.child(i)
            if item.checkState(0) == Qt.CheckState.Checked:
                checked_files.append(item.data(0, Qt.ItemDataRole.UserRole))
        result = []
        if not checked_files:
            return result
        try:
            from spectrum_file_reader import SpectrumFileReader
        except ImportError:
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from spectrum_file_reader import SpectrumFileReader
        for file_path in checked_files:
            try:
                reader = SpectrumFileReader()
                file_info, _ = reader.read_file(file_path)
                file_info = dict(file_info)
                file_info['文件名'] = os.path.basename(str(file_path))
                result.append(file_info)
            except Exception as e:
                result.append({'文件名': os.path.basename(str(file_path)), '错误': str(e)})
        return result

    def capture_spectrum_image(self, save_path):
        """
        捕获当前光谱视图为图片
        """
        try:
            if hasattr(self, 'raman_view') and self.raman_view is not None:
                canvas = self.raman_view.canvas
                canvas.figure.savefig(save_path, dpi=150, bbox_inches='tight', 
                                     facecolor='white', edgecolor='none')
                return True
        except Exception as e:
            print(f"捕获光谱图片失败: {e}")
        return False

    def refresh_spectrum_view(self):
        """根据勾选的文件刷新拉曼视图，支持多曲线叠加（用于初始加载文件时）"""
        # 检查 raman_view 是否已初始化
        if not hasattr(self, 'raman_view') or self.raman_view is None:
            return
            
        checked_files = []
        for i in range(self.file_root_item.childCount()):
            item = self.file_root_item.child(i)
            if item.checkState(0) == Qt.CheckState.Checked:
                checked_files.append(item.data(0, Qt.ItemDataRole.UserRole))
        if not checked_files:
            # 没有勾选任何文件，清空视图
            self.raman_view.wavelength = np.array([])
            self.raman_view.intensity = np.array([])
            self.raman_view.intensity_original = np.array([])
            self.raman_view.intensity_raw = np.array([])
            self.raman_view.intensity_history = []
            self.raman_view.timestamp_history = []
            
            # 新增：清除原始历史曲线数据
            self.raman_view._intensity_history_original = []
            
            # 新增：清除坐标轴相关数据
            self.raman_view._raman_shift_data = None
            self.raman_view._pixel_data = None
            self.raman_view._use_pixel_coordinates = False
            
            # 重置处理状态
            self.raman_view.processing_mode = "none"
            self.raman_view.is_smoothed = False
            self.raman_view.is_normalized = False
            self.raman_view.is_baseline_corrected = False
            self.raman_view.show_peak_point = False
            self.raman_view.detected_peaks = None
            self.raman_view.all_peaks_data = None  # 清除所有峰值数据
            self.raman_view.curve_labels = []
            
            self.raman_view.new_data_available = True
            # 强制刷新画布
            self.raman_view.canvas.draw_idle()
            return
            
        # 多曲线叠加显示
        all_wavelengths = []
        all_intensities = []
        
        # 导入文件读取器
        try:
            from spectrum_file_reader import SpectrumFileReader
        except ImportError:
            import sys
            sys.path.append(os.path.dirname(__file__))
            from spectrum_file_reader import SpectrumFileReader
        
        reader = SpectrumFileReader()
        
        for file_path in checked_files:
            try:
                file_info, spectrum_data = reader.read_file(file_path)
                all_wavelengths.append(spectrum_data[:, 0])  # 拉曼位移
                all_intensities.append(spectrum_data[:, 1])  # 强度
            except Exception as e:
                print(f"文件读取失败: {file_path}, {e}")
                
        if not all_wavelengths:
            return
        
        # ----------- 修复：不使用插值，保持原始数据完整性 -----------
        # 以第一个文件为主，其余文件作为历史曲线，各自保持原始x轴范围
        
        # 主曲线使用第一个文件的原始数据
        self.raman_view.wavelength = all_wavelengths[0]
        self.raman_view.intensity = all_intensities[0]
        self.raman_view.intensity_original = all_intensities[0].copy()
        self.raman_view.intensity_raw = all_intensities[0].copy()
        
        # 新增：保存拉曼位移和像素坐标数据
        self.raman_view._raman_shift_data = all_wavelengths[0].copy()
        self.raman_view._pixel_data = np.arange(len(all_wavelengths[0]))
        
        # 历史曲线保持各自的原始数据
        self.raman_view.intensity_history = [arr.copy() for arr in all_intensities[1:]]
        # 为历史曲线保存x轴数据
        if not hasattr(self.raman_view, 'wavelength_history'):
            self.raman_view.wavelength_history = []
        self.raman_view.wavelength_history = [x.copy() for x in all_wavelengths[1:]]
        
        print(f"refresh_spectrum_view: 多曲线加载完成，主曲线范围 {all_wavelengths[0][0]:.2f} - {all_wavelengths[0][-1]:.2f}, 共 {len(all_wavelengths)} 个文件")
        
        # 根据当前坐标系设置正确的x轴数据
        if getattr(self.raman_view, '_use_pixel_coordinates', False):
            # 使用像素坐标
            self.raman_view.wavelength = self.raman_view._pixel_data.copy()
        else:
            # 使用拉曼位移
            self.raman_view.wavelength = self.raman_view._raman_shift_data.copy()
        
        # 重置处理状态
        self.raman_view.processing_mode = "none"
        if hasattr(self.raman_view, 'is_smoothed'):
            self.raman_view.is_smoothed = False
        if hasattr(self.raman_view, 'is_normalized'):
            self.raman_view.is_normalized = False
        if hasattr(self.raman_view, 'is_baseline_corrected'):
            self.raman_view.is_baseline_corrected = False
        
        # 历史曲线赋值（只保留当前勾选的数量-1，剩余的彻底清空）
        self.raman_view.intensity_history = [arr.copy() for arr in all_intensities[1:]]
        self.raman_view.timestamp_history = [0 for _ in all_intensities[1:]]
        
        # 新增：保存原始历史曲线数据
        self.raman_view._intensity_history_original = [arr.copy() for arr in all_intensities[1:]]
        
        # 清空未用到的历史曲线
        for i in range(len(self.raman_view.history_lines)):
            if i >= len(self.raman_view.intensity_history):
                self.raman_view.history_lines[i].set_data([], [])
        
        # 颜色分配
        self.raman_view.color_list = ['b', 'g', 'r', 'c', 'm', 'y', 'k', 'orange', 'purple', 'brown', 'teal', 'navy']
        
        # 新增：设置图例标签为文件名
        curve_labels = [os.path.basename(str(f)) for f in checked_files]
        self.raman_view.set_curve_labels(curve_labels)
        
        # 根据当前坐标系更新坐标轴标签
        if getattr(self.raman_view, '_use_pixel_coordinates', False):
            self.raman_view.ax.set_xlabel('像素坐标')
        else:
            self.raman_view.ax.set_xlabel('拉曼位移 (cm-1)')
        
        self.raman_view.new_data_available = True
        # 强制刷新画布
        self.raman_view.canvas.draw_idle()


    def apply_default_coordinate_system(self):
        """根据默认横坐标设置应用坐标系"""
        try:
            if self.default_x_axis == "像素":
                # 设置使用像素坐标
                if hasattr(self, 'raman_view') and hasattr(self.raman_view, '_use_pixel_coordinates'):
                    self.raman_view._use_pixel_coordinates = True
                    print("应用默认坐标系：像素坐标")
                    # 重新设置图形以应用坐标轴标签
                    if hasattr(self.raman_view, 'setup_plot'):
                        self.raman_view.setup_plot()
                else:
                    print("RamanView尚未初始化，将在setup_ui后应用像素坐标设置")
            else:
                # 设置使用拉曼位移（默认）
                if hasattr(self, 'raman_view') and hasattr(self.raman_view, '_use_pixel_coordinates'):
                    self.raman_view._use_pixel_coordinates = False
                    print("应用默认坐标系：拉曼位移")
                    # 重新设置图形以应用坐标轴标签
                    if hasattr(self.raman_view, 'setup_plot'):
                        self.raman_view.setup_plot()
        except Exception as e:
            print(f"应用默认坐标系设置失败: {e}")

    def update_coordinate_system_from_settings(self, settings):
        """从设置更新坐标系"""
        try:
            x_axis = settings.get("x_axis", "拉曼位移")
            self.default_x_axis = x_axis
            
            # 应用到raman_view
            if hasattr(self, 'raman_view') and hasattr(self.raman_view, '_use_pixel_coordinates'):
                if x_axis == "像素":
                    self.raman_view._use_pixel_coordinates = True
                    # 更新坐标轴标签
                    if hasattr(self.raman_view, 'ax'):
                        self.raman_view.ax.set_xlabel('像素坐标')
                else:
                    self.raman_view._use_pixel_coordinates = False
                    # 更新坐标轴标签
                    if hasattr(self.raman_view, 'ax'):
                        self.raman_view.ax.set_xlabel('拉曼位移 (cm-1)')
                
                # 如果有数据，重新绘制
                if hasattr(self.raman_view, 'new_data_available'):
                    self.raman_view.new_data_available = True
                
                # 强制重绘
                if hasattr(self.raman_view, 'canvas'):
                    self.raman_view.canvas.draw_idle()
                
                print(f"坐标系已更新为: {x_axis}")
        except Exception as e:
            print(f"更新坐标系设置失败: {e}")






