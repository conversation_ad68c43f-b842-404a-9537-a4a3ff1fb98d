#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import sqlite3
import os
from typing import Dict, List, Optional
from contextlib import contextmanager

class DatabaseUserManager:
    """基于数据库的用户权限管理类"""
    
    def __init__(self):
        # 获取当前脚本文件的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.db_path = os.path.join(script_dir, "users.db")
        self.current_user = None
        self.current_role = None
        
        # 定义权限配置
        self.permissions = {
            "admin": {
                "can_change_password": True,
                "can_manage_users": True,
                "can_update_service": True,
                "can_software_management": True,
                "can_import_database": True,
                "can_auxiliary_test": True,
                "can_access_all_features": True,
                "can_password_management": True
            },
            "user": {
                "can_change_password": False,
                "can_manage_users": False,
                "can_update_service": False,
                "can_software_management": False,
                "can_import_database": False,
                "can_auxiliary_test": False,
                "can_access_all_features": False,
                "can_password_management": False
            }
        }
        
        # 初始化数据库
        self.init_database()
    
    @contextmanager
    def get_db_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 创建用户表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        role TEXT NOT NULL DEFAULT 'user',
                        can_change_password BOOLEAN NOT NULL DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建触发器来更新 updated_at 字段
                cursor.execute('''
                    CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
                    AFTER UPDATE ON users
                    BEGIN
                        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                    END
                ''')
                
                # 检查是否已有用户，如果没有则创建默认用户
                cursor.execute('SELECT COUNT(*) FROM users')
                user_count = cursor.fetchone()[0]
                
                if user_count == 0:
                    self.create_default_users(cursor)
                
                conn.commit()
                print("数据库初始化完成")
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
    
    def create_default_users(self, cursor):
        """创建默认用户"""
        default_users = [
            {
                "username": "admin",
                "password": "123456",
                "role": "admin",
                "can_change_password": True
            },
            {
                "username": "user",
                "password": "3214",
                "role": "user",
                "can_change_password": False
            }
        ]
        
        for user_data in default_users:
            password_hash = self.hash_password(user_data["password"])
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, can_change_password)
                VALUES (?, ?, ?, ?)
            ''', (
                user_data["username"],
                password_hash,
                user_data["role"],
                user_data["can_change_password"]
            ))
        
        print("默认用户创建完成")
    
    def hash_password(self, password: str) -> str:
        """对密码进行哈希加密"""
        # 使用更安全的加密方式，添加盐值
        salt = "raman_spectrum_salt"  # 可以设置为更复杂的盐值
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """验证用户登录"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT role FROM users 
                    WHERE username = ? AND password_hash = ?
                ''', (username, self.hash_password(password)))
                
                result = cursor.fetchone()
                if result:
                    return result['role']
                return None
                
        except Exception as e:
            print(f"用户验证失败: {e}")
            return None
    
    def login(self, username: str, role: str):
        """用户登录"""
        self.current_user = username
        self.current_role = role
    
    def logout(self):
        """用户登出"""
        self.current_user = None
        self.current_role = None
    
    def get_current_user(self) -> Optional[str]:
        """获取当前用户"""
        return self.current_user
    
    def get_current_role(self) -> Optional[str]:
        """获取当前用户角色"""
        return self.current_role
    
    def has_permission(self, permission: str) -> bool:
        """检查当前用户是否有指定权限"""
        if not self.current_role or self.current_role not in self.permissions:
            return False
        
        return self.permissions[self.current_role].get(permission, False)
    
    def can_change_password(self) -> bool:
        """检查是否可以更改密码"""
        return self.has_permission("can_change_password")
    
    def can_manage_users(self) -> bool:
        """检查是否可以管理用户"""
        return self.has_permission("can_manage_users")
    
    def can_update_service(self) -> bool:
        """检查是否可以更新服务"""
        return self.has_permission("can_update_service")
    
    def can_software_management(self) -> bool:
        """检查是否可以软件管理"""
        return self.has_permission("can_software_management")
    
    def can_import_database(self) -> bool:
        """检查是否可以导入数据库"""
        return self.has_permission("can_import_database")
        
    def can_auxiliary_test(self) -> bool:
        """检查是否可以使用辅助测试功能"""
        return self.has_permission("can_auxiliary_test")
    
    def can_access_all_features(self) -> bool:
        """检查是否可以访问所有功能"""
        return self.has_permission("can_access_all_features")
    
    def update_user_password(self, username: str, new_password: str) -> bool:
        """更新用户密码"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                password_hash = self.hash_password(new_password)
                cursor.execute('''
                    UPDATE users SET password_hash = ? WHERE username = ?
                ''', (password_hash, username))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                return False
                
        except Exception as e:
            print(f"更新用户密码失败: {e}")
            return False
    
    def get_all_users(self) -> List[str]:
        """获取所有用户名列表"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT username FROM users ORDER BY username')
                results = cursor.fetchall()
                return [row['username'] for row in results]
                
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []
    
    def get_user_role(self, username: str) -> Optional[str]:
        """获取用户角色"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT role FROM users WHERE username = ?', (username,))
                result = cursor.fetchone()
                if result:
                    return result['role']
                return None
                
        except Exception as e:
            print(f"获取用户角色失败: {e}")
            return None
    
    def is_admin(self) -> bool:
        """检查当前用户是否为管理员"""
        return self.current_role == "admin"
    
    def is_user(self) -> bool:
        """检查当前用户是否为普通用户"""
        return self.current_role == "user"
    
    def get_user_info(self) -> Dict:
        """获取当前用户信息"""
        if not self.current_user:
            return {}
        
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT username, role, can_change_password, created_at, updated_at
                    FROM users WHERE username = ?
                ''', (self.current_user,))
                
                result = cursor.fetchone()
                if result:
                    return dict(result)
                return {}
                
        except Exception as e:
            print(f"获取用户信息失败: {e}")
            return {}
    
    def create_user(self, username: str, password: str, role: str = "user", can_change_password: bool = False) -> bool:
        """创建新用户"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                password_hash = self.hash_password(password)
                cursor.execute('''
                    INSERT INTO users (username, password_hash, role, can_change_password)
                    VALUES (?, ?, ?, ?)
                ''', (username, password_hash, role, can_change_password))
                
                conn.commit()
                return True
                
        except sqlite3.IntegrityError:
            print(f"用户名 {username} 已存在")
            return False
        except Exception as e:
            print(f"创建用户失败: {e}")
            return False
    
    def delete_user(self, username: str) -> bool:
        """删除用户"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM users WHERE username = ?', (username,))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                return False
                
        except Exception as e:
            print(f"删除用户失败: {e}")
            return False
    
    def update_user_role(self, username: str, new_role: str) -> bool:
        """更新用户角色"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users SET role = ? WHERE username = ?
                ''', (new_role, username))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                return False
                
        except Exception as e:
            print(f"更新用户角色失败: {e}")
            return False
    
    def get_all_users_info(self) -> List[Dict]:
        """获取所有用户的详细信息"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT username, role, can_change_password, created_at, updated_at
                    FROM users ORDER BY username
                ''')
                
                results = cursor.fetchall()
                return [dict(row) for row in results]
                
        except Exception as e:
            print(f"获取所有用户信息失败: {e}")
            return []
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"备份数据库失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            import shutil
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.db_path)
                return True
            return False
        except Exception as e:
            print(f"恢复数据库失败: {e}")
            return False
