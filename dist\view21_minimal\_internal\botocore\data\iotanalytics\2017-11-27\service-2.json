{"version": "2.0", "metadata": {"apiVersion": "2017-11-27", "endpointPrefix": "iotanalytics", "protocol": "rest-json", "serviceFullName": "AWS IoT Analytics", "serviceId": "IoTAnalytics", "signatureVersion": "v4", "signingName": "iotanalytics", "uid": "iotanalytics-2017-11-27"}, "operations": {"BatchPutMessage": {"name": "BatchPutMessage", "http": {"method": "POST", "requestUri": "/messages/batch", "responseCode": 200}, "input": {"shape": "BatchPutMessageRequest"}, "output": {"shape": "BatchPutMessageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Sends messages to a channel.</p>"}, "CancelPipelineReprocessing": {"name": "CancelPipelineReprocessing", "http": {"method": "DELETE", "requestUri": "/pipelines/{pipelineName}/reprocessing/{reprocessingId}"}, "input": {"shape": "CancelPipelineReprocessingRequest"}, "output": {"shape": "CancelPipelineReprocessingResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Cancels the reprocessing of data through the pipeline.</p>"}, "CreateChannel": {"name": "CreateChannel", "http": {"method": "POST", "requestUri": "/channels", "responseCode": 201}, "input": {"shape": "CreateChannelRequest"}, "output": {"shape": "CreateChannelResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Used to create a channel. A channel collects data from an MQTT topic and archives the raw, unprocessed messages before publishing the data to a pipeline.</p>"}, "CreateDataset": {"name": "CreateDataset", "http": {"method": "POST", "requestUri": "/datasets", "responseCode": 201}, "input": {"shape": "CreateDatasetRequest"}, "output": {"shape": "CreateDatasetResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Used to create a dataset. A dataset stores data retrieved from a data store by applying a <code>queryAction</code> (a SQL query) or a <code>containerAction</code> (executing a containerized application). This operation creates the skeleton of a dataset. The dataset can be populated manually by calling <code>CreateDatasetContent</code> or automatically according to a trigger you specify.</p>"}, "CreateDatasetContent": {"name": "Create<PERSON><PERSON>set<PERSON><PERSON>nt", "http": {"method": "POST", "requestUri": "/datasets/{datasetName}/content"}, "input": {"shape": "CreateDatasetContentRequest"}, "output": {"shape": "CreateDatasetContentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates the content of a dataset by applying a <code>queryAction</code> (a SQL query) or a <code>containerAction</code> (executing a containerized application).</p>"}, "CreateDatastore": {"name": "CreateDatastore", "http": {"method": "POST", "requestUri": "/datastores", "responseCode": 201}, "input": {"shape": "CreateDatastoreRequest"}, "output": {"shape": "CreateDatastoreResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a data store, which is a repository for messages.</p>"}, "CreatePipeline": {"name": "Create<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/pipelines", "responseCode": 201}, "input": {"shape": "CreatePipelineRequest"}, "output": {"shape": "CreatePipelineResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a pipeline. A pipeline consumes messages from a channel and allows you to process the messages before storing them in a data store. You must specify both a <code>channel</code> and a <code>datastore</code> activity and, optionally, as many as 23 additional activities in the <code>pipelineActivities</code> array.</p>"}, "DeleteChannel": {"name": "DeleteChannel", "http": {"method": "DELETE", "requestUri": "/channels/{channelName}", "responseCode": 204}, "input": {"shape": "DeleteChannelRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the specified channel.</p>"}, "DeleteDataset": {"name": "DeleteDataset", "http": {"method": "DELETE", "requestUri": "/datasets/{datasetName}", "responseCode": 204}, "input": {"shape": "DeleteDatasetRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the specified dataset.</p> <p>You do not have to delete the content of the dataset before you perform this operation.</p>"}, "DeleteDatasetContent": {"name": "DeleteDatasetContent", "http": {"method": "DELETE", "requestUri": "/datasets/{datasetName}/content", "responseCode": 204}, "input": {"shape": "DeleteDatasetContentRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the content of the specified dataset.</p>"}, "DeleteDatastore": {"name": "DeleteDatastore", "http": {"method": "DELETE", "requestUri": "/datastores/{datastoreName}", "responseCode": 204}, "input": {"shape": "DeleteDatastoreRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the specified data store.</p>"}, "DeletePipeline": {"name": "DeletePipeline", "http": {"method": "DELETE", "requestUri": "/pipelines/{pipelineName}", "responseCode": 204}, "input": {"shape": "DeletePipelineRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the specified pipeline.</p>"}, "DescribeChannel": {"name": "DescribeChannel", "http": {"method": "GET", "requestUri": "/channels/{channelName}"}, "input": {"shape": "DescribeChannelRequest"}, "output": {"shape": "DescribeChannelResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a channel.</p>"}, "DescribeDataset": {"name": "DescribeDataset", "http": {"method": "GET", "requestUri": "/datasets/{datasetName}"}, "input": {"shape": "DescribeDatasetRequest"}, "output": {"shape": "DescribeDatasetResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a dataset.</p>"}, "DescribeDatastore": {"name": "DescribeDatastore", "http": {"method": "GET", "requestUri": "/datastores/{datastoreName}"}, "input": {"shape": "DescribeDatastoreRequest"}, "output": {"shape": "DescribeDatastoreResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a data store.</p>"}, "DescribeLoggingOptions": {"name": "DescribeLoggingOptions", "http": {"method": "GET", "requestUri": "/logging"}, "input": {"shape": "DescribeLoggingOptionsRequest"}, "output": {"shape": "DescribeLoggingOptionsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves the current settings of the IoT Analytics logging options.</p>"}, "DescribePipeline": {"name": "Describe<PERSON><PERSON>eline", "http": {"method": "GET", "requestUri": "/pipelines/{pipelineName}"}, "input": {"shape": "DescribePipelineRequest"}, "output": {"shape": "DescribePipelineResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about a pipeline.</p>"}, "GetDatasetContent": {"name": "GetDatasetContent", "http": {"method": "GET", "requestUri": "/datasets/{datasetName}/content"}, "input": {"shape": "GetDatasetContentRequest"}, "output": {"shape": "GetDatasetContentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves the contents of a dataset as presigned URIs.</p>"}, "ListChannels": {"name": "ListChannels", "http": {"method": "GET", "requestUri": "/channels"}, "input": {"shape": "ListChannelsRequest"}, "output": {"shape": "ListChannelsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a list of channels.</p>"}, "ListDatasetContents": {"name": "ListDatasetContents", "http": {"method": "GET", "requestUri": "/datasets/{datasetName}/contents"}, "input": {"shape": "ListDatasetContentsRequest"}, "output": {"shape": "ListDatasetContentsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists information about dataset contents that have been created.</p>"}, "ListDatasets": {"name": "ListDatasets", "http": {"method": "GET", "requestUri": "/datasets"}, "input": {"shape": "ListDatasetsRequest"}, "output": {"shape": "ListDatasetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves information about datasets.</p>"}, "ListDatastores": {"name": "ListDatastores", "http": {"method": "GET", "requestUri": "/datastores"}, "input": {"shape": "ListDatastoresRequest"}, "output": {"shape": "ListDatastoresResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a list of data stores.</p>"}, "ListPipelines": {"name": "ListPipelines", "http": {"method": "GET", "requestUri": "/pipelines"}, "input": {"shape": "ListPipelinesRequest"}, "output": {"shape": "ListPipelinesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a list of pipelines.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the tags (metadata) that you have assigned to the resource.</p>"}, "PutLoggingOptions": {"name": "PutLoggingOptions", "http": {"method": "PUT", "requestUri": "/logging"}, "input": {"shape": "PutLoggingOptionsRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Sets or updates the IoT Analytics logging options.</p> <p>If you update the value of any <code>loggingOptions</code> field, it takes up to one minute for the change to take effect. Also, if you change the policy attached to the role you specified in the <code>roleArn</code> field (for example, to correct an invalid policy), it takes up to five minutes for that change to take effect. </p>"}, "RunPipelineActivity": {"name": "RunPipelineActivity", "http": {"method": "POST", "requestUri": "/pipelineactivities/run"}, "input": {"shape": "RunPipelineActivityRequest"}, "output": {"shape": "RunPipelineActivityResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Simulates the results of running a pipeline activity on a message payload.</p>"}, "SampleChannelData": {"name": "SampleChannelData", "http": {"method": "GET", "requestUri": "/channels/{channelName}/sample"}, "input": {"shape": "SampleChannelDataRequest"}, "output": {"shape": "SampleChannelDataResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a sample of messages from the specified channel ingested during the specified timeframe. Up to 10 messages can be retrieved.</p>"}, "StartPipelineReprocessing": {"name": "StartPipelineReprocessing", "http": {"method": "POST", "requestUri": "/pipelines/{pipelineName}/reprocessing"}, "input": {"shape": "StartPipelineReprocessingRequest"}, "output": {"shape": "StartPipelineReprocessingResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Starts the reprocessing of raw message data through the pipeline.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds to or modifies the tags of the given resource. Tags are metadata that can be used to manage a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes the given tags (metadata) from the resource.</p>"}, "UpdateChannel": {"name": "UpdateChannel", "http": {"method": "PUT", "requestUri": "/channels/{channelName}"}, "input": {"shape": "UpdateChannelRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Used to update the settings of a channel.</p>"}, "UpdateDataset": {"name": "UpdateDataset", "http": {"method": "PUT", "requestUri": "/datasets/{datasetName}"}, "input": {"shape": "UpdateDatasetRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates the settings of a dataset.</p>"}, "UpdateDatastore": {"name": "UpdateDatastore", "http": {"method": "PUT", "requestUri": "/datastores/{datastoreName}"}, "input": {"shape": "UpdateDatastoreRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Used to update the settings of a data store.</p>"}, "UpdatePipeline": {"name": "Update<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "PUT", "requestUri": "/pipelines/{pipelineName}"}, "input": {"shape": "UpdatePipelineRequest"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates the settings of a pipeline. You must specify both a <code>channel</code> and a <code>datastore</code> activity and, optionally, as many as 23 additional activities in the <code>pipelineActivities</code> array.</p>"}}, "shapes": {"ActivityBatchSize": {"type": "integer", "max": 1000, "min": 1}, "ActivityName": {"type": "string", "max": 128, "min": 1}, "AddAttributesActivity": {"type": "structure", "required": ["name", "attributes"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the addAttributes activity.</p>"}, "attributes": {"shape": "AttributeNameMapping", "documentation": "<p>A list of 1-50 <code>AttributeNameMapping</code> objects that map an existing attribute to a new attribute.</p> <note> <p>The existing attributes remain in the message, so if you want to remove the originals, use <code>RemoveAttributeActivity</code>.</p> </note>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that adds other attributes based on existing attributes in the message.</p>"}, "AttributeName": {"type": "string", "max": 256, "min": 1}, "AttributeNameMapping": {"type": "map", "key": {"shape": "AttributeName"}, "value": {"shape": "AttributeName"}, "max": 50, "min": 1}, "AttributeNames": {"type": "list", "member": {"shape": "AttributeName"}, "max": 50, "min": 1}, "BatchPutMessageErrorEntries": {"type": "list", "member": {"shape": "BatchPutMessageErrorEntry"}}, "BatchPutMessageErrorEntry": {"type": "structure", "members": {"messageId": {"shape": "MessageId", "documentation": "<p>The ID of the message that caused the error. See the value corresponding to the <code>messageId</code> key in the message object.</p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p>The code associated with the error.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The message associated with the error.</p>"}}, "documentation": "<p>Contains informations about errors.</p>"}, "BatchPutMessageRequest": {"type": "structure", "required": ["channelName", "messages"], "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel where the messages are sent.</p>"}, "messages": {"shape": "Messages", "documentation": "<p>The list of messages to be sent. Each message has the format: { \"messageId\": \"string\", \"payload\": \"string\"}.</p> <p>The field names of message payloads (data) that you send to IoT Analytics:</p> <ul> <li> <p>Must contain only alphanumeric characters and undescores (_). No other special characters are allowed.</p> </li> <li> <p>Must begin with an alphabetic character or single underscore (_).</p> </li> <li> <p>Cannot contain hyphens (-).</p> </li> <li> <p>In regular expression terms: \"^[A-Za-z_]([A-Za-z0-9]*|[A-Za-z0-9][A-Za-z0-9_]*)$\". </p> </li> <li> <p>Cannot be more than 255 characters.</p> </li> <li> <p>Are case insensitive. (Fields named foo and FOO in the same payload are considered duplicates.)</p> </li> </ul> <p>For example, {\"temp_01\": 29} or {\"_temp_01\": 29} are valid, but {\"temp-01\": 29}, {\"01_temp\": 29} or {\"__temp_01\": 29} are invalid in message payloads. </p>"}}}, "BatchPutMessageResponse": {"type": "structure", "members": {"batchPutMessageErrorEntries": {"shape": "BatchPutMessageErrorEntries", "documentation": "<p>A list of any errors encountered when sending the messages to the channel.</p>"}}}, "BucketKeyExpression": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z0-9!_.*'()/{}:-]*$"}, "BucketName": {"type": "string", "max": 255, "min": 3, "pattern": "^[a-zA-Z0-9.\\-_]*$"}, "CancelPipelineReprocessingRequest": {"type": "structure", "required": ["pipelineName", "reprocessingId"], "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of pipeline for which data reprocessing is canceled.</p>", "location": "uri", "locationName": "pipelineName"}, "reprocessingId": {"shape": "ReprocessingId", "documentation": "<p>The ID of the reprocessing task (returned by <code>StartPipelineReprocessing</code>).</p>", "location": "uri", "locationName": "reprocessingId"}}}, "CancelPipelineReprocessingResponse": {"type": "structure", "members": {}}, "Channel": {"type": "structure", "members": {"name": {"shape": "ChannelName", "documentation": "<p>The name of the channel.</p>"}, "storage": {"shape": "ChannelStorage", "documentation": "<p>Where channel data is stored. You can choose one of <code>serviceManagedS3</code> or <code>customerManagedS3</code> storage. If not specified, the default is <code>serviceManagedS3</code>. You can't change this storage option after the channel is created.</p>"}, "arn": {"shape": "ChannelArn", "documentation": "<p>The ARN of the channel.</p>"}, "status": {"shape": "ChannelStatus", "documentation": "<p>The status of the channel.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the channel.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the channel was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>When the channel was last updated.</p>"}, "lastMessageArrivalTime": {"shape": "Timestamp", "documentation": "<p>The last time when a new message arrived in the channel.</p> <p>IoT Analytics updates this value at most once per minute for one channel. Hence, the <code>lastMessageArrivalTime</code> value is an approximation.</p> <p>This feature only applies to messages that arrived in the data store after October 23, 2020. </p>"}}, "documentation": "<p>A collection of data from an MQTT topic. Channels archive the raw, unprocessed messages before publishing the data to a pipeline.</p>"}, "ChannelActivity": {"type": "structure", "required": ["name", "channelName"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the channel activity.</p>"}, "channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel from which the messages are processed.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>The activity that determines the source of the messages to be processed.</p>"}, "ChannelArn": {"type": "string"}, "ChannelMessages": {"type": "structure", "members": {"s3Paths": {"shape": "S3PathChannelMessages", "documentation": "<p>Specifies one or more keys that identify the Amazon Simple Storage Service (Amazon S3) objects that save your channel messages.</p> <p>You must use the full path for the key.</p> <p>Example path: <code>channel/mychannel/__dt=2020-02-29 00:00:00/1582940490000_1582940520000_123456789012_mychannel_0_2118.0.json.gz</code> </p>"}}, "documentation": "<p>Specifies one or more sets of channel messages.</p>"}, "ChannelName": {"type": "string", "max": 128, "min": 1, "pattern": "(^(?!_{2}))(^[a-zA-Z0-9_]+$)"}, "ChannelStatistics": {"type": "structure", "members": {"size": {"shape": "EstimatedResourceSize", "documentation": "<p>The estimated size of the channel.</p>"}}, "documentation": "<p>Statistics information about the channel.</p>"}, "ChannelStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING"]}, "ChannelStorage": {"type": "structure", "members": {"serviceManagedS3": {"shape": "ServiceManagedChannelS3Storage", "documentation": "<p>Used to store channel data in an S3 bucket managed by IoT Analytics. You can't change the choice of S3 storage after the data store is created.</p>"}, "customerManagedS3": {"shape": "CustomerManagedChannelS3Storage", "documentation": "<p>Used to store channel data in an S3 bucket that you manage. If customer managed storage is selected, the <code>retentionPeriod</code> parameter is ignored. You can't change the choice of S3 storage after the data store is created.</p>"}}, "documentation": "<p>Where channel data is stored. You may choose one of <code>serviceManagedS3</code>, <code>customerManagedS3</code> storage. If not specified, the default is <code>serviceManagedS3</code>. This can't be changed after creation of the channel.</p>"}, "ChannelStorageSummary": {"type": "structure", "members": {"serviceManagedS3": {"shape": "ServiceManagedChannelS3StorageSummary", "documentation": "<p>Used to store channel data in an S3 bucket managed by IoT Analytics.</p>"}, "customerManagedS3": {"shape": "CustomerManagedChannelS3StorageSummary", "documentation": "<p>Used to store channel data in an S3 bucket that you manage.</p>"}}, "documentation": "<p>Where channel data is stored.</p>"}, "ChannelSummaries": {"type": "list", "member": {"shape": "ChannelSummary"}}, "ChannelSummary": {"type": "structure", "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel.</p>"}, "channelStorage": {"shape": "ChannelStorageSummary", "documentation": "<p>Where channel data is stored.</p>"}, "status": {"shape": "ChannelStatus", "documentation": "<p>The status of the channel.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the channel was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The last time the channel was updated.</p>"}, "lastMessageArrivalTime": {"shape": "Timestamp", "documentation": "<p>The last time when a new message arrived in the channel.</p> <p>IoT Analytics updates this value at most once per minute for one channel. Hence, the <code>lastMessageArrivalTime</code> value is an approximation.</p> <p>This feature only applies to messages that arrived in the data store after October 23, 2020. </p>"}}, "documentation": "<p>A summary of information about a channel.</p>"}, "Column": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "ColumnName", "documentation": "<p>The name of the column.</p>"}, "type": {"shape": "ColumnDataType", "documentation": "<p>The type of data. For more information about the supported data types, see <a href=\"https://docs.aws.amazon.com/glue/latest/dg/aws-glue-api-common.html\">Common data types</a> in the <i>Glue Developer Guide</i>.</p>"}}, "documentation": "<p>Contains information about a column that stores your data.</p>"}, "ColumnDataType": {"type": "string", "max": 131072, "min": 1, "pattern": "^[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*$"}, "ColumnName": {"type": "string", "max": 255, "min": 1, "pattern": "^[A-Za-z_]([A-Za-z0-9]*|[A-Za-z0-9][A-Za-z0-9_]*)$"}, "Columns": {"type": "list", "member": {"shape": "Column"}}, "ComputeType": {"type": "string", "enum": ["ACU_1", "ACU_2"]}, "ContainerDatasetAction": {"type": "structure", "required": ["image", "executionRoleArn", "resourceConfiguration"], "members": {"image": {"shape": "Image", "documentation": "<p>The ARN of the Docker container stored in your account. The Docker container contains an application and required support libraries and is used to generate dataset contents.</p>"}, "executionRoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that gives permission to the system to access required resources to run the <code>containerAction</code>. This includes, at minimum, permission to retrieve the dataset contents that are the input to the containerized application.</p>"}, "resourceConfiguration": {"shape": "ResourceConfiguration", "documentation": "<p>Configuration of the resource that executes the <code>containerAction</code>.</p>"}, "variables": {"shape": "Variables", "documentation": "<p>The values of variables used in the context of the execution of the containerized application (basically, parameters passed to the application). Each variable must have a name and a value given by one of <code>stringValue</code>, <code>datasetContentVersionValue</code>, or <code>outputFileUriValue</code>.</p>"}}, "documentation": "<p>Information required to run the <code>containerAction</code> to produce dataset contents.</p>"}, "CreateChannelRequest": {"type": "structure", "required": ["channelName"], "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel.</p>"}, "channelStorage": {"shape": "ChannelStorage", "documentation": "<p>Where channel data is stored. You can choose one of <code>serviceManagedS3</code> or <code>customerManagedS3</code> storage. If not specified, the default is <code>serviceManagedS3</code>. You can't change this storage option after the channel is created.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the channel. When <code>customerManagedS3</code> storage is selected, this parameter is ignored.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata which can be used to manage the channel.</p>"}}}, "CreateChannelResponse": {"type": "structure", "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel.</p>"}, "channelArn": {"shape": "ChannelArn", "documentation": "<p>The ARN of the channel.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the channel.</p>"}}}, "CreateDatasetContentRequest": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset.</p>", "location": "uri", "locationName": "datasetName"}, "versionId": {"shape": "DatasetContentVersion", "documentation": "<p>The version ID of the dataset content. To specify <code>versionId</code> for a dataset content, the dataset must use a <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/APIReference/API_DeltaTime.html\">DeltaTimer</a> filter.</p>"}}}, "CreateDatasetContentResponse": {"type": "structure", "members": {"versionId": {"shape": "DatasetContentVersion", "documentation": "<p>The version ID of the dataset contents that are being created.</p>"}}}, "CreateDatasetRequest": {"type": "structure", "required": ["datasetName", "actions"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset.</p>"}, "actions": {"shape": "DatasetActions", "documentation": "<p>A list of actions that create the dataset contents.</p>"}, "triggers": {"shape": "DatasetTriggers", "documentation": "<p>A list of triggers. A trigger causes dataset contents to be populated at a specified time interval or when another dataset's contents are created. The list of triggers can be empty or contain up to five <code>DataSetTrigger</code> objects.</p>"}, "contentDeliveryRules": {"shape": "DatasetContentDeliveryRules", "documentation": "<p>When dataset contents are created, they are delivered to destinations specified here.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>Optional. How long, in days, versions of dataset contents are kept for the dataset. If not specified or set to <code>null</code>, versions of dataset contents are retained for at most 90 days. The number of versions of dataset contents retained is determined by the <code>versioningConfiguration</code> parameter. For more information, see <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/userguide/getting-started.html#aws-iot-analytics-dataset-versions\"> Keeping Multiple Versions of IoT Analytics datasets</a> in the <i>IoT Analytics User Guide</i>.</p>"}, "versioningConfiguration": {"shape": "VersioningConfiguration", "documentation": "<p>Optional. How many versions of dataset contents are kept. If not specified or set to null, only the latest version plus the latest succeeded version (if they are different) are kept for the time period specified by the <code>retentionPeriod</code> parameter. For more information, see <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/userguide/getting-started.html#aws-iot-analytics-dataset-versions\">Keeping Multiple Versions of IoT Analytics datasets</a> in the <i>IoT Analytics User Guide</i>.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata which can be used to manage the dataset.</p>"}, "lateDataRules": {"shape": "LateDataRules", "documentation": "<p>A list of data rules that send notifications to CloudWatch, when data arrives late. To specify <code>lateDataRules</code>, the dataset must use a <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/APIReference/API_DeltaTime.html\">DeltaTimer</a> filter.</p>"}}}, "CreateDatasetResponse": {"type": "structure", "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset.</p>"}, "datasetArn": {"shape": "DatasetArn", "documentation": "<p>The ARN of the dataset.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, dataset contents are kept for the dataset.</p>"}}}, "CreateDatastoreRequest": {"type": "structure", "required": ["datastoreName"], "members": {"datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store.</p>"}, "datastoreStorage": {"shape": "DatastoreStorage", "documentation": "<p>Where data in a data store is stored.. You can choose <code>serviceManagedS3</code> storage, <code>customerManagedS3</code> storage, or <code>iotSiteWiseMultiLayerStorage</code> storage. The default is <code>serviceManagedS3</code>. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the data store. When <code>customerManagedS3</code> storage is selected, this parameter is ignored.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata which can be used to manage the data store.</p>"}, "fileFormatConfiguration": {"shape": "FileFormatConfiguration", "documentation": "<p>Contains the configuration information of file formats. IoT Analytics data stores support JSON and <a href=\"https://parquet.apache.org/\">Parquet</a>.</p> <p>The default file format is JSON. You can specify only one format.</p> <p>You can't change the file format after you create the data store.</p>"}, "datastorePartitions": {"shape": "DatastorePartitions", "documentation": "<p> Contains information about the partition dimensions in a data store. </p>"}}}, "CreateDatastoreResponse": {"type": "structure", "members": {"datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store.</p>"}, "datastoreArn": {"shape": "DatastoreArn", "documentation": "<p>The ARN of the data store.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the data store.</p>"}}}, "CreatePipelineRequest": {"type": "structure", "required": ["pipelineName", "pipelineActivities"], "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline.</p>"}, "pipelineActivities": {"shape": "PipelineActivities", "documentation": "<p>A list of <code>PipelineActivity</code> objects. Activities perform transformations on your messages, such as removing, renaming or adding message attributes; filtering messages based on attribute values; invoking your Lambda unctions on messages for advanced processing; or performing mathematical transformations to normalize device data.</p> <p>The list can be 2-25 <code>PipelineActivity</code> objects and must contain both a <code>channel</code> and a <code>datastore</code> activity. Each entry in the list must contain only one activity. For example:</p> <p> <code>pipelineActivities = [ { \"channel\": { ... } }, { \"lambda\": { ... } }, ... ]</code> </p>"}, "tags": {"shape": "TagList", "documentation": "<p>Metadata which can be used to manage the pipeline.</p>"}}}, "CreatePipelineResponse": {"type": "structure", "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline.</p>"}, "pipelineArn": {"shape": "PipelineArn", "documentation": "<p>The ARN of the pipeline.</p>"}}}, "CustomerManagedChannelS3Storage": {"type": "structure", "required": ["bucket", "roleArn"], "members": {"bucket": {"shape": "BucketName", "documentation": "<p>The name of the S3 bucket in which channel data is stored.</p>"}, "keyPrefix": {"shape": "S3KeyPrefix", "documentation": "<p>(Optional) The prefix used to create the keys of the channel data objects. Each object in an S3 bucket has a key that is its unique identifier in the bucket. Each object in a bucket has exactly one key. The prefix must end with a forward slash (/).</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants IoT Analytics permission to interact with your Amazon S3 resources.</p>"}}, "documentation": "<p>Used to store channel data in an S3 bucket that you manage. If customer-managed storage is selected, the <code>retentionPeriod</code> parameter is ignored. You can't change the choice of S3 storage after the data store is created.</p>"}, "CustomerManagedChannelS3StorageSummary": {"type": "structure", "members": {"bucket": {"shape": "BucketName", "documentation": "<p>The name of the S3 bucket in which channel data is stored.</p>"}, "keyPrefix": {"shape": "S3KeyPrefix", "documentation": "<p>(Optional) The prefix used to create the keys of the channel data objects. Each object in an S3 bucket has a key that is its unique identifier within the bucket (each object in a bucket has exactly one key). The prefix must end with a forward slash (/).</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants IoT Analytics permission to interact with your Amazon S3 resources.</p>"}}, "documentation": "<p>Used to store channel data in an S3 bucket that you manage.</p>"}, "CustomerManagedDatastoreS3Storage": {"type": "structure", "required": ["bucket", "roleArn"], "members": {"bucket": {"shape": "BucketName", "documentation": "<p>The name of the Amazon S3 bucket where your data is stored.</p>"}, "keyPrefix": {"shape": "S3KeyPrefix", "documentation": "<p>(Optional) The prefix used to create the keys of the data store data objects. Each object in an Amazon S3 bucket has a key that is its unique identifier in the bucket. Each object in a bucket has exactly one key. The prefix must end with a forward slash (/).</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants IoT Analytics permission to interact with your Amazon S3 resources.</p>"}}, "documentation": "<p>S3-customer-managed; When you choose customer-managed storage, the <code>retentionPeriod</code> parameter is ignored. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "CustomerManagedDatastoreS3StorageSummary": {"type": "structure", "members": {"bucket": {"shape": "BucketName", "documentation": "<p>The name of the Amazon S3 bucket where your data is stored.</p>"}, "keyPrefix": {"shape": "S3KeyPrefix", "documentation": "<p>(Optional) The prefix used to create the keys of the data store data objects. Each object in an Amazon S3 bucket has a key that is its unique identifier in the bucket. Each object in a bucket has exactly one key. The prefix must end with a forward slash (/).</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants IoT Analytics permission to interact with your Amazon S3 resources.</p>"}}, "documentation": "<p>Contains information about the data store that you manage.</p>"}, "Dataset": {"type": "structure", "members": {"name": {"shape": "DatasetName", "documentation": "<p>The name of the dataset.</p>"}, "arn": {"shape": "DatasetArn", "documentation": "<p>The ARN of the dataset.</p>"}, "actions": {"shape": "DatasetActions", "documentation": "<p>The <code>DatasetAction</code> objects that automatically create the dataset contents.</p>"}, "triggers": {"shape": "DatasetTriggers", "documentation": "<p>The <code>DatasetTrigger</code> objects that specify when the dataset is automatically updated.</p>"}, "contentDeliveryRules": {"shape": "DatasetContentDeliveryRules", "documentation": "<p>When dataset contents are created they are delivered to destinations specified here.</p>"}, "status": {"shape": "DatasetStatus", "documentation": "<p>The status of the dataset.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the dataset was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The last time the dataset was updated.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>Optional. How long, in days, message data is kept for the dataset.</p>"}, "versioningConfiguration": {"shape": "VersioningConfiguration", "documentation": "<p>Optional. How many versions of dataset contents are kept. If not specified or set to null, only the latest version plus the latest succeeded version (if they are different) are kept for the time period specified by the <code>retentionPeriod</code> parameter. For more information, see <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/userguide/getting-started.html#aws-iot-analytics-dataset-versions\"> Keeping Multiple Versions of IoT Analytics datasets</a> in the <i>IoT Analytics User Guide</i>.</p>"}, "lateDataRules": {"shape": "LateDataRules", "documentation": "<p>A list of data rules that send notifications to CloudWatch, when data arrives late. To specify <code>lateDataRules</code>, the dataset must use a <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/APIReference/API_DeltaTime.html\">DeltaTimer</a> filter.</p>"}}, "documentation": "<p>Information about a dataset.</p>"}, "DatasetAction": {"type": "structure", "members": {"actionName": {"shape": "DatasetActionName", "documentation": "<p>The name of the dataset action by which dataset contents are automatically created.</p>"}, "queryAction": {"shape": "SqlQueryDatasetAction", "documentation": "<p>An <code>SqlQueryDatasetAction</code> object that uses an SQL query to automatically create dataset contents.</p>"}, "containerAction": {"shape": "ContainerDatasetAction", "documentation": "<p>Information that allows the system to run a containerized application to create the dataset contents. The application must be in a Docker container along with any required support libraries.</p>"}}, "documentation": "<p>A <code>DatasetAction</code> object that specifies how dataset contents are automatically created.</p>"}, "DatasetActionName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9_]+$"}, "DatasetActionSummaries": {"type": "list", "member": {"shape": "DatasetActionSummary"}, "max": 1, "min": 1}, "DatasetActionSummary": {"type": "structure", "members": {"actionName": {"shape": "DatasetActionName", "documentation": "<p>The name of the action that automatically creates the dataset's contents.</p>"}, "actionType": {"shape": "DatasetActionType", "documentation": "<p>The type of action by which the dataset's contents are automatically created.</p>"}}, "documentation": "<p>Information about the action that automatically creates the dataset's contents.</p>"}, "DatasetActionType": {"type": "string", "enum": ["QUERY", "CONTAINER"]}, "DatasetActions": {"type": "list", "member": {"shape": "DatasetAction"}, "max": 1, "min": 1}, "DatasetArn": {"type": "string"}, "DatasetContentDeliveryDestination": {"type": "structure", "members": {"iotEventsDestinationConfiguration": {"shape": "IotEventsDestinationConfiguration", "documentation": "<p>Configuration information for delivery of dataset contents to IoT Events.</p>"}, "s3DestinationConfiguration": {"shape": "S3DestinationConfiguration", "documentation": "<p>Configuration information for delivery of dataset contents to Amazon S3.</p>"}}, "documentation": "<p>The destination to which dataset contents are delivered.</p>"}, "DatasetContentDeliveryRule": {"type": "structure", "required": ["destination"], "members": {"entryName": {"shape": "EntryName", "documentation": "<p>The name of the dataset content delivery rules entry.</p>"}, "destination": {"shape": "DatasetContentDeliveryDestination", "documentation": "<p>The destination to which dataset contents are delivered.</p>"}}, "documentation": "<p>When dataset contents are created, they are delivered to destination specified here.</p>"}, "DatasetContentDeliveryRules": {"type": "list", "member": {"shape": "DatasetContentDeliveryRule"}, "max": 20, "min": 0}, "DatasetContentState": {"type": "string", "enum": ["CREATING", "SUCCEEDED", "FAILED"]}, "DatasetContentStatus": {"type": "structure", "members": {"state": {"shape": "DatasetContentState", "documentation": "<p>The state of the dataset contents. Can be one of READY, CREATING, SUCCEEDED, or FAILED.</p>"}, "reason": {"shape": "Reason", "documentation": "<p>The reason the dataset contents are in this state.</p>"}}, "documentation": "<p>The state of the dataset contents and the reason they are in this state.</p>"}, "DatasetContentSummaries": {"type": "list", "member": {"shape": "DatasetContentSummary"}}, "DatasetContentSummary": {"type": "structure", "members": {"version": {"shape": "DatasetContentVersion", "documentation": "<p>The version of the dataset contents.</p>"}, "status": {"shape": "DatasetContentStatus", "documentation": "<p>The status of the dataset contents.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The actual time the creation of the dataset contents was started.</p>"}, "scheduleTime": {"shape": "Timestamp", "documentation": "<p>The time the creation of the dataset contents was scheduled to start.</p>"}, "completionTime": {"shape": "Timestamp", "documentation": "<p>The time the dataset content status was updated to SUCCEEDED or FAILED.</p>"}}, "documentation": "<p>Summary information about dataset contents.</p>"}, "DatasetContentVersion": {"type": "string", "max": 36, "min": 7}, "DatasetContentVersionValue": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset whose latest contents are used as input to the notebook or application.</p>"}}, "documentation": "<p>The dataset whose latest contents are used as input to the notebook or application.</p>"}, "DatasetEntries": {"type": "list", "member": {"shape": "DatasetEntry"}}, "DatasetEntry": {"type": "structure", "members": {"entryName": {"shape": "EntryName", "documentation": "<p>The name of the dataset item.</p>"}, "dataURI": {"shape": "PresignedURI", "documentation": "<p>The presigned URI of the dataset item.</p>"}}, "documentation": "<p>The reference to a dataset entry.</p>"}, "DatasetName": {"type": "string", "max": 128, "min": 1, "pattern": "(^(?!_{2}))(^[a-zA-Z0-9_]+$)"}, "DatasetStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING"]}, "DatasetSummaries": {"type": "list", "member": {"shape": "DatasetSummary"}}, "DatasetSummary": {"type": "structure", "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset.</p>"}, "status": {"shape": "DatasetStatus", "documentation": "<p>The status of the dataset.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The time the dataset was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The last time the dataset was updated.</p>"}, "triggers": {"shape": "DatasetTriggers", "documentation": "<p>A list of triggers. A trigger causes dataset content to be populated at a specified time interval or when another dataset is populated. The list of triggers can be empty or contain up to five <code>DataSetTrigger</code> objects</p>"}, "actions": {"shape": "DatasetActionSummaries", "documentation": "<p>A list of <code>DataActionSummary</code> objects.</p>"}}, "documentation": "<p>A summary of information about a dataset.</p>"}, "DatasetTrigger": {"type": "structure", "members": {"schedule": {"shape": "Schedule", "documentation": "<p>The Schedule when the trigger is initiated.</p>"}, "dataset": {"shape": "TriggeringDataset", "documentation": "<p>The dataset whose content creation triggers the creation of this dataset's contents.</p>"}}, "documentation": "<p>The <code>DatasetTrigger</code> that specifies when the dataset is automatically updated.</p>"}, "DatasetTriggers": {"type": "list", "member": {"shape": "DatasetTrigger"}, "max": 5, "min": 0}, "Datastore": {"type": "structure", "members": {"name": {"shape": "DatastoreName", "documentation": "<p>The name of the data store.</p>"}, "storage": {"shape": "DatastoreStorage", "documentation": "<p>Where data in a data store is stored.. You can choose <code>serviceManagedS3</code> storage, <code>customerManagedS3</code> storage, or <code>iotSiteWiseMultiLayerStorage</code> storage. The default is <code>serviceManagedS3</code>. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "arn": {"shape": "DatastoreArn", "documentation": "<p>The ARN of the data store.</p>"}, "status": {"shape": "DatastoreStatus", "documentation": "<p>The status of a data store:</p> <dl> <dt>CREATING</dt> <dd> <p>The data store is being created.</p> </dd> <dt>ACTIVE</dt> <dd> <p>The data store has been created and can be used.</p> </dd> <dt>DELETING</dt> <dd> <p>The data store is being deleted.</p> </dd> </dl>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the data store. When <code>customerManagedS3</code> storage is selected, this parameter is ignored.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the data store was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The last time the data store was updated.</p>"}, "lastMessageArrivalTime": {"shape": "Timestamp", "documentation": "<p>The last time when a new message arrived in the data store.</p> <p>IoT Analytics updates this value at most once per minute for Amazon Simple Storage Service one data store. Hence, the <code>lastMessageArrivalTime</code> value is an approximation.</p> <p>This feature only applies to messages that arrived in the data store after October 23, 2020. </p>"}, "fileFormatConfiguration": {"shape": "FileFormatConfiguration", "documentation": "<p>Contains the configuration information of file formats. IoT Analytics data stores support JSON and <a href=\"https://parquet.apache.org/\">Parquet</a>.</p> <p>The default file format is JSON. You can specify only one format.</p> <p>You can't change the file format after you create the data store.</p>"}, "datastorePartitions": {"shape": "DatastorePartitions", "documentation": "<p> Contains information about the partition dimensions in a data store. </p>"}}, "documentation": "<p>Information about a data store.</p>"}, "DatastoreActivity": {"type": "structure", "required": ["name", "datastoreName"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the datastore activity.</p>"}, "datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store where processed messages are stored.</p>"}}, "documentation": "<p>The datastore activity that specifies where to store the processed data.</p>"}, "DatastoreArn": {"type": "string"}, "DatastoreIotSiteWiseMultiLayerStorage": {"type": "structure", "required": ["customerManagedS3Storage"], "members": {"customerManagedS3Storage": {"shape": "IotSiteWiseCustomerManagedDatastoreS3Storage", "documentation": "<p> Used to store data used by IoT SiteWise in an Amazon S3 bucket that you manage. </p>"}}, "documentation": "<p> Used to store data used by IoT SiteWise in an Amazon S3 bucket that you manage. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "DatastoreIotSiteWiseMultiLayerStorageSummary": {"type": "structure", "members": {"customerManagedS3Storage": {"shape": "IotSiteWiseCustomerManagedDatastoreS3StorageSummary", "documentation": "<p>Used to store data used by IoT SiteWise in an Amazon S3 bucket that you manage.</p>"}}, "documentation": "<p> Contains information about the data store that you manage, which stores data used by IoT SiteWise. </p>"}, "DatastoreName": {"type": "string", "max": 128, "min": 1, "pattern": "(^(?!_{2}))(^[a-zA-Z0-9_]+$)"}, "DatastorePartition": {"type": "structure", "members": {"attributePartition": {"shape": "Partition", "documentation": "<p> A partition dimension defined by an <code>attributeName</code>. </p>"}, "timestampPartition": {"shape": "TimestampPartition", "documentation": "<p> A partition dimension defined by a timestamp attribute. </p>"}}, "documentation": "<p> A single dimension to partition a data store. The dimension must be an <code>AttributePartition</code> or a <code>TimestampPartition</code>. </p>"}, "DatastorePartitions": {"type": "structure", "members": {"partitions": {"shape": "Partitions", "documentation": "<p> A list of partition dimensions in a data store. </p>"}}, "documentation": "<p> Contains information about the partition dimensions in a data store. </p>"}, "DatastoreStatistics": {"type": "structure", "members": {"size": {"shape": "EstimatedResourceSize", "documentation": "<p>The estimated size of the data store.</p>"}}, "documentation": "<p>Statistical information about the data store.</p>"}, "DatastoreStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING"]}, "DatastoreStorage": {"type": "structure", "members": {"serviceManagedS3": {"shape": "ServiceManagedDatastoreS3Storage", "documentation": "<p>Used to store data in an Amazon S3 bucket managed by IoT Analytics. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "customerManagedS3": {"shape": "CustomerManagedDatastoreS3Storage", "documentation": "<p>S3-customer-managed; When you choose customer-managed storage, the <code>retentionPeriod</code> parameter is ignored. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "iotSiteWiseMultiLayerStorage": {"shape": "DatastoreIotSiteWiseMultiLayerStorage", "documentation": "<p> Used to store data used by IoT SiteWise in an Amazon S3 bucket that you manage. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}}, "documentation": "<p>Where data in a data store is stored.. You can choose <code>serviceManagedS3</code> storage, <code>customerManagedS3</code> storage, or <code>iotSiteWiseMultiLayerStorage</code> storage. The default is <code>serviceManagedS3</code>. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "DatastoreStorageSummary": {"type": "structure", "members": {"serviceManagedS3": {"shape": "ServiceManagedDatastoreS3StorageSummary", "documentation": "<p>Used to store data in an Amazon S3 bucket managed by IoT Analytics.</p>"}, "customerManagedS3": {"shape": "CustomerManagedDatastoreS3StorageSummary", "documentation": "<p>Used to store data in an Amazon S3 bucket managed by IoT Analytics.</p>"}, "iotSiteWiseMultiLayerStorage": {"shape": "DatastoreIotSiteWiseMultiLayerStorageSummary", "documentation": "<p> Used to store data used by IoT SiteWise in an Amazon S3 bucket that you manage. </p>"}}, "documentation": "<p>Contains information about your data store.</p>"}, "DatastoreSummaries": {"type": "list", "member": {"shape": "DatastoreSummary"}}, "DatastoreSummary": {"type": "structure", "members": {"datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store.</p>"}, "datastoreStorage": {"shape": "DatastoreStorageSummary", "documentation": "<p>Where data in a data store is stored.</p>"}, "status": {"shape": "DatastoreStatus", "documentation": "<p>The status of the data store.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the data store was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The last time the data store was updated.</p>"}, "lastMessageArrivalTime": {"shape": "Timestamp", "documentation": "<p>The last time when a new message arrived in the data store.</p> <p>IoT Analytics updates this value at most once per minute for Amazon Simple Storage Service one data store. Hence, the <code>lastMessageArrivalTime</code> value is an approximation.</p> <p>This feature only applies to messages that arrived in the data store after October 23, 2020. </p>"}, "fileFormatType": {"shape": "FileFormatType", "documentation": "<p>The file format of the data in the data store.</p>"}, "datastorePartitions": {"shape": "DatastorePartitions", "documentation": "<p> Contains information about the partition dimensions in a data store. </p>"}}, "documentation": "<p>A summary of information about a data store.</p>"}, "DeleteChannelRequest": {"type": "structure", "required": ["channelName"], "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel to delete.</p>", "location": "uri", "locationName": "channelName"}}}, "DeleteDatasetContentRequest": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset whose content is deleted.</p>", "location": "uri", "locationName": "datasetName"}, "versionId": {"shape": "DatasetContentVersion", "documentation": "<p>The version of the dataset whose content is deleted. You can also use the strings \"$LATEST\" or \"$LATEST_SUCCEEDED\" to delete the latest or latest successfully completed data set. If not specified, \"$LATEST_SUCCEEDED\" is the default.</p>", "location": "querystring", "locationName": "versionId"}}}, "DeleteDatasetRequest": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset to delete.</p>", "location": "uri", "locationName": "datasetName"}}}, "DeleteDatastoreRequest": {"type": "structure", "required": ["datastoreName"], "members": {"datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store to delete.</p>", "location": "uri", "locationName": "datastoreName"}}}, "DeletePipelineRequest": {"type": "structure", "required": ["pipelineName"], "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline to delete.</p>", "location": "uri", "locationName": "pipelineName"}}}, "DeltaTime": {"type": "structure", "required": ["offsetSeconds", "timeExpression"], "members": {"offsetSeconds": {"shape": "OffsetSeconds", "documentation": "<p>The number of seconds of estimated in-flight lag time of message data. When you create dataset contents using message data from a specified timeframe, some message data might still be in flight when processing begins, and so do not arrive in time to be processed. Use this field to make allowances for the in flight time of your message data, so that data not processed from a previous timeframe is included with the next timeframe. Otherwise, missed message data would be excluded from processing during the next timeframe too, because its timestamp places it within the previous timeframe.</p>"}, "timeExpression": {"shape": "TimeExpression", "documentation": "<p>An expression by which the time of the message data might be determined. This can be the name of a timestamp field or a SQL expression that is used to derive the time the message data was generated.</p>"}}, "documentation": "<p>Used to limit data to that which has arrived since the last execution of the action.</p>"}, "DeltaTimeSessionWindowConfiguration": {"type": "structure", "required": ["timeoutInMinutes"], "members": {"timeoutInMinutes": {"shape": "SessionTimeoutInMinutes", "documentation": "<p>A time interval. You can use <code>timeoutInMinutes</code> so that IoT Analytics can batch up late data notifications that have been generated since the last execution. IoT Analytics sends one batch of notifications to Amazon CloudWatch Events at one time.</p> <p>For more information about how to write a timestamp expression, see <a href=\"https://prestodb.io/docs/0.172/functions/datetime.html\">Date and Time Functions and Operators</a>, in the <i>Presto 0.172 Documentation</i>.</p>"}}, "documentation": "<p>A structure that contains the configuration information of a delta time session window.</p> <p> <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/APIReference/API_DeltaTime.html\"> <code>DeltaTime</code> </a> specifies a time interval. You can use <code>DeltaTime</code> to create dataset contents with data that has arrived in the data store since the last execution. For an example of <code>DeltaTime</code>, see <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/userguide/automate-create-dataset.html#automate-example6\"> Creating a SQL dataset with a delta window (CLI)</a> in the <i>IoT Analytics User Guide</i>.</p>"}, "DescribeChannelRequest": {"type": "structure", "required": ["channelName"], "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel whose information is retrieved.</p>", "location": "uri", "locationName": "channelName"}, "includeStatistics": {"shape": "IncludeStatisticsFlag", "documentation": "<p>If true, additional statistical information about the channel is included in the response. This feature can't be used with a channel whose S3 storage is customer-managed.</p>", "location": "querystring", "locationName": "includeStatistics"}}}, "DescribeChannelResponse": {"type": "structure", "members": {"channel": {"shape": "Channel", "documentation": "<p>An object that contains information about the channel.</p>"}, "statistics": {"shape": "ChannelStatistics", "documentation": "<p>Statistics about the channel. Included if the <code>includeStatistics</code> parameter is set to <code>true</code> in the request.</p>"}}}, "DescribeDatasetRequest": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset whose information is retrieved.</p>", "location": "uri", "locationName": "datasetName"}}}, "DescribeDatasetResponse": {"type": "structure", "members": {"dataset": {"shape": "Dataset", "documentation": "<p>An object that contains information about the dataset.</p>"}}}, "DescribeDatastoreRequest": {"type": "structure", "required": ["datastoreName"], "members": {"datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store</p>", "location": "uri", "locationName": "datastoreName"}, "includeStatistics": {"shape": "IncludeStatisticsFlag", "documentation": "<p>If true, additional statistical information about the data store is included in the response. This feature can't be used with a data store whose S3 storage is customer-managed.</p>", "location": "querystring", "locationName": "includeStatistics"}}}, "DescribeDatastoreResponse": {"type": "structure", "members": {"datastore": {"shape": "Datastore", "documentation": "<p>Information about the data store.</p>"}, "statistics": {"shape": "DatastoreStatistics", "documentation": "<p>Additional statistical information about the data store. Included if the <code>includeStatistics</code> parameter is set to <code>true</code> in the request.</p>"}}}, "DescribeLoggingOptionsRequest": {"type": "structure", "members": {}}, "DescribeLoggingOptionsResponse": {"type": "structure", "members": {"loggingOptions": {"shape": "LoggingOptions", "documentation": "<p>The current settings of the IoT Analytics logging options.</p>"}}}, "DescribePipelineRequest": {"type": "structure", "required": ["pipelineName"], "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline whose information is retrieved.</p>", "location": "uri", "locationName": "pipelineName"}}}, "DescribePipelineResponse": {"type": "structure", "members": {"pipeline": {"shape": "Pipeline", "documentation": "<p>A <code>Pipeline</code> object that contains information about the pipeline.</p>"}}}, "DeviceRegistryEnrichActivity": {"type": "structure", "required": ["name", "attribute", "thingName", "roleArn"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the <code>deviceRegistryEnrich</code> activity.</p>"}, "attribute": {"shape": "AttributeName", "documentation": "<p>The name of the attribute that is added to the message.</p>"}, "thingName": {"shape": "AttributeName", "documentation": "<p>The name of the IoT device whose registry information is added to the message.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that allows access to the device's registry information.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that adds data from the IoT device registry to your message.</p>"}, "DeviceShadowEnrichActivity": {"type": "structure", "required": ["name", "attribute", "thingName", "roleArn"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the <code>deviceShadowEnrich</code> activity.</p>"}, "attribute": {"shape": "AttributeName", "documentation": "<p>The name of the attribute that is added to the message.</p>"}, "thingName": {"shape": "AttributeName", "documentation": "<p>The name of the IoT device whose shadow information is added to the message.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that allows access to the device's shadow.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that adds information from the IoT Device Shadow service to a message.</p>"}, "DoubleValue": {"type": "double"}, "EndTime": {"type": "timestamp"}, "EntryName": {"type": "string"}, "ErrorCode": {"type": "string"}, "ErrorMessage": {"type": "string"}, "EstimatedResourceSize": {"type": "structure", "members": {"estimatedSizeInBytes": {"shape": "SizeInBytes", "documentation": "<p>The estimated size of the resource, in bytes.</p>"}, "estimatedOn": {"shape": "Timestamp", "documentation": "<p>The time when the estimate of the size of the resource was made.</p>"}}, "documentation": "<p>The estimated size of the resource.</p>"}, "FileFormatConfiguration": {"type": "structure", "members": {"jsonConfiguration": {"shape": "JsonConfiguration", "documentation": "<p>Contains the configuration information of the JSON format.</p>"}, "parquetConfiguration": {"shape": "ParquetConfiguration", "documentation": "<p>Contains the configuration information of the Parquet format.</p>"}}, "documentation": "<p>Contains the configuration information of file formats. IoT Analytics data stores support JSON and <a href=\"https://parquet.apache.org/\">Parquet</a>.</p> <p>The default file format is JSON. You can specify only one format.</p> <p>You can't change the file format after you create the data store.</p>"}, "FileFormatType": {"type": "string", "enum": ["JSON", "PARQUET"]}, "FilterActivity": {"type": "structure", "required": ["name", "filter"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the filter activity.</p>"}, "filter": {"shape": "FilterExpression", "documentation": "<p>An expression that looks like a SQL WHERE clause that must return a Boolean value. Messages that satisfy the condition are passed to the next activity. </p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that filters a message based on its attributes.</p>"}, "FilterExpression": {"type": "string", "max": 256, "min": 1}, "GetDatasetContentRequest": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset whose contents are retrieved.</p>", "location": "uri", "locationName": "datasetName"}, "versionId": {"shape": "DatasetContentVersion", "documentation": "<p>The version of the dataset whose contents are retrieved. You can also use the strings \"$LATEST\" or \"$LATEST_SUCCEEDED\" to retrieve the contents of the latest or latest successfully completed dataset. If not specified, \"$LATEST_SUCCEEDED\" is the default.</p>", "location": "querystring", "locationName": "versionId"}}}, "GetDatasetContentResponse": {"type": "structure", "members": {"entries": {"shape": "DatasetEntries", "documentation": "<p>A list of <code>DatasetEntry</code> objects.</p>"}, "timestamp": {"shape": "Timestamp", "documentation": "<p>The time when the request was made.</p>"}, "status": {"shape": "DatasetContentStatus", "documentation": "<p>The status of the dataset content.</p>"}}}, "GlueConfiguration": {"type": "structure", "required": ["tableName", "databaseName"], "members": {"tableName": {"shape": "GlueTableName", "documentation": "<p>The name of the table in your Glue Data Catalog that is used to perform the ETL operations. An Glue Data Catalog table contains partitioned data and descriptions of data sources and targets.</p>"}, "databaseName": {"shape": "GlueDatabaseName", "documentation": "<p>The name of the database in your Glue Data Catalog in which the table is located. An Glue Data Catalog database contains metadata tables.</p>"}}, "documentation": "<p>Configuration information for coordination with Glue, a fully managed extract, transform and load (ETL) service.</p>"}, "GlueDatabaseName": {"type": "string", "max": 150, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "GlueTableName": {"type": "string", "max": 150, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "Image": {"type": "string", "max": 255}, "IncludeStatisticsFlag": {"type": "boolean"}, "InternalFailureException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>There was an internal failure.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvalidRequestException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The request was not valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "IotEventsDestinationConfiguration": {"type": "structure", "required": ["inputName", "roleArn"], "members": {"inputName": {"shape": "IotEventsInputName", "documentation": "<p>The name of the IoT Events input to which dataset contents are delivered.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants IoT Analytics permission to deliver dataset contents to an IoT Events input.</p>"}}, "documentation": "<p>Configuration information for delivery of dataset contents to IoT Events.</p>"}, "IotEventsInputName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$"}, "IotSiteWiseCustomerManagedDatastoreS3Storage": {"type": "structure", "required": ["bucket"], "members": {"bucket": {"shape": "BucketName", "documentation": "<p> The name of the Amazon S3 bucket where your data is stored. </p>"}, "keyPrefix": {"shape": "S3KeyPrefix", "documentation": "<p> (Optional) The prefix used to create the keys of the data store data objects. Each object in an Amazon S3 bucket has a key that is its unique identifier in the bucket. Each object in a bucket has exactly one key. The prefix must end with a forward slash (/). </p>"}}, "documentation": "<p> Used to store data used by IoT SiteWise in an Amazon S3 bucket that you manage. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "IotSiteWiseCustomerManagedDatastoreS3StorageSummary": {"type": "structure", "members": {"bucket": {"shape": "BucketName", "documentation": "<p> The name of the Amazon S3 bucket where your data is stored. </p>"}, "keyPrefix": {"shape": "S3KeyPrefix", "documentation": "<p> (Optional) The prefix used to create the keys of the data store data objects. Each object in an Amazon S3 bucket has a key that is its unique identifier in the bucket. Each object in a bucket has exactly one key. The prefix must end with a forward slash (/). </p>"}}, "documentation": "<p> Contains information about the data store that you manage, which stores data used by IoT SiteWise. </p>"}, "JsonConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Contains the configuration information of the JSON format.</p>"}, "LambdaActivity": {"type": "structure", "required": ["name", "lambdaName", "batchSize"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the lambda activity.</p>"}, "lambdaName": {"shape": "LambdaName", "documentation": "<p>The name of the Lambda function that is run on the message.</p>"}, "batchSize": {"shape": "ActivityBatchSize", "documentation": "<p>The number of messages passed to the Lambda function for processing.</p> <p>The Lambda function must be able to process all of these messages within five minutes, which is the maximum timeout duration for Lambda functions.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that runs a Lambda function to modify the message.</p>"}, "LambdaName": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9_-]+$"}, "LateDataRule": {"type": "structure", "required": ["ruleConfiguration"], "members": {"ruleName": {"shape": "LateDataRuleName", "documentation": "<p>The name of the late data rule.</p>"}, "ruleConfiguration": {"shape": "LateDataRuleConfiguration", "documentation": "<p>The information needed to configure the late data rule.</p>"}}, "documentation": "<p>A structure that contains the name and configuration information of a late data rule.</p>"}, "LateDataRuleConfiguration": {"type": "structure", "members": {"deltaTimeSessionWindowConfiguration": {"shape": "DeltaTimeSessionWindowConfiguration", "documentation": "<p>The information needed to configure a delta time session window.</p>"}}, "documentation": "<p>The information needed to configure a delta time session window.</p>"}, "LateDataRuleName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9_]+$"}, "LateDataRules": {"type": "list", "member": {"shape": "LateDataRule"}, "max": 1, "min": 1}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The command caused an internal limit to be exceeded.</p>", "error": {"httpStatusCode": 410}, "exception": true}, "ListChannelsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p> <p>The default value is 100.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListChannelsResponse": {"type": "structure", "members": {"channelSummaries": {"shape": "ChannelSummaries", "documentation": "<p>A list of <code>ChannelSummary</code> objects.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to retrieve the next set of results, or <code>null</code> if there are no more results.</p>"}}}, "ListDatasetContentsRequest": {"type": "structure", "required": ["datasetName"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset whose contents information you want to list.</p>", "location": "uri", "locationName": "datasetName"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}, "scheduledOnOrAfter": {"shape": "Timestamp", "documentation": "<p>A filter to limit results to those dataset contents whose creation is scheduled on or after the given time. See the field <code>triggers.schedule</code> in the <code>CreateDataset</code> request. (timestamp)</p>", "location": "querystring", "locationName": "scheduledOnOrAfter"}, "scheduledBefore": {"shape": "Timestamp", "documentation": "<p>A filter to limit results to those dataset contents whose creation is scheduled before the given time. See the field <code>triggers.schedule</code> in the <code>CreateDataset</code> request. (timestamp)</p>", "location": "querystring", "locationName": "scheduledBefore"}}}, "ListDatasetContentsResponse": {"type": "structure", "members": {"datasetContentSummaries": {"shape": "DatasetContentSummaries", "documentation": "<p>Summary information about dataset contents that have been created.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to retrieve the next set of results, or <code>null</code> if there are no more results.</p>"}}}, "ListDatasetsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p> <p>The default value is 100.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDatasetsResponse": {"type": "structure", "members": {"datasetSummaries": {"shape": "DatasetSummaries", "documentation": "<p>A list of <code>DatasetSummary</code> objects.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to retrieve the next set of results, or <code>null</code> if there are no more results.</p>"}}}, "ListDatastoresRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p> <p>The default value is 100.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDatastoresResponse": {"type": "structure", "members": {"datastoreSummaries": {"shape": "DatastoreSummaries", "documentation": "<p>A list of <code>DatastoreSummary</code> objects.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to retrieve the next set of results, or <code>null</code> if there are no more results.</p>"}}}, "ListPipelinesRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p> <p>The default value is 100.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPipelinesResponse": {"type": "structure", "members": {"pipelineSummaries": {"shape": "PipelineSummaries", "documentation": "<p>A list of <code>PipelineSummary</code> objects.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to retrieve the next set of results, or <code>null</code> if there are no more results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource whose tags you want to list.</p>", "location": "querystring", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagList", "documentation": "<p>The tags (metadata) that you have assigned to the resource.</p>"}}}, "LogResult": {"type": "string"}, "LoggingEnabled": {"type": "boolean"}, "LoggingLevel": {"type": "string", "enum": ["ERROR"]}, "LoggingOptions": {"type": "structure", "required": ["roleArn", "level", "enabled"], "members": {"roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants permission to IoT Analytics to perform logging.</p>"}, "level": {"shape": "LoggingLevel", "documentation": "<p>The logging level. Currently, only ERROR is supported.</p>"}, "enabled": {"shape": "LoggingEnabled", "documentation": "<p>If true, logging is enabled for IoT Analytics.</p>"}}, "documentation": "<p>Information about logging options.</p>"}, "MathActivity": {"type": "structure", "required": ["name", "attribute", "math"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the math activity.</p>"}, "attribute": {"shape": "AttributeName", "documentation": "<p>The name of the attribute that contains the result of the math operation.</p>"}, "math": {"shape": "MathExpression", "documentation": "<p>An expression that uses one or more existing attributes and must return an integer value.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that computes an arithmetic expression using the message's attributes.</p>"}, "MathExpression": {"type": "string", "max": 256, "min": 1}, "MaxMessages": {"type": "integer", "max": 10, "min": 1}, "MaxResults": {"type": "integer", "max": 250, "min": 1}, "MaxVersions": {"type": "integer", "max": 1000, "min": 1}, "Message": {"type": "structure", "required": ["messageId", "payload"], "members": {"messageId": {"shape": "MessageId", "documentation": "<p>The ID you want to assign to the message. Each <code>messageId</code> must be unique within each batch sent.</p>"}, "payload": {"shape": "MessagePayload", "documentation": "<p>The payload of the message. This can be a JSON string or a base64-encoded string representing binary data, in which case you must decode it by means of a pipeline activity.</p>"}}, "documentation": "<p>Information about a message.</p>"}, "MessageId": {"type": "string", "max": 128, "min": 1, "pattern": "\\p{ASCII}*"}, "MessagePayload": {"type": "blob"}, "MessagePayloads": {"type": "list", "member": {"shape": "MessagePayload"}, "max": 10, "min": 1}, "Messages": {"type": "list", "member": {"shape": "Message"}}, "NextToken": {"type": "string"}, "OffsetSeconds": {"type": "integer"}, "OutputFileName": {"type": "string", "pattern": "[\\w\\.-]{1,255}"}, "OutputFileUriValue": {"type": "structure", "required": ["fileName"], "members": {"fileName": {"shape": "OutputFileName", "documentation": "<p>The URI of the location where dataset contents are stored, usually the URI of a file in an S3 bucket.</p>"}}, "documentation": "<p>The value of the variable as a structure that specifies an output file URI.</p>"}, "ParquetConfiguration": {"type": "structure", "members": {"schemaDefinition": {"shape": "SchemaDefinition", "documentation": "<p>Information needed to define a schema.</p>"}}, "documentation": "<p>Contains the configuration information of the Parquet format.</p>"}, "Partition": {"type": "structure", "required": ["attributeName"], "members": {"attributeName": {"shape": "PartitionAttributeName", "documentation": "<p> The name of the attribute that defines a partition dimension. </p>"}}, "documentation": "<p> A partition dimension defined by an attribute. </p>"}, "PartitionAttributeName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9_]+$"}, "Partitions": {"type": "list", "member": {"shape": "DatastorePartition"}, "max": 25, "min": 0}, "Pipeline": {"type": "structure", "members": {"name": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline.</p>"}, "arn": {"shape": "PipelineArn", "documentation": "<p>The ARN of the pipeline.</p>"}, "activities": {"shape": "PipelineActivities", "documentation": "<p>The activities that perform transformations on the messages.</p>"}, "reprocessingSummaries": {"shape": "ReprocessingSummaries", "documentation": "<p>A summary of information about the pipeline reprocessing.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the pipeline was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The last time the pipeline was updated.</p>"}}, "documentation": "<p>Contains information about a pipeline.</p>"}, "PipelineActivities": {"type": "list", "member": {"shape": "PipelineActivity"}, "max": 25, "min": 1}, "PipelineActivity": {"type": "structure", "members": {"channel": {"shape": "ChannelActivity", "documentation": "<p>Determines the source of the messages to be processed.</p>"}, "lambda": {"shape": "LambdaActivity", "documentation": "<p>Runs a Lambda function to modify the message.</p>"}, "datastore": {"shape": "DatastoreActivity", "documentation": "<p>Specifies where to store the processed message data.</p>"}, "addAttributes": {"shape": "AddAttributesActivity", "documentation": "<p>Adds other attributes based on existing attributes in the message.</p>"}, "removeAttributes": {"shape": "RemoveAttributesActivity", "documentation": "<p>Removes attributes from a message.</p>"}, "selectAttributes": {"shape": "SelectAttributesActivity", "documentation": "<p>Used to create a new message using only the specified attributes from the original message. </p>"}, "filter": {"shape": "FilterActivity", "documentation": "<p>Filters a message based on its attributes.</p>"}, "math": {"shape": "MathActivity", "documentation": "<p>Computes an arithmetic expression using the message's attributes and adds it to the message.</p>"}, "deviceRegistryEnrich": {"shape": "DeviceRegistryEnrichActivity", "documentation": "<p>Adds data from the IoT device registry to your message.</p>"}, "deviceShadowEnrich": {"shape": "DeviceShadowEnrichActivity", "documentation": "<p>Adds information from the IoT Device Shadow service to a message.</p>"}}, "documentation": "<p>An activity that performs a transformation on a message.</p>"}, "PipelineArn": {"type": "string"}, "PipelineName": {"type": "string", "max": 128, "min": 1, "pattern": "(^(?!_{2}))(^[a-zA-Z0-9_]+$)"}, "PipelineSummaries": {"type": "list", "member": {"shape": "PipelineSummary"}}, "PipelineSummary": {"type": "structure", "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline.</p>"}, "reprocessingSummaries": {"shape": "ReprocessingSummaries", "documentation": "<p>A summary of information about the pipeline reprocessing.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>When the pipeline was created.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>When the pipeline was last updated.</p>"}}, "documentation": "<p>A summary of information about a pipeline.</p>"}, "PresignedURI": {"type": "string"}, "PutLoggingOptionsRequest": {"type": "structure", "required": ["loggingOptions"], "members": {"loggingOptions": {"shape": "LoggingOptions", "documentation": "<p>The new values of the IoT Analytics logging options.</p>"}}}, "QueryFilter": {"type": "structure", "members": {"deltaTime": {"shape": "DeltaTime", "documentation": "<p>Used to limit data to that which has arrived since the last execution of the action.</p>"}}, "documentation": "<p>Information that is used to filter message data, to segregate it according to the timeframe in which it arrives.</p>"}, "QueryFilters": {"type": "list", "member": {"shape": "Query<PERSON><PERSON>er"}, "max": 1, "min": 0}, "Reason": {"type": "string"}, "RemoveAttributesActivity": {"type": "structure", "required": ["name", "attributes"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the <code>removeAttributes</code> activity.</p>"}, "attributes": {"shape": "AttributeNames", "documentation": "<p>A list of 1-50 attributes to remove from the message.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>An activity that removes attributes from a message.</p>"}, "ReprocessingId": {"type": "string"}, "ReprocessingStatus": {"type": "string", "enum": ["RUNNING", "SUCCEEDED", "CANCELLED", "FAILED"]}, "ReprocessingSummaries": {"type": "list", "member": {"shape": "ReprocessingSummary"}}, "ReprocessingSummary": {"type": "structure", "members": {"id": {"shape": "ReprocessingId", "documentation": "<p>The <code>reprocessingId</code> returned by <code>StartPipelineReprocessing</code>.</p>"}, "status": {"shape": "ReprocessingStatus", "documentation": "<p>The status of the pipeline reprocessing.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The time the pipeline reprocessing was created.</p>"}}, "documentation": "<p>Information about pipeline reprocessing.</p>"}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}, "resourceId": {"shape": "resourceId", "documentation": "<p>The ID of the resource.</p>"}, "resourceArn": {"shape": "resourceArn", "documentation": "<p>The ARN of the resource.</p>"}}, "documentation": "<p>A resource with the same name already exists.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceArn": {"type": "string", "max": 2048, "min": 20}, "ResourceConfiguration": {"type": "structure", "required": ["computeType", "volumeSizeInGB"], "members": {"computeType": {"shape": "ComputeType", "documentation": "<p>The type of the compute resource used to execute the <code>containerAction</code>. Possible values are: <code>ACU_1</code> (vCPU=4, memory=16 GiB) or <code>ACU_2</code> (vCPU=8, memory=32 GiB).</p>"}, "volumeSizeInGB": {"shape": "VolumeSizeInGB", "documentation": "<p>The size, in GB, of the persistent storage available to the resource instance used to execute the <code>containerAction</code> (min: 1, max: 50).</p>"}}, "documentation": "<p>The configuration of the resource used to execute the <code>containerAction</code>.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>A resource with the specified name could not be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "RetentionPeriod": {"type": "structure", "members": {"unlimited": {"shape": "UnlimitedRetentionPeriod", "documentation": "<p>If true, message data is kept indefinitely.</p>"}, "numberOfDays": {"shape": "RetentionPeriodInDays", "documentation": "<p>The number of days that message data is kept. The <code>unlimited</code> parameter must be false.</p>"}}, "documentation": "<p>How long, in days, message data is kept.</p>"}, "RetentionPeriodInDays": {"type": "integer", "min": 1}, "RoleArn": {"type": "string", "max": 2048, "min": 20}, "RunPipelineActivityRequest": {"type": "structure", "required": ["pipelineActivity", "payloads"], "members": {"pipelineActivity": {"shape": "PipelineActivity", "documentation": "<p>The pipeline activity that is run. This must not be a channel activity or a data store activity because these activities are used in a pipeline only to load the original message and to store the (possibly) transformed message. If a Lambda activity is specified, only short-running Lambda functions (those with a timeout of less than 30 seconds or less) can be used.</p>"}, "payloads": {"shape": "MessagePayloads", "documentation": "<p>The sample message payloads on which the pipeline activity is run.</p>"}}}, "RunPipelineActivityResponse": {"type": "structure", "members": {"payloads": {"shape": "MessagePayloads", "documentation": "<p>The enriched or transformed sample message payloads as base64-encoded strings. (The results of running the pipeline activity on each input sample message payload, encoded in base64.)</p>"}, "logResult": {"shape": "LogResult", "documentation": "<p>In case the pipeline activity fails, the log message that is generated.</p>"}}}, "S3DestinationConfiguration": {"type": "structure", "required": ["bucket", "key", "roleArn"], "members": {"bucket": {"shape": "BucketName", "documentation": "<p>The name of the S3 bucket to which dataset contents are delivered.</p>"}, "key": {"shape": "BucketKeyExpression", "documentation": "<p>The key of the dataset contents object in an S3 bucket. Each object has a key that is a unique identifier. Each object has exactly one key.</p> <p>You can create a unique key with the following options:</p> <ul> <li> <p>Use <code>!{iotanalytics:scheduleTime}</code> to insert the time of a scheduled SQL query run.</p> </li> <li> <p>Use <code>!{iotanalytics:versionId}</code> to insert a unique hash that identifies a dataset content.</p> </li> <li> <p>Use <code>!{iotanalytics:creationTime}</code> to insert the creation time of a dataset content.</p> </li> </ul> <p>The following example creates a unique key for a CSV file: <code>dataset/mydataset/!{iotanalytics:scheduleTime}/!{iotanalytics:versionId}.csv</code> </p> <note> <p>If you don't use <code>!{iotanalytics:versionId}</code> to specify the key, you might get duplicate keys. For example, you might have two dataset contents with the same <code>scheduleTime</code> but different <code>versionId</code>s. This means that one dataset content overwrites the other. </p> </note>"}, "glueConfiguration": {"shape": "GlueConfiguration", "documentation": "<p>Configuration information for coordination with Glue, a fully managed extract, transform and load (ETL) service.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the role that grants IoT Analytics permission to interact with your Amazon S3 and Glue resources.</p>"}}, "documentation": "<p>Configuration information for delivery of dataset contents to Amazon Simple Storage Service (Amazon S3).</p>"}, "S3KeyPrefix": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z0-9!_.*'()/{}:-]*/$"}, "S3PathChannelMessage": {"type": "string", "max": 1024, "min": 1, "pattern": "^[a-zA-Z0-9/_!'(){}\\*\\s\\.\\-\\=\\:]+$"}, "S3PathChannelMessages": {"type": "list", "member": {"shape": "S3PathChannelMessage"}, "max": 100, "min": 1}, "SampleChannelDataRequest": {"type": "structure", "required": ["channelName"], "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel whose message samples are retrieved.</p>", "location": "uri", "locationName": "channelName"}, "maxMessages": {"shape": "MaxMessages", "documentation": "<p>The number of sample messages to be retrieved. The limit is 10. The default is also 10.</p>", "location": "querystring", "locationName": "maxMessages"}, "startTime": {"shape": "StartTime", "documentation": "<p>The start of the time window from which sample messages are retrieved.</p>", "location": "querystring", "locationName": "startTime"}, "endTime": {"shape": "EndTime", "documentation": "<p>The end of the time window from which sample messages are retrieved.</p>", "location": "querystring", "locationName": "endTime"}}}, "SampleChannelDataResponse": {"type": "structure", "members": {"payloads": {"shape": "MessagePayloads", "documentation": "<p>The list of message samples. Each sample message is returned as a base64-encoded string.</p>"}}}, "Schedule": {"type": "structure", "members": {"expression": {"shape": "ScheduleExpression", "documentation": "<p>The expression that defines when to trigger an update. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/events/ScheduledEvents.html\">Schedule Expressions for Rules</a> in the <i>Amazon CloudWatch Events User Guide</i>.</p>"}}, "documentation": "<p>The schedule for when to trigger an update.</p>"}, "ScheduleExpression": {"type": "string"}, "SchemaDefinition": {"type": "structure", "members": {"columns": {"shape": "Columns", "documentation": "<p>Specifies one or more columns that store your data.</p> <p>Each schema can have up to 100 columns. Each column can have up to 100 nested types.</p>"}}, "documentation": "<p>Information needed to define a schema.</p>"}, "SelectAttributesActivity": {"type": "structure", "required": ["name", "attributes"], "members": {"name": {"shape": "ActivityName", "documentation": "<p>The name of the <code>selectAttributes</code> activity.</p>"}, "attributes": {"shape": "AttributeNames", "documentation": "<p>A list of the attributes to select from the message.</p>"}, "next": {"shape": "ActivityName", "documentation": "<p>The next activity in the pipeline.</p>"}}, "documentation": "<p>Used to create a new message using only the specified attributes from the original message.</p>"}, "ServiceManagedChannelS3Storage": {"type": "structure", "members": {}, "documentation": "<p>Used to store channel data in an S3 bucket managed by IoT Analytics. You can't change the choice of S3 storage after the data store is created.</p>"}, "ServiceManagedChannelS3StorageSummary": {"type": "structure", "members": {}, "documentation": "<p>Used to store channel data in an S3 bucket managed by IoT Analytics.</p>"}, "ServiceManagedDatastoreS3Storage": {"type": "structure", "members": {}, "documentation": "<p>Used to store data in an Amazon S3 bucket managed by IoT Analytics. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "ServiceManagedDatastoreS3StorageSummary": {"type": "structure", "members": {}, "documentation": "<p>Contains information about the data store that is managed by IoT Analytics.</p>"}, "ServiceUnavailableException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The service is temporarily unavailable.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "SessionTimeoutInMinutes": {"type": "integer", "max": 60, "min": 1}, "SizeInBytes": {"type": "double"}, "SqlQuery": {"type": "string"}, "SqlQueryDatasetAction": {"type": "structure", "required": ["sqlQuery"], "members": {"sqlQuery": {"shape": "SqlQuery", "documentation": "<p>A SQL query string.</p>"}, "filters": {"shape": "QueryFilters", "documentation": "<p>Prefilters applied to message data.</p>"}}, "documentation": "<p>The SQL query to modify the message.</p>"}, "StartPipelineReprocessingRequest": {"type": "structure", "required": ["pipelineName"], "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline on which to start reprocessing.</p>", "location": "uri", "locationName": "pipelineName"}, "startTime": {"shape": "StartTime", "documentation": "<p>The start time (inclusive) of raw message data that is reprocessed.</p> <p>If you specify a value for the <code>startTime</code> parameter, you must not use the <code>channelMessages</code> object.</p>"}, "endTime": {"shape": "EndTime", "documentation": "<p>The end time (exclusive) of raw message data that is reprocessed.</p> <p>If you specify a value for the <code>endTime</code> parameter, you must not use the <code>channelMessages</code> object.</p>"}, "channelMessages": {"shape": "ChannelMessages", "documentation": "<p>Specifies one or more sets of channel messages that you want to reprocess.</p> <p>If you use the <code>channelMessages</code> object, you must not specify a value for <code>startTime</code> and <code>endTime</code>.</p>"}}}, "StartPipelineReprocessingResponse": {"type": "structure", "members": {"reprocessingId": {"shape": "ReprocessingId", "documentation": "<p>The ID of the pipeline reprocessing activity that was started.</p>"}}}, "StartTime": {"type": "timestamp"}, "StringValue": {"type": "string", "max": 1024, "min": 0}, "Tag": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "TagKey", "documentation": "<p>The tag's key.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The tag's value.</p>"}}, "documentation": "<p>A set of key-value pairs that are used to manage the resource.</p>"}, "TagKey": {"type": "string", "max": 256, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource whose tags you want to modify.</p>", "location": "querystring", "locationName": "resourceArn"}, "tags": {"shape": "TagList", "documentation": "<p>The new or modified tags for the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 1}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TimeExpression": {"type": "string"}, "Timestamp": {"type": "timestamp"}, "TimestampFormat": {"type": "string", "max": 50, "min": 1, "pattern": "^[a-zA-Z0-9\\s\\[\\]_,.'/:-]*$"}, "TimestampPartition": {"type": "structure", "required": ["attributeName"], "members": {"attributeName": {"shape": "PartitionAttributeName", "documentation": "<p> The attribute name of the partition defined by a timestamp. </p>"}, "timestampFormat": {"shape": "TimestampFormat", "documentation": "<p> The timestamp format of a partition defined by a timestamp. The default format is seconds since epoch (January 1, 1970 at midnight UTC time). </p>"}}, "documentation": "<p> A partition dimension defined by a timestamp attribute. </p>"}, "TriggeringDataset": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "DatasetName", "documentation": "<p>The name of the dataset whose content generation triggers the new dataset content generation.</p>"}}, "documentation": "<p>Information about the dataset whose content generation triggers the new dataset content generation.</p>"}, "UnlimitedRetentionPeriod": {"type": "boolean"}, "UnlimitedVersioning": {"type": "boolean"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource whose tags you want to remove.</p>", "location": "querystring", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of those tags which you want to remove.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateChannelRequest": {"type": "structure", "required": ["channelName"], "members": {"channelName": {"shape": "ChannelName", "documentation": "<p>The name of the channel to be updated.</p>", "location": "uri", "locationName": "channelName"}, "channelStorage": {"shape": "ChannelStorage", "documentation": "<p>Where channel data is stored. You can choose one of <code>serviceManagedS3</code> or <code>customerManagedS3</code> storage. If not specified, the default is <code>serviceManagedS3</code>. You can't change this storage option after the channel is created.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the channel. The retention period can't be updated if the channel's Amazon S3 storage is customer-managed.</p>"}}}, "UpdateDatasetRequest": {"type": "structure", "required": ["datasetName", "actions"], "members": {"datasetName": {"shape": "DatasetName", "documentation": "<p>The name of the dataset to update.</p>", "location": "uri", "locationName": "datasetName"}, "actions": {"shape": "DatasetActions", "documentation": "<p>A list of <code>DatasetAction</code> objects.</p>"}, "triggers": {"shape": "DatasetTriggers", "documentation": "<p>A list of <code>DatasetTrigger</code> objects. The list can be empty or can contain up to five <code>DatasetTrigger</code> objects.</p>"}, "contentDeliveryRules": {"shape": "DatasetContentDeliveryRules", "documentation": "<p>When dataset contents are created, they are delivered to destinations specified here.</p>"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, dataset contents are kept for the dataset.</p>"}, "versioningConfiguration": {"shape": "VersioningConfiguration", "documentation": "<p>Optional. How many versions of dataset contents are kept. If not specified or set to null, only the latest version plus the latest succeeded version (if they are different) are kept for the time period specified by the <code>retentionPeriod</code> parameter. For more information, see <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/userguide/getting-started.html#aws-iot-analytics-dataset-versions\">Keeping Multiple Versions of IoT Analytics datasets</a> in the <i>IoT Analytics User Guide</i>.</p>"}, "lateDataRules": {"shape": "LateDataRules", "documentation": "<p>A list of data rules that send notifications to CloudWatch, when data arrives late. To specify <code>lateDataRules</code>, the dataset must use a <a href=\"https://docs.aws.amazon.com/iotanalytics/latest/APIReference/API_DeltaTime.html\">DeltaTimer</a> filter.</p>"}}}, "UpdateDatastoreRequest": {"type": "structure", "required": ["datastoreName"], "members": {"datastoreName": {"shape": "DatastoreName", "documentation": "<p>The name of the data store to be updated.</p>", "location": "uri", "locationName": "datastoreName"}, "retentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>How long, in days, message data is kept for the data store. The retention period can't be updated if the data store's Amazon S3 storage is customer-managed.</p>"}, "datastoreStorage": {"shape": "DatastoreStorage", "documentation": "<p>Where data in a data store is stored.. You can choose <code>serviceManagedS3</code> storage, <code>customerManagedS3</code> storage, or <code>iotSiteWiseMultiLayerStorage</code> storage. The default is <code>serviceManagedS3</code>. You can't change the choice of Amazon S3 storage after your data store is created. </p>"}, "fileFormatConfiguration": {"shape": "FileFormatConfiguration", "documentation": "<p>Contains the configuration information of file formats. IoT Analytics data stores support JSON and <a href=\"https://parquet.apache.org/\">Parquet</a>.</p> <p>The default file format is JSON. You can specify only one format.</p> <p>You can't change the file format after you create the data store.</p>"}}}, "UpdatePipelineRequest": {"type": "structure", "required": ["pipelineName", "pipelineActivities"], "members": {"pipelineName": {"shape": "PipelineName", "documentation": "<p>The name of the pipeline to update.</p>", "location": "uri", "locationName": "pipelineName"}, "pipelineActivities": {"shape": "PipelineActivities", "documentation": "<p>A list of <code>PipelineActivity</code> objects. Activities perform transformations on your messages, such as removing, renaming or adding message attributes; filtering messages based on attribute values; invoking your Lambda functions on messages for advanced processing; or performing mathematical transformations to normalize device data.</p> <p>The list can be 2-25 <code>PipelineActivity</code> objects and must contain both a <code>channel</code> and a <code>datastore</code> activity. Each entry in the list must contain only one activity. For example:</p> <p> <code>pipelineActivities = [ { \"channel\": { ... } }, { \"lambda\": { ... } }, ... ]</code> </p>"}}}, "Variable": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "VariableName", "documentation": "<p>The name of the variable.</p>"}, "stringValue": {"shape": "StringValue", "documentation": "<p>The value of the variable as a string.</p>"}, "doubleValue": {"shape": "DoubleValue", "documentation": "<p>The value of the variable as a double (numeric).</p>", "box": true}, "datasetContentVersionValue": {"shape": "DatasetContentVersionValue", "documentation": "<p>The value of the variable as a structure that specifies a dataset content version.</p>"}, "outputFileUriValue": {"shape": "OutputFileUriValue", "documentation": "<p>The value of the variable as a structure that specifies an output file URI.</p>"}}, "documentation": "<p>An instance of a variable to be passed to the <code>containerAction</code> execution. Each variable must have a name and a value given by one of <code>stringValue</code>, <code>datasetContentVersionValue</code>, or <code>outputFileUriValue</code>.</p>"}, "VariableName": {"type": "string", "max": 256, "min": 1}, "Variables": {"type": "list", "member": {"shape": "Variable"}, "max": 50, "min": 0}, "VersioningConfiguration": {"type": "structure", "members": {"unlimited": {"shape": "UnlimitedVersioning", "documentation": "<p>If true, unlimited versions of dataset contents are kept.</p>"}, "maxVersions": {"shape": "MaxVersions", "documentation": "<p>How many versions of dataset contents are kept. The <code>unlimited</code> parameter must be <code>false</code>.</p>"}}, "documentation": "<p>Information about the versioning of dataset contents.</p>"}, "VolumeSizeInGB": {"type": "integer", "max": 50, "min": 1}, "errorMessage": {"type": "string"}, "resourceArn": {"type": "string"}, "resourceId": {"type": "string"}}, "documentation": "<p>IoT Analytics allows you to collect large amounts of device data, process messages, and store them. You can then query the data and run sophisticated analytics on it. IoT Analytics enables advanced data exploration through integration with Jupyter Notebooks and data visualization through integration with Amazon QuickSight.</p> <p>Traditional analytics and business intelligence tools are designed to process structured data. IoT data often comes from devices that record noisy processes (such as temperature, motion, or sound). As a result the data from these devices can have significant gaps, corrupted messages, and false readings that must be cleaned up before analysis can occur. Also, IoT data is often only meaningful in the context of other data from external sources. </p> <p>IoT Analytics automates the steps required to analyze data from IoT devices. IoT Analytics filters, transforms, and enriches IoT data before storing it in a time-series data store for analysis. You can set up the service to collect only the data you need from your devices, apply mathematical transforms to process the data, and enrich the data with device-specific metadata such as device type and location before storing it. Then, you can analyze your data by running queries using the built-in SQL query engine, or perform more complex analytics and machine learning inference. IoT Analytics includes pre-built models for common IoT use cases so you can answer questions like which devices are about to fail or which customers are at risk of abandoning their wearable devices.</p>"}