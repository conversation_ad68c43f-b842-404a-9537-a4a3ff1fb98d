{"version": "2.0", "metadata": {"apiVersion": "2020-07-08", "endpointPrefix": "workspaces-web", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon WorkSpaces Web", "serviceId": "WorkSpaces Web", "signatureVersion": "v4", "signingName": "workspaces-web", "uid": "workspaces-web-2020-07-08"}, "operations": {"AssociateBrowserSettings": {"name": "AssociateBrowserSettings", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}/browserSettings", "responseCode": 200}, "input": {"shape": "AssociateBrowserSettingsRequest"}, "output": {"shape": "AssociateBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a browser settings resource with a web portal.</p>", "idempotent": true}, "AssociateIpAccessSettings": {"name": "AssociateIpAccessSettings", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}/ipAccessSettings", "responseCode": 200}, "input": {"shape": "AssociateIpAccessSettingsRequest"}, "output": {"shape": "AssociateIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates an IP access settings resource with a web portal.</p>", "idempotent": true}, "AssociateNetworkSettings": {"name": "AssociateNetworkSettings", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}/networkSettings", "responseCode": 200}, "input": {"shape": "AssociateNetworkSettingsRequest"}, "output": {"shape": "AssociateNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a network settings resource with a web portal.</p>", "idempotent": true}, "AssociateTrustStore": {"name": "AssociateTrustStore", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}/trustStores", "responseCode": 200}, "input": {"shape": "AssociateTrustStoreRequest"}, "output": {"shape": "AssociateTrustStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Associates a trust store with a web portal.</p>", "idempotent": true}, "AssociateUserAccessLoggingSettings": {"name": "AssociateUserAccessLoggingSettings", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}/userAccessLoggingSettings", "responseCode": 200}, "input": {"shape": "AssociateUserAccessLoggingSettingsRequest"}, "output": {"shape": "AssociateUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a user access logging settings resource with a web portal.</p>", "idempotent": true}, "AssociateUserSettings": {"name": "AssociateUserSettings", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}/userSettings", "responseCode": 200}, "input": {"shape": "AssociateUserSettingsRequest"}, "output": {"shape": "AssociateUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a user settings resource with a web portal.</p>", "idempotent": true}, "CreateBrowserSettings": {"name": "CreateBrowserSettings", "http": {"method": "POST", "requestUri": "/browserSettings", "responseCode": 200}, "input": {"shape": "CreateBrowserSettingsRequest"}, "output": {"shape": "CreateBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a browser settings resource that can be associated with a web portal. Once associated with a web portal, browser settings control how the browser will behave once a user starts a streaming session for the web portal. </p>"}, "CreateIdentityProvider": {"name": "CreateIdentityProvider", "http": {"method": "POST", "requestUri": "/identityProviders", "responseCode": 200}, "input": {"shape": "CreateIdentityProviderRequest"}, "output": {"shape": "CreateIdentityProviderResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates an identity provider resource that is then associated with a web portal.</p>"}, "CreateIpAccessSettings": {"name": "CreateIpAccessSettings", "http": {"method": "POST", "requestUri": "/ipAccessSettings", "responseCode": 200}, "input": {"shape": "CreateIpAccessSettingsRequest"}, "output": {"shape": "CreateIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates an IP access settings resource that can be associated with a web portal.</p>"}, "CreateNetworkSettings": {"name": "CreateNetworkSettings", "http": {"method": "POST", "requestUri": "/networkSettings", "responseCode": 200}, "input": {"shape": "CreateNetworkSettingsRequest"}, "output": {"shape": "CreateNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a network settings resource that can be associated with a web portal. Once associated with a web portal, network settings define how streaming instances will connect with your specified VPC. </p>"}, "CreatePortal": {"name": "CreatePortal", "http": {"method": "POST", "requestUri": "/portals", "responseCode": 200}, "input": {"shape": "CreatePortalRequest"}, "output": {"shape": "CreatePortalResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a web portal.</p>"}, "CreateTrustStore": {"name": "CreateTrustStore", "http": {"method": "POST", "requestUri": "/trustStores", "responseCode": 200}, "input": {"shape": "CreateTrustStoreRequest"}, "output": {"shape": "CreateTrustStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a trust store that can be associated with a web portal. A trust store contains certificate authority (CA) certificates. Once associated with a web portal, the browser in a streaming session will recognize certificates that have been issued using any of the CAs in the trust store. If your organization has internal websites that use certificates issued by private CAs, you should add the private CA certificate to the trust store. </p>"}, "CreateUserAccessLoggingSettings": {"name": "CreateUserAccessLoggingSettings", "http": {"method": "POST", "requestUri": "/userAccessLoggingSettings", "responseCode": 200}, "input": {"shape": "CreateUserAccessLoggingSettingsRequest"}, "output": {"shape": "CreateUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a user access logging settings resource that can be associated with a web portal.</p>"}, "CreateUserSettings": {"name": "CreateUserSettings", "http": {"method": "POST", "requestUri": "/userSettings", "responseCode": 200}, "input": {"shape": "CreateUserSettingsRequest"}, "output": {"shape": "CreateUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a user settings resource that can be associated with a web portal. Once associated with a web portal, user settings control how users can transfer data between a streaming session and the their local devices. </p>"}, "DeleteBrowserSettings": {"name": "DeleteBrowserSettings", "http": {"method": "DELETE", "requestUri": "/browserSettings/{browserSettingsArn+}", "responseCode": 200}, "input": {"shape": "DeleteBrowserSettingsRequest"}, "output": {"shape": "DeleteBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes browser settings.</p>", "idempotent": true}, "DeleteIdentityProvider": {"name": "DeleteIdentityProvider", "http": {"method": "DELETE", "requestUri": "/identityProviders/{identityProviderArn+}", "responseCode": 200}, "input": {"shape": "DeleteIdentityProviderRequest"}, "output": {"shape": "DeleteIdentityProviderResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the identity provider.</p>", "idempotent": true}, "DeleteIpAccessSettings": {"name": "DeleteIpAccessSettings", "http": {"method": "DELETE", "requestUri": "/ipAccessSettings/{ipAccessSettingsArn+}", "responseCode": 200}, "input": {"shape": "DeleteIpAccessSettingsRequest"}, "output": {"shape": "DeleteIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes IP access settings.</p>", "idempotent": true}, "DeleteNetworkSettings": {"name": "DeleteNetworkSettings", "http": {"method": "DELETE", "requestUri": "/networkSettings/{networkSettingsArn+}", "responseCode": 200}, "input": {"shape": "DeleteNetworkSettingsRequest"}, "output": {"shape": "DeleteNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes network settings.</p>", "idempotent": true}, "DeletePortal": {"name": "DeletePortal", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}", "responseCode": 200}, "input": {"shape": "DeletePortalRequest"}, "output": {"shape": "DeletePortalResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a web portal.</p>", "idempotent": true}, "DeleteTrustStore": {"name": "DeleteTrustStore", "http": {"method": "DELETE", "requestUri": "/trustStores/{trustStoreArn+}", "responseCode": 200}, "input": {"shape": "DeleteTrustStoreRequest"}, "output": {"shape": "DeleteTrustStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the trust store.</p>", "idempotent": true}, "DeleteUserAccessLoggingSettings": {"name": "DeleteUserAccessLoggingSettings", "http": {"method": "DELETE", "requestUri": "/userAccessLoggingSettings/{userAccessLoggingSettingsArn+}", "responseCode": 200}, "input": {"shape": "DeleteUserAccessLoggingSettingsRequest"}, "output": {"shape": "DeleteUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes user access logging settings.</p>", "idempotent": true}, "DeleteUserSettings": {"name": "DeleteUserSettings", "http": {"method": "DELETE", "requestUri": "/userSettings/{userSettingsArn+}", "responseCode": 200}, "input": {"shape": "DeleteUserSettingsRequest"}, "output": {"shape": "DeleteUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes user settings.</p>", "idempotent": true}, "DisassociateBrowserSettings": {"name": "DisassociateBrowserSettings", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}/browserSettings", "responseCode": 200}, "input": {"shape": "DisassociateBrowserSettingsRequest"}, "output": {"shape": "DisassociateBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disassociates browser settings from a web portal.</p>", "idempotent": true}, "DisassociateIpAccessSettings": {"name": "DisassociateIpAccessSettings", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}/ipAccessSettings", "responseCode": 200}, "input": {"shape": "DisassociateIpAccessSettingsRequest"}, "output": {"shape": "DisassociateIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disassociates IP access settings from a web portal.</p>", "idempotent": true}, "DisassociateNetworkSettings": {"name": "DisassociateNetworkSettings", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}/networkSettings", "responseCode": 200}, "input": {"shape": "DisassociateNetworkSettingsRequest"}, "output": {"shape": "DisassociateNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disassociates network settings from a web portal.</p>", "idempotent": true}, "DisassociateTrustStore": {"name": "DisassociateTrustStore", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}/trustStores", "responseCode": 200}, "input": {"shape": "DisassociateTrustStoreRequest"}, "output": {"shape": "DisassociateTrustStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disassociates a trust store from a web portal.</p>", "idempotent": true}, "DisassociateUserAccessLoggingSettings": {"name": "DisassociateUserAccessLoggingSettings", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}/userAccessLoggingSettings", "responseCode": 200}, "input": {"shape": "DisassociateUserAccessLoggingSettingsRequest"}, "output": {"shape": "DisassociateUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disassociates user access logging settings from a web portal.</p>", "idempotent": true}, "DisassociateUserSettings": {"name": "DisassociateUserSettings", "http": {"method": "DELETE", "requestUri": "/portals/{portalArn+}/userSettings", "responseCode": 200}, "input": {"shape": "DisassociateUserSettingsRequest"}, "output": {"shape": "DisassociateUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disassociates user settings from a web portal.</p>", "idempotent": true}, "GetBrowserSettings": {"name": "GetBrowserSettings", "http": {"method": "GET", "requestUri": "/browserSettings/{browserSettingsArn+}", "responseCode": 200}, "input": {"shape": "GetBrowserSettingsRequest"}, "output": {"shape": "GetBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets browser settings.</p>"}, "GetIdentityProvider": {"name": "GetIdentityProvider", "http": {"method": "GET", "requestUri": "/identityProviders/{identityProviderArn+}", "responseCode": 200}, "input": {"shape": "GetIdentityProviderRequest"}, "output": {"shape": "GetIdentityProviderResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the identity provider.</p>"}, "GetIpAccessSettings": {"name": "GetIpAccessSettings", "http": {"method": "GET", "requestUri": "/ipAccessSettings/{ipAccessSettingsArn+}", "responseCode": 200}, "input": {"shape": "GetIpAccessSettingsRequest"}, "output": {"shape": "GetIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the IP access settings.</p>"}, "GetNetworkSettings": {"name": "GetNetworkSettings", "http": {"method": "GET", "requestUri": "/networkSettings/{networkSettingsArn+}", "responseCode": 200}, "input": {"shape": "GetNetworkSettingsRequest"}, "output": {"shape": "GetNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the network settings.</p>"}, "GetPortal": {"name": "GetPortal", "http": {"method": "GET", "requestUri": "/portals/{portalArn+}", "responseCode": 200}, "input": {"shape": "GetPortalRequest"}, "output": {"shape": "GetPortalResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the web portal.</p>"}, "GetPortalServiceProviderMetadata": {"name": "GetPortalServiceProviderMetadata", "http": {"method": "GET", "requestUri": "/portalIdp/{portalArn+}", "responseCode": 200}, "input": {"shape": "GetPortalServiceProviderMetadataRequest"}, "output": {"shape": "GetPortalServiceProviderMetadataResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the service provider metadata.</p>"}, "GetTrustStore": {"name": "GetTrustStore", "http": {"method": "GET", "requestUri": "/trustStores/{trustStoreArn+}", "responseCode": 200}, "input": {"shape": "GetTrustStoreRequest"}, "output": {"shape": "GetTrustStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the trust store.</p>"}, "GetTrustStoreCertificate": {"name": "GetTrustStoreCertificate", "http": {"method": "GET", "requestUri": "/trustStores/{trustStoreArn+}/certificate", "responseCode": 200}, "input": {"shape": "GetTrustStoreCertificateRequest"}, "output": {"shape": "GetTrustStoreCertificateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the trust store certificate.</p>"}, "GetUserAccessLoggingSettings": {"name": "GetUserAccessLoggingSettings", "http": {"method": "GET", "requestUri": "/userAccessLoggingSettings/{userAccessLoggingSettingsArn+}", "responseCode": 200}, "input": {"shape": "GetUserAccessLoggingSettingsRequest"}, "output": {"shape": "GetUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets user access logging settings.</p>"}, "GetUserSettings": {"name": "GetUserSettings", "http": {"method": "GET", "requestUri": "/userSettings/{userSettingsArn+}", "responseCode": 200}, "input": {"shape": "GetUserSettingsRequest"}, "output": {"shape": "GetUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets user settings.</p>"}, "ListBrowserSettings": {"name": "ListBrowserSettings", "http": {"method": "GET", "requestUri": "/browserSettings", "responseCode": 200}, "input": {"shape": "ListBrowserSettingsRequest"}, "output": {"shape": "ListBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of browser settings.</p>"}, "ListIdentityProviders": {"name": "ListIdentityProviders", "http": {"method": "GET", "requestUri": "/portals/{portalArn+}/identityProviders", "responseCode": 200}, "input": {"shape": "ListIdentityProvidersRequest"}, "output": {"shape": "ListIdentityProvidersResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of identity providers for a specific web portal.</p>"}, "ListIpAccessSettings": {"name": "ListIpAccessSettings", "http": {"method": "GET", "requestUri": "/ipAccessSettings", "responseCode": 200}, "input": {"shape": "ListIpAccessSettingsRequest"}, "output": {"shape": "ListIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of IP access settings.</p>"}, "ListNetworkSettings": {"name": "ListNetworkSettings", "http": {"method": "GET", "requestUri": "/networkSettings", "responseCode": 200}, "input": {"shape": "ListNetworkSettingsRequest"}, "output": {"shape": "ListNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of network settings.</p>"}, "ListPortals": {"name": "ListPortals", "http": {"method": "GET", "requestUri": "/portals", "responseCode": 200}, "input": {"shape": "ListPortalsRequest"}, "output": {"shape": "ListPortalsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list or web portals.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn+}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of tags for a resource.</p>"}, "ListTrustStoreCertificates": {"name": "ListTrustStoreCertificates", "http": {"method": "GET", "requestUri": "/trustStores/{trustStoreArn+}/certificates", "responseCode": 200}, "input": {"shape": "ListTrustStoreCertificatesRequest"}, "output": {"shape": "ListTrustStoreCertificatesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of trust store certificates.</p>"}, "ListTrustStores": {"name": "ListTrustStores", "http": {"method": "GET", "requestUri": "/trustStores", "responseCode": 200}, "input": {"shape": "ListTrustStoresRequest"}, "output": {"shape": "ListTrustStoresResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of trust stores.</p>"}, "ListUserAccessLoggingSettings": {"name": "ListUserAccessLoggingSettings", "http": {"method": "GET", "requestUri": "/userAccessLoggingSettings", "responseCode": 200}, "input": {"shape": "ListUserAccessLoggingSettingsRequest"}, "output": {"shape": "ListUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of user access logging settings.</p>"}, "ListUserSettings": {"name": "ListUserSettings", "http": {"method": "GET", "requestUri": "/userSettings", "responseCode": 200}, "input": {"shape": "ListUserSettingsRequest"}, "output": {"shape": "ListUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of user settings.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn+}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Adds or overwrites one or more tags for the specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn+}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Removes one or more tags from the specified resource.</p>", "idempotent": true}, "UpdateBrowserSettings": {"name": "UpdateBrowserSettings", "http": {"method": "PATCH", "requestUri": "/browserSettings/{browserSettingsArn+}", "responseCode": 200}, "input": {"shape": "UpdateBrowserSettingsRequest"}, "output": {"shape": "UpdateBrowserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates browser settings.</p>"}, "UpdateIdentityProvider": {"name": "UpdateIdentityProvider", "http": {"method": "PATCH", "requestUri": "/identityProviders/{identityProviderArn+}", "responseCode": 200}, "input": {"shape": "UpdateIdentityProviderRequest"}, "output": {"shape": "UpdateIdentityProviderResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates the identity provider. </p>"}, "UpdateIpAccessSettings": {"name": "UpdateIpAccessSettings", "http": {"method": "PATCH", "requestUri": "/ipAccessSettings/{ipAccessSettingsArn+}", "responseCode": 200}, "input": {"shape": "UpdateIpAccessSettingsRequest"}, "output": {"shape": "UpdateIpAccessSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates IP access settings.</p>"}, "UpdateNetworkSettings": {"name": "UpdateNetworkSettings", "http": {"method": "PATCH", "requestUri": "/networkSettings/{networkSettingsArn+}", "responseCode": 200}, "input": {"shape": "UpdateNetworkSettingsRequest"}, "output": {"shape": "UpdateNetworkSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates network settings.</p>"}, "UpdatePortal": {"name": "UpdatePortal", "http": {"method": "PUT", "requestUri": "/portals/{portalArn+}", "responseCode": 200}, "input": {"shape": "UpdatePortalRequest"}, "output": {"shape": "UpdatePortalResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates a web portal.</p>", "idempotent": true}, "UpdateTrustStore": {"name": "UpdateTrustStore", "http": {"method": "PATCH", "requestUri": "/trustStores/{trustStoreArn+}", "responseCode": 200}, "input": {"shape": "UpdateTrustStoreRequest"}, "output": {"shape": "UpdateTrustStoreResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates the trust store.</p>"}, "UpdateUserAccessLoggingSettings": {"name": "UpdateUserAccessLoggingSettings", "http": {"method": "PATCH", "requestUri": "/userAccessLoggingSettings/{userAccessLoggingSettingsArn+}", "responseCode": 200}, "input": {"shape": "UpdateUserAccessLoggingSettingsRequest"}, "output": {"shape": "UpdateUserAccessLoggingSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates the user access logging settings.</p>"}, "UpdateUserSettings": {"name": "UpdateUserSettings", "http": {"method": "PATCH", "requestUri": "/userSettings/{userSettingsArn+}", "responseCode": 200}, "input": {"shape": "UpdateUserSettingsRequest"}, "output": {"shape": "UpdateUserSettingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates the user settings.</p>"}}, "shapes": {"ARN": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:[\\w+=\\/,.@-]+:[a-zA-Z0-9\\-]+:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:[a-zA-Z]+(\\/[a-fA-F0-9\\-]{36})+$"}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Access is denied.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ArnList": {"type": "list", "member": {"shape": "ARN"}}, "AssociateBrowserSettingsRequest": {"type": "structure", "required": ["browserSettingsArn", "portalArn"], "members": {"browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>", "location": "querystring", "locationName": "browserSettingsArn"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "AssociateBrowserSettingsResponse": {"type": "structure", "required": ["browserSettingsArn", "portalArn"], "members": {"browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}}}, "AssociateIpAccessSettingsRequest": {"type": "structure", "required": ["ipAccessSettingsArn", "portalArn"], "members": {"ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings.</p>", "location": "querystring", "locationName": "ipAccessSettingsArn"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "AssociateIpAccessSettingsResponse": {"type": "structure", "required": ["ipAccessSettingsArn", "portalArn"], "members": {"ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings resource.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}}}, "AssociateNetworkSettingsRequest": {"type": "structure", "required": ["networkSettingsArn", "portalArn"], "members": {"networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>", "location": "querystring", "locationName": "networkSettingsArn"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "AssociateNetworkSettingsResponse": {"type": "structure", "required": ["networkSettingsArn", "portalArn"], "members": {"networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}}}, "AssociateTrustStoreRequest": {"type": "structure", "required": ["portalArn", "trustStoreArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>", "location": "querystring", "locationName": "trustStoreArn"}}}, "AssociateTrustStoreResponse": {"type": "structure", "required": ["portalArn", "trustStoreArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>"}}}, "AssociateUserAccessLoggingSettingsRequest": {"type": "structure", "required": ["portalArn", "userAccessLoggingSettingsArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>", "location": "querystring", "locationName": "userAccessLoggingSettingsArn"}}}, "AssociateUserAccessLoggingSettingsResponse": {"type": "structure", "required": ["portalArn", "userAccessLoggingSettingsArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>"}}}, "AssociateUserSettingsRequest": {"type": "structure", "required": ["portalArn", "userSettingsArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>", "location": "querystring", "locationName": "userSettingsArn"}}}, "AssociateUserSettingsResponse": {"type": "structure", "required": ["portalArn", "userSettingsArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>"}}}, "AuthenticationType": {"type": "string", "enum": ["Standard", "IAM_Identity_Center"]}, "BrowserPolicy": {"type": "string", "max": 131072, "min": 2, "pattern": "\\{[\\S\\s]*\\}\\s*", "sensitive": true}, "BrowserSettings": {"type": "structure", "required": ["browserSettingsArn"], "members": {"associatedPortalArns": {"shape": "ArnList", "documentation": "<p>A list of web portal ARNs that this browser settings is associated with.</p>"}, "browserPolicy": {"shape": "BrowserPolicy", "documentation": "<p>A JSON string containing Chrome Enterprise policies that will be applied to all streaming sessions.</p>"}, "browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>"}}, "documentation": "<p>The browser settings resource that can be associated with a web portal. Once associated with a web portal, browser settings control how the browser will behave once a user starts a streaming session for the web portal. </p>"}, "BrowserSettingsList": {"type": "list", "member": {"shape": "BrowserSettingsSummary"}}, "BrowserSettingsSummary": {"type": "structure", "required": ["browserSettingsArn"], "members": {"browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>"}}, "documentation": "<p>The summary for browser settings.</p>"}, "BrowserType": {"type": "string", "enum": ["Chrome"]}, "Certificate": {"type": "structure", "members": {"body": {"shape": "CertificateAuthorityBody", "documentation": "<p>The body of the certificate.</p>"}, "issuer": {"shape": "CertificatePrincipal", "documentation": "<p>The entity that issued the certificate.</p>"}, "notValidAfter": {"shape": "Timestamp", "documentation": "<p>The certificate is not valid after this date.</p>"}, "notValidBefore": {"shape": "Timestamp", "documentation": "<p>The certificate is not valid before this date.</p>"}, "subject": {"shape": "CertificatePrincipal", "documentation": "<p>The entity the certificate belongs to.</p>"}, "thumbprint": {"shape": "CertificateThumbprint", "documentation": "<p>A hexadecimal identifier for the certificate.</p>"}}, "documentation": "<p>The certificate.</p>"}, "CertificateAuthorityBody": {"type": "blob"}, "CertificateList": {"type": "list", "member": {"shape": "CertificateAuthorityBody"}}, "CertificatePrincipal": {"type": "string", "max": 256, "min": 1, "pattern": "^\\S+$"}, "CertificateSummary": {"type": "structure", "members": {"issuer": {"shape": "CertificatePrincipal", "documentation": "<p>The entity that issued the certificate.</p>"}, "notValidAfter": {"shape": "Timestamp", "documentation": "<p>The certificate is not valid after this date.</p>"}, "notValidBefore": {"shape": "Timestamp", "documentation": "<p>The certificate is not valid before this date.</p>"}, "subject": {"shape": "CertificatePrincipal", "documentation": "<p>The entity the certificate belongs to.</p>"}, "thumbprint": {"shape": "CertificateThumbprint", "documentation": "<p>A hexadecimal identifier for the certificate.</p>"}}, "documentation": "<p>The summary of the certificate.</p>"}, "CertificateSummaryList": {"type": "list", "member": {"shape": "CertificateSummary"}}, "CertificateThumbprint": {"type": "string", "max": 64, "min": 64, "pattern": "^[A-Fa-f0-9]{64}$"}, "CertificateThumbprintList": {"type": "list", "member": {"shape": "CertificateThumbprint"}}, "ClientToken": {"type": "string", "max": 512, "min": 1}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>Identifier of the resource affected.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>Type of the resource affected.</p>"}}, "documentation": "<p>There is a conflict.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CookieDomain": {"type": "string", "max": 253, "min": 0, "pattern": "^(\\.?)(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)*[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$"}, "CookieName": {"type": "string", "max": 4096, "min": 0}, "CookiePath": {"type": "string", "max": 2000, "min": 0, "pattern": "^/(\\S)*$"}, "CookieSpecification": {"type": "structure", "required": ["domain"], "members": {"domain": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The domain of the cookie.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The name of the cookie.</p>"}, "path": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The path of the cookie.</p>"}}, "documentation": "<p>Specifies a single cookie or set of cookies in an end user's browser.</p>"}, "CookieSpecifications": {"type": "list", "member": {"shape": "CookieSpecification"}, "max": 10, "min": 0}, "CookieSynchronizationConfiguration": {"type": "structure", "required": ["allowlist"], "members": {"allowlist": {"shape": "CookieSpecifications", "documentation": "<p>The list of cookie specifications that are allowed to be synchronized to the remote browser.</p>"}, "blocklist": {"shape": "CookieSpecifications", "documentation": "<p>The list of cookie specifications that are blocked from being synchronized to the remote browser.</p>"}}, "documentation": "<p>The configuration that specifies which cookies should be synchronized from the end user's local browser to the remote browser.</p>", "sensitive": true}, "CreateBrowserSettingsRequest": {"type": "structure", "required": ["browserPolicy"], "members": {"additionalEncryptionContext": {"shape": "EncryptionContextMap", "documentation": "<p>Additional encryption context of the browser settings.</p>"}, "browserPolicy": {"shape": "BrowserPolicy", "documentation": "<p>A JSON string containing Chrome Enterprise policies that will be applied to all streaming sessions.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request.</p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK. </p>", "idempotencyToken": true}, "customerManagedKey": {"shape": "keyArn", "documentation": "<p>The custom managed key of the browser settings.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the browser settings resource. A tag is a key-value pair.</p>"}}}, "CreateBrowserSettingsResponse": {"type": "structure", "required": ["browserSettingsArn"], "members": {"browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>"}}}, "CreateIdentityProviderRequest": {"type": "structure", "required": ["identityProviderDetails", "identityProviderName", "identityProviderType", "portalArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request.</p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "identityProviderDetails": {"shape": "IdentityProviderDetails", "documentation": "<p>The identity provider details. The following list describes the provider detail keys for each identity provider type. </p> <ul> <li> <p>For Google and Login with Amazon:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> </ul> </li> <li> <p>For Facebook:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> <li> <p> <code>api_version</code> </p> </li> </ul> </li> <li> <p>For Sign in with Apple:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>team_id</code> </p> </li> <li> <p> <code>key_id</code> </p> </li> <li> <p> <code>private_key</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> </ul> </li> <li> <p>For OIDC providers:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>attributes_request_method</code> </p> </li> <li> <p> <code>oidc_issuer</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> <li> <p> <code>authorize_url</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> <li> <p> <code>token_url</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> <li> <p> <code>attributes_url</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> <li> <p> <code>jwks_uri</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> </ul> </li> <li> <p>For SAML providers:</p> <ul> <li> <p> <code>MetadataFile</code> OR <code>MetadataURL</code> </p> </li> <li> <p> <code>IDPSignout</code> (boolean) <i>optional</i> </p> </li> </ul> </li> </ul>"}, "identityProviderName": {"shape": "IdentityProviderName", "documentation": "<p>The identity provider name.</p>"}, "identityProviderType": {"shape": "IdentityProviderType", "documentation": "<p>The identity provider type.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}}}, "CreateIdentityProviderResponse": {"type": "structure", "required": ["identityProviderArn"], "members": {"identityProviderArn": {"shape": "SubresourceARN", "documentation": "<p>The ARN of the identity provider.</p>"}}}, "CreateIpAccessSettingsRequest": {"type": "structure", "required": ["ipRules"], "members": {"additionalEncryptionContext": {"shape": "EncryptionContextMap", "documentation": "<p>Additional encryption context of the IP access settings.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "customerManagedKey": {"shape": "keyArn", "documentation": "<p>The custom managed key of the IP access settings.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the IP access settings.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The display name of the IP access settings.</p>"}, "ipRules": {"shape": "IpRuleList", "documentation": "<p>The IP rules of the IP access settings.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the browser settings resource. A tag is a key-value pair.</p>"}}}, "CreateIpAccessSettingsResponse": {"type": "structure", "required": ["ipAccessSettingsArn"], "members": {"ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings resource.</p>"}}}, "CreateNetworkSettingsRequest": {"type": "structure", "required": ["securityGroupIds", "subnetIds", "vpcId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>One or more security groups used to control access from streaming instances to your VPC.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>The subnets in which network interfaces are created to connect streaming instances to your VPC. At least two of these subnets must be in different availability zones.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the network settings resource. A tag is a key-value pair.</p>"}, "vpcId": {"shape": "VpcId", "documentation": "<p>The VPC that streaming instances will connect to.</p>"}}}, "CreateNetworkSettingsResponse": {"type": "structure", "required": ["networkSettingsArn"], "members": {"networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>"}}}, "CreatePortalRequest": {"type": "structure", "members": {"additionalEncryptionContext": {"shape": "EncryptionContextMap", "documentation": "<p>The additional encryption context of the portal.</p>"}, "authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The type of authentication integration points used when signing into the web portal. Defaults to <code>Standard</code>.</p> <p> <code>Standard</code> web portals are authenticated directly through your identity provider. You need to call <code>CreateIdentityProvider</code> to integrate your identity provider with your web portal. User and group access to your web portal is controlled through your identity provider.</p> <p> <code>IAM_Identity_Center</code> web portals are authenticated through AWS IAM Identity Center (successor to AWS Single Sign-On). They provide additional features, such as IdP-initiated authentication. Identity sources (including external identity provider integration), plus user and group access to your web portal, can be configured in the IAM Identity Center.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "customerManagedKey": {"shape": "keyArn", "documentation": "<p>The customer managed key of the web portal.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the web portal. This is not visible to users who log into the web portal.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the web portal. A tag is a key-value pair.</p>"}}}, "CreatePortalResponse": {"type": "structure", "required": ["portalArn", "portalEndpoint"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "portalEndpoint": {"shape": "PortalEndpoint", "documentation": "<p>The endpoint URL of the web portal that users access in order to start streaming sessions.</p>"}}}, "CreateTrustStoreRequest": {"type": "structure", "required": ["certificateList"], "members": {"certificateList": {"shape": "CertificateList", "documentation": "<p>A list of CA certificates to be added to the trust store.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the trust store. A tag is a key-value pair.</p>"}}}, "CreateTrustStoreResponse": {"type": "structure", "required": ["trustStoreArn"], "members": {"trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>"}}}, "CreateUserAccessLoggingSettingsRequest": {"type": "structure", "required": ["kinesisStreamArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "kinesisStreamArn": {"shape": "KinesisStreamArn", "documentation": "<p>The ARN of the Kinesis stream.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the user settings resource. A tag is a key-value pair.</p>"}}}, "CreateUserAccessLoggingSettingsResponse": {"type": "structure", "required": ["userAccessLoggingSettingsArn"], "members": {"userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>"}}}, "CreateUserSettingsRequest": {"type": "structure", "required": ["copyAllowed", "downloadAllowed", "pasteAllowed", "printAllowed", "uploadAllowed"], "members": {"additionalEncryptionContext": {"shape": "EncryptionContextMap", "documentation": "<p>The additional encryption context of the user settings.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "cookieSynchronizationConfiguration": {"shape": "CookieSynchronizationConfiguration", "documentation": "<p>The configuration that specifies which cookies should be synchronized from the end user's local browser to the remote browser.</p>"}, "copyAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can copy text from the streaming session to the local device.</p>"}, "customerManagedKey": {"shape": "keyArn", "documentation": "<p>The customer managed key used to encrypt sensitive information in the user settings.</p>"}, "disconnectTimeoutInMinutes": {"shape": "DisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect.</p>"}, "downloadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can download files from the streaming session to the local device.</p>"}, "idleDisconnectTimeoutInMinutes": {"shape": "IdleDisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the disconnect timeout interval begins.</p>"}, "pasteAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can paste text from the local device to the streaming session.</p>"}, "printAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can print to the local device.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tags to add to the user settings resource. A tag is a key-value pair.</p>"}, "uploadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can upload files from the local device to the streaming session.</p>"}}}, "CreateUserSettingsResponse": {"type": "structure", "required": ["userSettingsArn"], "members": {"userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>"}}}, "DeleteBrowserSettingsRequest": {"type": "structure", "required": ["browserSettingsArn"], "members": {"browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>", "location": "uri", "locationName": "browserSettingsArn"}}}, "DeleteBrowserSettingsResponse": {"type": "structure", "members": {}}, "DeleteIdentityProviderRequest": {"type": "structure", "required": ["identityProviderArn"], "members": {"identityProviderArn": {"shape": "SubresourceARN", "documentation": "<p>The ARN of the identity provider.</p>", "location": "uri", "locationName": "identityProviderArn"}}}, "DeleteIdentityProviderResponse": {"type": "structure", "members": {}}, "DeleteIpAccessSettingsRequest": {"type": "structure", "required": ["ipAccessSettingsArn"], "members": {"ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings.</p>", "location": "uri", "locationName": "ipAccessSettingsArn"}}}, "DeleteIpAccessSettingsResponse": {"type": "structure", "members": {}}, "DeleteNetworkSettingsRequest": {"type": "structure", "required": ["networkSettingsArn"], "members": {"networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>", "location": "uri", "locationName": "networkSettingsArn"}}}, "DeleteNetworkSettingsResponse": {"type": "structure", "members": {}}, "DeletePortalRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DeletePortalResponse": {"type": "structure", "members": {}}, "DeleteTrustStoreRequest": {"type": "structure", "required": ["trustStoreArn"], "members": {"trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>", "location": "uri", "locationName": "trustStoreArn"}}}, "DeleteTrustStoreResponse": {"type": "structure", "members": {}}, "DeleteUserAccessLoggingSettingsRequest": {"type": "structure", "required": ["userAccessLoggingSettingsArn"], "members": {"userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>", "location": "uri", "locationName": "userAccessLoggingSettingsArn"}}}, "DeleteUserAccessLoggingSettingsResponse": {"type": "structure", "members": {}}, "DeleteUserSettingsRequest": {"type": "structure", "required": ["userSettingsArn"], "members": {"userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>", "location": "uri", "locationName": "userSettingsArn"}}}, "DeleteUserSettingsResponse": {"type": "structure", "members": {}}, "Description": {"type": "string", "max": 256, "min": 1, "pattern": "^.+$", "sensitive": true}, "DisassociateBrowserSettingsRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DisassociateBrowserSettingsResponse": {"type": "structure", "members": {}}, "DisassociateIpAccessSettingsRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DisassociateIpAccessSettingsResponse": {"type": "structure", "members": {}}, "DisassociateNetworkSettingsRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DisassociateNetworkSettingsResponse": {"type": "structure", "members": {}}, "DisassociateTrustStoreRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DisassociateTrustStoreResponse": {"type": "structure", "members": {}}, "DisassociateUserAccessLoggingSettingsRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DisassociateUserAccessLoggingSettingsResponse": {"type": "structure", "members": {}}, "DisassociateUserSettingsRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "DisassociateUserSettingsResponse": {"type": "structure", "members": {}}, "DisconnectTimeoutInMinutes": {"type": "integer", "box": true, "max": 600, "min": 1}, "DisplayName": {"type": "string", "max": 64, "min": 1, "pattern": "^.+$", "sensitive": true}, "EnabledType": {"type": "string", "enum": ["Disabled", "Enabled"]}, "EncryptionContextMap": {"type": "map", "key": {"shape": "StringType"}, "value": {"shape": "StringType"}}, "ExceptionMessage": {"type": "string"}, "FieldName": {"type": "string"}, "GetBrowserSettingsRequest": {"type": "structure", "required": ["browserSettingsArn"], "members": {"browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>", "location": "uri", "locationName": "browserSettingsArn"}}}, "GetBrowserSettingsResponse": {"type": "structure", "members": {"browserSettings": {"shape": "BrowserSettings", "documentation": "<p>The browser settings.</p>"}}}, "GetIdentityProviderRequest": {"type": "structure", "required": ["identityProviderArn"], "members": {"identityProviderArn": {"shape": "SubresourceARN", "documentation": "<p>The ARN of the identity provider.</p>", "location": "uri", "locationName": "identityProviderArn"}}}, "GetIdentityProviderResponse": {"type": "structure", "members": {"identityProvider": {"shape": "IdentityProvider", "documentation": "<p>The identity provider.</p>"}}}, "GetIpAccessSettingsRequest": {"type": "structure", "required": ["ipAccessSettingsArn"], "members": {"ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings.</p>", "location": "uri", "locationName": "ipAccessSettingsArn"}}}, "GetIpAccessSettingsResponse": {"type": "structure", "members": {"ipAccessSettings": {"shape": "IpAccessSettings", "documentation": "<p>The IP access settings.</p>"}}}, "GetNetworkSettingsRequest": {"type": "structure", "required": ["networkSettingsArn"], "members": {"networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>", "location": "uri", "locationName": "networkSettingsArn"}}}, "GetNetworkSettingsResponse": {"type": "structure", "members": {"networkSettings": {"shape": "NetworkSettings", "documentation": "<p>The network settings.</p>"}}}, "GetPortalRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "GetPortalResponse": {"type": "structure", "members": {"portal": {"shape": "Portal", "documentation": "<p>The web portal.</p>"}}}, "GetPortalServiceProviderMetadataRequest": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "GetPortalServiceProviderMetadataResponse": {"type": "structure", "required": ["portalArn"], "members": {"portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "serviceProviderSamlMetadata": {"shape": "SamlMetadata", "documentation": "<p>The service provider SAML metadata.</p>"}}}, "GetTrustStoreCertificateRequest": {"type": "structure", "required": ["thumbprint", "trustStoreArn"], "members": {"thumbprint": {"shape": "CertificateThumbprint", "documentation": "<p>The thumbprint of the trust store certificate.</p>", "location": "querystring", "locationName": "thumbprint"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store certificate.</p>", "location": "uri", "locationName": "trustStoreArn"}}}, "GetTrustStoreCertificateResponse": {"type": "structure", "required": ["trustStoreArn"], "members": {"certificate": {"shape": "Certificate", "documentation": "<p>The certificate of the trust store certificate.</p>"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store certificate.</p>"}}}, "GetTrustStoreRequest": {"type": "structure", "required": ["trustStoreArn"], "members": {"trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>", "location": "uri", "locationName": "trustStoreArn"}}}, "GetTrustStoreResponse": {"type": "structure", "members": {"trustStore": {"shape": "TrustStore", "documentation": "<p>The trust store.</p>"}}}, "GetUserAccessLoggingSettingsRequest": {"type": "structure", "required": ["userAccessLoggingSettingsArn"], "members": {"userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>", "location": "uri", "locationName": "userAccessLoggingSettingsArn"}}}, "GetUserAccessLoggingSettingsResponse": {"type": "structure", "members": {"userAccessLoggingSettings": {"shape": "UserAccessLoggingSettings", "documentation": "<p>The user access logging settings.</p>"}}}, "GetUserSettingsRequest": {"type": "structure", "required": ["userSettingsArn"], "members": {"userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>", "location": "uri", "locationName": "userSettingsArn"}}}, "GetUserSettingsResponse": {"type": "structure", "members": {"userSettings": {"shape": "UserSettings", "documentation": "<p>The user settings.</p>"}}}, "IdentityProvider": {"type": "structure", "required": ["identityProviderArn"], "members": {"identityProviderArn": {"shape": "SubresourceARN", "documentation": "<p>The ARN of the identity provider.</p>"}, "identityProviderDetails": {"shape": "IdentityProviderDetails", "documentation": "<p>The identity provider details. The following list describes the provider detail keys for each identity provider type. </p> <ul> <li> <p>For Google and Login with Amazon:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> </ul> </li> <li> <p>For Facebook:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> <li> <p> <code>api_version</code> </p> </li> </ul> </li> <li> <p>For Sign in with Apple:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>team_id</code> </p> </li> <li> <p> <code>key_id</code> </p> </li> <li> <p> <code>private_key</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> </ul> </li> <li> <p>For OIDC providers:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>attributes_request_method</code> </p> </li> <li> <p> <code>oidc_issuer</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> <li> <p> <code>authorize_url</code> <i>if not available from discovery URL specified by oidc_issuer key</i> </p> </li> <li> <p> <code>token_url</code> <i>if not available from discovery URL specified by oidc_issuer key</i> </p> </li> <li> <p> <code>attributes_url</code> <i>if not available from discovery URL specified by oidc_issuer key</i> </p> </li> <li> <p> <code>jwks_uri</code> <i>if not available from discovery URL specified by oidc_issuer key</i> </p> </li> </ul> </li> <li> <p>For SAML providers:</p> <ul> <li> <p> <code>MetadataFile</code> OR <code>MetadataURL</code> </p> </li> <li> <p> <code>IDPSignout</code> <i>optional</i> </p> </li> </ul> </li> </ul>"}, "identityProviderName": {"shape": "IdentityProviderName", "documentation": "<p>The identity provider name.</p>"}, "identityProviderType": {"shape": "IdentityProviderType", "documentation": "<p>The identity provider type.</p>"}}, "documentation": "<p>The identity provider.</p>"}, "IdentityProviderDetails": {"type": "map", "key": {"shape": "StringType"}, "value": {"shape": "StringType"}, "sensitive": true}, "IdentityProviderList": {"type": "list", "member": {"shape": "IdentityProviderSummary"}}, "IdentityProviderName": {"type": "string", "max": 32, "min": 1, "pattern": "^[^_][\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}][^_]+$", "sensitive": true}, "IdentityProviderSummary": {"type": "structure", "required": ["identityProviderArn"], "members": {"identityProviderArn": {"shape": "SubresourceARN", "documentation": "<p>The ARN of the identity provider.</p>"}, "identityProviderName": {"shape": "IdentityProviderName", "documentation": "<p>The identity provider name.</p>"}, "identityProviderType": {"shape": "IdentityProviderType", "documentation": "<p>The identity provider type.</p>"}}, "documentation": "<p>The summary of the identity provider.</p>"}, "IdentityProviderType": {"type": "string", "enum": ["SAML", "Facebook", "Google", "LoginWithAmazon", "SignInWithApple", "OIDC"]}, "IdleDisconnectTimeoutInMinutes": {"type": "integer", "box": true, "max": 60, "min": 0}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>Advice to clients on when the call can be safely retried.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>There is an internal server error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "IpAccessSettings": {"type": "structure", "required": ["ipAccessSettingsArn"], "members": {"associatedPortalArns": {"shape": "ArnList", "documentation": "<p>A list of web portal ARNs that this IP access settings resource is associated with.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The creation date timestamp of the IP access settings.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the IP access settings.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p> The display name of the IP access settings.</p>"}, "ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings resource.</p>"}, "ipRules": {"shape": "IpRuleList", "documentation": "<p>The IP rules of the IP access settings.</p>"}}, "documentation": "<p>The IP access settings resource that can be associated with a web portal. </p>"}, "IpAccessSettingsList": {"type": "list", "member": {"shape": "IpAccessSettingsSummary"}}, "IpAccessSettingsSummary": {"type": "structure", "required": ["ipAccessSettingsArn"], "members": {"creationDate": {"shape": "Timestamp", "documentation": "<p>The creation date timestamp of the IP access settings.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the IP access settings.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The display name of the IP access settings.</p>"}, "ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of IP access settings.</p>"}}, "documentation": "<p>The summary of IP access settings.</p>"}, "IpRange": {"type": "string", "documentation": "<p>A single IP address or an IP address range in CIDR notation</p>", "pattern": "^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(?:/([0-9]|[12][0-9]|3[0-2])|)$", "sensitive": true}, "IpRule": {"type": "structure", "required": ["ipRange"], "members": {"description": {"shape": "Description", "documentation": "<p>The description of the IP rule.</p>"}, "ipRange": {"shape": "IpRange", "documentation": "<p>The IP range of the IP rule.</p>"}}, "documentation": "<p>The IP rules of the IP access settings.</p>"}, "IpRuleList": {"type": "list", "member": {"shape": "IpRule"}, "max": 100, "min": 1, "sensitive": true}, "KinesisStreamArn": {"type": "string", "documentation": "<p>Kinesis stream ARN to which log events are published.</p>", "max": 2048, "min": 20, "pattern": "arn:[\\w+=/,.@-]+:kinesis:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:stream/.+"}, "ListBrowserSettingsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListBrowserSettingsResponse": {"type": "structure", "members": {"browserSettings": {"shape": "BrowserSettingsList", "documentation": "<p>The browser settings.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "ListIdentityProvidersRequest": {"type": "structure", "required": ["portalArn"], "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "ListIdentityProvidersResponse": {"type": "structure", "members": {"identityProviders": {"shape": "IdentityProviderList", "documentation": "<p>The identity providers.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "ListIpAccessSettingsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListIpAccessSettingsResponse": {"type": "structure", "members": {"ipAccessSettings": {"shape": "IpAccessSettingsList", "documentation": "<p>The IP access settings.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "ListNetworkSettingsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListNetworkSettingsResponse": {"type": "structure", "members": {"networkSettings": {"shape": "NetworkSettingsList", "documentation": "<p>The network settings.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "ListPortalsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListPortalsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation. </p>"}, "portals": {"shape": "PortalList", "documentation": "<p>The portals in the list.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagList", "documentation": "<p>The tags of the resource.</p>"}}}, "ListTrustStoreCertificatesRequest": {"type": "structure", "required": ["trustStoreArn"], "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store</p>", "location": "uri", "locationName": "trustStoreArn"}}}, "ListTrustStoreCertificatesResponse": {"type": "structure", "required": ["trustStoreArn"], "members": {"certificateList": {"shape": "CertificateSummaryList", "documentation": "<p>The certificate list.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.&gt;</p>"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>"}}}, "ListTrustStoresRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListTrustStoresResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}, "trustStores": {"shape": "TrustStoreSummaryList", "documentation": "<p>The trust stores.</p>"}}}, "ListUserAccessLoggingSettingsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListUserAccessLoggingSettingsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}, "userAccessLoggingSettings": {"shape": "UserAccessLoggingSettingsList", "documentation": "<p>The user access logging settings.</p>"}}}, "ListUserSettingsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to be included in the next page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListUserSettingsResponse": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation. </p>"}, "userSettings": {"shape": "UserSettingsList", "documentation": "<p>The user settings.</p>"}}}, "MaxResults": {"type": "integer", "box": true, "min": 1}, "NetworkSettings": {"type": "structure", "required": ["networkSettingsArn"], "members": {"associatedPortalArns": {"shape": "ArnList", "documentation": "<p>A list of web portal ARNs that this network settings is associated with.</p>"}, "networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>One or more security groups used to control access from streaming instances to your VPC. </p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>The subnets in which network interfaces are created to connect streaming instances to your VPC. At least two of these subnets must be in different availability zones.</p>"}, "vpcId": {"shape": "VpcId", "documentation": "<p>The VPC that streaming instances will connect to.</p>"}}, "documentation": "<p>A network settings resource that can be associated with a web portal. Once associated with a web portal, network settings define how streaming instances will connect with your specified VPC. </p>"}, "NetworkSettingsList": {"type": "list", "member": {"shape": "NetworkSettingsSummary"}}, "NetworkSettingsSummary": {"type": "structure", "required": ["networkSettingsArn"], "members": {"networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>"}, "vpcId": {"shape": "VpcId", "documentation": "<p>The VPC ID of the network settings.</p>"}}, "documentation": "<p>The summary of network settings.</p>"}, "PaginationToken": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\S+$"}, "Portal": {"type": "structure", "required": ["portalArn"], "members": {"authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The type of authentication integration points used when signing into the web portal. Defaults to <code>Standard</code>.</p> <p> <code>Standard</code> web portals are authenticated directly through your identity provider. You need to call <code>CreateIdentityProvider</code> to integrate your identity provider with your web portal. User and group access to your web portal is controlled through your identity provider.</p> <p> <code>IAM_Identity_Center</code> web portals are authenticated through AWS IAM Identity Center (successor to AWS Single Sign-On). They provide additional features, such as IdP-initiated authentication. Identity sources (including external identity provider integration), plus user and group access to your web portal, can be configured in the IAM Identity Center.</p>"}, "browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings that is associated with this web portal.</p>"}, "browserType": {"shape": "BrowserType", "documentation": "<p>The browser that users see when using a streaming session.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The creation date of the web portal.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the web portal.</p>"}, "ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings.</p>"}, "networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings that is associated with the web portal.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "portalEndpoint": {"shape": "PortalEndpoint", "documentation": "<p>The endpoint URL of the web portal that users access in order to start streaming sessions.</p>"}, "portalStatus": {"shape": "PortalStatus", "documentation": "<p>The status of the web portal.</p>"}, "rendererType": {"shape": "RendererType", "documentation": "<p>The renderer that is used in streaming sessions.</p>"}, "statusReason": {"shape": "StatusReason", "documentation": "<p>A message that explains why the web portal is in its current status.</p>"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store that is associated with the web portal.</p>"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings that is associated with the web portal.</p>"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings that is associated with the web portal.</p>"}}, "documentation": "<p>The web portal.</p>"}, "PortalEndpoint": {"type": "string", "max": 253, "min": 1, "pattern": "^[a-zA-Z0-9]?((?!-)([A-Za-z0-9-]*[A-Za-z0-9])\\.)+[a-zA-Z0-9]+$"}, "PortalList": {"type": "list", "member": {"shape": "PortalSummary"}}, "PortalStatus": {"type": "string", "enum": ["Incomplete", "Pending", "Active"]}, "PortalSummary": {"type": "structure", "required": ["portalArn"], "members": {"authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The type of authentication integration points used when signing into the web portal. Defaults to <code>Standard</code>.</p> <p> <code>Standard</code> web portals are authenticated directly through your identity provider. You need to call <code>CreateIdentityProvider</code> to integrate your identity provider with your web portal. User and group access to your web portal is controlled through your identity provider.</p> <p> <code>IAM_Identity_Center</code> web portals are authenticated through AWS IAM Identity Center (successor to AWS Single Sign-On). They provide additional features, such as IdP-initiated authentication. Identity sources (including external identity provider integration), plus user and group access to your web portal, can be configured in the IAM Identity Center.</p>"}, "browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings that is associated with the web portal.</p>"}, "browserType": {"shape": "BrowserType", "documentation": "<p>The browser type of the web portal.</p>"}, "creationDate": {"shape": "Timestamp", "documentation": "<p>The creation date of the web portal.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the web portal.</p>"}, "ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings.</p>"}, "networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings that is associated with the web portal.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>"}, "portalEndpoint": {"shape": "PortalEndpoint", "documentation": "<p>The endpoint URL of the web portal that users access in order to start streaming sessions.</p>"}, "portalStatus": {"shape": "PortalStatus", "documentation": "<p>The status of the web portal.</p>"}, "rendererType": {"shape": "RendererType", "documentation": "<p>The renderer that is used in streaming sessions.</p>"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust that is associated with this web portal.</p>"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings that is associated with the web portal.</p>"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings that is associated with the web portal.</p>"}}, "documentation": "<p>The summary of the portal.</p>"}, "QuotaCode": {"type": "string"}, "RendererType": {"type": "string", "enum": ["AppStream"]}, "ResourceId": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>Hypothetical identifier of the resource affected.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>Hypothetical type of the resource affected.</p>"}}, "documentation": "<p>The resource cannot be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceType": {"type": "string"}, "RetryAfterSeconds": {"type": "integer"}, "SamlMetadata": {"type": "string", "max": 204800, "min": 1, "pattern": "^.+$"}, "SecurityGroupId": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\w+\\-]+$"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 5, "min": 1}, "ServiceCode": {"type": "string"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}, "quotaCode": {"shape": "QuotaCode", "documentation": "<p>The originating quota.</p>"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>Identifier of the resource affected.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p> Type of the resource affected.</p>"}, "serviceCode": {"shape": "ServiceCode", "documentation": "<p>The originating service.</p>"}}, "documentation": "<p>The service quota has been exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "StatusReason": {"type": "string", "max": 1024, "min": 1, "pattern": ".*"}, "StringType": {"type": "string", "max": 131072, "min": 0, "pattern": "^[\\s\\S]*$"}, "SubnetId": {"type": "string", "max": 32, "min": 1, "pattern": "^subnet-([0-9a-f]{8}|[0-9a-f]{17})$"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetId"}, "max": 3, "min": 2}, "SubresourceARN": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:[\\w+=\\/,.@-]+:[a-zA-Z0-9\\-]+:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:[a-zA-Z]+(\\/[a-fA-F0-9\\-]{36}){2,}$"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the tag</p>"}}, "documentation": "<p>The tag.</p>", "sensitive": true}, "TagExceptionMessage": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$", "sensitive": true}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token returns the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "resourceArn": {"shape": "ARN", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagList", "documentation": "<p>The tags of the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$", "sensitive": true}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "ExceptionMessage"}, "quotaCode": {"shape": "QuotaCode", "documentation": "<p>The originating quota.</p>"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>Advice to clients on when the call can be safely retried.</p>", "location": "header", "locationName": "Retry-After"}, "serviceCode": {"shape": "ServiceCode", "documentation": "<p>The originating service.</p>"}}, "documentation": "<p>There is a throttling error.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Timestamp": {"type": "timestamp"}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "TagExceptionMessage"}, "resourceName": {"shape": "ARN", "documentation": "<p>Name of the resource affected.</p>"}}, "documentation": "<p>There are too many tags.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "TrustStore": {"type": "structure", "required": ["trustStoreArn"], "members": {"associatedPortalArns": {"shape": "ArnList", "documentation": "<p>A list of web portal ARNs that this trust store is associated with.</p>"}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>"}}, "documentation": "<p>A trust store that can be associated with a web portal. A trust store contains certificate authority (CA) certificates. Once associated with a web portal, the browser in a streaming session will recognize certificates that have been issued using any of the CAs in the trust store. If your organization has internal websites that use certificates issued by private CAs, you should add the private CA certificate to the trust store. </p>"}, "TrustStoreSummary": {"type": "structure", "members": {"trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>"}}, "documentation": "<p>The summary of the trust store.</p>"}, "TrustStoreSummaryList": {"type": "list", "member": {"shape": "TrustStoreSummary"}}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateBrowserSettingsRequest": {"type": "structure", "required": ["browserSettingsArn"], "members": {"browserPolicy": {"shape": "BrowserPolicy", "documentation": "<p>A JSON string containing Chrome Enterprise policies that will be applied to all streaming sessions. </p>"}, "browserSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the browser settings.</p>", "location": "uri", "locationName": "browserSettingsArn"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}}}, "UpdateBrowserSettingsResponse": {"type": "structure", "required": ["browserSettings"], "members": {"browserSettings": {"shape": "BrowserSettings", "documentation": "<p>The browser settings.</p>"}}}, "UpdateIdentityProviderRequest": {"type": "structure", "required": ["identityProviderArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "identityProviderArn": {"shape": "SubresourceARN", "documentation": "<p>The ARN of the identity provider.</p>", "location": "uri", "locationName": "identityProviderArn"}, "identityProviderDetails": {"shape": "IdentityProviderDetails", "documentation": "<p>The details of the identity provider. The following list describes the provider detail keys for each identity provider type. </p> <ul> <li> <p>For Google and Login with Amazon:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> </ul> </li> <li> <p>For Facebook:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> <li> <p> <code>api_version</code> </p> </li> </ul> </li> <li> <p>For Sign in with Apple:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>team_id</code> </p> </li> <li> <p> <code>key_id</code> </p> </li> <li> <p> <code>private_key</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> </ul> </li> <li> <p>For OIDC providers:</p> <ul> <li> <p> <code>client_id</code> </p> </li> <li> <p> <code>client_secret</code> </p> </li> <li> <p> <code>attributes_request_method</code> </p> </li> <li> <p> <code>oidc_issuer</code> </p> </li> <li> <p> <code>authorize_scopes</code> </p> </li> <li> <p> <code>authorize_url</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> <li> <p> <code>token_url</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> <li> <p> <code>attributes_url</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> <li> <p> <code>jwks_uri</code> <i>if not available from discovery URL specified by <code>oidc_issuer</code> key</i> </p> </li> </ul> </li> <li> <p>For SAML providers:</p> <ul> <li> <p> <code>MetadataFile</code> OR <code>MetadataURL</code> </p> </li> <li> <p> <code>IDPSignout</code> (boolean) <i>optional</i> </p> </li> </ul> </li> </ul>"}, "identityProviderName": {"shape": "IdentityProviderName", "documentation": "<p>The name of the identity provider.</p>"}, "identityProviderType": {"shape": "IdentityProviderType", "documentation": "<p>The type of the identity provider.</p>"}}}, "UpdateIdentityProviderResponse": {"type": "structure", "required": ["identity<PERSON><PERSON><PERSON>"], "members": {"identityProvider": {"shape": "IdentityProvider", "documentation": "<p>The identity provider.</p>"}}}, "UpdateIpAccessSettingsRequest": {"type": "structure", "required": ["ipAccessSettingsArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "description": {"shape": "Description", "documentation": "<p>The description of the IP access settings.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The display name of the IP access settings.</p>"}, "ipAccessSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the IP access settings.</p>", "location": "uri", "locationName": "ipAccessSettingsArn"}, "ipRules": {"shape": "IpRuleList", "documentation": "<p>The updated IP rules of the IP access settings.</p>"}}}, "UpdateIpAccessSettingsResponse": {"type": "structure", "required": ["ipAccessSettings"], "members": {"ipAccessSettings": {"shape": "IpAccessSettings", "documentation": "<p>The IP access settings.</p>"}}}, "UpdateNetworkSettingsRequest": {"type": "structure", "required": ["networkSettingsArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "networkSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the network settings.</p>", "location": "uri", "locationName": "networkSettingsArn"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>One or more security groups used to control access from streaming instances to your VPC.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>The subnets in which network interfaces are created to connect streaming instances to your VPC. At least two of these subnets must be in different availability zones.</p>"}, "vpcId": {"shape": "VpcId", "documentation": "<p>The VPC that streaming instances will connect to.</p>"}}}, "UpdateNetworkSettingsResponse": {"type": "structure", "required": ["networkSettings"], "members": {"networkSettings": {"shape": "NetworkSettings", "documentation": "<p>The network settings.</p>"}}}, "UpdatePortalRequest": {"type": "structure", "required": ["portalArn"], "members": {"authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The type of authentication integration points used when signing into the web portal. Defaults to <code>Standard</code>.</p> <p> <code>Standard</code> web portals are authenticated directly through your identity provider. You need to call <code>CreateIdentityProvider</code> to integrate your identity provider with your web portal. User and group access to your web portal is controlled through your identity provider.</p> <p> <code>IAM_Identity_Center</code> web portals are authenticated through AWS IAM Identity Center (successor to AWS Single Sign-On). They provide additional features, such as IdP-initiated authentication. Identity sources (including external identity provider integration), plus user and group access to your web portal, can be configured in the IAM Identity Center.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The name of the web portal. This is not visible to users who log into the web portal.</p>"}, "portalArn": {"shape": "ARN", "documentation": "<p>The ARN of the web portal.</p>", "location": "uri", "locationName": "portalArn"}}}, "UpdatePortalResponse": {"type": "structure", "members": {"portal": {"shape": "Portal", "documentation": "<p>The web portal.</p>"}}}, "UpdateTrustStoreRequest": {"type": "structure", "required": ["trustStoreArn"], "members": {"certificatesToAdd": {"shape": "CertificateList", "documentation": "<p>A list of CA certificates to add to the trust store.</p>"}, "certificatesToDelete": {"shape": "CertificateThumbprintList", "documentation": "<p>A list of CA certificates to delete from a trust store.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>", "location": "uri", "locationName": "trustStoreArn"}}}, "UpdateTrustStoreResponse": {"type": "structure", "required": ["trustStoreArn"], "members": {"trustStoreArn": {"shape": "ARN", "documentation": "<p>The ARN of the trust store.</p>"}}}, "UpdateUserAccessLoggingSettingsRequest": {"type": "structure", "required": ["userAccessLoggingSettingsArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "kinesisStreamArn": {"shape": "KinesisStreamArn", "documentation": "<p>The ARN of the Kinesis stream.</p>"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>", "location": "uri", "locationName": "userAccessLoggingSettingsArn"}}}, "UpdateUserAccessLoggingSettingsResponse": {"type": "structure", "required": ["userAccessLoggingSettings"], "members": {"userAccessLoggingSettings": {"shape": "UserAccessLoggingSettings", "documentation": "<p>The user access logging settings.</p>"}}}, "UpdateUserSettingsRequest": {"type": "structure", "required": ["userSettingsArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, subsequent retries with the same client token return the result from the original successful request. </p> <p>If you do not specify a client token, one is automatically generated by the AWS SDK.</p>", "idempotencyToken": true}, "cookieSynchronizationConfiguration": {"shape": "CookieSynchronizationConfiguration", "documentation": "<p>The configuration that specifies which cookies should be synchronized from the end user's local browser to the remote browser.</p> <p>If the allowlist and blocklist are empty, the configuration becomes null.</p>"}, "copyAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can copy text from the streaming session to the local device.</p>"}, "disconnectTimeoutInMinutes": {"shape": "DisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect.</p>"}, "downloadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can download files from the streaming session to the local device.</p>"}, "idleDisconnectTimeoutInMinutes": {"shape": "IdleDisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the disconnect timeout interval begins.</p>"}, "pasteAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can paste text from the local device to the streaming session.</p>"}, "printAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can print to the local device.</p>"}, "uploadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can upload files from the local device to the streaming session.</p>"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>", "location": "uri", "locationName": "userSettingsArn"}}}, "UpdateUserSettingsResponse": {"type": "structure", "required": ["userSettings"], "members": {"userSettings": {"shape": "UserSettings", "documentation": "<p>The user settings.</p>"}}}, "UserAccessLoggingSettings": {"type": "structure", "required": ["userAccessLoggingSettingsArn"], "members": {"associatedPortalArns": {"shape": "ArnList", "documentation": "<p>A list of web portal ARNs that this user access logging settings is associated with.</p>"}, "kinesisStreamArn": {"shape": "KinesisStreamArn", "documentation": "<p>The ARN of the Kinesis stream.</p>"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>"}}, "documentation": "<p>A user access logging settings resource that can be associated with a web portal.</p>"}, "UserAccessLoggingSettingsList": {"type": "list", "member": {"shape": "UserAccessLoggingSettingsSummary"}}, "UserAccessLoggingSettingsSummary": {"type": "structure", "required": ["userAccessLoggingSettingsArn"], "members": {"kinesisStreamArn": {"shape": "KinesisStreamArn", "documentation": "<p>The ARN of the Kinesis stream.</p>"}, "userAccessLoggingSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user access logging settings.</p>"}}, "documentation": "<p>The summary of user access logging settings.</p>"}, "UserSettings": {"type": "structure", "required": ["userSettingsArn"], "members": {"associatedPortalArns": {"shape": "ArnList", "documentation": "<p>A list of web portal ARNs that this user settings is associated with.</p>"}, "cookieSynchronizationConfiguration": {"shape": "CookieSynchronizationConfiguration", "documentation": "<p>The configuration that specifies which cookies should be synchronized from the end user's local browser to the remote browser.</p>"}, "copyAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can copy text from the streaming session to the local device.</p>"}, "disconnectTimeoutInMinutes": {"shape": "DisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect.</p>"}, "downloadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can download files from the streaming session to the local device.</p>"}, "idleDisconnectTimeoutInMinutes": {"shape": "IdleDisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the disconnect timeout interval begins.</p>"}, "pasteAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can paste text from the local device to the streaming session.</p>"}, "printAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can print to the local device.</p>"}, "uploadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can upload files from the local device to the streaming session.</p>"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>"}}, "documentation": "<p>A user settings resource that can be associated with a web portal. Once associated with a web portal, user settings control how users can transfer data between a streaming session and the their local devices. </p>"}, "UserSettingsList": {"type": "list", "member": {"shape": "UserSettingsSummary"}}, "UserSettingsSummary": {"type": "structure", "required": ["userSettingsArn"], "members": {"cookieSynchronizationConfiguration": {"shape": "CookieSynchronizationConfiguration", "documentation": "<p>The configuration that specifies which cookies should be synchronized from the end user's local browser to the remote browser.</p>"}, "copyAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can copy text from the streaming session to the local device.</p>"}, "disconnectTimeoutInMinutes": {"shape": "DisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect.</p>"}, "downloadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can download files from the streaming session to the local device.</p>"}, "idleDisconnectTimeoutInMinutes": {"shape": "IdleDisconnectTimeoutInMinutes", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the disconnect timeout interval begins.</p>"}, "pasteAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can paste text from the local device to the streaming session.</p>"}, "printAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can print to the local device.</p>"}, "uploadAllowed": {"shape": "EnabledType", "documentation": "<p>Specifies whether the user can upload files from the local device to the streaming session.</p>"}, "userSettingsArn": {"shape": "ARN", "documentation": "<p>The ARN of the user settings.</p>"}}, "documentation": "<p>The summary of user settings.</p>"}, "ValidationException": {"type": "structure", "members": {"fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The field that caused the error.</p>"}, "message": {"shape": "ExceptionMessage"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>Reason the request failed validation</p>"}}, "documentation": "<p>There is a validation error.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "ExceptionMessage", "documentation": "<p>The message describing why the field failed validation.</p>"}, "name": {"shape": "FieldName", "documentation": "<p>The name of the field that failed validation.</p>"}}, "documentation": "<p>Information about a field passed inside a request that resulted in an exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["unknownOperation", "<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "other"]}, "VpcId": {"type": "string", "max": 255, "min": 1, "pattern": "^vpc-[0-9a-z]*$"}, "keyArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:[\\w+=\\/,.@-]+:kms:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:key\\/[a-zA-Z0-9-]+$"}}, "documentation": "<p>WorkSpaces Web is a low cost, fully managed WorkSpace built specifically to facilitate secure, web-based workloads. WorkSpaces Web makes it easy for customers to safely provide their employees with access to internal websites and SaaS web applications without the administrative burden of appliances or specialized client software. WorkSpaces Web provides simple policy tools tailored for user interactions, while offloading common tasks like capacity management, scaling, and maintaining browser images.</p>"}