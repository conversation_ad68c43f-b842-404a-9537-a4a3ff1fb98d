# -*- coding: utf-8 -*-
# Licensed under a 3-clause BSD style license - see LICENSE.rst

# This file was automatically generated from ply. To re-generate this file,
# remove it from this folder, then build astropy and run the tests in-place:
#
#   python setup.py build_ext --inplace
#   pytest astropy/units
#
# You can then commit the changes to this file.


# generic_parsetab.py
# This file is automatically generated. Do not edit.
# pylint: disable=W,C,R
_tabversion = '3.10'

_lr_method = 'LALR'

_lr_signature = 'CARET CLOSE_PAREN COMMA DOUBLE_STAR FUNCNAME OPEN_PAREN PERIOD SIGN SOLIDUS STAR UFLOAT UINT UNIT\n            main : unit\n                 | structured_unit\n                 | structured_subunit\n            \n            structured_subunit : OPEN_PAREN structured_unit CLOSE_PAREN\n            \n            structured_unit : subunit COMMA\n                            | subunit COMMA subunit\n            \n            subunit : unit\n                    | structured_unit\n                    | structured_subunit\n            \n            unit : product_of_units\n                 | factor product_of_units\n                 | factor product product_of_units\n                 | division_product_of_units\n                 | factor division_product_of_units\n                 | factor product division_product_of_units\n                 | inverse_unit\n                 | factor inverse_unit\n                 | factor product inverse_unit\n                 | factor\n            \n            division_product_of_units : division_product_of_units division product_of_units\n                                      | product_of_units\n            \n            inverse_unit : division unit_expression\n            \n            factor : factor_fits\n                   | factor_float\n                   | factor_int\n            \n            factor_float : signed_float\n                         | signed_float UINT signed_int\n                         | signed_float UINT power numeric_power\n            \n            factor_int : UINT\n                       | UINT signed_int\n                       | UINT power numeric_power\n                       | UINT UINT signed_int\n                       | UINT UINT power numeric_power\n            \n            factor_fits : UINT power OPEN_PAREN signed_int CLOSE_PAREN\n                        | UINT power OPEN_PAREN UINT CLOSE_PAREN\n                        | UINT power signed_int\n                        | UINT power UINT\n                        | UINT SIGN UINT\n                        | UINT OPEN_PAREN signed_int CLOSE_PAREN\n            \n            product_of_units : unit_expression product product_of_units\n                             | unit_expression product_of_units\n                             | unit_expression\n            \n            unit_expression : function\n                            | unit_with_power\n                            | OPEN_PAREN product_of_units CLOSE_PAREN\n            \n            unit_with_power : UNIT power numeric_power\n                            | UNIT numeric_power\n                            | UNIT\n            \n            numeric_power : sign UINT\n                          | OPEN_PAREN paren_expr CLOSE_PAREN\n            \n            paren_expr : sign UINT\n                       | signed_float\n                       | frac\n            \n            frac : sign UINT division sign UINT\n            \n            sign : SIGN\n                 |\n            \n            product : STAR\n                    | PERIOD\n            \n            division : SOLIDUS\n            \n            power : DOUBLE_STAR\n                  | CARET\n            \n            signed_int : SIGN UINT\n            \n            signed_float : sign UINT\n                         | sign UFLOAT\n            \n            function_name : FUNCNAME\n            \n            function : function_name OPEN_PAREN main CLOSE_PAREN\n            '
    
_lr_action_items = {'OPEN_PAREN':([0,6,10,11,12,13,14,15,16,17,18,20,21,22,23,25,27,30,31,32,33,34,35,40,44,46,48,49,51,52,53,56,57,66,68,69,71,73,74,77,78,79,81,82,87,88,91,92,93,94,96,97,],[10,32,35,32,-23,-24,-25,32,-43,-44,45,-26,-59,51,55,-65,32,-57,-58,32,32,10,35,32,72,-30,-60,-61,10,55,-47,-63,-64,-45,-32,55,-37,-36,-31,-38,-27,55,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'UINT':([0,10,18,19,20,21,23,24,34,35,44,47,48,49,51,52,54,55,56,57,69,70,72,75,79,84,98,99,],[18,18,43,-55,50,-59,-56,56,18,18,71,77,-60,-61,18,-56,82,-56,-63,-64,-56,88,89,88,-56,95,-56,100,]),'SOLIDUS':([0,5,6,7,10,11,12,13,14,16,17,18,20,23,26,27,28,30,31,34,35,37,41,46,51,53,56,57,58,59,62,66,67,68,71,73,74,77,78,81,82,87,88,91,92,93,94,95,96,97,],[21,-21,21,21,21,-42,-23,-24,-25,-43,-44,-29,-26,-48,-21,21,21,-57,-58,21,21,-21,-41,-30,21,-47,-63,-64,-21,21,-20,-45,-40,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,21,-35,-34,]),'UNIT':([0,6,10,11,12,13,14,15,16,17,18,20,21,23,27,30,31,32,33,34,35,40,46,51,53,56,57,66,68,71,73,74,77,78,81,82,87,88,91,92,93,94,96,97,],[23,23,23,23,-23,-24,-25,23,-43,-44,-29,-26,-59,-48,23,-57,-58,23,23,23,23,23,-30,23,-47,-63,-64,-45,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'FUNCNAME':([0,6,10,11,12,13,14,15,16,17,18,20,21,23,27,30,31,32,33,34,35,40,46,51,53,56,57,66,68,71,73,74,77,78,81,82,87,88,91,92,93,94,96,97,],[25,25,25,25,-23,-24,-25,25,-43,-44,-29,-26,-59,-48,25,-57,-58,25,25,25,25,25,-30,25,-47,-63,-64,-45,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'SIGN':([0,10,18,21,23,34,35,43,44,45,48,49,50,51,52,55,69,72,79,98,],[19,19,47,-59,19,19,19,70,75,70,-60,-61,70,19,19,19,19,75,19,19,]),'UFLOAT':([0,10,19,24,34,35,51,55,72,75,84,],[-56,-56,-55,57,-56,-56,-56,-56,-56,-55,57,]),'$end':([1,2,3,4,5,6,7,8,11,12,13,14,16,17,18,20,23,26,28,29,34,38,39,41,42,46,53,56,57,58,59,60,62,63,64,65,66,67,68,71,73,74,77,78,81,82,87,88,91,92,93,94,96,97,],[0,-1,-2,-3,-10,-19,-13,-16,-42,-23,-24,-25,-43,-44,-29,-26,-48,-11,-14,-17,-5,-7,-9,-41,-22,-30,-47,-63,-64,-12,-15,-18,-20,-6,-8,-4,-45,-40,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'CLOSE_PAREN':([2,3,4,5,6,7,8,11,12,13,14,16,17,18,20,23,26,28,29,34,36,37,38,39,41,42,46,53,56,57,58,59,60,61,62,63,64,65,66,67,68,71,73,74,76,77,78,80,81,82,83,85,86,87,88,89,90,91,92,93,94,95,96,97,100,],[-1,-2,-3,-10,-19,-13,-16,-42,-23,-24,-25,-43,-44,-29,-26,-48,-11,-14,-17,-5,65,66,-7,-9,-41,-22,-30,-47,-63,-64,-12,-15,-18,66,-20,-6,-8,-4,-45,-40,-32,-37,-36,-31,91,-38,-27,93,-46,-49,94,-52,-53,-33,-62,96,97,-39,-28,-66,-50,-51,-35,-34,-54,]),'COMMA':([2,3,4,5,6,7,8,9,11,12,13,14,16,17,18,20,23,26,28,29,34,36,37,38,39,41,42,46,53,56,57,58,59,60,62,63,64,65,66,67,68,71,73,74,77,78,81,82,87,88,91,92,93,94,96,97,],[-7,-8,-9,-10,-19,-13,-16,34,-42,-23,-24,-25,-43,-44,-29,-26,-48,-11,-14,-17,-5,-8,-10,-7,-9,-41,-22,-30,-47,-63,-64,-12,-15,-18,-20,34,-8,-4,-45,-40,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'STAR':([6,11,12,13,14,16,17,18,20,23,46,53,56,57,66,68,71,73,74,77,78,81,82,87,88,91,92,93,94,96,97,],[30,30,-23,-24,-25,-43,-44,-29,-26,-48,-30,-47,-63,-64,-45,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'PERIOD':([6,11,12,13,14,16,17,18,20,23,46,53,56,57,66,68,71,73,74,77,78,81,82,87,88,91,92,93,94,96,97,],[31,31,-23,-24,-25,-43,-44,-29,-26,-48,-30,-47,-63,-64,-45,-32,-37,-36,-31,-38,-27,-46,-49,-33,-62,-39,-28,-66,-50,-35,-34,]),'DOUBLE_STAR':([18,23,43,50,],[48,48,48,48,]),'CARET':([18,23,43,50,],[49,49,49,49,]),}

_lr_action = {}
for _k, _v in _lr_action_items.items():
   for _x,_y in zip(_v[0],_v[1]):
      if not _x in _lr_action:  _lr_action[_x] = {}
      _lr_action[_x][_k] = _y
del _lr_action_items

_lr_goto_items = {'main':([0,51,],[1,80,]),'unit':([0,10,34,35,51,],[2,38,38,38,2,]),'structured_unit':([0,10,34,35,51,],[3,36,64,36,3,]),'structured_subunit':([0,10,34,35,51,],[4,39,39,39,4,]),'product_of_units':([0,6,10,11,27,32,33,34,35,40,51,],[5,26,37,41,58,61,62,5,37,67,5,]),'factor':([0,10,34,35,51,],[6,6,6,6,6,]),'division_product_of_units':([0,6,10,27,34,35,51,],[7,28,7,59,7,7,7,]),'inverse_unit':([0,6,10,27,34,35,51,],[8,29,8,60,8,8,8,]),'subunit':([0,10,34,35,51,],[9,9,63,9,9,]),'unit_expression':([0,6,10,11,15,27,32,33,34,35,40,51,],[11,11,11,11,42,11,11,11,11,11,11,11,]),'factor_fits':([0,10,34,35,51,],[12,12,12,12,12,]),'factor_float':([0,10,34,35,51,],[13,13,13,13,13,]),'factor_int':([0,10,34,35,51,],[14,14,14,14,14,]),'division':([0,6,7,10,27,28,34,35,51,59,95,],[15,15,33,15,15,33,15,15,15,33,98,]),'function':([0,6,10,11,15,27,32,33,34,35,40,51,],[16,16,16,16,16,16,16,16,16,16,16,16,]),'unit_with_power':([0,6,10,11,15,27,32,33,34,35,40,51,],[17,17,17,17,17,17,17,17,17,17,17,17,]),'signed_float':([0,10,34,35,51,55,72,],[20,20,20,20,20,85,85,]),'function_name':([0,6,10,11,15,27,32,33,34,35,40,51,],[22,22,22,22,22,22,22,22,22,22,22,22,]),'sign':([0,10,23,34,35,44,51,52,55,69,72,79,98,],[24,24,54,24,24,54,24,54,84,54,84,54,99,]),'product':([6,11,],[27,40,]),'power':([18,23,43,50,],[44,52,69,79,]),'signed_int':([18,43,44,45,50,72,],[46,68,73,76,78,90,]),'numeric_power':([23,44,52,69,79,],[53,74,81,87,92,]),'paren_expr':([55,72,],[83,83,]),'frac':([55,72,],[86,86,]),}

_lr_goto = {}
for _k, _v in _lr_goto_items.items():
   for _x, _y in zip(_v[0], _v[1]):
       if not _x in _lr_goto: _lr_goto[_x] = {}
       _lr_goto[_x][_k] = _y
del _lr_goto_items
_lr_productions = [
  ("S' -> main","S'",1,None,None,None),
  ('main -> unit','main',1,'p_main','generic.py',196),
  ('main -> structured_unit','main',1,'p_main','generic.py',197),
  ('main -> structured_subunit','main',1,'p_main','generic.py',198),
  ('structured_subunit -> OPEN_PAREN structured_unit CLOSE_PAREN','structured_subunit',3,'p_structured_subunit','generic.py',209),
  ('structured_unit -> subunit COMMA','structured_unit',2,'p_structured_unit','generic.py',218),
  ('structured_unit -> subunit COMMA subunit','structured_unit',3,'p_structured_unit','generic.py',219),
  ('subunit -> unit','subunit',1,'p_subunit','generic.py',241),
  ('subunit -> structured_unit','subunit',1,'p_subunit','generic.py',242),
  ('subunit -> structured_subunit','subunit',1,'p_subunit','generic.py',243),
  ('unit -> product_of_units','unit',1,'p_unit','generic.py',249),
  ('unit -> factor product_of_units','unit',2,'p_unit','generic.py',250),
  ('unit -> factor product product_of_units','unit',3,'p_unit','generic.py',251),
  ('unit -> division_product_of_units','unit',1,'p_unit','generic.py',252),
  ('unit -> factor division_product_of_units','unit',2,'p_unit','generic.py',253),
  ('unit -> factor product division_product_of_units','unit',3,'p_unit','generic.py',254),
  ('unit -> inverse_unit','unit',1,'p_unit','generic.py',255),
  ('unit -> factor inverse_unit','unit',2,'p_unit','generic.py',256),
  ('unit -> factor product inverse_unit','unit',3,'p_unit','generic.py',257),
  ('unit -> factor','unit',1,'p_unit','generic.py',258),
  ('division_product_of_units -> division_product_of_units division product_of_units','division_product_of_units',3,'p_division_product_of_units','generic.py',270),
  ('division_product_of_units -> product_of_units','division_product_of_units',1,'p_division_product_of_units','generic.py',271),
  ('inverse_unit -> division unit_expression','inverse_unit',2,'p_inverse_unit','generic.py',281),
  ('factor -> factor_fits','factor',1,'p_factor','generic.py',287),
  ('factor -> factor_float','factor',1,'p_factor','generic.py',288),
  ('factor -> factor_int','factor',1,'p_factor','generic.py',289),
  ('factor_float -> signed_float','factor_float',1,'p_factor_float','generic.py',295),
  ('factor_float -> signed_float UINT signed_int','factor_float',3,'p_factor_float','generic.py',296),
  ('factor_float -> signed_float UINT power numeric_power','factor_float',4,'p_factor_float','generic.py',297),
  ('factor_int -> UINT','factor_int',1,'p_factor_int','generic.py',310),
  ('factor_int -> UINT signed_int','factor_int',2,'p_factor_int','generic.py',311),
  ('factor_int -> UINT power numeric_power','factor_int',3,'p_factor_int','generic.py',312),
  ('factor_int -> UINT UINT signed_int','factor_int',3,'p_factor_int','generic.py',313),
  ('factor_int -> UINT UINT power numeric_power','factor_int',4,'p_factor_int','generic.py',314),
  ('factor_fits -> UINT power OPEN_PAREN signed_int CLOSE_PAREN','factor_fits',5,'p_factor_fits','generic.py',332),
  ('factor_fits -> UINT power OPEN_PAREN UINT CLOSE_PAREN','factor_fits',5,'p_factor_fits','generic.py',333),
  ('factor_fits -> UINT power signed_int','factor_fits',3,'p_factor_fits','generic.py',334),
  ('factor_fits -> UINT power UINT','factor_fits',3,'p_factor_fits','generic.py',335),
  ('factor_fits -> UINT SIGN UINT','factor_fits',3,'p_factor_fits','generic.py',336),
  ('factor_fits -> UINT OPEN_PAREN signed_int CLOSE_PAREN','factor_fits',4,'p_factor_fits','generic.py',337),
  ('product_of_units -> unit_expression product product_of_units','product_of_units',3,'p_product_of_units','generic.py',356),
  ('product_of_units -> unit_expression product_of_units','product_of_units',2,'p_product_of_units','generic.py',357),
  ('product_of_units -> unit_expression','product_of_units',1,'p_product_of_units','generic.py',358),
  ('unit_expression -> function','unit_expression',1,'p_unit_expression','generic.py',369),
  ('unit_expression -> unit_with_power','unit_expression',1,'p_unit_expression','generic.py',370),
  ('unit_expression -> OPEN_PAREN product_of_units CLOSE_PAREN','unit_expression',3,'p_unit_expression','generic.py',371),
  ('unit_with_power -> UNIT power numeric_power','unit_with_power',3,'p_unit_with_power','generic.py',380),
  ('unit_with_power -> UNIT numeric_power','unit_with_power',2,'p_unit_with_power','generic.py',381),
  ('unit_with_power -> UNIT','unit_with_power',1,'p_unit_with_power','generic.py',382),
  ('numeric_power -> sign UINT','numeric_power',2,'p_numeric_power','generic.py',393),
  ('numeric_power -> OPEN_PAREN paren_expr CLOSE_PAREN','numeric_power',3,'p_numeric_power','generic.py',394),
  ('paren_expr -> sign UINT','paren_expr',2,'p_paren_expr','generic.py',403),
  ('paren_expr -> signed_float','paren_expr',1,'p_paren_expr','generic.py',404),
  ('paren_expr -> frac','paren_expr',1,'p_paren_expr','generic.py',405),
  ('frac -> sign UINT division sign UINT','frac',5,'p_frac','generic.py',414),
  ('sign -> SIGN','sign',1,'p_sign','generic.py',420),
  ('sign -> <empty>','sign',0,'p_sign','generic.py',421),
  ('product -> STAR','product',1,'p_product','generic.py',430),
  ('product -> PERIOD','product',1,'p_product','generic.py',431),
  ('division -> SOLIDUS','division',1,'p_division','generic.py',437),
  ('power -> DOUBLE_STAR','power',1,'p_power','generic.py',443),
  ('power -> CARET','power',1,'p_power','generic.py',444),
  ('signed_int -> SIGN UINT','signed_int',2,'p_signed_int','generic.py',450),
  ('signed_float -> sign UINT','signed_float',2,'p_signed_float','generic.py',456),
  ('signed_float -> sign UFLOAT','signed_float',2,'p_signed_float','generic.py',457),
  ('function_name -> FUNCNAME','function_name',1,'p_function_name','generic.py',463),
  ('function -> function_name OPEN_PAREN main CLOSE_PAREN','function',4,'p_function','generic.py',469),
]
