#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QComboBox, QLabel,
    QDateEdit, QSpinBox, QTextEdit, QSplitter, QMessageBox, QFileDialog,
    QGroupBox
)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QFont
from language_manager import get_text, set_language

class LogViewerWindow(QMainWindow):
    """日志查看器窗口 - 仅管理员可用"""
    
    def __init__(self, log_manager=None):
        super().__init__()
        self.log_manager = log_manager
        
        if not self.log_manager:
            try:
                from log_manager import get_log_manager
                self.log_manager = get_log_manager()
            except ImportError:
                QMessageBox.critical(self, "错误", "无法导入日志管理器")
                return
        
        self.init_ui()
        self.load_logs()
        
        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_logs)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(get_text("log_viewer"))
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 过滤器控件组
        self.filter_group = QGroupBox(get_text("log_filter"))
        filter_layout = QHBoxLayout(self.filter_group)
        
        # 日志级别过滤
        self.level_label = QLabel(get_text("level"))
        filter_layout.addWidget(self.level_label)
        self.level_combo = QComboBox()
        self.level_combo.addItems([get_text("all"), "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.currentTextChanged.connect(self.load_logs)
        filter_layout.addWidget(self.level_combo)
        
        # 日志类别过滤
        self.category_label = QLabel(get_text("category"))
        filter_layout.addWidget(self.category_label)
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            get_text("all"), "INFO", "WARNING", "ERROR", "CRITICAL", "USER_ACTION", 
            "SYSTEM", "EXCEPTION", "CLEANUP", "EXPORT"
        ])
        self.category_combo.currentTextChanged.connect(self.load_logs)
        filter_layout.addWidget(self.category_combo)
        
        # 天数过滤
        self.days_label = QLabel(get_text("days"))
        filter_layout.addWidget(self.days_label)
        self.days_spin = QSpinBox()
        self.days_spin.setRange(1, 365)
        self.days_spin.setValue(7)
        self.days_spin.valueChanged.connect(self.load_logs)
        filter_layout.addWidget(self.days_spin)
        
        # 限制条数
        self.limit_label = QLabel(get_text("limit"))
        filter_layout.addWidget(self.limit_label)
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(100, 10000)
        self.limit_spin.setValue(1000)
        self.limit_spin.setSuffix(get_text("records"))
        self.limit_spin.valueChanged.connect(self.load_logs)
        filter_layout.addWidget(self.limit_spin)
        
        # 刷新按钮
        refresh_btn = QPushButton(get_text("refresh"))
        refresh_btn.clicked.connect(self.load_logs)
        filter_layout.addWidget(refresh_btn)
        
        # 导出按钮
        export_btn = QPushButton(get_text("export_logs"))
        export_btn.clicked.connect(self.export_logs)
        filter_layout.addWidget(export_btn)
        
        # 清理按钮
        cleanup_btn = QPushButton(get_text("cleanup_now"))
        cleanup_btn.clicked.connect(self.cleanup_logs)
        filter_layout.addWidget(cleanup_btn)
        
        filter_layout.addStretch()
        main_layout.addWidget(self.filter_group)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 日志表格
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(6)
        self.log_table.setHorizontalHeaderLabels([
            get_text("time"), get_text("level_col"), get_text("category_col"), 
            get_text("message"), get_text("details"), get_text("create_time")
        ])
        
        # 设置列宽
        self.log_table.setColumnWidth(0, 150)  # 时间
        self.log_table.setColumnWidth(1, 80)   # 级别
        self.log_table.setColumnWidth(2, 120)  # 类别
        self.log_table.setColumnWidth(3, 400)  # 消息
        self.log_table.setColumnWidth(4, 200)  # 详情
        self.log_table.setColumnWidth(5, 150)  # 创建时间
        
        # 设置选择模式
        self.log_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.log_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        splitter.addWidget(self.log_table)
        
        # 详情文本框
        self.detail_text = QTextEdit()
        self.detail_text.setMaximumHeight(200)
        self.detail_text.setReadOnly(True)
        self.detail_text.setFont(QFont("Consolas", 9))
        splitter.addWidget(self.detail_text)
        
        main_layout.addWidget(splitter)
        
        # 状态栏信息
        status_layout = QHBoxLayout()
        self.status_label = QLabel(get_text("ready"))
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        # 自动刷新开关
        self.auto_refresh_btn = QPushButton(get_text("stop_auto_refresh"))
        self.auto_refresh_btn.setCheckable(True)
        self.auto_refresh_btn.toggled.connect(self.toggle_auto_refresh)
        status_layout.addWidget(self.auto_refresh_btn)
        
        main_layout.addLayout(status_layout)
    
    def load_logs(self):
        """加载日志数据"""
        try:
            # 获取过滤参数
            level = self.level_combo.currentText() if self.level_combo.currentText() != get_text("all") else None
            category = self.category_combo.currentText() if self.category_combo.currentText() != get_text("all") else None
            days = self.days_spin.value()
            limit = self.limit_spin.value()
            
            # 从数据库获取日志
            logs = self.log_manager.get_logs(level=level, category=category, days=days, limit=limit)
            
            # 更新表格
            self.log_table.setRowCount(len(logs))
            
            for row, log in enumerate(logs):
                log_id, timestamp, level, category, message, details, encrypted_data, hash_value, created_at = log
                
                # 填充表格
                self.log_table.setItem(row, 0, QTableWidgetItem(timestamp))
                self.log_table.setItem(row, 1, QTableWidgetItem(level))
                self.log_table.setItem(row, 2, QTableWidgetItem(category))
                self.log_table.setItem(row, 3, QTableWidgetItem(message))
                self.log_table.setItem(row, 4, QTableWidgetItem(details[:50] + "..." if details and len(details) > 50 else details or ""))
                self.log_table.setItem(row, 5, QTableWidgetItem(created_at))
                
                # 根据级别设置行颜色
                if level == "ERROR":
                    for col in range(6):
                        if self.log_table.item(row, col):
                            self.log_table.item(row, col).setBackground(Qt.GlobalColor.lightGray)
                elif level == "CRITICAL":
                    for col in range(6):
                        if self.log_table.item(row, col):
                            self.log_table.item(row, col).setBackground(Qt.GlobalColor.red)
                elif level == "WARNING":
                    for col in range(6):
                        if self.log_table.item(row, col):
                            self.log_table.item(row, col).setBackground(Qt.GlobalColor.yellow)
            
            # 更新状态
            self.status_label.setText(get_text("loaded_logs").format(len(logs), datetime.now().strftime('%H:%M:%S')))
            
        except Exception as e:
            QMessageBox.critical(self, get_text("error"), f"加载日志失败: {e}")
    
    def on_selection_changed(self):
        """选择行变化时显示详细信息"""
        current_row = self.log_table.currentRow()
        if current_row >= 0:
            # 获取详情信息
            details = self.log_table.item(current_row, 4).text() if self.log_table.item(current_row, 4) else ""
            timestamp = self.log_table.item(current_row, 0).text()
            level = self.log_table.item(current_row, 1).text()
            category = self.log_table.item(current_row, 2).text()
            message = self.log_table.item(current_row, 3).text()
            
            # 格式化显示
            detail_text = f"{get_text('time')}: {timestamp}\n"
            detail_text += f"{get_text('level_col')}: {level}\n"
            detail_text += f"{get_text('category_col')}: {category}\n"
            detail_text += f"{get_text('message')}: {message}\n"
            detail_text += f"{get_text('details')}: {details}\n"
            
            self.detail_text.setText(detail_text)
    
    def toggle_auto_refresh(self, checked):
        """切换自动刷新"""
        if checked:
            self.refresh_timer.stop()
            self.auto_refresh_btn.setText(get_text("start_auto_refresh"))
        else:
            self.refresh_timer.start(30000)
            self.auto_refresh_btn.setText(get_text("stop_auto_refresh"))
    
    def export_logs(self):
        """导出日志到文件"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, get_text("export_logs_title"), 
                f"logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                days = self.days_spin.value()
                success = self.log_manager.export_logs(file_path, days=days)
                
                if success:
                    QMessageBox.information(self, get_text("success"), f"日志已导出到: {file_path}")
                else:
                    QMessageBox.warning(self, get_text("failed"), "日志导出失败")
        except Exception as e:
            QMessageBox.critical(self, get_text("error"), f"导出日志时出错: {e}")
    
    def cleanup_logs(self):
        """立即清理过期日志"""
        try:
            reply = QMessageBox.question(
                self, get_text("confirm_cleanup"), 
                get_text("cleanup_confirm_message"),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.log_manager.cleanup_old_logs(days=15)
                QMessageBox.information(self, get_text("cleanup_complete"), get_text("logs_cleanup_complete"))
                self.load_logs()  # 重新加载
        except Exception as e:
            QMessageBox.critical(self, get_text("error"), f"清理日志时出错: {e}")
    
    def update_language(self, language):
        """更新语言"""
        set_language(language)
        
        # 更新窗口标题
        self.setWindowTitle(get_text("log_viewer"))
        
        # 更新过滤器组标题
        if hasattr(self, 'filter_group'):
            self.filter_group.setTitle(get_text("log_filter"))
        
        # 更新标签文本
        if hasattr(self, 'level_label'):
            self.level_label.setText(get_text("level"))
        if hasattr(self, 'category_label'):
            self.category_label.setText(get_text("category"))
        if hasattr(self, 'days_label'):
            self.days_label.setText(get_text("days"))
        if hasattr(self, 'limit_label'):
            self.limit_label.setText(get_text("limit"))
        
        # 更新标签文本
        if hasattr(self, 'level_combo'):
            current_level = self.level_combo.currentText()
            self.level_combo.clear()
            self.level_combo.addItems([get_text("all"), "INFO", "WARNING", "ERROR", "CRITICAL"])
            # 恢复之前的选择
            if current_level in ["INFO", "WARNING", "ERROR", "CRITICAL"]:
                self.level_combo.setCurrentText(current_level)
            elif current_level == "全部" or current_level == "All":
                self.level_combo.setCurrentText(get_text("all"))
        
        if hasattr(self, 'category_combo'):
            current_category = self.category_combo.currentText()
            self.category_combo.clear()
            self.category_combo.addItems([
                get_text("all"), "INFO", "WARNING", "ERROR", "CRITICAL", "USER_ACTION", 
                "SYSTEM", "EXCEPTION", "CLEANUP", "EXPORT"
            ])
            # 恢复之前的选择
            if current_category in ["INFO", "WARNING", "ERROR", "CRITICAL", "USER_ACTION", "SYSTEM", "EXCEPTION", "CLEANUP", "EXPORT"]:
                self.category_combo.setCurrentText(current_category)
            elif current_category == "全部" or current_category == "All":
                self.category_combo.setCurrentText(get_text("all"))
        
        # 更新按钮文本
        if hasattr(self, 'auto_refresh_btn'):
            if self.auto_refresh_btn.isChecked():
                self.auto_refresh_btn.setText(get_text("start_auto_refresh"))
            else:
                self.auto_refresh_btn.setText(get_text("stop_auto_refresh"))
        
        # 更新状态标签
        if hasattr(self, 'status_label'):
            self.status_label.setText(get_text("ready"))
        
        # 更新表格标题
        if hasattr(self, 'log_table'):
            self.log_table.setHorizontalHeaderLabels([
                get_text("time"), get_text("level_col"), get_text("category_col"), 
                get_text("message"), get_text("details"), get_text("create_time")
            ])
    
    def closeEvent(self, event):
        """关闭事件"""
        self.refresh_timer.stop()
        event.accept()


def main():
    """独立运行日志查看器"""
    app = QApplication(sys.argv)
    
    try:
        # 导入日志管理器
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        from log_manager import get_log_manager
        log_manager = get_log_manager()
        
        window = LogViewerWindow(log_manager)
        window.show()
        
        sys.exit(app.exec())
    except Exception as e:
        print(f"启动日志查看器失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
