/*
*   math2html: convert LaTeX equations to HTML output.
*
*   Copyright (C) 2009,2010 <PERSON>
*
*   Released under the terms of the `2-Clause BSD license'_, in short:
*   Copying and distribution of this file, with or without modification,
*   are permitted in any medium without royalty provided the copyright
*   notice and this notice are preserved.
*   This file is offered as-is, without any warranty.
*
* .. _2-Clause BSD license: http://www.spdx.org/licenses/BSD-2-Clause
*
*   Based on eLyXer: convert LyX source files to HTML output.
*   http://elyxer.nongnu.org/
*/
/* --end--
* CSS file for LaTeX formulas.
*/

/* Formulas */
.formula {
	text-align: center;
	font-family: "Droid Serif", "DejaVu Serif", "STIX", serif;
	margin: 1.2em 0;
}
span.formula {
	white-space: nowrap;
}
div.formula {
	padding: 0.5ex;
	margin-left: auto;
	margin-right: auto;
}

/* Basic features */
a.eqnumber {
	display: inline-block;
	float: right;
	clear: right;
	font-weight: bold;
}
span.unknown {
	color: #800000;
}
span.ignored, span.arraydef {
	display: none;
}
.formula i {
	letter-spacing: 0.1ex;
}

/* Alignment */
.align-left, .align-l {
	text-align: left;
}
.align-right, .align-r {
	text-align: right;
}
.align-center, .align-c {
	text-align: center;
}

/* Structures */
span.overline, span.bar {
	text-decoration: overline;
}
.fraction, .fullfraction {
	display: inline-block;
	vertical-align: middle;
	text-align: center;
}
.fraction .fraction {
	font-size: 80%;
	line-height: 100%;
}
span.numerator {
	display: block;
}
span.denominator {
	display: block;
	padding: 0ex;
	border-top: thin solid;
}
sup.numerator, sup.unit {
	font-size: 70%;
	vertical-align: 80%;
}
sub.denominator, sub.unit {
	font-size: 70%;
	vertical-align: -20%;
}
span.sqrt {
	display: inline-block;
	vertical-align: middle;
	padding: 0.1ex;
}
sup.root {
	font-size: 70%;
	position: relative;
	left: 1.4ex;
}
span.radical {
	display: inline-block;
	padding: 0ex;
	font-size: 150%;
	vertical-align: top;
}
span.root {
	display: inline-block;
	border-top: thin solid;
	padding: 0ex;
	vertical-align: middle;
}
span.symbol {
	line-height: 125%;
	font-size: 125%;
}
span.bigsymbol {
	line-height: 150%;
	font-size: 150%;
}
span.largesymbol {
	font-size: 175%;
}
span.hugesymbol {
	font-size: 200%;
}
span.scripts {
	display: inline-table;
	vertical-align: middle;
}
.script {
	display: table-row;
	text-align: left;
	line-height: 150%;
}
span.limits {
	display: inline-table;
	vertical-align: middle;
}
.limit {
	display: table-row;
	line-height: 99%;
}
sup.limit, sub.limit {
	line-height: 100%;
}
span.symbolover {
	display: inline-block;
	text-align: center;
	position: relative;
	float: right;
	right: 100%;
	bottom: 0.5em;
	width: 0px;
}
span.withsymbol {
	display: inline-block;
}
span.symbolunder {
	display: inline-block;
	text-align: center;
	position: relative;
	float: right;
	right: 80%;
	top: 0.3em;
	width: 0px;
}

/* Environments */
span.array, span.bracketcases, span.binomial, span.environment {
	display: inline-table;
	text-align: center;
	border-collapse: collapse;
	margin: 0em;
	vertical-align: middle;
}
span.arrayrow, span.binomrow {
	display: table-row;
	padding: 0ex;
	border: 0ex;
}
span.arraycell, span.bracket, span.case, span.binomcell, span.environmentcell {
	display: table-cell;
	padding: 0ex 0.2ex;
	line-height: 99%;
	border: 0ex;
}
/*
* CSS file for LaTeX formulas, extra stuff:
* binomials, vertical braces, stackrel, fonts and colors.
*/

/* Inline binomials */
span.binom {
	display: inline-block;
	vertical-align: middle;
	text-align: center;
	font-size: 80%;
}
span.binomstack {
	display: block;
	padding: 0em;
}

/* Over- and underbraces */
span.overbrace {
	border-top: 2pt solid;
}
span.underbrace {
	border-bottom: 2pt solid;
}

/* Stackrel */
span.stackrel {
	display: inline-block;
	text-align: center;
}
span.upstackrel {
	display: block;
	padding: 0em;
	font-size: 80%;
	line-height: 64%;
	position: relative;
	top: 0.15em;

}
span.downstackrel {
	display: block;
	vertical-align: bottom;
	padding: 0em;
}

/* Fonts */
span.mathsf, span.textsf {
	font-style: normal;
	font-family: sans-serif;
}
span.mathrm, span.textrm {
	font-style: normal;
	font-family: serif;
}
span.text, span.textnormal {
	font-style: normal;
}
span.textipa {
	color: #008080;
}
span.fraktur {
	font-family: "Lucida Blackletter", eufm10, blackletter;
}
span.blackboard {
	font-family: Blackboard, msbm10, serif;
}
span.scriptfont {
	font-family: "Monotype Corsiva", "Apple Chancery", "URW Chancery L", cursive;
	font-style: italic;
}

/* Colors */
span.colorbox {
	display: inline-block;
	padding: 5px;
}
span.fbox {
	display: inline-block;
	border: thin solid black;
	padding: 2px;
}
span.boxed, span.framebox {
	display: inline-block;
	border: thin solid black;
	padding: 5px;
}

