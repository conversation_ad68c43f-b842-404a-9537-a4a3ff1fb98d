# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 获取项目根目录
project_root = os.path.abspath('.')
ui2_dir = os.path.join(project_root, 'UI2')
spectrumeter_dir = os.path.join(project_root, 'spectrumeter')

# 数据文件列表 - 只包含必要的文件
datas = [
    # UI2目录下的资源文件
    (os.path.join(ui2_dir, 'logo48.ico'), '.'),
    (os.path.join(ui2_dir, 'software_settings.json'), '.'),
    (os.path.join(ui2_dir, 'users.db'), '.'),
    (os.path.join(ui2_dir, 'recent_files.json'), '.'),
    
    # spectrumeter目录下的DLL文件
    (os.path.join(spectrumeter_dir, 'UserApplication.dll'), '.'),
    (os.path.join(spectrumeter_dir, 'SiUSBXp.dll'), '.'),
    (os.path.join(spectrumeter_dir, 'UserApplication.lib'), '.'),
    
    # spectrumeter目录下的Python文件
    (os.path.join(spectrumeter_dir, 'laser_controller_usb.py'), '.'),
    (os.path.join(spectrumeter_dir, 'raman_spectrometer.py'), '.'),
    (os.path.join(spectrumeter_dir, 'PythonExample_OtOGeneral.py'), '.'),
]

# 二进制文件 - 手动添加关键的DLL文件
binaries = [
    (os.path.join(spectrumeter_dir, 'UserApplication.dll'), '.'),
    (os.path.join(spectrumeter_dir, 'SiUSBXp.dll'), '.'),
]

# 隐藏导入模块 - 只包含必要的模块
hiddenimports = [
    # PyQt6相关
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtPrintSupport',
    'PyQt6.sip',
    
    # matplotlib相关
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_qtagg',
    'matplotlib.backends.backend_agg',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_tkinter',
    
    # 科学计算库
    'numpy',
    'scipy',
    'scipy.signal',
    'scipy.sparse',
    'scipy.sparse.linalg',
    'scipy.linalg',
    'scipy.special',
    'scipy.stats',
    'scipy.optimize',
    'scipy.interpolate',
    'scipy.integrate',
    
    # pandas相关 - 只包含核心模块
    'pandas',
    'pandas._libs',
    'pandas._libs.window',
    'pandas._libs.window.aggregations',
    'pandas._libs.window.indexers',
    'pandas._libs.reduction',
    'pandas._libs.algos',
    'pandas._libs.hashtable',
    'pandas._libs.lib',
    'pandas._libs.tslib',
    'pandas.core',
    'pandas.core.window',
    'pandas.core.groupby',
    'pandas.io',
    'pandas.plotting',
    
    # 其他重要库
    'cryptography',
    'requests',
    'sqlite3',
    'pickle',
    'json',
    'csv',
    'ctypes',
    'threading',
    'datetime',
    'glob',
    'traceback',
    'atexit',
    'tempfile',
    'os',
    'sys',
    're',
    'time',
    'struct',
    'dataclasses',
    'typing',
    
    # 项目内部模块
    'language_manager',
    'log_manager', 
    'menu_bar',
    'sidebar',
    'editor',
    'status_bar',
    'login_dialog',
    'password_dialog',
    'db_user_manager',
    'integrated_measurement_logic',
    'report_dialog',
    'save_files_dialog',
    'spectrum_file_reader',
    'nod_Spectrum_read',
    'nod_file_serializer',
    'nod_raman_read',
    
    # spectrumeter模块
    'laser_controller_usb',
    'raman_spectrometer',
    'PythonExample_OtOGeneral',
]

# 排除不必要的模块
excludes = [
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'test',
    'unittest',
    'pdb',
    'doctest',
    'tkinter',
    'IPython',
    'jupyter',
    'notebook',
    'sphinx',
    'pytest',
    'setuptools',
    'distutils',
    'wheel',
    'pip',
    'conda',
    'sklearn',
    'tensorflow',
    'torch',
    'keras',
    'sympy',
    'astropy',
    'imageio',
    'skimage',
    'plotly',
    'bokeh',
    'seaborn',
    'statsmodels',
    'networkx',
    'nltk',
    'gensim',
    'spacy',
    'opencv',
    'cv2',
    'PIL.SpiderImagePlugin',
    'PIL.MicImagePlugin',
    'PIL.FpxImagePlugin',
]

a = Analysis(
    [os.path.join(ui2_dir, 'main.py')],
    pathex=[ui2_dir, spectrumeter_dir],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='view21',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(ui2_dir, 'logo48.ico'),
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='view21_minimal'
)
