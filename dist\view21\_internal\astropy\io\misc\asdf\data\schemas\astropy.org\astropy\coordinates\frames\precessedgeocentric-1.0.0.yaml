%YAML 1.1
---
$schema: "http://stsci.edu/schemas/yaml-schema/draft-01"
id: "http://astropy.org/schemas/astropy/coordinates/frames/precessedgeocentric-1.0.0"
tag: "tag:astropy.org:astropy/coordinates/frames/precessedgeocentric-1.0.0"

title: |
  Represents a PrecessedGeocentric coordinate object from astropy

examples:
  -
    - A PrecessedGeocentric frame without data
    - |
        !<tag:astropy.org:astropy/coordinates/frames/precessedgeocentric-1.0.0>
          frame_attributes:
            equinox: !time/time-1.1.0 J2000.000
            obsgeoloc: !<tag:astropy.org:astropy/coordinates/representation-1.0.0>
              components:
                x: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m, value: 0.0}
                y: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m, value: 0.0}
                z: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m, value: 0.0}
              type: CartesianRepresentation
            obsgeovel: !<tag:astropy.org:astropy/coordinates/representation-1.0.0>
              components:
                x: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m s-1, value: 0.0}
                y: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m s-1, value: 0.0}
                z: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m s-1, value: 0.0}
              type: CartesianRepresentation
            obstime: !time/time-1.1.0 J2000.000
  -
    - A PrecessedGeocentric frame with data
    - |
        !<tag:astropy.org:astropy/coordinates/frames/precessedgeocentric-1.0.0>
            data: !<tag:astropy.org:astropy/coordinates/representation-1.0.0>
              components:
                lat: !<tag:astropy.org:astropy/coordinates/latitude-1.0.0> {unit: !unit/unit-1.0.0 deg,
                  value: 1.0}
                lon: !<tag:astropy.org:astropy/coordinates/longitude-1.0.0>
                  unit: !unit/unit-1.0.0 deg
                  value: 1.0
                  wrap_angle: !<tag:astropy.org:astropy/coordinates/angle-1.0.0> {unit: !unit/unit-1.0.0 deg,
                    value: 360.0}
              type: UnitSphericalRepresentation
            frame_attributes:
              equinox: !time/time-1.1.0 J2000.000
              obsgeoloc: !<tag:astropy.org:astropy/coordinates/representation-1.0.0>
                components:
                  x: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m, value: 0.0}
                  y: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m, value: 0.0}
                  z: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m, value: 0.0}
                type: CartesianRepresentation
              obsgeovel: !<tag:astropy.org:astropy/coordinates/representation-1.0.0>
                components:
                  x: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m s-1, value: 0.0}
                  y: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m s-1, value: 0.0}
                  z: !unit/quantity-1.1.0 {unit: !unit/unit-1.0.0 m s-1, value: 0.0}
                type: CartesianRepresentation
              obstime: !time/time-1.1.0 J2000.000

allOf:
  - $ref: baseframe-1.0.0
  - properties:
      frame_attributes:
        type: object
        properties:
          equinox:
            $ref: "tag:stsci.edu:asdf/time/time-1.1.0"
          obstime:
            $ref: "tag:stsci.edu:asdf/time/time-1.1.0"
          obsgeoloc:
            $ref: "../representation-1.0.0"
          obsgeovel:
            $ref: "../representation-1.0.0"
...
