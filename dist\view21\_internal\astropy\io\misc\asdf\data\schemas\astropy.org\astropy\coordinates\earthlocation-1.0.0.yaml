%YAML 1.1
---
$schema: "http://stsci.edu/schemas/yaml-schema/draft-01"
id: "http://astropy.org/schemas/astropy/coordinates/earthlocation-1.0.0"
tag: "tag:astropy.org:astropy/coordinates/earthlocation-1.0.0"

title: |
  Represents EarthLocation objects from astropy.

description: |
  Location on the Earth.

type: object
properties:
  x:
    description: |
      X component of location in geocentric representation
    $ref: "tag:stsci.edu:asdf/unit/quantity-1.1.0"
  y:
    description: |
      Y component of location in geocentric representation
    $ref: "tag:stsci.edu:asdf/unit/quantity-1.1.0"
  z:
    description: |
      Z component of location in geocentric representation
    $ref: "tag:stsci.edu:asdf/unit/quantity-1.1.0"
  ellipsoid:
    description: |
      Reference ellipsoid that is used when representing geodetic coordinates.
    type: string
    enum: [WGS84, GRS80, WGS72]

required: [x, y, z]
additionalProperties: False
...
