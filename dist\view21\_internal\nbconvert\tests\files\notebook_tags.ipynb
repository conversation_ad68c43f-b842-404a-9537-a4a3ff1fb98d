{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["mycelltag", "mysecondcelltag"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this cell should have tags in html output\n"]}], "source": ["print(\"this cell should have tags in html output\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this cell should NOT have tags in html output\n"]}], "source": ["print(\"this cell should NOT have tags in html output\")"]}, {"cell_type": "markdown", "metadata": {"tags": ["mymarkdowncelltag"]}, "source": ["This markdown cell should have tags in the html output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This markdown cell should **not** have tags in the html output"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}