import sys
import struct
import math
import numpy as np
import json
import os
from ctypes import *
from array import *
import time

try:
    from laser_controller_usb import LaserController
    LASER_AVAILABLE = True
except ImportError:
    print("无法导入激光控制器模块")
    LASER_AVAILABLE = False

import PythonExample_OtOGeneral as oto

class RamanSpectrometer:
    def __init__(self):
        self.hand = None
        self.frame_size = None
        self.SD_lambda = None
        self.raman_shift = None
        self.integration_time = 50  # 默认积分时间50ms
        self.laser_power = 0  # 默认激光功率
        self.linear_correction_data = None
        self.linear_correction_file = "linear_correction.json"
        
        # 数据存储结构: [像素, 波长, 拉曼位移, 原始强度, 线性校正后强度]
        self.spectrum_data = []
        
        # 初始化激光控制器
        if LASER_AVAILABLE:
            try:
                self.laser_controller = LaserController()
                print("激光控制器初始化成功")
            except Exception as e:
                print(f"激光控制器初始化失败: {e}")
                self.laser_controller = None
        else:
            self.laser_controller = None
        
    def connect_device(self):
        """步骤1: 连接设备并获取设备信息"""
        try:
            self.hand = oto.openDevice()
            oto.getSpectrometerInfo(self.hand)
            
            # 获取设备信息
            SN = (c_char * 16)()
            errorcode = oto.dll.UAI_SpectrometerGetSerialNumber(self.hand, pointer(SN))
            if errorcode != 0:
                return "no"
                
            MN = (c_char * 16)()
            errorcode = oto.dll.UAI_SpectrometerGetModelName(self.hand, pointer(MN))
            if errorcode != 0:
                return "no"
                
            FW_Version = c_uint()
            errorcode = oto.dll.UAI_FirmwareGetVersion(self.hand, pointer(FW_Version))
            if errorcode != 0:
                return "no"
                
            print("光谱仪设备已连接")
            print(f"SN: {SN.value.decode('utf-8')}")
            print(f"MN: {MN.value.decode('utf-8')}")
            print(f"Firmware: {FW_Version.value}")
            return "yes"
            
        except Exception as e:
            print(f"设备连接失败: {e}")
            return "no"
    
    def get_frame_size(self):
        """步骤2: 获取像素值"""
        try:
            self.frame_size = c_short()
            errorcode = oto.dll.UAI_SpectromoduleGetFrameSize(self.hand, pointer(self.frame_size))
            if errorcode != 0:
                raise Exception(f"UAI_SpectromoduleGetFrameSize: {errorcode}")
            
            print(f"帧大小: {self.frame_size.value}")
            return True
        except Exception as e:
            print(f"获取帧大小失败: {e}")
            return False
    
    def get_wavelength_and_raman_shift(self):
        """步骤3-4: 获取波长并计算拉曼位移"""
        try:
            # 获取波长
            self.SD_lambda = (c_float * self.frame_size.value)()
            errorcode = oto.dll.UAI_SpectrometerWavelengthAcquire(self.hand, pointer(self.SD_lambda))
            if errorcode != 0:
                raise Exception(f"UAI_SpectrometerWavelengthAcquire: {errorcode}")
            
            # 计算拉曼位移 (假设激发波长为785nm)
            excitation_wavelength = 785.0  # nm
            self.raman_shift = []
            for wavelength in self.SD_lambda:
                if wavelength > 0:
                    # 拉曼位移 = 1/激发波长 - 1/散射波长 (cm^-1)
                    raman_shift = (1/excitation_wavelength - 1/wavelength) * 1e7
                    self.raman_shift.append(raman_shift)
                else:
                    self.raman_shift.append(0)
            
            print("波长和拉曼位移计算完成")
            return True
        except Exception as e:
            print(f"获取波长失败: {e}")
            return False
    
    def set_integration_time(self, time_ms):
        """设置积分时间"""
        self.integration_time = time_ms
        print(f"积分时间设置为: {time_ms}ms")
    
    def set_laser_power(self, power_percent):
        """设置激光功率（百分比）"""
        try:
            # 验证百分比范围
            if power_percent < 0.0 or power_percent > 100.0:
                raise ValueError(f"功率百分比必须在0-100范围内: {power_percent}%")
            
            # 将百分比转换为毫瓦 (最大功率500mW)
            power_mw = (power_percent / 100.0) * 500.0
            
            if LASER_AVAILABLE and hasattr(self, 'laser_controller'):
                self.laser_controller.set_power_percent(power_percent)
                self.laser_power = power_mw
                print(f"激光功率设置为: {power_percent}% ({power_mw:.1f}mW)")
            else:
                self.laser_power = power_mw
                print(f"激光功率设置为: {power_percent}% ({power_mw:.1f}mW) (模拟模式)")
        except Exception as e:
            print(f"设置激光功率失败: {e}")
            self.laser_power = 0
    
    def turn_on_laser(self):
        """开启激光"""
        try:
            if LASER_AVAILABLE and hasattr(self, 'laser_controller'):
                self.laser_controller.enable_laser()
                print("激光已开启")
                return True
            else:
                print("激光控制器不可用")
                return False
        except Exception as e:
            print(f"开启激光失败: {e}")
            return False
    
    def turn_off_laser(self):
        """关闭激光"""
        try:
            if LASER_AVAILABLE and hasattr(self, 'laser_controller'):
                self.laser_controller.disable_laser()
                print("激光已关闭")
                return True
            else:
                print("激光控制器不可用")
                return False
        except Exception as e:
            print(f"关闭激光失败: {e}")
            return False
    
    def get_error_message(self, errorcode):
        """根据错误码返回详细的错误信息"""
        error_messages = {
            0: "成功",
            -1: "设备未连接",
            -2: "设备忙",
            -3: "参数错误",
            -4: "内存不足",
            -5: "超时",
            -6: "设备错误",
            -7: "不支持的操作",
            -8: "数据格式错误",
            -9: "硬件故障",
            -10: "固件版本不兼容"
        }
        return error_messages.get(errorcode, f"未知错误 (错误码: {errorcode})")
    
    def read_laser_power(self):
        """读取激光功率（返回百分比）"""
        try:
            if LASER_AVAILABLE and hasattr(self, 'laser_controller'):
                # 从激光控制器获取当前功率
                status = self.laser_controller.get_status_summary()
                power_mw = status.get('current_power_mw', 0)
                # 转换为百分比
                power_percent = (power_mw / 500.0) * 100.0
                return power_percent
            else:
                # 将毫瓦转换为百分比
                power_percent = (self.laser_power / 500.0) * 100.0
                return power_percent
        except Exception as e:
            print(f"读取激光功率失败: {e}")
            return 0
    
    def check_device_status(self):
        """检查设备状态：串口连接和激光器状态"""
        status_info = {
            'spectrometer_connected': False,
            'laser_available': False,
            'laser_connected': False,
            'laser_enabled': False,
            'error_messages': []
        }
        
        # 1. 检查光谱仪连接状态
        try:
            if self.hand is None:
                status_info['error_messages'].append("光谱仪未连接")
            else:
                # 尝试获取设备信息来验证连接
                SN = (c_char * 16)()
                errorcode = oto.dll.UAI_SpectrometerGetSerialNumber(self.hand, pointer(SN))
                if errorcode == 0:
                    status_info['spectrometer_connected'] = True
                else:
                    status_info['error_messages'].append(f"光谱仪连接验证失败 (错误码: {errorcode})")
        except Exception as e:
            status_info['error_messages'].append(f"光谱仪状态检查失败: {e}")
        
        # 2. 检查激光器状态（简化版本，只需要激光电源开关打开）
        if LASER_AVAILABLE:
            status_info['laser_available'] = True
            try:
                if hasattr(self, 'laser_controller') and self.laser_controller:
                    # 检查激光控制器连接状态
                    status = self.laser_controller.get_status_summary()
                    if status.get('connected', False):
                        status_info['laser_connected'] = True
                        # 简化：只要激光控制器连接，就认为激光器可用
                        status_info['laser_enabled'] = True
                    else:
                        status_info['error_messages'].append("激光控制器未连接")
                else:
                    status_info['error_messages'].append("激光控制器未初始化")
            except Exception as e:
                status_info['error_messages'].append(f"激光器状态检查失败: {e}")
        else:
            # 如果激光控制器模块不可用，仍然可以进行采集（手动控制激光）
            status_info['laser_available'] = True
            status_info['laser_connected'] = True
            status_info['laser_enabled'] = True
            status_info['error_messages'].append("激光控制器模块不可用，请手动控制激光电源")
        
        return status_info
    
    def is_acquisition_ready(self):
        """检查是否可以进行采集"""
        status = self.check_device_status()
        
        if not status['spectrometer_connected']:
            print("❌ 光谱仪未连接，无法进行采集")
            return False
        
        # 激光器检查：只要光谱仪连接就可以进行采集
        # 激光器通过手动开关控制，不需要软件检查
        print("✅ 设备状态正常，可以进行采集")
        print("⚠️  请确保激光电源开关已打开")
        return True
    
    def collect_dark_background(self, max_retries=3):
        """采集暗背景，支持重试机制"""
        for attempt in range(max_retries):
            try:
                buffer = (c_float * self.frame_size.value)()
                errorcode = oto.dll.UAI_SpectrometerDataOneshot(self.hand, self.integration_time * 1000, pointer(buffer), 1)
                if errorcode != 0:
                    error_msg = self.get_error_message(errorcode)
                    raise Exception(f"暗背景采集失败 (错误码: {errorcode}): {error_msg}")
                
                # 应用硬件线性校准
                errorcode = oto.dll.UAI_LinearityCorrection(self.hand, self.frame_size, pointer(buffer))
                if errorcode != 0:
                    error_msg = self.get_error_message(errorcode)
                    print(f"暗背景硬件线性校准失败 (错误码: {errorcode}): {error_msg}")
                    # 即使线性校准失败，也继续使用原始数据
                
                dark_background = [float(buffer[i]) for i in range(self.frame_size.value)]
                print("暗背景采集完成（已应用硬件线性校准）")
                return dark_background
            except Exception as e:
                print(f"暗背景采集失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    print(f"等待2秒后重试...")
                    time.sleep(2)
                else:
                    print("暗背景采集最终失败，已达到最大重试次数")
                    return None
    
    def collect_spectrum_with_laser(self, max_retries=3):
        """使用激光采集光谱，支持重试机制"""
        for attempt in range(max_retries):
            try:
                buffer = (c_float * self.frame_size.value)()
                errorcode = oto.dll.UAI_SpectrometerDataOneshot(self.hand, self.integration_time * 1000, pointer(buffer), 1)
                if errorcode != 0:
                    error_msg = self.get_error_message(errorcode)
                    raise Exception(f"激光光谱采集失败 (错误码: {errorcode}): {error_msg}")
                
                # 应用硬件线性校准
                errorcode = oto.dll.UAI_LinearityCorrection(self.hand, self.frame_size, pointer(buffer))
                if errorcode != 0:
                    error_msg = self.get_error_message(errorcode)
                    print(f"硬件线性校准失败 (错误码: {errorcode}): {error_msg}")
                    # 即使线性校准失败，也继续使用原始数据
                
                laser_spectrum = [float(buffer[i]) for i in range(self.frame_size.value)]
                print("激光光谱采集完成（已应用硬件线性校准）")
                return laser_spectrum
            except Exception as e:
                print(f"激光光谱采集失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    print(f"等待2秒后重试...")
                    time.sleep(2)
                else:
                    print("激光光谱采集最终失败，已达到最大重试次数")
                    return None
    
    def apply_background_removal(self, spectrum, dark_background):
        """背景去除"""
        try:
            corrected_spectrum = []
            for i in range(len(spectrum)):
                #print(spectrum[i], dark_background[i])
                corrected_intensity = spectrum[i] - dark_background[i]
                corrected_spectrum.append(corrected_intensity)  # 保留原始相减值
            
            print("背景去除完成")
            return corrected_spectrum
        except Exception as e:
            print(f"背景去除失败: {e}")
            return spectrum
    
    def apply_linear_correction(self, spectrum):
        """应用线性校正"""
        try:
            if self.linear_correction_data is None:
                print("没有线性校正数据，跳过校正")
                return spectrum
            
            corrected_spectrum = []
            for i in range(len(spectrum)):
                if i < len(self.linear_correction_data):
                    correction_factor = self.linear_correction_data[i]
                    corrected_intensity = spectrum[i] * correction_factor
                    corrected_spectrum.append(corrected_intensity)
                else:
                    corrected_spectrum.append(spectrum[i])
            
            print("线性校正完成")
            return corrected_spectrum
        except Exception as e:
            print(f"线性校正失败: {e}")
            return spectrum
    
    def perform_linear_correction(self, reference_spectrum=None):
        """执行线性校正并保存校正数据
        如果未传入reference_spectrum，则不进行线性校正。
        """
        try:
            if reference_spectrum is None:
                print("未提供参考光谱，跳过线性校正")
                return False
            else:
                # 自定义强度校准逻辑
                correction_factors = []
                for i in range(len(reference_spectrum)):
                    if i < len(self.spectrum_data) and self.spectrum_data[i][3] > 0:  # 原始强度
                        factor = reference_spectrum[i] / self.spectrum_data[i][3]
                        correction_factors.append(factor)
                    else:
                        correction_factors.append(1.0)
                # 保存校正数据
                correction_data = {
                    'correction_factors': correction_factors,
                    'timestamp': time.time()
                }
                with open(self.linear_correction_file, 'w') as f:
                    json.dump(correction_data, f)
                self.linear_correction_data = correction_factors
                print("线性校正数据已保存")
                return True
        except Exception as e:
            print(f"线性校正失败: {e}")
            return False
    
    def load_linear_correction(self):
        """加载线性校正数据"""
        try:
            if os.path.exists(self.linear_correction_file):
                with open(self.linear_correction_file, 'r') as f:
                    data = json.load(f)
                    self.linear_correction_data = data['correction_factors']
                print("线性校正数据已加载")
                return True
            else:
                print("没有找到线性校正数据文件")
                return False
        except Exception as e:
            print(f"加载线性校正数据失败: {e}")
            return False
    
    def close_laser_controller(self):
        """关闭激光控制器"""
        try:
            if hasattr(self, 'laser_controller') and self.laser_controller:
                self.laser_controller.close()
                print("激光控制器已关闭")
        except Exception as e:
            print(f"关闭激光控制器失败: {e}")
    
    def __del__(self):
        """析构函数，确保激光控制器被正确关闭"""
        self.close_laser_controller()
    
    def start_acquisition(self, max_retries=3):
        """开始采集流程"""
        try:
            print("开始光谱采集...")
            
            # 0. 检查设备状态
            print("检查设备状态...")
            if not self.is_acquisition_ready():
                print("设备状态检查失败，终止采集流程")
                return False
            
            # 1. 采集暗背景
            print("采集暗背景...")
            dark_background = self.collect_dark_background(max_retries)
            if dark_background is None:
                print("暗背景采集失败，终止采集流程")
                return False
            
            # 2. 确保激光处于关闭状态，然后开启激光
            print("确保激光处于关闭状态...")
            self.turn_off_laser()
            time.sleep(0.3)  # 等待激光完全关闭
            
            print("开启激光...")
            if not self.turn_on_laser():
                print("激光开启失败，终止采集流程")
                return False
            
            # 3. 读取激光功率
            actual_power = self.read_laser_power()
            print(f"实际激光功率: {actual_power:.1f}%")
            
            # 4. 采集激光光谱
            print("采集激光光谱...")
            laser_spectrum = self.collect_spectrum_with_laser(max_retries)
            if laser_spectrum is None:
                print("激光光谱采集失败，终止采集流程")
                return False
            
            # 5. 关闭激光
            self.turn_off_laser()
            
            # 6. 背景去除
            corrected_spectrum = self.apply_background_removal(laser_spectrum, dark_background)
            
            # 7. 构建数据数组
            self.spectrum_data = []
            for i in range(self.frame_size.value):
                pixel = i
                wavelength = float(self.SD_lambda[i])
                raman_shift = self.raman_shift[i] if i < len(self.raman_shift) else 0
                raw_intensity = corrected_spectrum[i]
                
                # 应用线性校正
                linear_corrected_intensity = raw_intensity
                if self.linear_correction_data is not None and i < len(self.linear_correction_data):
                    linear_corrected_intensity = raw_intensity * self.linear_correction_data[i]
                
                self.spectrum_data.append([pixel, wavelength, raman_shift, raw_intensity, linear_corrected_intensity])
            
            print("光谱采集完成")
            return True
            
        except Exception as e:
            print(f"采集过程失败: {e}")
            # 确保激光被关闭
            try:
                self.turn_off_laser()
            except:
                pass
            return False
    
    def save_spectrum_data(self, filename):
        """保存光谱数据"""
        try:
            with open(filename, 'w') as f:
                f.write("像素,波长(nm),拉曼位移(cm^-1),原始强度,线性校正后强度\n")
                for data in self.spectrum_data:
                    f.write(f"{data[0]},{data[1]:.2f},{data[2]:.2f},{data[3]:.6f},{data[4]:.6f}\n")
            print(f"数据已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def get_spectrum_data_json(self):
        """获取光谱数据的JSON格式，用于前端显示"""
        if not self.spectrum_data:
            return None
        
        try:
            data = {
                'pixels': [],
                'wavelengths': [],
                'raman_shifts': [],
                'raw_intensities': [],
                'corrected_intensities': []
            }
            
            for spectrum_point in self.spectrum_data:
                data['pixels'].append(spectrum_point[0])
                data['wavelengths'].append(spectrum_point[1])
                data['raman_shifts'].append(spectrum_point[2])
                data['raw_intensities'].append(spectrum_point[3])
                data['corrected_intensities'].append(spectrum_point[4])
            
            return data
        except Exception as e:
            print(f"生成JSON数据失败: {e}")
            return None
    
    def get_device_info(self):
        """获取设备信息"""
        try:
            if self.hand is None:
                return None
            
            SN = (c_char * 16)()
            errorcode = oto.dll.UAI_SpectrometerGetSerialNumber(self.hand, pointer(SN))
            if errorcode != 0:
                return None
                
            MN = (c_char * 16)()
            errorcode = oto.dll.UAI_SpectrometerGetModelName(self.hand, pointer(MN))
            if errorcode != 0:
                return None
                
            FW_Version = c_uint()
            errorcode = oto.dll.UAI_FirmwareGetVersion(self.hand, pointer(FW_Version))
            if errorcode != 0:
                return None
            
            return {
                'serial_number': SN.value.decode('utf-8'),
                'model_name': MN.value.decode('utf-8'),
                'firmware_version': FW_Version.value,
                'frame_size': self.frame_size.value if self.frame_size else None,
                'integration_time': self.integration_time,
                'laser_power': self.laser_power
            }
        except Exception as e:
            print(f"获取设备信息失败: {e}")
            return None

def main():
    """主函数 - 用于测试"""
    print("拉曼光谱仪数据采集系统")
    print("=" * 50)
    
    # 创建光谱仪实例
    spectrometer = RamanSpectrometer()
    
    # 连接设备
    if spectrometer.connect_device() == "yes":
        print("设备连接成功")
        
        # 获取设备信息
        if spectrometer.get_frame_size():
            if spectrometer.get_wavelength_and_raman_shift():
                # 加载线性校正数据
                spectrometer.load_linear_correction()
                
                # 设置参数
                spectrometer.set_integration_time(50)
                spectrometer.set_laser_power(10)
                
                # 开始采集
                if spectrometer.start_acquisition():
                    print("采集完成")
                    
                    # 获取JSON格式数据
                    json_data = spectrometer.get_spectrum_data_json()
                    if json_data:
                        print("数据已准备就绪，可用于前端显示")
                    
                    # 保存数据
                    spectrometer.save_spectrum_data("test_spectrum.csv")
                else:
                    print("采集失败")
            else:
                print("获取波长失败")
        else:
            print("获取帧大小失败")
    else:
        print("设备连接失败")
    
    # 关闭激光控制器
    spectrometer.close_laser_controller()

if __name__ == "__main__":
    main() 